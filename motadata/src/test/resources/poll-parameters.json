{"testPollLinuxMetric": "172.16.8.135", "testPollLinux2Metric": "fd00:1:1:1:250:56ff:febb:eb2", "testPollLinuxCPUCoreMetric": "172.16.8.135", "testPollLinuxDiskMetric": "172.16.8.135", "testPollLinuxNetworkInterfaceMetric": "172.16.8.135", "testPollLinuxAvailabilityMetric": "172.16.8.135", "testPollLinuxNetworkServiceMetric": "172.16.8.135", "testPollLinuxProcessMetric": "172.16.8.135", "testPollLinuxDirectoryMetric": "172.16.8.135", "testPollLinuxFileMetric": "172.16.8.135", "testPollWindowsMetric": "***********0", "testPollWindowsCPUCoreMetric": "***********0", "testPollWindowsDiskMetric": "***********0", "testPollWindowsNetworkInterfaceMetric": "***********0", "testPollWindowsAvailabilityMetric": "***********0", "testPollWindowsNetworkServiceMetric": "***********0", "testPollWindowsProcessMetric": "***********0", "testPollWindowsDirectoryMetric": "***********0", "testPollWindowsFileMetric": "***********0", "testPollWindowsTaskSchedulerMetric": "***********0", "testPollSolarisMetric": "172.16.8.133", "testPollSolarisCPUCoreMetric": "172.16.8.133", "testPollSolarisDiskMetric": "172.16.8.133", "testPollSolarisNetworkInterfaceMetric": "172.16.8.133", "testPollSolarisAvailabilityMetric": "172.16.8.133", "testPollSolarisNetworkServiceMetric": "172.16.8.133", "testPollSolarisProcessMetric": "172.16.8.133", "testPollWindowsClusterMetric": "172.16.9.161", "testPollWindowsClusterDiskMetric": "172.16.9.161", "testPollIBMAIXMetric": "172.16.10.65", "testPollIBMAIXCPUCoreMetric": "172.16.10.65", "testPollIBMAIXDiskMetric": "172.16.10.65", "testPollIBMAIXNetworkInterfaceMetric": "172.16.10.65", "testPollIBMAIXNetworkServiceMetric": "172.16.10.65", "testPollHPUXMetric": "172.16.10.62", "testPollHPUXCPUCoreMetric": "172.16.10.62", "testPollHPUXDiskMetric": "172.16.10.62", "testPollHPUXNetworkServiceMetric": "172.16.10.62", "testPollWindowsDotNetAppMetric": "**********30", "testPollSNMPv1SwitchSNMPInterfaceMetric": "************", "testPollSNMPv3SwitchSNMPInterfaceMetric": "**********", "testPollSNMPv2cFirewallSNMPInterfaceMetric": "***********", "testPollSNMPv2cFirewallSNMPScalarMetric": "***********", "testPollSNMPv3SwitchSNMPScalarMetric": "**********", "testPollSNMPv3TabularMetric": "**********", "testPollSNMPv2cFirewallAvailabilityMetric": "***********", "testPollSNMPv1AvailabilityMetric": "************", "testPollSNMPv3SwitchAvailabilityMetric": "**********", "testPollSNMPv1SwitchVLANMetric": "************", "testPollSNMPv3SwitchVLANMetric": "**********", "testPollSNMPv3SwitchSTPMetric": "**********", "testPollSNMPv3SwitchRoutingMetric": "**********", "testPollSNMPv2cFirewallRemoteVPNMetric": "***********", "testPollSNMPv2cFirewallSiteVPNMetric": "***********", "testPollNetworkDeviceConfigMetric": "************", "testPollSwitchPortMapperMetric": "**********", "testPollNetworkConnectionMetric": "************", "testPollVRFMetric": "**********", "testPollBGPMetric": "***********", "testPollISISMetric": "***********", "testPollOSPFMetric": "***********", "testPollIPSLAICMPEchoMetric": "***********", "testPollIPSLAICMPJitterMetric": "***********", "testPollIPSLAPathEchoMetric": "***********", "testPollSNMPv1SwitchAvailabilityMetric": "************", "testPollCiscoUCSChassisMetric": "************", "testPollCiscoUCSRackMountMetric": "************", "testPollCiscoUCSFabricInterconnectMetric": "************", "testPollCiscoUCSAvailabilityMetric": "************", "testPollSymantecMessagingGatewayMetric": "*************", "testPollPingServiceCheckMetric": "************", "testPollPortServiceCheckMetric": "*************", "testPollRADIUSServiceCheckMetric": "***********", "testPollNTPServiceCheckMetric": "************", "testPollFTPServiceCheckMetric": "************", "testPollDomainServiceCheckMetric": "motadata.com", "testPollDNSServiceCheckMetric": "************", "testPollEmailServiceCheckMetric": "smtp.gmail.com", "testPollCertificateServiceCheckMetric": "https://www.motadata.com", "testPollURLServiceCheckMetric": "postman-echo.com/get", "testPollvCenterMetric": "*************", "testPollvCenterAvailabilityMetric": "*************", "testPollvCenterDataStoreMetric": "*************", "testPollvCenterDataCenterMetric": "*************", "testPollESXiMetric": "172.16.11.11", "testPollESXiAvailabilityMetric": "172.16.11.11", "testPollESXiConfigMetric": "172.16.11.11", "testPollESXiHardwareSensorMetric": "172.16.11.11", "testESXiNetworkMetric": "172.16.11.11", "testPollESXiStorageMetric": "172.16.11.11", "testPollESXiDataStoreMetric": "172.16.11.11", "testPollESXiVMMetric": "172.16.11.11", "testPollHyperVMetric": "***********0", "testPollHyperVAvailabilityMetric": "***********0", "testPollHyperVConfigMetric": "***********0", "testPollHyperVNetworkMetric": "***********0", "testPollHyperVServiceMetric": "***********0", "testPollHyperVStorageMetric": "***********0", "testPollHyperVVMMetric": "***********0", "testPollCitrixXenMetric": "172.16.8.52", "testPollCitrixXenAvailabilityMetric": "172.16.8.52", "testPollCitrixXenConfigMetric": "172.16.8.52", "testPollCitrixXenNetworkMetric": "172.16.8.52", "testPollCitrixXenStorageMetric": "172.16.8.52", "testPollCitrixXenVMMetric": "172.16.8.52", "testPollCitrixXenClusterMetric": "172.16.8.170", "testPollCitrixXenClusterAvailabilityMetric": "172.16.8.170", "testPollCitrixXenClusterConfigMetric": "172.16.8.170", "testPollHyperVClusterMetric": "**********30", "testPollHyperVClusterAvailabilityMetric": "**********30", "testPollHyperVClusterStorageMetric": "**********30", "testPollHyperVClusterSecondMonitor": "172.16.9.161", "testPollCiscoWirelessMetric": "172.16.9.44", "testPollCiscoWirelessRougeAPMetric": "172.16.9.44", "testPollCiscoWirelessClientMetric": "172.16.9.44", "testPollCiscoWirelessAvailabilityMetric": "172.16.9.44", "testPollRuckusWirelessMetric": "10.20.40.4", "testPollRuckusWirelessClientMetric": "10.20.40.4", "testPollRuckusWirelessRogueAPMetric": "10.20.40.4", "testPollRuckusWirelessWLANMetric": "10.20.40.4", "testPollRuckusWirelessAvailabilityMetric": "10.20.40.4", "testPollArubaWirelessMetric": "172.16.10.242", "testPollArubaWirelessRougeAPMetric": "172.16.10.242", "testPollArubaWirelessClientMetric": "172.16.10.242", "testPollArubaWirelessAvailabilityMetric": "172.16.10.242", "testPollAWSCloudMetric": "AWS Cloud (************)", "testPollAWSCloudBillingMetric": "AWS Cloud (************)", "testPollAWSEC2Metric": "Amazon EC2", "testPollAWSSNSMetric": "Amazon SNS", "testPollAWSDynamoDBMetric": "Amazon DynamoDB", "testPollAWSEBSMetric": "Amazon EBS", "testPollAWSELBMetric": "AWS ELB", "testPollAWSRDSMetric": "Amazon RDS", "testPollAWSS3Metric": "Amazon S3", "testPollAzureCloudMetric": "AWS Cloud (************)", "testPollAzureCosmosDBMetric": "Azure Cosmos DB", "testPollAzureSQLDatabaseMetric": "Azure SQL Database", "testPollAzureStorageMetric": "Azure Storage", "testPollAzureVMMetric": "Azure VM", "testPollAzureWebAppMetric": "Azure WebApp", "testPollAzureBillingMetric": "Azure Cloud (5807cfb0-41a6-4da6-b920-71d934d4a2af)", "testPollOffice365Metric": "Office 365 (motadataindia)", "testPollSharepointOnlineMetric": "SharePoint Online", "testPollExchangeOnlineStatusMetric": "exchangeonlinestatus", "testPollMicrosoftTeamsMetric": "Microsoft Teams", "testPollOneDriveMetric": "OneDrive", "testPollSharepointOnlineStatusMetric": "sharepointonlinestatus", "testPollMicrosoftTeamsStatusMetric": "microsoftteamsstatus", "testPollOneDriveStatusMetric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "testPollOffice365StatusMetric": "office365status", "testPollExchangeOnlineMetric": "exchangeonlinemailbox", "testPollAmazonCloudFrontMetric": "Amazon CloudFront", "testPollAWSAutoScalingMetric": "AWS Auto Scaling", "testPollAWSLambdaMetric": "AWS Lambda", "testPollAmazonSQSMetric": "Amazon SQS", "testPollAWSElasticBeanstalkMetric": "AWS Elastic Beanstalk", "testPollAzureServiceBusMetric": "Azure Service Bus", "testPollAzureApplicationGatewayMetric": "Azure Application Gateway", "testPollAzureFunctionMetric": "Azure Function", "testPollAzureLoadBalancerMetric": "Azure Load Balancer", "testPollAzureVMScaleSetMetric": "Azure VM Scale Set", "testPollAzureCDNMetric": "Azure CDN", "testSNMPHPEILOMetric": "172.16.10.245", "testSNMPDellIDRACMetric": "172.16.10.246", "testPollIBMTapeLibraryMetric": "10.187.210.63", "testPollPrismMetric": "172.16.10.212", "testPollPrismClusterMetric": "172.16.10.212", "testPollPrismStorageMetric": "172.16.10.212", "testPollPrismDiskMetric": "172.16.10.212", "testPollPrismAvailabilityMetric": "172.16.10.212", "testPollNutanixMetric": "172.16.10.210", "testPollNutanixVMMetric": "172.16.10.210", "testPollNutanixDiskMetric": "172.16.10.210", "testPollNutanixAvailabilityMetric": "172.16.10.210", "testPollCiscovManageMetric": "10.10.20.90", "testPollCiscovEdgeBGPRouteMetric": "10.10.1.11", "testPollCiscovEdgeBGPNeighborMetric": "10.10.1.11", "testPollCiscovManageSiteMetric": "10.10.20.90", "testPollCiscovSmartMetric": "10.10.1.5", "testPollCiscovBondMetric": "10.10.1.3", "testPollCiscovEdgeMetric": "10.10.1.11", "testPollCiscovEdgeInterfaceMetric": "10.10.1.11", "testPollCiscovEdgeTunnelMetric": "10.10.1.11", "testPollCiscovEdgeTLOCMetric": "10.10.1.11", "testPollCiscovEdgeHardwareSensorMetric": "10.10.1.11", "testPollCiscoMerakiMetric": "n149.meraki.com", "testPollCiscoMerakiNetworkMetric": "n149.meraki.com", "testPollCiscoMerakiClientMetric": "n149.meraki.com", "testPollCiscoMerakiVPNMetric": "n149.meraki.com", "testPollCiscoMerakiSecurityMetric": "10.10.10.118", "testPollCiscoMerakiSubnetMetric": "10.10.10.118", "testPollCiscoMerakiVLANMetric": "10.10.10.118", "testPollCiscoMerakiInterfaceMetric": "10.10.10.118", "testPollCiscoMerakiSwitchMetric": "10.10.10.131", "testPollCiscoMerakiSTPMetric": "10.10.10.131", "testPollCiscoMerakiRadioMetric": "10.10.10.156", "testPollCiscoMerakiEthernetMetric": "10.10.10.156", "testPollCiscoMerakiSSIDMetric": "10.10.10.156", "testPollCiscoMerakiChannelMetric": "10.10.10.156", "testPollNetAppONTAPClusterNodeMetric": "************", "testPollNetAppONTAPClusterHardwareSensorMetric": "************", "testPollNetAppONTAPClusterInterfaceMetric": "************", "testPollNetAppONTAPClusterDiskMetric": "************", "testPollNetAppONTAPClusterStorageMetric": "************", "testPollNetAppONTAPClusterPortMetric": "************", "testNetAppONTAPClusterSVMMetric": "************", "testPollNetAppONTAPClusterMetric": "************", "testHPEStoreOnceMetric": "************", "testHPEStoreOnceDiskMetric": "************", "testHPEStoreOnceServiceSetMetric": "************", "testHPEStoreOncePortMetric": "************", "testPollCiscoACIMetric": "***********", "testPollCiscoACIFabricMetric": "***********", "testPollCiscoACITenantMetric": "***********", "testPollCiscoACIEndpointMetric": "***********", "testHPEPrimeraMetric": "************", "testHPEPrimeraDiskMetric": "************", "testHPEPrimeraHostMetric": "************", "testHPEPrimeraNodeMetric": "************", "testHPEPrimeraVolumeMetric": "************", "testHPEPrimeraPortMetric": "************", "testHPE3PARMetric": "************", "testHPE3PARDiskMetric": "************", "testHPE3PARHostMetric": "************", "testHPE3PARNodeMetric": "************", "testHPE3PARVolumeMetric": "************", "testHPE3PARPortMetric": "************", "testDellEMCUnityMetric": "************", "testDellEMCUnityEnclosureMetric": "************", "testDellEMCUnityFileSystemMetric": "************", "testDellEMCUnitFileShareMetric": "************", "testDellEMCUnityStorageMetric": "************", "testDellEMCUnityStorageProcessorMetric": "************"}