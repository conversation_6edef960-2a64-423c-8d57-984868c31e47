{"TestVirtualizationRediscovery": {"esxi": "************"}, "TestServerRediscovery": {"windows": "************", "linux": "************"}, "TestNetworkRediscovery": {"network": "**********"}, "TestWANLinkRediscovery": {"ipsla": "***********"}, "TestHCIRediscovery": {"nutanix": "*************"}, "testRediscoverApps": {"scheduler.job.type": "Rediscover", "scheduler.start.date": "18-07-2023", "scheduler.times": ["00:00"], "scheduler.timeline": "Once", "scheduler.context": {"rediscover.job": "Application"}}, "testRediscoverProcess": {"scheduler.job.type": "Rediscover", "scheduler.start.date": "13-07-2020", "scheduler.times": ["00:00"], "scheduler.timeline": "Once", "scheduler.context": {"rediscover.job": "Process"}}, "testRediscoverWindowsService": {"scheduler.job.type": "Rediscover", "scheduler.start.date": "13-07-2020", "scheduler.times": ["00:00"], "scheduler.timeline": "Once", "scheduler.context": {"rediscover.job": "Windows Service"}}, "testRediscoverWindowsServiceAutoProvision": {"scheduler.job.type": "Rediscover", "scheduler.start.date": "13-07-2020", "scheduler.times": ["00:00"], "scheduler.timeline": "Once", "scheduler.context": {"auto.provision.status": "yes", "rediscover.job": "Windows Service"}}, "testRediscoverDirectoryAutoProvision": {"scheduler.job.type": "Rediscover", "scheduler.start.date": "13-07-2020", "scheduler.times": ["00:00"], "scheduler.timeline": "Once", "scheduler.context": {"auto.provision.status": "yes", "rediscover.job": "File/Directory"}}, "testRediscoverDirectory": {"scheduler.job.type": "Rediscover", "scheduler.start.date": "13-07-2020", "scheduler.times": ["00:00"], "scheduler.timeline": "Once", "scheduler.context": {"rediscover.job": "File/Directory"}}, "testRediscoverVirtualizationObjectHavingAutoProvision": {"scheduler.job.type": "Rediscover", "scheduler.start.date": "13-07-2020", "scheduler.times": ["00:00"], "scheduler.timeline": "Once", "scheduler.context": {"auto.provision.status": "yes", "rediscover.job": "Virtual Machine"}}, "testRediscoverVirtualizationObject": {"scheduler.job.type": "Rediscover", "scheduler.start.date": "13-07-2020", "scheduler.times": ["00:00"], "scheduler.timeline": "Once", "scheduler.context": {"rediscover.job": "Virtual Machine"}}, "testRediscoverCloudObject": {"scheduler.job.type": "Rediscover", "scheduler.start.date": "13-07-2020", "scheduler.times": ["00:00"], "scheduler.timeline": "Once", "scheduler.context": {"rediscover.job": "Cloud"}}, "testRediscoverCloudObjectAutoProvision": {"scheduler.job.type": "Rediscover", "scheduler.start.date": "13-07-2020", "scheduler.email.recipients": ["<EMAIL>"], "scheduler.times": ["00:00"], "scheduler.timeline": "Once", "scheduler.context": {"auto.provision.status": "yes", "rediscover.job": "Cloud"}}, "testRediscoverNetworkInterface": {"scheduler.job.type": "Rediscover", "scheduler.start.date": "13-07-2020", "scheduler.times": ["00:00"], "scheduler.timeline": "Once", "scheduler.context": {"scheduler.sms.recipients": ["9726133498"], "rediscover.job": "Network Interface"}}, "testRediscoverNetworkInterfaceAutoProvision": {"scheduler.job.type": "Rediscover", "scheduler.start.date": "13-07-2020", "scheduler.times": ["00:00"], "scheduler.timeline": "Once", "scheduler.context": {"auto.provision.status": "yes", "rediscover.job": "Network Interface"}}, "testIPSLAICMPEchoRediscovery": {"rediscover.job": "WAN Link", "discovery.credential.profiles": 10000000000001, "object.vendor": "Cisco Systems", "object.type": "ipslaicmpecho", "discovery.object.type": "ip.address", "operation.frequency": 120, "operation.timeout": 60000, "user.name": "admin", "object": {"interface.ip.address": "**********", "destination.ip.address": "**********", "source.router.location": "Delhi", "destination.router.location": "Mumbai", "internet.service.provider": "Airtel", "interface.name": "Fa/0-0"}}, "testIPSLAICMPJitterRediscovery": {"rediscover.job": "WAN Link", "discovery.credential.profiles": 10000000000001, "object.vendor": "Cisco Systems", "object.type": "ipslaicmpjitter", "discovery.object.type": "ip.address", "operation.frequency": 120, "operation.timeout": 60000, "user.name": "admin", "object": {"destination.ip.address": "**********", "source.router.location": "Delhi", "destination.router.location": "Ahmedabad", "internet.service.provider": "Airtel"}}, "testIPSLAPathEchoRediscovery": {"rediscover.job": "WAN Link", "discovery.credential.profiles": 10000000000001, "object.vendor": "Cisco Systems", "object.type": "ipslapathecho", "discovery.object.type": "ip.address", "operation.frequency": 120, "operation.timeout": 60000, "user.name": "admin", "object": {"destination.ip.address": "**********", "source.router.location": "Delhi", "destination.router.location": "Ahmedabad", "internet.service.provider": "Airtel"}}, "testIPSLACSVRediscovery": {"rediscover.job": "WAN Link", "discovery.credential.profiles": 10000000000001, "object.vendor": "Cisco Systems", "discovery.object.type": "csv", "operation.frequency": 120, "operation.timeout": 60000, "user.name": "admin", "timeout": 60}, "testRediscoverHCIObject": {"scheduler.job.type": "Rediscover", "scheduler.start.date": "12-09-2024", "scheduler.times": ["00:00"], "scheduler.timeline": "Once", "scheduler.context": {"rediscover.job": "Virtual Machine (HCI)"}}, "testRediscoverHCIObjectHavingAutoProvision": {"scheduler.job.type": "Rediscover", "scheduler.start.date": "12-09-2024", "scheduler.times": ["00:00"], "scheduler.timeline": "Once", "scheduler.context": {"auto.provision.status": "yes", "rediscover.job": "Virtual Machine (HCI)"}}, "testDockerRediscovery": {"rediscover.job": "Container", "metric.plugin": "dockercontainer", "discovery.context": {"port": 2375, "discover.all.containers": "yes"}, "user.name": "admin", "linux": "***********9"}}