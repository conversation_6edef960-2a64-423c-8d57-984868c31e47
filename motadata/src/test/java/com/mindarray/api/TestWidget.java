/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.store.TemplateConfigStore;
import com.mindarray.store.WidgetConfigStore;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.VISUALIZATION_TEMPLATE_API_ENDPOINT;
import static com.mindarray.TestAPIConstants.VISUALIZATION_WIDGET_API_ENDPOINT;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(VertxExtension.class)
@Timeout(60 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
@Execution(ExecutionMode.SAME_THREAD)
public class TestWidget
{
    private static final JsonArray IDS = new JsonArray();
    private static final Logger LOGGER = new Logger(TestWidget.class, MOTADATA_API, "Test Widget");
    private static long widgetId;

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateWidgetHistogramScalarMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"visualization.name\":\"TestWidget\",\"visualization.description\":\"TestWidget\",\"visualization.granularity\":\"5 m\",\"visualization.timeline\":{\"relative.timeline\":\"today\"},\"visualization.category\":\"Chart\",\"visualization.type\":\"Area\",\"visualization.data.sources\":[{\"type\":\"metric\",\"filters\":{\"data.filter\":{},\"result.filter\":{}},\"data.points\":[{\"data.point\":\"ping.min.latency.ms\",\"aggregator\":\"avg\"}]}],\"visualization.properties\":{\"chart\":{\"rotation.angle\":0,\"chart.legend\":\"no\",\"chart.label\":\"no\",\"highchart.settings\":{},\"sorting\":{\"limit\":10,\"order\":\"desc\"}}},\"granularity\":{\"value\":5,\"unit\":\"m\"}}");

        context.put(VisualizationConstants.VISUALIZATION_NAME, context.getString(VisualizationConstants.VISUALIZATION_NAME) + " " + System.currentTimeMillis());

        TestAPIUtil.post(VISUALIZATION_WIDGET_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(WidgetConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.WIDGET.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.add(response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();

                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateWidgetHistogramInstanceMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"visualization.name\":\"TestWidget\",\"visualization.description\":\"TestWidget\",\"visualization.granularity\":\"5 m\",\"visualization.timeline\":{\"relative.timeline\":\"today\"},\"visualization.category\":\"Chart\",\"visualization.type\":\"Area\",\"visualization.data.sources\":[{\"type\":\"metric\",\"filters\":{\"data.filter\":{},\"result.filter\":{}},\"data.points\":[{\"data.point\":\"interface~in.packets\",\"aggregator\":\"avg\"}]}],\"visualization.properties\":{\"chart\":{\"rotation.angle\":0,\"chart.legend\":\"no\",\"chart.label\":\"no\",\"highchart.settings\":{},\"sorting\":{\"limit\":10,\"order\":\"desc\"}}},\"granularity\":{\"value\":5,\"unit\":\"m\"}}");

        context.put(VisualizationConstants.VISUALIZATION_NAME, context.getString(VisualizationConstants.VISUALIZATION_NAME) + " " + System.currentTimeMillis());

        TestAPIUtil.post(VISUALIZATION_WIDGET_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(WidgetConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.WIDGET.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.add(response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();

                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testCreateWidgetGridScalarMetric(VertxTestContext testContext, TestInfo testInfo)
    {

        var context = new JsonObject("{\"visualization.name\":\"TestWidget-Grid\",\"visualization.description\":\"TestWidget-Grid\",\"visualization.timeline\":{\"relative.timeline\":\"today\"},\"visualization.category\":\"Grid\",\"visualization.type\":\"Grid\",\"visualization.data.sources\":[{\"type\":\"metric\",\"filters\":{\"data.filter\":{},\"result.filter\":{}},\"visualization.result.by\":[\"monitor\"],\"data.points\":[{\"data.point\":\"ping.latency.ms\",\"aggregator\":\"avg\"}]}],\"visualization.properties\":{\"grid\":{\"searchable\":\"yes\",\"column.selection\":\"no\",\"header\":\"yes\",\"style\":{\"header.font.size\":\"small\"},\"columns\":[{\"name\":\"monitor\",\"show\":\"yes\",\"sortable\":\"yes\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":1,\"style\":{}},{\"name\":\"ping.latency.ms.avg\",\"show\":\"yes\",\"sortable\":\"yes\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":2,\"style\":{}}]}},\"visualization.result.by\":[\"monitor\"],\"granularity\":{\"value\":5,\"unit\":\"m\"}}");

        context.put(VisualizationConstants.VISUALIZATION_NAME, context.getString(VisualizationConstants.VISUALIZATION_NAME) + " " + System.currentTimeMillis());

        TestAPIUtil.post(VISUALIZATION_WIDGET_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(WidgetConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.WIDGET.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.add(response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();

                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testCreateWidgetGridInstanceMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"visualization.name\":\"TestWidget-Grid\",\"visualization.description\":\"TestWidget-Grid\",\"visualization.timeline\":{\"relative.timeline\":\"today\"},\"visualization.category\":\"Grid\",\"visualization.type\":\"Grid\",\"visualization.data.sources\":[{\"type\":\"metric\",\"filters\":{\"data.filter\":{},\"result.filter\":{}},\"visualization.result.by\":[\"monitor\"],\"data.points\":[{\"data.point\":\"interface~in.packets\",\"aggregator\":\"avg\"}]}],\"visualization.properties\":{\"grid\":{\"searchable\":\"yes\",\"column.selection\":\"no\",\"header\":\"yes\",\"style\":{\"header.font.size\":\"small\"},\"columns\":[{\"name\":\"monitor\",\"show\":\"yes\",\"sortable\":\"yes\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":1,\"style\":{}},{\"name\":\"ping.latency.ms.avg\",\"show\":\"yes\",\"sortable\":\"yes\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":2,\"style\":{}}]}},\"visualization.result.by\":[\"interface\"],\"granularity\":{\"value\":5,\"unit\":\"m\"}}");

        context.put(VisualizationConstants.VISUALIZATION_NAME, context.getString(VisualizationConstants.VISUALIZATION_NAME) + " " + System.currentTimeMillis());

        TestAPIUtil.post(VISUALIZATION_WIDGET_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(WidgetConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.WIDGET.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.add(response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testCreateWidgetTopNInstanceMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"visualization.name\":\"TestTopNWidget\",\"visualization.description\":\"Test TopN Widgets\",\"visualization.timeline\":{\"relative.timeline\":\"today\"},\"visualization.category\":\"TopN\",\"visualization.type\":\"Area\",\"visualization.data.sources\":[{\"type\":\"metric\",\"filters\":{\"data.filter\":{},\"result.filter\":{}},\"visualization.result.by\":[\"monitor\"],\"data.points\":[{\"data.point\":\"ping.min.latency.ms\",\"aggregator\":\"avg\"}]}],\"visualization.properties\":{\"chart\":{\"chart.legend\":\"no\",\"chart.label\":\"no\",\"highchart.settings\":{},\"sorting\":{\"limit\":10,\"order\":\"asc\",\"column\":\"ping.min.latency.ms.avg\"}}},\"visualization.result.by\":[\"monitor\"],\"granularity\":{\"value\":5,\"unit\":\"m\"}}");

        context.put(VisualizationConstants.VISUALIZATION_NAME, context.getString(VisualizationConstants.VISUALIZATION_NAME) + " " + System.currentTimeMillis());

        TestAPIUtil.post(VISUALIZATION_WIDGET_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(WidgetConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.WIDGET.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testCreateWidgetGaugeScalarMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"visualization.name\":\"TestGaugeWidget\",\"visualization.description\":\"Test Gauge Widgets\",\"visualization.timeline\":{\"relative.timeline\":\"today\"},\"visualization.category\":\"Gauge\",\"visualization.type\":\"MetroTile\",\"visualization.data.sources\":[{\"type\":\"metric\",\"filters\":{\"data.filter\":{},\"result.filter\":{}},\"data.points\":[{\"data.point\":\"ping.min.latency.ms\",\"aggregator\":\"avg\"}]}],\"visualization.properties\":{\"gauge\":{\"style\":{\"chart.legend\":\"no\",\"chart.label\":\"no\",\"type\":\"number\",\"font.size\":\"small\",\"color.conditions\":[{\"color\":\"#f04e3e\"},{\"color\":\"#f58518\"},{\"color\":\"#f5bc18\"}]}}},\"granularity\":{\"value\":5,\"unit\":\"m\"}}");

        context.put(VisualizationConstants.VISUALIZATION_NAME, context.getString(VisualizationConstants.VISUALIZATION_NAME) + " " + System.currentTimeMillis());

        TestAPIUtil.post(VISUALIZATION_WIDGET_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(WidgetConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.WIDGET.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testGetAllWidget(VertxTestContext testContext)
    {
        TestAPIUtil.get(VISUALIZATION_WIDGET_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            var entities = body.getJsonArray(RESULT);

            Assertions.assertNotNull(entities);

            Assertions.assertFalse(entities.isEmpty());

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testUpdateWidget(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = WidgetConfigStore.getStore().getItem(IDS.getLong(0));

        context.put(VisualizationConstants.VISUALIZATION_TIMELINE, new JsonObject("{\"relative.timeline\":\"today\"}"));

        TestAPIUtil.put(VISUALIZATION_WIDGET_API_ENDPOINT + "/" + IDS.getLong(0), context, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertUpdateEntityTestResult(WidgetConfigStore.getStore(), context, response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.WIDGET.getName()), LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testDeleteWidget(VertxTestContext testContext)
    {
        TestAPIUtil.delete(VISUALIZATION_WIDGET_API_ENDPOINT + "/" + IDS.getLong(1), testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertDeleteEntityTestResult(WidgetConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_DELETED, APIConstants.Entity.WIDGET.getName()));

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testDeleteAssignedWidget(VertxTestContext testContext)
    {
        TestAPIUtil.delete(VISUALIZATION_WIDGET_API_ENDPOINT + "/" + 10000000001455L, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(SC_BAD_REQUEST, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertTrue(String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED_IN_USED, APIConstants.Entity.WIDGET.getName()).equalsIgnoreCase(body.getString(MESSAGE)) ||

                    String.format(ErrorMessageConstants.ENTITY_DELETE_NOT_ALLOWED, APIConstants.Entity.WIDGET.getName()).equalsIgnoreCase(body.getString(MESSAGE)));

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testCreateCustomTabWidget(VertxTestContext testContext, TestInfo testInfo)
    {
        var customTab = TemplateConfigStore.getStore().getItemsByValue("_type", "1").getJsonObject(0);

        if (customTab != null)
        {
            var customTabId = customTab.getLong(ID);

            var payload = new JsonObject("{\"id\":-1,\"visualization.name\":\"TemplateWidget\",\"visualization.timeline\":{\"relative.timeline\":\"today\",\"visualization.time.range.inclusive\":\"no\"},\"visualization.category\":\"Grid\",\"visualization.type\":\"Grid\",\"visualization.data.sources\":[{\"type\":\"metric\",\"filters\":{\"data.filter\":{},\"result.filter\":{},\"drill.down.filter\":{}},\"visualization.result.by\":[\"monitor\"],\"data.points\":[{\"data.point\":\"system.cpu.percent\",\"aggregator\":\"avg\"}]}],\"visualization.properties\":{\"grid\":{\"visualization.grid.properties.required\":\"no\",\"searchable\":\"yes\",\"column.selection\":\"no\",\"header\":\"yes\",\"layout\":\"Grid\",\"style\":{\"header.font.size\":\"small\"},\"columns\":[{\"name\":\"monitor\",\"show\":\"yes\",\"sortable\":\"yes\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":1,\"style\":{}},{\"name\":\"system.cpu.percent.avg\",\"show\":\"yes\",\"sortable\":\"yes\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":2,\"style\":{}},{\"name\":\"object.type\",\"show\":\"no\",\"sortable\":\"yes\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":3,\"style\":{}},{\"name\":\"object.vendor\",\"show\":\"no\",\"sortable\":\"yes\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":4,\"style\":{}},{\"name\":\"object.ip\",\"show\":\"no\",\"sortable\":\"yes\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":5,\"style\":{}}]}},\"visualization.result.by\":[\"monitor\"],\"container.type\":\"dashboard\"}");

            payload.put(VisualizationConstants.VISUALIZATION_NAME, payload.getString(VisualizationConstants.VISUALIZATION_NAME) + " " + System.currentTimeMillis());

            TestAPIUtil.post(VISUALIZATION_WIDGET_API_ENDPOINT, payload, testContext.succeeding(response ->
                    testContext.verify(() ->
                    {
                        TestAPIUtil.assertCreateEntityTestResult(WidgetConfigStore.getStore(), payload, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.WIDGET.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                        widgetId = response.bodyAsJsonObject().getLong(GlobalConstants.ID);

                        customTab.put("template.widgets", customTab.getJsonArray("template.widgets", new JsonArray()).add(new JsonObject().put("id", widgetId).put("h", 4).put("w", 6).put("x", 0).put("y", 0)));

                        TestAPIUtil.put(VISUALIZATION_TEMPLATE_API_ENDPOINT + "/" + customTabId, customTab,
                                testContext.succeeding(asyncResult ->
                                        testContext.verify(() ->
                                        {
                                            TestAPIUtil.assertUpdateEntityTestResult(TemplateConfigStore.getStore(), customTab, asyncResult.bodyAsJsonObject(),
                                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "Template"), LOGGER, testInfo.getTestMethod().get().getName());

                                            testContext.completeNow();
                                        })));
                    })));


        }

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testDeleteCustomTabWidget(VertxTestContext testContext)
    {
        TestAPIUtil.delete(VISUALIZATION_WIDGET_API_ENDPOINT + "/" + widgetId, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(SC_BAD_REQUEST, response.statusCode());

            var result = response.bodyAsJsonObject();

            Assertions.assertNotNull(result);

            assertTrue(String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED_IN_USED, APIConstants.Entity.WIDGET.getName()).equalsIgnoreCase(result.getString(MESSAGE)) ||

                    String.format(ErrorMessageConstants.ENTITY_DELETE_NOT_ALLOWED, APIConstants.Entity.WIDGET.getName()).equalsIgnoreCase(result.getString(MESSAGE)));

            testContext.completeNow();

        })));
    }
}
