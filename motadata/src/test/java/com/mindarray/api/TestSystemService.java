/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.store.SystemServiceConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.SYSTEM_SERVICE_API_ENDPOINT;
import static com.mindarray.api.SystemService.*;


@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
@Execution(ExecutionMode.SAME_THREAD)
public class TestSystemService
{
    private static final Logger LOGGER = new Logger(TestSystemService.class, MOTADATA_API, "Test System Service");
    private static long id;

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateSystemService(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(SYSTEM_SERVICE, "Service").put(SYSTEM_SERVICE_APP_TYPE, "Active Directory").put(SYSTEM_SERVICE_OS, "Windows");

        TestAPIUtil.post(SYSTEM_SERVICE_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(SystemServiceConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "System Service"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            id = response.bodyAsJsonObject().getLong(ID);

                            testContext.completeNow();
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testGetAllSystemService(VertxTestContext testContext)
    {
        TestAPIUtil.get(SYSTEM_SERVICE_API_ENDPOINT
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETAllRequestTestResult(response, SystemServiceConfigStore.getStore(), null);

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testGetSystemService(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.get(SYSTEM_SERVICE_API_ENDPOINT + "/" + id
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {

                            TestAPIUtil.assertGETRequestTestResult(response, id, SystemServiceConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testUpdateSystemService(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(SYSTEM_SERVICE, "Service1").put(SYSTEM_SERVICE_APP_TYPE, "DHCPServer");

        TestAPIUtil.put(SYSTEM_SERVICE_API_ENDPOINT + "/" + id, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(SystemServiceConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "System Service"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testDeleteSystemService(VertxTestContext testContext)
    {

        TestAPIUtil.delete(SYSTEM_SERVICE_API_ENDPOINT + "/" + id,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertDeleteEntityTestResult(SystemServiceConfigStore.getStore(), response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_DELETED, "System Service"));

                            testContext.completeNow();
                        })));
    }

    //3578 improvment
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testCreateActiveDirectorySystemService(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(SYSTEM_SERVICE, "Service2").put(SYSTEM_SERVICE_APP_TYPE, "Active Directory").put(SYSTEM_SERVICE_OS, "Windows");

        TestAPIUtil.post(SYSTEM_SERVICE_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(SystemServiceConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "System Service"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            id = response.bodyAsJsonObject().getLong(ID);

                            testContext.completeNow();
                        })));

    }

    //3578 improvment
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testUpdateSystemServiceEmptyApplicationType(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(SYSTEM_SERVICE, "Service2");

        TestAPIUtil.put(SYSTEM_SERVICE_API_ENDPOINT + "/" + id, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(SystemServiceConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "System Service"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    //3578 improvment
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testGetSystemServiceAfterUpdate(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(SYSTEM_SERVICE_API_ENDPOINT + "/" + id
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETRequestTestResult(response, id, SystemServiceConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                            Assertions.assertFalse(response.bodyAsJsonObject().getJsonObject(RESULT).containsKey(SYSTEM_SERVICE_APP_TYPE));

                            testContext.completeNow();
                        })));
    }
}


