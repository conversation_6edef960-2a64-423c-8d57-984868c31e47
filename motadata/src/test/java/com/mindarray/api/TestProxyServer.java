/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.store.ProxyServerConfigStore;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.PROXY_SERVER_API_ENDPOINT;
import static com.mindarray.api.ProxyServer.PROXY_SERVER_TYPE;
import static com.mindarray.eventbus.EventBusConstants.UI_ACTION_PROXY_SERVER_TEST;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
@Execution(ExecutionMode.SAME_THREAD)
public class TestProxyServer
{
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateProxyServer(VertxTestContext testContext)
    {
        var context = new JsonObject().put(ProxyServer.PROXY_SERVER_HOST, "************")
                .put(ProxyServer.PROXY_SERVER_PORT, 3128)
                .put(ProxyServer.PROXY_SERVER_USERNAME, "geodb_user")
                .put(ProxyServer.PROXY_SERVER_PASSWORD, "Trace#12Org$89Mind%^T")
                .put(ProxyServer.PROXY_SERVER_TIME_OUT, 600)
                .put(PROXY_SERVER_TYPE, "HTTP");

        TestUtil.vertx().eventBus().send(UI_ACTION_PROXY_SERVER_TEST, context.copy().put(TARGET, "https://geodb.motadata.ai/geo-location.zip").put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(User.USER_NAME, "admin"));

        TestAPIUtil.put(PROXY_SERVER_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var result = response.bodyAsJsonObject();

                            Assertions.assertNotNull(result);

                            Assertions.assertTrue(result.containsKey(STATUS));

                            Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testGetProxyServer(VertxTestContext testContext)
    {
        TestAPIUtil.get(PROXY_SERVER_API_ENDPOINT,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var result = response.bodyAsJsonObject();

                            Assertions.assertNotNull(result.getJsonObject(GlobalConstants.RESULT));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testDeleteProxyServer(VertxTestContext testContext)
    {
        TestAPIUtil.delete(PROXY_SERVER_API_ENDPOINT,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var result = response.bodyAsJsonObject();

                            Assertions.assertTrue(result.containsKey(STATUS));

                            Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                            Assertions.assertEquals(0, ProxyServerConfigStore.getStore().getItems().size());

                            testContext.completeNow();
                        })));
    }
}
