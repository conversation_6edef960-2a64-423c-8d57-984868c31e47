/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.store.MetricExplorerConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.MOTADATA_API;
import static com.mindarray.GlobalConstants.RESULT;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(70 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestMetricExplorer
{
    private static final String ENTITY_NAME = "Metric Explorer";

    private static final Logger LOGGER = new Logger(TestMetricExplorer.class, MOTADATA_API, "Test Metric Explorer");


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    public void testCreateMetricExplorer1(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"metric.explorer.name\":\"System Metrics Explorer 1\",\"metric.explorer.description\":\"System Metrics Explorer 1\",\"metric.explorer.access.type\":\"public\",\"metric.explorer.users\":[],\"metric.explorer.global.view.enabled\":\"no\",\"metric.explorer.object.type\":\"server\",\"metric.explorer.object.id\":102,\"metric.explorer.context\":{}}");

        TestAPIUtil.post(TestAPIConstants.VISUALIZATION_METRIC_EXPLORER_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {

            TestAPIUtil.assertCreateEntityTestResult(MetricExplorerConfigStore.getStore(), context, response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    public void testCreateMetricExplorer2(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"metric.explorer.name\":\"System Metrics Explorer 2\",\"metric.explorer.description\":\"System Metrics Explorer 2\",\"metric.explorer.access.type\":\"public\",\"metric.explorer.users\":[],\"metric.explorer.global.view.enabled\":\"yes\",\"metric.explorer.object.type\":\"server\",\"metric.explorer.object.id\":-1,\"metric.explorer.context\":{}}");

        TestAPIUtil.post(TestAPIConstants.VISUALIZATION_METRIC_EXPLORER_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {

            TestAPIUtil.assertCreateEntityTestResult(MetricExplorerConfigStore.getStore(), context, response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    public void testCreateMetricExplorer3(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"metric.explorer.name\":\"System Metrics Explorer 3\",\"metric.explorer.description\":\"System Metrics Explorer 3\",\"metric.explorer.access.type\":\"public\",\"metric.explorer.users\":[],\"metric.explorer.global.view.enabled\":\"no\",\"metric.explorer.object.type\":\"server\",\"metric.explorer.object.id\":-1,\"metric.explorer.context\":{}}");

        TestAPIUtil.post(TestAPIConstants.VISUALIZATION_METRIC_EXPLORER_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {

            TestAPIUtil.assertCreateEntityTestResult(MetricExplorerConfigStore.getStore(), context, response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    public void testCreateMetricExplorer4(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"metric.explorer.name\":\"System Metrics Explorer 4\",\"metric.explorer.description\":\"System Metrics Explorer 4\",\"metric.explorer.access.type\":\"public\",\"metric.explorer.users\":[],\"metric.explorer.global.view.enabled\":\"no\",\"metric.explorer.object.type\":\"network\",\"metric.explorer.object.id\":103,\"metric.explorer.context\":{}}");

        TestAPIUtil.post(TestAPIConstants.VISUALIZATION_METRIC_EXPLORER_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {

            TestAPIUtil.assertCreateEntityTestResult(MetricExplorerConfigStore.getStore(), context, response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    public void testGetMetricExplorerGlobal(VertxTestContext testContext) throws InterruptedException
    {

        TestAPIUtil.get(TestAPIConstants.VISUALIZATION_METRIC_EXPLORER_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(200, response.statusCode());

            Assertions.assertEquals("System Metrics Explorer 2", response.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0).getString(MetricExplorer.METRIC_EXPLORER_NAME));

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    public void testGetMetricExplorerByObject(VertxTestContext testContext) throws InterruptedException
    {

        TestAPIUtil.get(TestAPIConstants.VISUALIZATION_METRIC_EXPLORER_API_ENDPOINT + "?filter=" + new JsonObject("{\"metric.explorer.object.id\":102,\"metric.explorer.object.type\":\"server\"}").encode(), testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(200, response.statusCode());

            Assertions.assertEquals(2, response.bodyAsJsonObject().getJsonArray(RESULT).size());

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    public void testGetMetricExplorerByType(VertxTestContext testContext) throws InterruptedException
    {

        TestAPIUtil.get(TestAPIConstants.VISUALIZATION_METRIC_EXPLORER_API_ENDPOINT + "?filter=" + new JsonObject("{\"metric.explorer.object.id\":103,\"metric.explorer.object.type\":\"network\"}").encode(), testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(200, response.statusCode());

            Assertions.assertEquals(1, response.bodyAsJsonObject().getJsonArray(RESULT).size());

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }
}