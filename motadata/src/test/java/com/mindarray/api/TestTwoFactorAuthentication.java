/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.notification.Notification;
import com.mindarray.store.ActiveUserCacheStore;
import com.mindarray.store.TwoAuthenticationConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.util.CipherUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;

import static com.mindarray.ErrorMessageConstants.*;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.APIConstants.AUTH_TOTP;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.MailServerConfiguration.*;
import static com.mindarray.api.User.*;
import static com.mindarray.db.ConfigDBConstants.COLLECTION_USER;
import static com.mindarray.db.ConfigDBConstants.FIELD_NAME;
import static com.mindarray.eventbus.EventBusConstants.UI_ACTION_TWO_FACTOR_AUTHENTICATION_TEST;
import static org.apache.http.HttpStatus.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(60 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestTwoFactorAuthentication
{
    private static final Logger LOGGER = new Logger(TestTwoFactorAuthentication.class, MOTADATA_API, "Test Two Factor Authentication");

    private static final JsonObject TLS_CONTEXT = new JsonObject()
            .put(MAIL_SERVER_HOST, "smtp.gmail.com")
            .put(MAIL_SERVER_PORT, 587)
            .put(MAIL_SERVER_CREDENTIAL_PROFILE, CredentialProfile.DEFAULT_EMAIL_CREDENTIAL_PROFILE)
            .put(MAIL_SERVER_PROTOCOL, MAIL_SERVER_PROTOCOL_TLS)
            .put(MAIL_SERVER_AUTH_STATUS, YES)
            .put(MAIL_SERVER_USERNAME, "<EMAIL>")
            .put(MAIL_SERVER_PASSWORD, "Mind@123");

    private static final JsonObject TWO_FACTOR_USER_CONTEXT = new JsonObject()
            .put(USER_NAME, "twoFactorUser")
            .put(USER_FIRST_NAME, "motadata1")
            .put(USER_LAST_NAME, "admin1")
            .put(USER_EMAIL, "<EMAIL>")
            .put(USER_MOBILE, 123456789)
            .put(USER_PASSWORD, "MindUser@123")
            .put(USER_STATUS, "yes")
            .put(USER_ROLE, DEFAULT_ID)
            .put(USER_GROUPS, new JsonArray().add(DEFAULT_ID))
            .put(USER_TYPE, USER_TYPE_SYSTEM);

    private static String accessToken = null;

    private static MessageConsumer<JsonObject> messageConsumer;


    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {

        TestAPIUtil.post(USER_API_ENDPOINT, TWO_FACTOR_USER_CONTEXT.copy(),
                result ->
                {
                    if (result.succeeded())
                    {
                        var response = result.result();

                        assertEquals(SC_OK, response.statusCode());

                        var body = response.bodyAsJsonObject();

                        Assertions.assertNotNull(body);

                        assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                        var user = UserConfigStore.getStore().getItemByValue(USER_NAME, "twoFactorUser");

                        Assertions.assertNotNull(user);

                        TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, user.getString(USER_NAME))
                                .put(USER_PASSWORD, new CipherUtil().decrypt(user.getString(USER_PASSWORD))), asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                var httpResponse = asyncResult.result();

                                assertEquals(HttpStatus.SC_OK, httpResponse.statusCode());

                                accessToken = httpResponse.bodyAsJsonObject().getString(APIConstants.AUTH_ACCESS_TOKEN);

                                testContext.completeNow();
                            }
                            else
                            {
                                testContext.failNow(asyncResult.cause());
                            }
                        });
                    }
                    else
                    {
                        testContext.failNow(result.cause());
                    }
                });

    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        TestAPIUtil.put(TWO_FACTOR_AUTHENTICATION_API_ENDPOINT + "/" + DEFAULT_ID,
                new JsonObject().put(TwoFactorAuthentication.TWO_FACTOR_AUTHENTICATION_ENABLE, "no")
                        .put(TwoFactorAuthentication.TWO_FACTOR_AUTHENTICATION_MODE, TwoFactorAuthentication.AuthenticationType.EMAIL.getName()),
                testContext.succeeding(response -> testContext.verify(testContext::completeNow)));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetAllTwoFactorAuthentication(VertxTestContext testContext)
    {
        TestAPIUtil.get(TWO_FACTOR_AUTHENTICATION_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            body = body.getJsonArray(RESULT).getJsonObject(0);

            Assertions.assertNotNull(body);

            Assertions.assertFalse(body.isEmpty());

            Assertions.assertTrue(body.containsKey(TwoFactorAuthentication.TWO_FACTOR_AUTHENTICATION_ENABLE));

            Assertions.assertTrue(body.containsKey(TwoFactorAuthentication.TWO_FACTOR_AUTHENTICATION_MODE));

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testUpdateTwoFactorAuthenticationPreConditionFail(VertxTestContext testContext)
    {
        TestAPIUtil.put(TWO_FACTOR_AUTHENTICATION_API_ENDPOINT + "/" + DEFAULT_ID,
                new JsonObject().put(TwoFactorAuthentication.TWO_FACTOR_AUTHENTICATION_ENABLE, "yes")
                        .put(TwoFactorAuthentication.TWO_FACTOR_AUTHENTICATION_MODE, TwoFactorAuthentication.AuthenticationType.AUTHENTICATOR_APP.getName()),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    assertEquals(SC_BAD_REQUEST, response.statusCode());

                    assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                    assertTrue(response.bodyAsJsonObject().containsKey(MESSAGE));

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testUpdateTwoFactorAuthentication(VertxTestContext testContext, TestInfo testInfo)
    {
        var mailServerContext = new JsonObject().mergeIn(TLS_CONTEXT);

        var item = TwoAuthenticationConfigStore.getStore().getItem();

        TestAPIUtil.get(EMAIL_CONFIG_API_ENDPOINT, result ->
        {
            if (result.succeeded())
            {
                TestAPIUtil.put(EMAIL_CONFIG_API_ENDPOINT + result.result().bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0).getLong(ID), mailServerContext,
                        asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                TestAPIUtil.put(TWO_FACTOR_AUTHENTICATION_API_ENDPOINT + "/" + DEFAULT_ID,
                                        new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + accessToken),
                                        new JsonObject().put(TwoFactorAuthentication.TWO_FACTOR_AUTHENTICATION_ENABLE, "yes")
                                                .put(TwoFactorAuthentication.TWO_FACTOR_AUTHENTICATION_MODE, TwoFactorAuthentication.AuthenticationType.AUTHENTICATOR_APP.getName()),
                                        testContext.succeeding(response -> testContext.verify(() ->
                                        {
                                            TestAPIUtil.assertUpdateEntityTestResult(TwoAuthenticationConfigStore.getStore(), item, response.bodyAsJsonObject(),
                                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "Two Factor Authentication"), LOGGER, testInfo.getTestMethod().get().getName());
                                            testContext.completeNow();
                                        })));
                            }
                            else
                            {
                                testContext.failNow("Mail Server update failed");
                            }

                        });
            }
            else
            {
                testContext.failNow("Mail Server get api failed");
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testLoginUserWithTwoFactorAuthenticationFirstTime(VertxTestContext testContext)
    {
        TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, "twoFactorUser").put(USER_PASSWORD, "MindUser@123"),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    var result = response.bodyAsJsonObject();

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.containsKey(USER_TOTP_KEY));

                    Assertions.assertTrue(result.containsKey("url"));

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testLoginUserWithTwoFactorAuthenticationInvalidTOTPWithKey(VertxTestContext testContext)
    {
        var item = UserConfigStore.getStore().getItemByValue(USER_NAME, "twoFactorUser");

        if (item != null)
        {
            TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT, new JsonObject(), new JsonObject().put(USER_NAME, item.getString(USER_NAME))
                    .put(USER_TOTP_KEY, "ABCDEFGHIJK").put(AUTH_TOTP, 123456).put(USER_PASSWORD, new CipherUtil().decrypt(item.getString(USER_PASSWORD))), result ->
            {
                if (result.succeeded())
                {
                    Assertions.assertEquals(SC_UNAUTHORIZED, result.result().statusCode());

                    Assertions.assertTrue(result.result().bodyAsJsonObject().containsKey(MESSAGE));

                    Assertions.assertEquals(TOTP_INVALID, result.result().bodyAsJsonObject().getString(MESSAGE));

                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(result.cause());
                }

            });
        }
        else
        {
            testContext.failNow("User not found");
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testLoginUserWithTwoFactorAuthenticationInvalidTOTPWithoutKey(VertxTestContext testContext)
    {
        var item = UserConfigStore.getStore().getItemByValue(USER_NAME, "twoFactorUser");

        if (item != null)
        {
            Bootstrap.configDBService().update(COLLECTION_USER,
                    new JsonObject().put(FIELD_NAME, ID).put(VALUE, CommonUtil.getLong(item.getLong(ID))),
                    new JsonObject().put(USER_TOTP_KEY, new CipherUtil().decrypt("ABCDEFGHIJK")),
                    item.getString(USER_NAME),
                    SYSTEM_REMOTE_ADDRESS,
                    result ->
                    {
                        if (result.succeeded())
                        {
                            UserConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(
                                    asyncResult ->
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT, new JsonObject(), new JsonObject().put(USER_NAME, "twoFactorUser").put(USER_PASSWORD, "MindUser@123").put(AUTH_TOTP, 123456),
                                                    testContext.succeeding(response -> testContext.verify(() ->
                                                    {
                                                        Assertions.assertEquals(SC_UNAUTHORIZED, response.statusCode());

                                                        Assertions.assertTrue(response.bodyAsJsonObject().containsKey(MESSAGE));

                                                        Assertions.assertEquals(TOTP_INVALID, response.bodyAsJsonObject().getString(MESSAGE));

                                                        testContext.completeNow();

                                                    })));
                                        }
                                        else
                                        {
                                            testContext.failNow("user config update failed");
                                        }

                                    });
                        }
                        else
                        {
                            testContext.failNow("user update failed");
                        }

                    });
        }
        else
        {
            testContext.failNow("User not found");
        }
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testUpdateTwoFactorAuthenticationTypeMail(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = TwoAuthenticationConfigStore.getStore().getItem();

        TestAPIUtil.put(TWO_FACTOR_AUTHENTICATION_API_ENDPOINT + "/" + DEFAULT_ID,
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + accessToken),
                new JsonObject().put(TwoFactorAuthentication.TWO_FACTOR_AUTHENTICATION_ENABLE, "yes")
                        .put(TwoFactorAuthentication.TWO_FACTOR_AUTHENTICATION_MODE, TwoFactorAuthentication.AuthenticationType.EMAIL.getName()),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    TestAPIUtil.assertUpdateEntityTestResult(TwoAuthenticationConfigStore.getStore(), item, response.bodyAsJsonObject(),
                            String.format(InfoMessageConstants.ENTITY_UPDATED, "Two Factor Authentication"), LOGGER, testInfo.getTestMethod().get().getName());
                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testTwoFactorAuthenticationTestEvent(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_NOTIFICATION, message ->
        {

            Assertions.assertEquals(Notification.NotificationType.EMAIL.getName(), message.body().getString(Notification.NOTIFICATION_TYPE));

            Assertions.assertEquals("<EMAIL>", message.body().getJsonArray(Notification.EMAIL_NOTIFICATION_RECIPIENTS).getString(0));

            Assertions.assertNotNull(ActiveUserCacheStore.getStore().getItem("<EMAIL>"));

            messageConsumer.unregister(asyncResult -> testContext.completeNow());
        });

        Bootstrap.vertx().eventBus().send(UI_ACTION_TWO_FACTOR_AUTHENTICATION_TEST, new JsonObject("{\"two.factor.authentication.enable\":\"yes\",\"two.factor.authentication.mode\":\"Email\",\"type\":\"create\"}").put(USER_NAME, "twoFactorUser"));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testLoginUserWithTwoFactorAuthenticationModeEmailInvalidOTP(VertxTestContext testContext)
    {
        var item = UserConfigStore.getStore().getItemByValue(USER_NAME, "twoFactorUser");

        if (item != null)
        {
            var credential = new JsonObject().put(USER_NAME, item.getString(USER_NAME)).put(USER_PASSWORD, new CipherUtil().decrypt(item.getString(USER_PASSWORD)));

            TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT, new JsonObject(), credential, result ->
            {
                if (result.succeeded())
                {
                    TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT,
                            new JsonObject(),
                            credential.put(AUTH_TOTP, 123456),
                            testContext.succeeding(response -> testContext.verify(() ->
                            {
                                Assertions.assertEquals(SC_UNAUTHORIZED, response.statusCode());

                                Assertions.assertTrue(response.bodyAsJsonObject().containsKey(MESSAGE));

                                Assertions.assertEquals(OTP_INVALID, response.bodyAsJsonObject().getString(MESSAGE));

                                testContext.completeNow();

                            })));
                }
                else
                {
                    testContext.failNow(result.cause());
                }

            });
        }
        else
        {
            testContext.failNow("User not found");
        }
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testLoginUserWithTwoFactorAuthenticationModeEmailExpiredOTP(VertxTestContext testContext) throws InterruptedException
    {
        testContext.awaitCompletion(3, TimeUnit.MINUTES);

        var item = UserConfigStore.getStore().getItemByValue(USER_NAME, "twoFactorUser");

        if (item != null)
        {
            TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT, new JsonObject(), new JsonObject().put(USER_NAME, item.getString(USER_NAME)).put(AUTH_TOTP, 123456).put(USER_PASSWORD, new CipherUtil().decrypt(item.getString(USER_PASSWORD))), result ->
            {
                if (result.succeeded())
                {
                    Assertions.assertEquals(SC_UNAUTHORIZED, result.result().statusCode());

                    Assertions.assertTrue(result.result().bodyAsJsonObject().containsKey(MESSAGE));

                    Assertions.assertEquals(OTP_EXPIRED, result.result().bodyAsJsonObject().getString(MESSAGE));

                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(result.cause());
                }

            });
        }
        else
        {
            testContext.failNow("User not found");
        }
    }

}
