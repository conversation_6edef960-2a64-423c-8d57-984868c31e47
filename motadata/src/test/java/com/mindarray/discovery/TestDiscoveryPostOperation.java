
/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.discovery;

import com.mindarray.*;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.CredentialProfile;
import com.mindarray.api.Discovery;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.CredentialProfileConfigStore;
import com.mindarray.store.DiscoveryConfigStore;
import com.mindarray.store.GroupConfigStore;
import com.mindarray.store.SchedulerConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.CronExpressionUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.AIOpsObject.OBJECT_TYPE;
import static com.mindarray.api.APIConstants.REQUEST_PARAM_TYPE;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.Group.*;
import static com.mindarray.api.SNMPTrapListenerConfiguration.*;
import static com.mindarray.api.Scheduler.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.AUTO_PROVISION_STATUS;
import static com.mindarray.nms.NMSConstants.OBJECTS;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@Timeout(100 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestDiscoveryPostOperation
{
    public static final JsonObject DISCOVERY_PROFILES = new JsonObject();

    private static final Logger LOGGER = new Logger(TestDiscoveryPostOperation.class, MOTADATA_NMS, "Test Discovery Post Operation");
    private static final String SCHEDULER_ENTITY_NAME = "Scheduler";
    private static final String PATTERN = "dd-MM-yyyy";
    private static final Map<String, Long> CONTEXTS = new HashMap<>();
    private static MessageConsumer<JsonObject> messageConsumer = null;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            TestUtil.startEventStreaming(new JsonObject().put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_COMPLETED));

            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "discovery-parameters.json");

            if (file.exists())
            {
                DISCOVERY_PROFILES.mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));

                testContext.completeNow();
            }
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());

            exception.printStackTrace();
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    private void assertCredentialTestResult(VertxTestContext testContext, String status, String errorCode)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                if (eventContext.containsKey(EVENT_STATE) && eventContext.getString(EVENT_STATE).equalsIgnoreCase(EVENT_STATE_COMPLETED))
                {
                    Assertions.assertEquals(eventContext.getString(STATUS), status);

                    if (errorCode != null)
                    {
                        Assertions.assertEquals(eventContext.getString(ERROR_CODE), errorCode);
                    }

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
            catch (Exception exception)
            {
                messageConsumer.unregister(result -> testContext.failNow(exception));

            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateDiscoveryScheduler(VertxTestContext testContext)
    {
        var scheduler = new JsonObject().put(SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date())).put(SCHEDULER_TIMES, new JsonArray().add("00:05")).put(SCHEDULER_TIMELINE, CronExpressionUtil.CRON_ONCE).put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.DISCOVERY.getName()).put(SCHEDULER_CONTEXT, new JsonObject().put(AUTO_PROVISION_STATUS, NO).put(OBJECTS, new JsonArray().add(DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, "SNMP-Ip-Range-Test").getLong(ID))));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                                {
                                    TestAPIUtil.assertMultiCreateEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(), SCHEDULER_ENTITY_NAME);

                                    CONTEXTS.put("scheduler", response.bodyAsJsonObject().getJsonArray(ID).getLong(0));

                                    testContext.completeNow();
                                }))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testGetAllDiscoveryScheduler(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.get(SCHEDULER_API_ENDPOINT + "?filter=" + new JsonObject().put(ID, DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, "SNMP-Ip-Range-Test").getLong(ID)).put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.DISCOVERY.getName())
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var items = response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT);

                            assertTrue(items != null && !items.isEmpty());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testUpdateDiscoveryScheduler(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = new JsonObject().put(SCHEDULER_TIMELINE, CronExpressionUtil.CRON_MONTHLY).put(SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date())).put(SCHEDULER_TIMES, new JsonArray().add("00:05")).put(SCHEDULER_EMAIL_RECIPIENTS, new JsonArray()).put(SCHEDULER_SMS_RECIPIENTS, new JsonArray()).put(SCHEDULER_DATES, new JsonArray().add("1")).put(SCHEDULER_MONTHS, new JsonArray().add("January")).put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.DISCOVERY.getName()).put(SCHEDULER_CONTEXT, new JsonObject().put(AUTO_PROVISION_STATUS, NO).put(OBJECTS, new JsonArray().add(DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, "SNMP-Ip-Range-Test").getLong(ID))));

        TestAPIUtil.put(SCHEDULER_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXTS.get("scheduler")), item,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(SchedulerConfigStore.getStore(), item, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, SCHEDULER_ENTITY_NAME), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testDeleteDiscoveryScheduler(VertxTestContext testContext)
    {
        TestAPIUtil.delete(SCHEDULER_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXTS.get("scheduler")) + "?" + SCHEDULER_JOB_TYPE + "=" + JobScheduler.JobType.DISCOVERY.getName(),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertDeleteEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_DELETED, SCHEDULER_ENTITY_NAME));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testUpdateDiscovery1(VertxTestContext testContext, TestInfo testInfo)
    {
        var discoveryParameters = TestConstants.prepareParams("snmp.discovery.ip.range.parameters").copy().put(Discovery.DISCOVERY_NAME, "SNMP-Test" + System.currentTimeMillis());

        discoveryParameters.put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013"))).put("discovery.name", "SNMP-Ip-Range-Test11");

        TestAPIUtil.put(DISCOVERY_API_ENDPOINT + "/" + DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, "SNMP-Ip-Range-Test").getLong(ID), discoveryParameters,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(DiscoveryConfigStore.getStore(), discoveryParameters, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, "Discovery"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testDeleteDiscovery1(VertxTestContext testContext)
    {
        TestAPIUtil.delete(DISCOVERY_API_ENDPOINT + "/" + DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, "SNMP-Ip-Range-Test11").getLong(ID), testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertDeleteEntityTestResult(DiscoveryConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_DELETED, "Discovery"));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testUpdateDiscovery(VertxTestContext testContext, TestInfo testInfo)
    {
        var discoveryParameters = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, "**********").put(Discovery.DISCOVERY_NAME, "SNMP-Test" + System.currentTimeMillis());

        discoveryParameters.put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013"))).put("discovery.name", "SNMP-Ip-Range-Test11");

        TestAPIUtil.put(DISCOVERY_API_ENDPOINT + "/" + discoveryParameters.getLong(ID), discoveryParameters,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(DiscoveryConfigStore.getStore(), discoveryParameters, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, "Discovery"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testUpdateDiscoveryResult(VertxTestContext testContext)
    {
        var discoveryParameters = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, "**********");

        TestAPIUtil.get(DISCOVERY_API_ENDPOINT + "/" + discoveryParameters.getLong(ID) + "/result",
                testContext.succeeding(getResponse ->
                        testContext.verify(() ->
                        {


                            var discoveryResultID = getResponse.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0).getLong(ID);

                            TestAPIUtil.put(DISCOVERY_API_ENDPOINT + "/" + discoveryParameters.getLong(ID) + "/result", new JsonObject().put(ID, discoveryResultID).put(AIOpsObject.OBJECT_TYPE, "Router"),
                                    testContext.succeeding(response ->
                                            testContext.verify(() ->
                                            {

                                                assertEquals(String.format(InfoMessageConstants.ENTITY_UPDATED, "Discovery profile SNMP-Ip-Range-Test11").trim(), response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE).trim());

                                                assertEquals(HttpStatus.SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                                                assertNotNull(response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                                                testContext.completeNow();

                                            })));
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testDeleteDiscovery(VertxTestContext testContext)
    {
        TestAPIUtil.delete(DISCOVERY_API_ENDPOINT + "/" + DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, "**********").getLong(ID), testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertDeleteEntityTestResult(DiscoveryConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_DELETED, "Discovery"));

            testContext.completeNow();
        })));
    }

    @Test
    void testGetDiscoveries(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(DISCOVERY_API_ENDPOINT
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @Test
    void testGetDiscovery(VertxTestContext testContext)
    {
        var id = DiscoveryConfigStore.getStore().getItem().getLong(ID);

        TestAPIUtil.get(DISCOVERY_API_ENDPOINT + "/" + id
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(SC_OK, response.statusCode());

                            var body = response.bodyAsJsonObject();

                            assertNotNull(body);

                            assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                            body = body.getJsonObject(RESULT);

                            assertNotNull(body);

                            assertFalse(body.isEmpty());

                            assertEquals(id, body.getLong(GlobalConstants.ID));

                            testContext.completeNow();
                        })));
    }

    @Test
    void testGetPortRemoteDiscovery(VertxTestContext testContext)
    {
        TestAPIUtil.get(DISCOVERY_API_ENDPOINT + "/" + DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testPortServiceCheckDiscovery").getString(Discovery.DISCOVERY_NAME)).getString(ID) + "/result"
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            Assertions.assertEquals(SC_OK, response.statusCode());

                            Assertions.assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                            testContext.completeNow();
                        })));
    }

    @Test()
    void testGetDiscoveryProgress(VertxTestContext testContext)
    {
        var discovery = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_OBJECT_TYPE, NMSConstants.Type.LINUX.getName());

        assertNotNull(discovery);

        TestAPIUtil.post(DISCOVERY_API_ENDPOINT + "/" + discovery.getLong(ID) + "/run",
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertNotNull(response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                            TestAPIUtil.get(DISCOVERY_API_ENDPOINT + "/" + discovery.getLong(ID) + "/progress"
                                    , testContext.succeeding(httpResponse ->
                                            testContext.verify(testContext::completeNow)));
                        })));
    }

    @Test
    void testUpdateWindowsCredentialProfile(VertxTestContext testContext)
    {

        var discoveryId = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, "Window-Test-EXCHANGE19\\administrator").getLong(ID);

        Bootstrap.configDBService().getById(ConfigDBConstants.COLLECTION_DISCOVERY,
                discoveryId,
                response ->
                {
                    if (response.failed())
                    {
                        testContext.failNow(response.cause());
                    }

                    var result = response.result();

                    Assertions.assertFalse(result.isEmpty());

                    Assertions.assertTrue(result.containsKey(Discovery.DISCOVERY_CREDENTIAL_PROFILES));

                    Assertions.assertFalse(result.getJsonArray(Discovery.DISCOVERY_CREDENTIAL_PROFILES).isEmpty());

                    var credentialId = result.getJsonArray(Discovery.DISCOVERY_CREDENTIAL_PROFILES).getLong(0);

                    Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_CREDENTIAL_PROFILE,
                            new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, credentialId),
                            new JsonObject().put(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT, new JsonObject().put(CredentialProfile.CREDENTIAL_PROFILE_NAME, "abc")),
                            DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                            futureResult -> CredentialProfileConfigStore.getStore().updateItem(credentialId).onComplete(asyncResult -> testContext.completeNow()));
                });
    }

    @Test
    void testAbortDiscoveryNotRunning(VertxTestContext testContext)
    {
        var discovery = DiscoveryConfigStore.getStore().getItem();

        assertNotNull(discovery);

        TestAPIUtil.post(DISCOVERY_API_ENDPOINT + "/" + discovery.getLong(ID) + "/abort",
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(HttpStatus.SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                            testContext.completeNow();
                        })));
    }

    @Test
    void testAbortDiscoveryRunning(VertxTestContext testContext)
    {
        var discovery = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, "ssh-172.16.8.135");

        assertNotNull(discovery);

        TestAPIUtil.post(DISCOVERY_API_ENDPOINT + "/" + discovery.getLong(ID) + "/run",
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertNotNull(response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                            TestAPIUtil.post(DISCOVERY_API_ENDPOINT + "/" + discovery.getLong(ID) + "/abort",
                                    testContext.succeeding(httpResponse ->
                                            testContext.verify(() ->
                                            {
                                                var responseCode = httpResponse.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE);

                                                if (responseCode == HttpStatus.SC_BAD_REQUEST || responseCode == SC_OK)
                                                {
                                                    testContext.completeNow();
                                                }
                                                else
                                                {
                                                    testContext.failNow(new Exception("failed to abort discovery.."));
                                                }
                                            })));
                        })));
    }

    @Test
    void testSNMPv3AuthNoPrivacyCredentialProfile(VertxTestContext context)
    {
        var credential = CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, "snmp-v3-3-172.16.14.6-Auth").put(CredentialProfile.CREDENTIAL_PROFILE_NAME, "SNMP V3 AuthNoPrivacy Credential Profile");

        TestAPIUtil.createCredentialProfile(credential, context).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                var credentialProfileContext = credential.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);

                credentialProfileContext.put(SNMP_V3_SECURITY_USERNAME, "sdds");

                credentialProfileContext.put(SNMP_V3_AUTHENTICATION_PROTOCOL, "SHA");

                credentialProfileContext.put(SNMP_V3_AUTHENTICATION_PASSWORD, "sdds");

                TestAPIUtil.put(TestAPIConstants.CREDENTIAL_PROFILE_API_ENDPOINT + "/" + credentialId, credential, context.succeeding(credentialResponse ->
                        context.verify(() ->
                        {
                            Assertions.assertEquals(SC_OK, credentialResponse.statusCode());

                            credentialProfileContext.remove(SNMP_V3_AUTHENTICATION_PROTOCOL);

                            credential.put(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT, credentialProfileContext);

                            TestAPIUtil.put(TestAPIConstants.CREDENTIAL_PROFILE_API_ENDPOINT + "/" + credentialId, credential, context.succeeding(updateCredentialResponse ->
                            {
                                Assertions.assertEquals(SC_OK, updateCredentialResponse.statusCode());

                                context.completeNow();

                            }));

                        })));
            }
        });
    }

    @Test
    void testCheckGroupANDOperator(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(FIELD_GROUP_NAME, "groupTestAnd").put(FIELD_PARENT_GROUP, 0)
                .put(GROUP_CONTEXT, new JsonObject().put(GROUP_AUTO_ASSIGN, YES)).put(GROUP_OPERATOR, "and").put(OBJECT_TYPE, new JsonArray().add("Cisco Wireless")).put(AutoAssignmentRule.OBJECT_NAME.getName(), new JsonArray().add("CISCO-WLC").add("cisco").add("Ruckus"));

        TestAPIUtil.post(GROUP_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(GroupConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.GROUP.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @Test
    void testCheckGroupANDOperator2(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(FIELD_GROUP_NAME, "groupTestAnd2").put(FIELD_PARENT_GROUP, 0)
                .put(GROUP_CONTEXT, new JsonObject().put(GROUP_AUTO_ASSIGN, YES)).put(GROUP_OPERATOR, "and").put(OBJECT_TYPE, new JsonArray().add("Cisco Wireless")).put(AutoAssignmentRule.IP_ADDRESS_RANGE.getName(), new JsonArray().add("***********-49").add("***********-123").add("************-189"));

        TestAPIUtil.post(GROUP_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(GroupConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.GROUP.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    // #3447
    @Test
    void testUpdateDiscoveryAzureCloud(VertxTestContext testContext) throws InterruptedException
    {

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var discovery = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, "Azure-Cloud");

        var discoveryParameters = new JsonObject().put(Discovery.DISCOVERY_NAME, discovery.getString(Discovery.DISCOVERY_NAME));

        TestAPIUtil.put(TestAPIConstants.DISCOVERY_API_ENDPOINT + "/" + discovery.getLong(ID), discoveryParameters, testContext.succeeding(result ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, result.statusCode());

                    assertNotNull(DiscoveryConfigStore.getStore().getItem(discovery.getLong(ID)));

                    testContext.completeNow();

                })));

    }

    //bug-3543
    @Test
    void testCredentialProfileInvalidCommunity0(VertxTestContext testContext)
    {
        var item = CredentialProfileConfigStore.getStore().getItem(10000000000001L);

        Assertions.assertNotNull(item);

        item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT).put("snmp.community", "123455");

        item.put(EVENT_TYPE, UI_ACTION_CREDENTIAL_PROFILE_TEST).put(REQUEST_PARAM_TYPE, APIConstants.REQUEST_UPDATE).put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER)
                .put(TARGET, "172.16.10.49");

        assertCredentialTestResult(testContext, STATUS_FAIL, null);

        TestUtil.vertx().eventBus().send(UI_ACTION_CREDENTIAL_PROFILE_TEST, item);
    }


    //bug-3543
    @Test
    void testCredentialProfileEmptyCommunity(VertxTestContext testContext)
    {
        var item = CredentialProfileConfigStore.getStore().getItem(10000000000001L);

        Assertions.assertNotNull(item);

        item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT).remove("snmp.community");

        item.put(EVENT_TYPE, UI_ACTION_CREDENTIAL_PROFILE_TEST).put(REQUEST_PARAM_TYPE, APIConstants.REQUEST_UPDATE).put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER)
                .put(TARGET, "172.16.10.43");

        assertCredentialTestResult(testContext, STATUS_SUCCEED, ErrorCodes.ERROR_CODE_SUCCESS);

        TestUtil.vertx().eventBus().send(UI_ACTION_CREDENTIAL_PROFILE_TEST, item);
    }

    @Test
    void testLinuxCredentialProfileInvalidCredentials(VertxTestContext testContext)
    {
        var context = TestConstants.prepareParams("testLinuxDiscovery");

        Assertions.assertNotNull(context);

        context.put(EVENT_TYPE, UI_ACTION_CREDENTIAL_PROFILE_TEST).put(REQUEST_PARAM_TYPE, APIConstants.REQUEST_CREATE)
                .put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER)
                .put(TARGET, "172.16.8.132");

        assertCredentialTestResult(testContext, STATUS_FAIL, null);//Fix code for mocking

        TestUtil.vertx().eventBus().send(UI_ACTION_CREDENTIAL_PROFILE_TEST, context);
    }

    @Test
    void testCredentialProfileInvalidCommunity1(VertxTestContext testContext)
    {
        var item = CredentialProfileConfigStore.getStore().getItem(10000000000001L);

        Assertions.assertNotNull(item);

        item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT).put("snmp.community", "123");

        item.put(EVENT_TYPE, UI_ACTION_CREDENTIAL_PROFILE_TEST).put(REQUEST_PARAM_TYPE, APIConstants.REQUEST_UPDATE).put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER)
                .put(TARGET, "172.16.10.49");

        assertCredentialTestResult(testContext, STATUS_FAIL, null);

        TestUtil.vertx().eventBus().send(UI_ACTION_CREDENTIAL_PROFILE_TEST, item);
    }

    @Test
    void testCredentialProfileInvalidCredential(VertxTestContext testContext)
    {
        var item = CredentialProfileConfigStore.getStore().getItem(10000000000001L);

        Assertions.assertNotNull(item);

        item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT).remove("snmp.community");

        item.put(EVENT_TYPE, UI_ACTION_CREDENTIAL_PROFILE_TEST).put(REQUEST_PARAM_TYPE, APIConstants.REQUEST_UPDATE).put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER)
                .put(TARGET, "172.16.10.49");

        item.put(ID, 123456L);

        assertCredentialTestResult(testContext, STATUS_FAIL, null);

        TestUtil.vertx().eventBus().send(UI_ACTION_CREDENTIAL_PROFILE_TEST, item);
    }

    @Test
    void testDiscoveryResultExportFunctionality(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_CSV_EXPORT_READY) && message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var result = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                assertEquals(HttpStatus.SC_OK, result.getInteger(APIConstants.RESPONSE_CODE));

                messageConsumer.unregister(asyncResult -> testContext.completeNow());
            }

        });

        var id = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testLinuxRangeDiscovery").getString(Discovery.DISCOVERY_NAME)).getLong(ID);

        var payload = new JsonObject()
                .put("ui.event.uuid", "1a1ca179-50a0-4d96-8ccb-fa5ed2f1a903")
                .put("uuid", "1a1ca179-50a0-4d96-8ccb-fa5ed2f1a903")
                .put("ids", new JsonArray().add(id))
                .put("session-id", TestUtil.getSessionId())
                .put("user.name", "admin")
                .put("event.type", "event.discovery.export");

        TestUtil.vertx().eventBus().publish(UI_ACTION_DISCOVERY_RESULT_EXPORT, payload);

    }

    @Test
    void testSSHCredentialProfileValidCredentials(VertxTestContext testContext)
    {
        var context = TestConstants.prepareParams("testLinuxInvalidCredentialDiscovery");

        Assertions.assertNotNull(context);

        context.put(EVENT_TYPE, UI_ACTION_CREDENTIAL_PROFILE_TEST).put(REQUEST_PARAM_TYPE, APIConstants.REQUEST_CREATE)
                .put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER)
                .put(TARGET, "172.16.10.98");

        assertCredentialTestResult(testContext, STATUS_SUCCEED, ErrorCodes.ERROR_CODE_SUCCESS);

        TestUtil.vertx().eventBus().send(UI_ACTION_CREDENTIAL_PROFILE_TEST, context);
    }

    @Test
    void testPowershellCredentialProfileValidCredentials(VertxTestContext testContext)
    {
        var context = TestConstants.prepareParams("testWindowsDiscovery");

        Assertions.assertNotNull(context);

        context.put(EVENT_TYPE, UI_ACTION_CREDENTIAL_PROFILE_TEST).put(REQUEST_PARAM_TYPE, APIConstants.REQUEST_CREATE)
                .put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER)
                .put(TARGET, "172.16.8.113");

        assertCredentialTestResult(testContext, STATUS_SUCCEED, ErrorCodes.ERROR_CODE_SUCCESS);

        TestUtil.vertx().eventBus().send(UI_ACTION_CREDENTIAL_PROFILE_TEST, context);
    }

    @Test
    void testSNMPCredentialProfileValidCredentials(VertxTestContext testContext)
    {
        var context = TestConstants.prepareParams("testCiscoWirelessDiscovery");

        Assertions.assertNotNull(context);

        context.put(EVENT_TYPE, UI_ACTION_CREDENTIAL_PROFILE_TEST).put(REQUEST_PARAM_TYPE, APIConstants.REQUEST_CREATE)
                .put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER)
                .put(TARGET, "172.16.10.43");

        assertCredentialTestResult(testContext, STATUS_SUCCEED, ErrorCodes.ERROR_CODE_SUCCESS);

        TestUtil.vertx().eventBus().send(UI_ACTION_CREDENTIAL_PROFILE_TEST, context);
    }
}
