/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.compliance;

import com.mindarray.*;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.CompliancePolicy;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.CompliancePolicyConfigStore;
import com.mindarray.store.ConfigurationConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.CompositeFuture;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.Configuration.CONFIG_MANAGEMENT_STATUS;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.compliance.ComplianceConstants.*;

@Timeout(60 * 1000)
@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestCompliance
{
    private static final Logger LOGGER = new Logger(TestCompliance.class, MOTADATA_COMPLIANCE, "Test Compliance");

    private static MessageConsumer<JsonObject> messageConsumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        var futures = new ArrayList<Future<Void>>();

        var trailPromise = Promise.<Void>promise();

        var entityStatsPromise = Promise.<Void>promise();

        var policyStatsPromise = Promise.<Void>promise();

        futures.add(trailPromise.future());

        futures.add(entityStatsPromise.future());

        futures.add(policyStatsPromise.future());

        // For Raw Table
        Bootstrap.configDBService().execute(TABLE_COMPLIANCE_TRAIL, "drop table " + TABLE_COMPLIANCE_TRAIL, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
        {
            if (result.succeeded())
            {
                LOGGER.debug("Deletion Successfully for " + TABLE_COMPLIANCE_TRAIL);

                trailPromise.complete();
            }
            else
            {
                LOGGER.debug("Deletion UnSuccessfully for " + TABLE_COMPLIANCE_TRAIL);

                LOGGER.debug(result.cause());

                trailPromise.fail(result.cause());
            }
        });

        // For Object Table
        Bootstrap.configDBService().execute(TABLE_COMPLIANCE_STATS_ENTITY, "drop table " + TABLE_COMPLIANCE_STATS_ENTITY, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
        {
            if (result.succeeded())
            {
                LOGGER.debug("Deletion Successfully for " + TABLE_COMPLIANCE_STATS_ENTITY);

                entityStatsPromise.complete();
            }
            else
            {
                LOGGER.debug("Deletion UnSuccessfully for " + TABLE_COMPLIANCE_STATS_ENTITY);

                LOGGER.debug(result.cause());

                entityStatsPromise.fail(result.cause());
            }
        });

        // For Audit Table
        Bootstrap.configDBService().execute(TABLE_COMPLIANCE_STATS_POLICY, "drop table " + TABLE_COMPLIANCE_STATS_POLICY, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
        {
            if (result.succeeded())
            {
                LOGGER.debug("Deletion Successfully for " + TABLE_COMPLIANCE_STATS_POLICY);

                policyStatsPromise.complete();
            }
            else
            {
                LOGGER.debug("Deletion UnSuccessfully for " + TABLE_COMPLIANCE_STATS_POLICY);

                LOGGER.debug(result.cause());

                policyStatsPromise.fail(result.cause());
            }
        });

        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                var trailFuture = Promise.<Void>promise();

                var entityStatsFuture = Promise.<Void>promise();

                var policyStatsFuture = Promise.<Void>promise();

                futures.add(trailFuture.future());

                futures.add(entityStatsFuture.future());

                futures.add(policyStatsFuture.future());

                Bootstrap.configDBService().execute(ComplianceConstants.TABLE_COMPLIANCE_STATS_POLICY, ComplianceConstants.TABLE_QUERY_COMPLIANCE_STATS_POLICY, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                {
                    if (asyncResult.succeeded())
                    {
                        LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_STATS_POLICY + " Table Existed/Created Successfully ");

                        trailFuture.complete();

                    }
                    else
                    {
                        trailFuture.fail(result.cause());

                        LOGGER.error(asyncResult.cause().getCause());
                    }
                });

                Bootstrap.configDBService().execute(ComplianceConstants.TABLE_COMPLIANCE_STATS_ENTITY, ComplianceConstants.TABLE_QUERY_COMPLIANCE_STATS_ENTITY, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                {
                    if (asyncResult.succeeded())
                    {
                        LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_STATS_ENTITY + " Table Existed/Created Successfully");

                        entityStatsFuture.complete();
                    }
                    else
                    {
                        entityStatsFuture.fail(result.cause());

                        LOGGER.error(asyncResult.cause().getCause());
                    }
                });

                Bootstrap.configDBService().execute(ComplianceConstants.TABLE_COMPLIANCE_TRAIL, ComplianceConstants.TABLE_QUERY_COMPLIANCE_TRAIL, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                {
                    if (asyncResult.succeeded())
                    {
                        LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_TRAIL + " Table Existed/Created Successfully");

                        policyStatsFuture.complete();
                    }
                    else
                    {
                        policyStatsFuture.fail(result.cause());

                        LOGGER.error(asyncResult.cause().getCause());
                    }
                });

                Future.join(futures).onComplete(handler ->
                        testContext.completeNow());
            }

        });

    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testExecuteCompliancePolicy(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = CompliancePolicyConfigStore.getStore().getItemByValue(CompliancePolicy.COMPLIANCE_POLICY_NAME, "112 Policy");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.compliance.manager", message ->
        {
            try
            {
                var event = message.body();

                LOGGER.info(String.format("%s : event %s", testInfo.getTestMethod().get().getName(), event));

                Assertions.assertEquals(item.getLong(GlobalConstants.ID), event.getLong(GlobalConstants.ID));

                Assertions.assertEquals(1, event.getInteger(ComplianceState.VULNERABLE.name().toLowerCase()));

                Assertions.assertEquals(1, event.getInteger(ComplianceState.POOR.name().toLowerCase()));

                Assertions.assertEquals(0, event.getInteger(ComplianceState.MODERATE.name().toLowerCase()));

                Assertions.assertEquals(0, event.getInteger(ComplianceState.SECURE.name().toLowerCase()));

                Assertions.assertEquals(item.getLong(GlobalConstants.ID), event.getLong(GlobalConstants.ID));

                Assertions.assertEquals("Scan Completed", event.getString(MESSAGE));

                Assertions.assertEquals(25, event.getInteger(ComplianceConstants.COMPLIANCE_PERCENTAGE));

                testContext.completeNow();
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        TestUtil.vertx().eventBus().send(EventBusConstants.UI_ACTION_COMPLIANCE_POLICY_RUN, new JsonObject().put(CompliancePolicy.COMPLIANCE_POLICY_ENTITIES, new JsonArray().add(item.getLong(GlobalConstants.ID))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testExecuteCompliancePolicyInvalidObject(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = CompliancePolicyConfigStore.getStore().getItemByValue(CompliancePolicy.COMPLIANCE_POLICY_NAME, "Demo Fail Policy");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.compliance.manager", message ->
        {
            try
            {
                var event = message.body();

                LOGGER.info(String.format("%s : event %s", testInfo.getTestMethod().get().getName(), event));

                Assertions.assertEquals("No object Found for Compliance Policy", event.getString(MESSAGE));

                TestAPIUtil.delete(TestAPIConstants.COMPLIANCE_POLICY_API_ENDPOINT + item.getLong(ID), testContext.succeeding(response -> testContext.verify(() ->
                {
                    LOGGER.info(String.format("Response: %s", response.bodyAsJsonObject()));

                    TestAPIUtil.assertDeleteEntityTestResult(CompliancePolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_DELETED, APIConstants.Entity.COMPLIANCE_POLICY.getName()));

                    testContext.completeNow();
                })));
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        TestUtil.vertx().eventBus().send(EventBusConstants.UI_ACTION_COMPLIANCE_POLICY_RUN, new JsonObject().put(CompliancePolicy.COMPLIANCE_POLICY_ENTITIES, new JsonArray().add(item.getLong(GlobalConstants.ID))));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testExecuteCompliancePolicySecondFail(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = CompliancePolicyConfigStore.getStore().getItemByValue(CompliancePolicy.COMPLIANCE_POLICY_NAME, "Test Version 15.2(4)M7");

        var entities = item.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).getJsonArray(ENTITIES);

        updateStatus(entities, NO).onComplete(result ->
        {
            messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.compliance.manager", message ->
            {
                try
                {
                    var event = message.body();

                    LOGGER.info(String.format("%s : event %s", testInfo.getTestMethod().get().getName(), event));

                    Assertions.assertEquals("Scan stopped as config status is off", event.getString(MESSAGE));

                    updateStatus(entities, YES).onComplete(response ->
                            testContext.completeNow());
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            TestUtil.vertx().eventBus().send(EventBusConstants.UI_ACTION_COMPLIANCE_POLICY_RUN, new JsonObject().put(CompliancePolicy.COMPLIANCE_POLICY_ENTITIES, new JsonArray().add(item.getLong(GlobalConstants.ID))));
        });

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testExecuteCompliancePolicySecond(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = CompliancePolicyConfigStore.getStore().getItemByValue(CompliancePolicy.COMPLIANCE_POLICY_NAME, "Test Version 15.2(4)M7");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.compliance.manager", message ->
        {
            try
            {
                var event = message.body();

                LOGGER.info(String.format("%s : event %s", testInfo.getTestMethod().get().getName(), event));

                Assertions.assertEquals(item.getLong(GlobalConstants.ID), event.getLong(GlobalConstants.ID));

                Assertions.assertEquals(0, event.getInteger(ComplianceState.VULNERABLE.name().toLowerCase()));

                Assertions.assertEquals(0, event.getInteger(ComplianceState.POOR.name().toLowerCase()));

                Assertions.assertEquals(0, event.getInteger(ComplianceState.MODERATE.name().toLowerCase()));

                Assertions.assertEquals(1, event.getInteger(ComplianceState.SECURE.name().toLowerCase()));

                Assertions.assertEquals(item.getLong(GlobalConstants.ID), event.getLong(GlobalConstants.ID));

                Assertions.assertEquals("Scan Completed", event.getString(MESSAGE));

                Assertions.assertEquals(100, event.getInteger(ComplianceConstants.COMPLIANCE_PERCENTAGE));

                testContext.completeNow();
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        TestUtil.vertx().eventBus().send(EventBusConstants.UI_ACTION_COMPLIANCE_POLICY_RUN, new JsonObject().put(CompliancePolicy.COMPLIANCE_POLICY_ENTITIES, new JsonArray().add(item.getLong(GlobalConstants.ID))));
    }

    private CompositeFuture updateStatus(JsonArray entities, String status)
    {
        var futures = new ArrayList<Future<Void>>();

        for (var index = 0; index < entities.size(); index++)
        {
            var item = ConfigurationConfigStore.getStore().getItem(ConfigurationConfigStore.getStore().getIdByObjectId(ObjectConfigStore.getStore().getItem(entities.getLong(index)).getInteger(AIOpsObject.OBJECT_ID)));

            var future = Promise.<Void>promise();

            futures.add(future.future());

            Bootstrap.vertx().eventBus().<JsonObject>request(
                    EventBusConstants.EVENT_CONFIG_MANAGE,
                    new JsonObject().put(ID, item.getLong(ID)).put(CONFIG_MANAGEMENT_STATUS, status).put(USER_NAME, SYSTEM_USER).put(REMOTE_ADDRESS, SYSTEM_REMOTE_ADDRESS),
                    reply ->
                    {
                        if (reply.succeeded())
                        {
                            future.complete();
                        }
                        else
                        {

                            future.fail(reply.cause());
                        }
                    }
            );
        }

        return Future.join(futures);
    }
}
