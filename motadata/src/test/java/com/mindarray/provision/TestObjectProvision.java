/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.provision;

import com.mindarray.*;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Discovery;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.nms.ObjectManager;
import com.mindarray.store.DiscoveryConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.OBJECT_API_ENDPOINT;
import static com.mindarray.api.AIOpsObject.OBJECT_TYPE;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@Timeout(100 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")
public class TestObjectProvision
{

    private static final Logger LOGGER = new Logger(TestObjectProvision.class, MOTADATA_NMS, "Object Provision Test");
    private static MessageConsumer<JsonObject> messageConsumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext) throws InterruptedException
    {
        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        testContext.completeNow();
    }

    private static void assertProvisionObjectTestResult(long discoveryId, Promise<Void> promise)
    {
        try
        {
            var discovery = DiscoveryConfigStore.getStore().getItem(discoveryId);

            Bootstrap.configDBService().getAll(ConfigDBConstants.COLLECTION_DISCOVERY_RESULT + discovery.getLong(ID), asyncResult ->
            {
                try
                {
                    if (asyncResult.failed() || asyncResult.result().isEmpty())
                    {
                        promise.fail(String.format("failed to get discovery object %s", discovery.getString(Discovery.DISCOVERY_NAME)));
                    }
                    else
                    {
                        var futures = new ArrayList<Future<Void>>();

                        var probes = asyncResult.result();

                        var errors = new StringBuilder(0);

                        if (discovery.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CLOUD.getName()) || discovery.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SDN.getName()))
                        {
                            discovery.remove(ID);
                        }

                        for (var index = 0; index < probes.size(); index++)
                        {
                            var future = Promise.<Void>promise();

                            futures.add(future.future());

                            var objectId = NOT_AVAILABLE;

                            var probe = probes.getJsonObject(index);

                            var type = NMSConstants.Type.valueOfName(probe.getString(AIOpsObject.OBJECT_TYPE));

                            probe.mergeIn(discovery).put(SESSION_ID, TestUtil.getSessionId());

                            assertNotNull(type);

                            assertTrue(Arrays.asList(NMSConstants.Type.values()).contains(type));

                            if (type.toString().equals("PORT") || type.toString().equals("URL"))
                            {
                                var objects = ObjectConfigStore.getStore().flatItemsByMapValueField(type, AIOpsObject.OBJECT_IP, probe.getString(AIOpsObject.OBJECT_TARGET), AIOpsObject.OBJECT_CONTEXT, PORT, probe.getJsonObject(Discovery.DISCOVERY_CONTEXT).getInteger(PORT));

                                objectId = !objects.isEmpty() ? objects.getJsonObject(0).getInteger(AIOpsObject.OBJECT_ID) : objectId;
                            }
                            else
                            {
                                objectId = ObjectConfigStore.getStore().getObjectIdByTarget(probe.getString(AIOpsObject.OBJECT_TARGET), type);

                                if (objectId == NOT_AVAILABLE)
                                {
                                    objectId = ObjectConfigStore.getStore().getObjectIdByIP(probe.getString(AIOpsObject.OBJECT_IP), type);

                                    if (objectId == NOT_AVAILABLE)
                                    {
                                        objectId = ObjectConfigStore.getStore().getObjectIdByObjectName(probe.getString(AIOpsObject.OBJECT_NAME));
                                    }
                                }
                            }

                            if (objectId != NOT_AVAILABLE)
                            {
                                errors.append("Object ").append(probe.getString(AIOpsObject.OBJECT_TARGET)).append(" is already provisioned").append(SEPARATOR);

                                future.fail(errors.toString());
                            }
                            else
                            {
                                LOGGER.trace(String.format("Request send for object provision: %s", probe.getString(AIOpsObject.OBJECT_IP)));

                                TestUtil.vertx().eventBus().<JsonObject>request(EVENT_OBJECT_PROVISION, probe.put(EVENT_REPLY, YES),
                                        new DeliveryOptions().setSendTimeout(300000L), reply ->
                                        {
                                            try
                                            {
                                                if (reply.failed())
                                                {
                                                    future.fail(reply.cause());
                                                }
                                                else
                                                {
                                                    var result = reply.result().body();

                                                    Assertions.assertNotNull(result);

                                                    Assertions.assertTrue(result.containsKey(STATUS));

                                                    Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                                                    LOGGER.info(String.format("%s : %s object provisioned successfully", result.getString(AIOpsObject.OBJECT_IP), result.getString(AIOpsObject.OBJECT_NAME)));

                                                    future.complete();
                                                }
                                            }
                                            catch (Exception exception)
                                            {
                                                LOGGER.error(exception);

                                                future.fail(exception);
                                            }

                                        });
                            }
                        }

                        Future.all(futures).onComplete(result ->
                        {

                            if (result.succeeded())
                            {
                                promise.complete();
                            }
                            else
                            {
                                promise.fail(result.cause());
                            }

                        });

                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    promise.fail(exception);
                }
            });
        }
        catch (Exception exception)
        {
            promise.fail(exception);

            LOGGER.error(exception);
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testNetworkCategoryObjectProvision(VertxTestContext testContext)
    {
        var updated = new JsonArray();

        var futures = new ArrayList<Future<Void>>();

        var items = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_CATEGORY, NMSConstants.Category.NETWORK.getName());

        for (var index = 0; index < items.size(); index++)
        {
            var objectType = items.getJsonObject(index).getString(Discovery.DISCOVERY_OBJECT_TYPE);

            if (!(objectType.equalsIgnoreCase(NMSConstants.Type.CISCO_WIRELESS.getName()) ||
                    objectType.equalsIgnoreCase(NMSConstants.Type.ARUBA_WIRELESS.getName()) ||
                    objectType.equalsIgnoreCase(NMSConstants.Type.RUCKUS_WIRELESS.getName()) ||
                    items.getJsonObject(index).getString(Discovery.DISCOVERY_NAME).equalsIgnoreCase("SNMP-Test V2C-172.16.10.1") ||
                    items.getJsonObject(index).getString(Discovery.DISCOVERY_NAME).equalsIgnoreCase("testTriggerInvalidDiscoveryScheduler")))
            {
                updated.add(items.getJsonObject(index));
            }
        }

        provisionObject(futures, updated, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testLinuxServerObjectProvision(VertxTestContext testContext)
    {
        var futures = new ArrayList<Future<Void>>();

        var items = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_OBJECT_TYPE, NMSConstants.Type.LINUX.getName());

        provisionObject(futures, items, testContext, false);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testHPUXObjectProvision(VertxTestContext testContext)
    {
        var futures = new ArrayList<Future<Void>>();

        var items = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_OBJECT_TYPE, NMSConstants.Type.HP_UX.getName());

        provisionObject(futures, items, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testSolarisObjectProvision(VertxTestContext testContext)
    {
        var futures = new ArrayList<Future<Void>>();

        var items = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_NAME, "Solaris-Test-172.16.8.133");

        provisionObject(futures, items, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testIBMAIXObjectProvision(VertxTestContext testContext)
    {
        var futures = new ArrayList<Future<Void>>();

        var items = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_OBJECT_TYPE, NMSConstants.Type.IBM_AIX.getName());

        provisionObject(futures, items, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testWindowServerObjectProvision(VertxTestContext testContext)
    {
        var futures = new ArrayList<Future<Void>>();

        var items = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_OBJECT_TYPE, NMSConstants.Type.WINDOWS.getName());

        provisionObject(futures, items, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testWirelessCategoryObjectProvision(VertxTestContext testContext) throws InterruptedException
    {
        var updated = new JsonArray();

        var futures = new ArrayList<Future<Void>>();

        var items = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_CATEGORY, NMSConstants.Category.NETWORK.getName());

        for (var index = 0; index < items.size(); index++)
        {
            var objectType = items.getJsonObject(index).getString(Discovery.DISCOVERY_OBJECT_TYPE);

            if ((objectType.equalsIgnoreCase(NMSConstants.Type.CISCO_WIRELESS.getName()) ||
                    objectType.equalsIgnoreCase(NMSConstants.Type.ARUBA_WIRELESS.getName()) ||
                    objectType.equalsIgnoreCase(NMSConstants.Type.RUCKUS_WIRELESS.getName())))
            {
                updated.add(items.getJsonObject(index));
            }
        }

        provisionObject(futures, updated, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testVirtualizationCategoryObjectProvision(VertxTestContext testContext) throws InterruptedException
    {
        var futures = new ArrayList<Future<Void>>();

        var items = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_CATEGORY, NMSConstants.Category.VIRTUALIZATION.getName());

        provisionObject(futures, items, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testServiceCheckCategoryObjectProvision(VertxTestContext testContext)
    {
        var futures = new ArrayList<Future<Void>>();

        var items = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_CATEGORY, NMSConstants.Category.SERVICE_CHECK.getName());

        var updated = new JsonArray();

        for (var index = 0; index < items.size(); index++)
        {
            var item = items.getJsonObject(index);

            if (item.getInteger(Discovery.DISCOVERY_DISCOVERED_OBJECTS) >= 1)
            {
                updated.add(item);
            }
            else
            {
                LOGGER.info(String.format("%s failed to discover ", item.getString(Discovery.DISCOVERY_NAME)));
            }
        }

        provisionObject(futures, updated, testContext, true);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testOtherCategoryObjectProvision(VertxTestContext testContext)
    {
        var futures = new ArrayList<Future<Void>>();

        var items = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_CATEGORY, NMSConstants.Category.OTHER.getName());

        provisionObject(futures, items, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testWindowClusterObjectProvision(VertxTestContext testContext)
    {
        var futures = new ArrayList<Future<Void>>();

        var items = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_OBJECT_TYPE, NMSConstants.Type.WINDOWS_CLUSTER.getName());

        provisionObject(futures, items, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testOffice365ObjectProvision(VertxTestContext testContext)
    {
        var futures = new ArrayList<Future<Void>>();

        var items = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_OBJECT_TYPE, NMSConstants.Type.OFFICE_365.getName());

        provisionCloudObject(futures, items, testContext, NMSConstants.Type.OFFICE_365);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testAWSObjectProvision(VertxTestContext testContext)
    {
        var futures = new ArrayList<Future<Void>>();

        var items = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_OBJECT_TYPE, NMSConstants.Type.AWS_CLOUD.getName());

        provisionCloudObject(futures, items, testContext, NMSConstants.Type.AWS_CLOUD);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testAzureClusterObjectProvision(VertxTestContext testContext)
    {
        var futures = new ArrayList<Future<Void>>();

        var items = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_OBJECT_TYPE, NMSConstants.Type.AZURE_CLOUD.getName());

        provisionCloudObject(futures, items, testContext, NMSConstants.Type.AZURE_CLOUD);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testDeleteOffice365Object(VertxTestContext testContext)
    {
        var objects = ObjectConfigStore.getStore().flatItemsByValues(OBJECT_TYPE, new JsonArray().add(NMSConstants.Type.OFFICE_365.getName()).add(NMSConstants.Type.MICROSOFT_TEAMS.getName()).add(NMSConstants.Type.SHAREPOINT_ONLINE.getName()).add(NMSConstants.Type.EXCHANGE_ONLINE.getName()).add(NMSConstants.Type.ONEDRIVE.getName()), ID);

        Assertions.assertNotNull(objects);

        assertFalse(objects.isEmpty());

        TestAPIUtil.deleteAll(OBJECT_API_ENDPOINT, new JsonObject()
                .put(REQUEST_PARAM_IDS, objects), testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(InfoMessageConstants.OBJECT_ARCHIVED_SUCCEEDED, response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

            assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

            testContext.completeNow();
        })));
    }

    //3667-bug
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testOffice365ObjectProvisionAfterDelete(VertxTestContext testContext) throws InterruptedException
    {

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var futures = new ArrayList<Future<Void>>();

        var items = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_OBJECT_TYPE, NMSConstants.Type.OFFICE_365.getName());

        provisionCloudObject(futures, items, testContext, NMSConstants.Type.OFFICE_365);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testNetworkFirewallProvisionViaAPI(VertxTestContext testContext) throws Exception
    {
        MotadataConfigUtil.loadConfigs(MotadataConfigUtil.getConfigs().put("archived.object.enabled", YES));

        Bootstrap.vertx().undeploy(Bootstrap.getDeployedVerticles().get(ObjectManager.class.getSimpleName()), asyncHandler ->
        {
            if (asyncHandler.succeeded())
            {
                Bootstrap.getDeployedVerticles().remove(ObjectManager.class.getSimpleName());

                Bootstrap.startEngine(new ObjectManager(), ObjectManager.class.getSimpleName(), null).onComplete(asyncFuture ->
                {
                    if (asyncFuture.succeeded())
                    {
                        Assertions.assertTrue(MotadataConfigUtil.archivedObjectEnabled());

                        var discovery = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, "SNMP-Test V2C-172.16.10.1");

                        try
                        {
                            Bootstrap.configDBService().getAll(ConfigDBConstants.COLLECTION_DISCOVERY_RESULT + discovery.getLong(ID), asyncResponse ->
                            {
                                try
                                {
                                    if (asyncResponse.failed() || asyncResponse.result().isEmpty())
                                    {
                                        testContext.failNow(String.format("failed to get discovery object %s", discovery.getString(Discovery.DISCOVERY_NAME)));
                                    }
                                    else
                                    {

                                        var futures = new ArrayList<Future<Void>>();

                                        var probes = asyncResponse.result();

                                        var errors = new StringBuilder(0);

                                        for (var index = 0; index < probes.size(); index++)
                                        {
                                            var future = Promise.<Void>promise();

                                            futures.add(future.future());

                                            var objectId = NOT_AVAILABLE;

                                            var probe = probes.getJsonObject(index);

                                            var type = NMSConstants.Type.valueOfName(probe.getString(AIOpsObject.OBJECT_TYPE));

                                            probe.mergeIn(discovery).put(SESSION_ID, TestUtil.getSessionId());

                                            assertNotNull(type);

                                            assertTrue(Arrays.asList(NMSConstants.Type.values()).contains(type));

                                            objectId = ObjectConfigStore.getStore().getObjectIdByTarget(probe.getString(AIOpsObject.OBJECT_TARGET), type);

                                            if (objectId == NOT_AVAILABLE)
                                            {
                                                objectId = ObjectConfigStore.getStore().getObjectIdByIP(probe.getString(AIOpsObject.OBJECT_IP), type);

                                                if (objectId == NOT_AVAILABLE)
                                                {
                                                    objectId = ObjectConfigStore.getStore().getObjectIdByObjectName(probe.getString(AIOpsObject.OBJECT_NAME));
                                                }
                                            }

                                            if (objectId != NOT_AVAILABLE)
                                            {
                                                errors.append("Object ").append(probe.getString(AIOpsObject.OBJECT_TARGET)).append(" is already provisioned").append(SEPARATOR);

                                                future.fail(errors.toString());
                                            }
                                            else
                                            {
                                                LOGGER.trace(String.format("Request send for object provision: %s", probe.getString(AIOpsObject.OBJECT_IP)));


                                                TestAPIUtil.post(TestAPIConstants.OBJECT_PROVISION_API_ENDPOINT, new JsonObject().put(REQUEST_PARAMS, JsonArray.of(probe)), testContext.succeeding(response ->
                                                {
                                                    Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                                                    var result = response.bodyAsJsonObject();

                                                    Assertions.assertNotNull(result);

                                                    Assertions.assertTrue(result.containsKey(STATUS));

                                                    Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                                                    LOGGER.info(String.format("%s : %s object provisioned successfully", result.getString(AIOpsObject.OBJECT_IP), result.getString(AIOpsObject.OBJECT_NAME)));

                                                    future.complete();
                                                }));

                                            }
                                        }

                                        Future.all(futures).onComplete(asyncResult ->
                                        {

                                            if (asyncResult.succeeded())
                                            {
                                                testContext.completeNow();
                                            }
                                            else
                                            {
                                                testContext.failNow(asyncResult.cause());
                                            }

                                        });

                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    testContext.failNow(exception.getCause());
                                }
                            });
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            testContext.failNow(exception.getCause());
                        }
                    }
                    else
                    {
                        testContext.failNow(asyncFuture.cause().getMessage());
                    }
                });
            }
            else
            {
                testContext.failNow(asyncHandler.cause().getMessage());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testHCICategoryObjectProvision(VertxTestContext testContext) throws InterruptedException
    {
        var futures = new ArrayList<Future<Void>>();

        var items = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_CATEGORY, NMSConstants.Category.HCI.getName());

        provisionObject(futures, items, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testSDNCategoryObjectProvision(VertxTestContext testContext)
    {
        var futures = new ArrayList<Future<Void>>();

        var items = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_CATEGORY, NMSConstants.Category.SDN.getName());

        provisionObject(futures, items, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testStorageCategoryObjectProvision(VertxTestContext testContext)
    {
        var futures = new ArrayList<Future<Void>>();

        var items = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_CATEGORY, NMSConstants.Category.STORAGE.getName());

        provisionObject(futures, items, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testQueryObjectStatus(VertxTestContext testContext)
    {
        var event = new JsonObject().put("event.context", new JsonObject().put(SESSION_ID, TestUtil.getSessionId())
                        .put(UI_EVENT_UUID, "4ad1e808-2ce3-4994-8fc6-b598e54b2f15")).put(SESSION_ID, TestUtil.getSessionId())
                .put(UI_EVENT_UUID, "4ad1e808-2ce3-4994-8fc6-b598e54b2f15").put("user.name", "admin");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(EVENT_OBJECT_STATUS_QUERY) && message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var context = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                LOGGER.info(String.format("%s event received Object Status Query %s ", EVENT_OBJECT_STATUS_QUERY, context.encode()));

                assertNotNull(context);

                assertTrue(context.containsKey("object.status"));

                assertFalse(context.getJsonObject("object.status").isEmpty());

                assertTrue(context.containsKey("instance.status"));

                assertFalse(context.getJsonObject("instance.status").isEmpty());

                messageConsumer.unregister(result -> testContext.completeNow());
            }
        });

        TestUtil.vertx().eventBus().send(EVENT_OBJECT_STATUS_QUERY, event);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testProcessObjectStatusChangeEvent(VertxTestContext testContext)
    {
        var event = new JsonObject("{\"id\":72776577216,\"status.key\":72776577216,\"status\":\"Up\",\"status.type\":\"object.status\",\"user.name\":\"admin\"}");

        TestUtil.vertx().eventBus().send(EVENT_OBJECT_STATUS_CHANGE, event);

        event = new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(EVENT_OBJECT_STATUS_QUERY) && message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var context = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                LOGGER.info(String.format("%s event received in Object Status Change %s ", EVENT_OBJECT_STATUS_QUERY, context.encode()));

                assertNotNull(context);

                assertTrue(context.containsKey("object.status"));

                assertFalse(context.getJsonObject("object.status").isEmpty());

                messageConsumer.unregister(result -> testContext.completeNow());
            }
        });

        TestUtil.vertx().eventBus().publish(EVENT_USER_PING, event);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testIBMAS400ObjectProvision(VertxTestContext testContext)
    {

        provisionObject(new ArrayList<>(), DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_OBJECT_TYPE, NMSConstants.Type.IBM_AS_400.getName()), testContext, false);
    }

    private void assertProvisionObjectTestResult(Set<String> items)
    {
        for (var item : items)
        {
            var object = ObjectConfigStore.getStore().getItemByIP(item);

            if (object == null)
            {
                object = ObjectConfigStore.getStore().getIdByObjectName(item);

                if (object == null)
                {
                    object = ObjectConfigStore.getStore().getItemByTarget(item);
                }
            }

            LOGGER.info(String.format("object %s added to config store with id : %s", item, object));

            Assertions.assertNotNull(object);

            assertFalse(ObjectConfigStore.getStore().getTypes().isEmpty());
        }
    }

    private void provisionObject(List<Future<Void>> futures, JsonArray items, VertxTestContext testContext, boolean isServiceCheck)
    {
        try
        {
            var provisionedObject = new HashSet<String>();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (!provisionedObject.contains(item.getString("discovery.target")) && (!item.containsKey("discovery.type") || "ip.address".equalsIgnoreCase(item.getString("discovery.type"))))
                {
                    testContext.awaitCompletion(200, TimeUnit.MILLISECONDS);

                    var promise = Promise.<Void>promise();

                    futures.add(promise.future());

                    assertProvisionObjectTestResult(items.getJsonObject(index).getLong(GlobalConstants.ID), promise);

                    provisionedObject.add(items.getJsonObject(index).getString("discovery.target"));
                }
            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    if (!isServiceCheck)
                    {
                        assertProvisionObjectTestResult(provisionedObject);
                    }

                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    private void provisionCloudObject(List<Future<Void>> futures, JsonArray items, VertxTestContext testContext, NMSConstants.Type type)
    {
        try
        {
            for (var index = 0; index < items.size(); index++)
            {
                if (items.getJsonObject(index).getInteger(Discovery.DISCOVERY_DISCOVERED_OBJECTS) >= 1)
                {
                    testContext.awaitCompletion(1, TimeUnit.SECONDS);

                    var promise = Promise.<Void>promise();

                    futures.add(promise.future());

                    assertProvisionObjectTestResult(items.getJsonObject(index).getLong(GlobalConstants.ID), promise);
                }
            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    assertFalse(ObjectConfigStore.getStore().getItemsByType(type).isEmpty());

                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }

    }

}
