/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.rediscovery;

import com.mindarray.*;
import com.mindarray.api.Metric;
import com.mindarray.api.Scheduler;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.ObjectStatusCacheStore;
import com.mindarray.store.SchedulerConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.METRIC_API_ENDPOINT;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@Timeout(180 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")
public class TestHCIRediscovery
{
    public static final Map<Long, JsonObject> REDISCOVERY_ITEMS = new ConcurrentHashMap<>();
    private static final Logger LOGGER = new Logger(TestHCIRediscovery.class, MOTADATA_NMS, "HCI Rediscovery Test");
    private static final JsonObject METRICS = new JsonObject();
    private static final JsonArray VMS = new JsonArray();
    private static final JsonObject PARAMETERS = new JsonObject();
    public static MessageConsumer<JsonObject> messageHandler = null;
    private static MessageConsumer<JsonObject> messageConsumer = null;
    private static Long nutanixId = null;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "rediscover-parameters.json");

            Assertions.assertTrue(file.exists());

            PARAMETERS.mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));

            Assertions.assertFalse(PARAMETERS.isEmpty());

            nutanixId = ObjectConfigStore.getStore().getItemByIP(PARAMETERS.getJsonObject(testInfo.getDisplayName()).getString("nutanix"));

            Assertions.assertNotNull(nutanixId);

            assertRediscoveryConsumerSetup();

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        if (messageHandler != null)
        {
            messageHandler.unregister(result -> testContext.completeNow());
        }
    }

    public static JsonObject prepareParams(String parameter)
    {
        return new JsonObject().mergeIn(PARAMETERS.getJsonObject(parameter));
    }

    public static void testRediscover(VertxTestContext testContext, JsonObject rediscoveryParameters, JsonObject object, boolean abort)
    {
        var objects = rediscoveryParameters.getJsonArray(NMSConstants.OBJECTS);

        rediscoveryParameters.remove(NMSConstants.OBJECTS);

        var autoProvision = object.containsKey(NMSConstants.AUTO_PROVISION_STATUS) && object.getString(NMSConstants.AUTO_PROVISION_STATUS).equalsIgnoreCase(YES);

        TestAPIUtil.post(TestAPIConstants.SCHEDULER_API_ENDPOINT, rediscoveryParameters, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    try
                    {
                        LOGGER.info(String.format("scheduler API response : %s ", response.bodyAsJsonObject().encode()));

                        Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                        var retries = new AtomicInteger();

                        var schedulerId = response.bodyAsJsonObject().getJsonArray(ID).getLong(0);

                        TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(3), timer ->
                        {
                            if (SchedulerConfigStore.getStore().getItem(schedulerId) != null)
                            {
                                TestUtil.vertx().cancelTimer(timer);

                                TestNMSUtil.setSchedulerId(response.bodyAsJsonObject().getJsonArray(ID).getLong(0));

                                objects.forEach(rediscoverObject -> ObjectStatusCacheStore.getStore().updateItem(CommonUtil.getLong(rediscoverObject), STATUS_UP, DateTimeUtil.currentSeconds()));

                                LOGGER.trace(String.format("HCI rediscovery request sent for: %s", ObjectConfigStore.getStore().getObjectNames(response.bodyAsJsonObject().getJsonArray(ID))));

                                TestUtil.vertx().eventBus().send(EventBusConstants.UI_ACTION_SCHEDULER_RUN, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER).put(ID, response.bodyAsJsonObject().getJsonArray(ID).getLong(0)));

                                if (abort)
                                {
                                    TestUtil.vertx().eventBus().send(EVENT_REDISCOVER_STOP, new JsonObject().put(ID, response.bodyAsJsonObject().getJsonArray(ID).getLong(0))
                                            .put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER));

                                    for (var index = 0; index < 5; index++)
                                    {
                                        TestUtil.vertx().eventBus().send(EventBusConstants.UI_ACTION_SCHEDULER_RUN, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER).put(ID, response.bodyAsJsonObject().getJsonArray(ID).getLong(0)));

                                        TestUtil.vertx().setTimer(1000, id -> TestUtil.vertx().eventBus().send(EVENT_REDISCOVER_STOP, new JsonObject().put(ID, response.bodyAsJsonObject().getJsonArray(ID).getLong(0))
                                                .put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER)));
                                    }

                                    testContext.completeNow();
                                }
                                else
                                {
                                    REDISCOVERY_ITEMS.put(response.bodyAsJsonObject().getJsonArray(ID).getLong(0), new JsonObject().put("object", object).put("auto.provision", autoProvision).put("metric.type", rediscoveryParameters.getJsonObject(Scheduler.SCHEDULER_CONTEXT).getString(NMSConstants.REDISCOVER_JOB)).put("context", testContext));
                                }
                            }
                            else if (retries.get() > 5)
                            {
                                TestUtil.vertx().cancelTimer(timer);

                                testContext.failNow("scheduler id not found in scheduler config store");
                            }
                            else
                            {
                                retries.incrementAndGet();
                            }
                        });
                    }
                    catch (Exception exception)
                    {
                        testContext.failNow(exception);
                    }
                })));
    }

    private static void assertRediscoveryConsumerSetup()
    {
        messageHandler = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                var event = message.body();

                var eventContext = CodecUtil.toJSONObject(event.getBinary(EVENT_CONTEXT));

                if (event.getString(EVENT_TYPE) != null && event.getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_STREAMING_BROADCAST) && (eventContext.getString(EVENT_STATE) != null && eventContext.getString(EVENT_STATE).equals(EVENT_STATE_COMPLETED) && eventContext.getJsonObject(EVENT_CONTEXT) != null
                        && eventContext.getJsonObject(EVENT_CONTEXT).containsKey(EVENT_SCHEDULER) && REDISCOVERY_ITEMS.containsKey(eventContext.getJsonObject(EVENT_CONTEXT).getLong(EVENT_SCHEDULER))))
                {
                    var schedulerContext = REDISCOVERY_ITEMS.get(eventContext.getJsonObject(EVENT_CONTEXT).getLong(EVENT_SCHEDULER));

                    var testContext = (VertxTestContext) schedulerContext.getValue("context");

                    var object = schedulerContext.getJsonObject("object");

                    var autoProvision = schedulerContext.getBoolean("auto.provision");

                    LOGGER.info("Received rediscovery event : " + event.encode());

                    if (eventContext.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                    {
                        Assertions.assertTrue(Boolean.TRUE);

                        if ((object != null && object.isEmpty()) || autoProvision)
                        {
                            REDISCOVERY_ITEMS.remove(eventContext.getJsonObject(EVENT_CONTEXT).getLong(EVENT_SCHEDULER));

                            testContext.completeNow();
                        }
                    }
                    else if (eventContext.getJsonObject(EVENT_CONTEXT) == null || eventContext.getJsonObject(EVENT_CONTEXT).getString(NMSConstants.REDISCOVER_JOB) == null)
                    {
                        testContext.failNow(new Exception("Rediscover failed"));
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });
    }

    @AfterEach
    void afterEach(VertxTestContext testContext) throws InterruptedException
    {
        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetHCIObject(VertxTestContext testContext)
    {
        TestAPIUtil.get(METRIC_API_ENDPOINT + "/" + nutanixId, testContext.succeeding(response ->
        {
            assertEquals(HttpStatus.SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            assertNotNull(body);

            assertEquals(HttpStatus.SC_OK, body.getInteger(RESPONSE_CODE));

            var result = JsonObject.mapFrom(body.getValue(RESULT));

            assertTrue(result != null && !result.isEmpty());

            var item = JsonObject.mapFrom(body.getValue(RESULT));

            if (item.containsKey("Nutanix VM"))
            {
                METRICS.put(RESULT, result);

                var items = item.getJsonArray("Nutanix VM").getJsonObject(0).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS);

                LOGGER.info(String.format("items : %s ", items));

                for (var index = 0; index < items.size(); index++)
                {
                    if (items.getJsonObject(index).getString(STATUS).equals(STATUS_UP))
                    {
                        VMS.add(JsonObject.mapFrom(item.getJsonArray("Nutanix VM").getJsonObject(0).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS).remove(index)));

                        break;
                    }
                }

                LOGGER.info(String.format("items after removing : %s ", item.getJsonArray("Nutanix VM").getJsonObject(0).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS)));

                TestAPIUtil.put(METRIC_API_ENDPOINT + "/" + nutanixId, new JsonObject().put(REQUEST_PARAMS, new JsonArray().add(item.getJsonArray("Nutanix VM").getJsonObject(0))), testContext.succeeding(httpResponse ->
                {
                    assertEquals(HttpStatus.SC_OK, httpResponse.statusCode());

                    assertNotNull(httpResponse.bodyAsJsonObject());

                    assertEquals(HttpStatus.SC_OK, httpResponse.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                    TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(5), id ->
                            TestAPIUtil.get(METRIC_API_ENDPOINT + "/" + nutanixId, testContext.succeeding(httpResult ->
                            {
                                assertEquals(HttpStatus.SC_OK, httpResult.statusCode());

                                var output = httpResult.bodyAsJsonObject();

                                assertNotNull(output);

                                assertEquals(HttpStatus.SC_OK, output.getInteger(RESPONSE_CODE));

                                output = JsonObject.mapFrom(output.getValue(RESULT));

                                assertTrue(output != null && !output.isEmpty());

                                LOGGER.info(String.format("after getting items : %s", output.getJsonArray("Nutanix VM").getJsonObject(0).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS)));

                                assertNotEquals(output.getJsonArray("Nutanix VM").getJsonObject(0).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS).size(), METRICS.getJsonObject(RESULT).getJsonArray("Nutanix VM").getJsonObject(0).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS).size(), "size is equal");

                                testContext.completeNow();

                            })));

                }));
            }
        }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testRediscoverHCIObject(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = prepareParams(testInfo.getTestMethod().get().getName()).getJsonObject(Scheduler.SCHEDULER_CONTEXT).put(NMSConstants.OBJECTS, new JsonArray().add(nutanixId));

        testRediscover(testContext, prepareParams(testInfo.getTestMethod().get().getName()).mergeIn(context), VMS.getJsonObject(0), false);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
        {
            try
            {
                var eventContext = new JsonObject();

                if (message.body().containsKey(EVENT_COMPRESSION_TYPE) && message.body().getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
                {
                    eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));
                }
                else
                {
                    eventContext = message.body().getJsonObject(EventBusConstants.EVENT_CONTEXT);
                }

                if (message.body().getString(EVENT_TYPE) != null && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_REDISCOVER_STOP))
                {
                    var item = MetricConfigStore.getStore().getItemByMetricPlugin(nutanixId, NMSConstants.MetricPlugin.NUTANIX_VM.getName());

                    var items = MetricConfigStore.getStore().getItem(item).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS);

                    Assertions.assertFalse(items.contains(VMS.getJsonObject(0)));

                    var params = new JsonArray();

                    params.add(new JsonObject().put(ID, eventContext.getLong(ID))
                            .put(NMSConstants.REDISCOVER_JOB, eventContext.getString(NMSConstants.REDISCOVER_JOB))
                            .put(SESSION_ID, TestUtil.getSessionId())
                            .put(NMSConstants.OBJECT, VMS.getJsonObject(0)).put(NMSConstants.OBJECTS, new JsonArray().add(VMS.getJsonObject(0)))
                            .put(EVENT_SCHEDULER, TestNMSUtil.getSchedulerId()));

                    TestAPIUtil.post(TestAPIConstants.OBJECT_PROVISION_API_ENDPOINT, new JsonObject().put(REQUEST_PARAMS, params), testContext.succeeding(response ->
                    {
                        Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                        var result = response.bodyAsJsonObject();

                        Assertions.assertEquals(HttpStatus.SC_OK, result.getInteger(RESPONSE_CODE));

                        Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                        Assertions.assertEquals(InfoMessageConstants.OBJECT_PROVISION_START_SUCCEEDED, result.getString(MESSAGE));

                        var retries = new AtomicInteger();

                        TestUtil.vertx().setPeriodic(2000, timer ->
                        {
                            retries.getAndIncrement();

                            var objects = MetricConfigStore.getStore().getItem(item).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS);

                            if (objects != null && objects.contains(VMS.getJsonObject(0)))
                            {
                                TestUtil.vertx().cancelTimer(timer);

                                messageConsumer.unregister(asyncResult -> testContext.completeNow());
                            }
                            else if (retries.get() == 10)

                            {
                                TestUtil.vertx().cancelTimer(timer);

                                messageConsumer.unregister(asyncResult -> testContext.failNow(new Exception("VM has not provisioned")));
                            }
                        });
                    }));
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testRediscoverHCIObjectHavingAutoProvision(VertxTestContext testContext, TestInfo testInfo)
    {
        testRediscover(testContext, prepareParams(testInfo.getTestMethod().get().getName()).put(NMSConstants.OBJECTS,
                ObjectConfigStore.getStore().getItemsByTypes(new JsonArray().add(NMSConstants.Type.NUTANIX.getName()))), new JsonObject(), false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testRediscoverHavingAutoProvisionStatusOnAndInvalidObjects(VertxTestContext testContext)
    {
        var items = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_PLUGIN, new JsonArray().add(NMSConstants.MetricPlugin.NUTANIX_VM.getName()));

        assertNotNull(items);

        assertFalse(items.isEmpty());

        var futures = new ArrayList<Future<Long>>();

        for (var index = 0; index < items.size(); index++)
        {
            var item = items.getJsonObject(index);

            item.getJsonObject(Metric.METRIC_CONTEXT).remove(NMSConstants.OBJECTS);

            var promise = Promise.<Long>promise();

            futures.add(promise.future());

            Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_METRIC,
                    new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                    item,
                    DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                    handler -> promise.complete(item.getLong(ID)));
        }

        var objects = ObjectConfigStore.getStore().getItemsByTypes(new JsonArray().add(NMSConstants.Type.NUTANIX.getName()));

        Future.join(futures).onComplete(result -> MetricConfigStore.getStore().updateItems(new JsonArray(result.result().list())).onComplete(asyncResult -> testRediscover(testContext, prepareParams("testRediscoverHCIObjectHavingAutoProvision").put(NMSConstants.OBJECTS, objects), new JsonObject(), false)));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testCheckAutoProvisionOfRediscoveredMetrics(VertxTestContext testContext)
    {
        var items = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_PLUGIN, new JsonArray().add(NMSConstants.MetricPlugin.NUTANIX_VM.getName()));

        Assertions.assertNotNull(items);

        assertFalse(items.isEmpty());

        var valid = false;

        for (var index = 0; index < items.size(); index++)
        {
            if (items.getJsonObject(index).getJsonObject(Metric.METRIC_CONTEXT) != null && items.getJsonObject(index).getJsonObject(Metric.METRIC_CONTEXT).containsKey(NMSConstants.OBJECTS)
                    && !items.getJsonObject(index).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS).isEmpty())
            {
                valid = true;

                break;
            }
        }

        if (valid)
        {
            testContext.completeNow();
        }
    }
}
