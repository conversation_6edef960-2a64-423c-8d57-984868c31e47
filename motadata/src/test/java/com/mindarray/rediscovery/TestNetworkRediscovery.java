/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.rediscovery;

import com.mindarray.*;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Metric;
import com.mindarray.api.Scheduler;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.SchedulerConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.CronExpressionUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.METRIC_API_ENDPOINT;
import static com.mindarray.TestAPIConstants.SCHEDULER_API_ENDPOINT;
import static com.mindarray.TestConstants.prepareParams;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.Scheduler.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.*;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(VertxExtension.class)
@Timeout(180 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")
public class TestNetworkRediscovery
{

    public static final JsonObject PARAMETERS = new JsonObject();
    private static final String SCHEDULER_ENTITY_NAME = "Scheduler";
    private static final String PATTERN = "dd-MM-yyyy";
    private static final Map<String, Long> CONTEXTS = new HashMap<>();
    private static final JsonObject METRICS = new JsonObject();
    private static final JsonArray INTERFACES = new JsonArray();
    private static final Logger LOGGER = new Logger(TestNetworkRediscovery.class, MOTADATA_NMS, "Test Network Rediscovery");
    public static MessageConsumer<JsonObject> messageConsumer = null;
    private static long networkId = 0L;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "rediscover-parameters.json");

            Assertions.assertTrue(file.exists());

            PARAMETERS.mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));

            Assertions.assertFalse(PARAMETERS.isEmpty());

            networkId = ObjectConfigStore.getStore().getItemByIP(PARAMETERS.getJsonObject(testInfo.getDisplayName()).getString("network"));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext) throws InterruptedException
    {
        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        testContext.completeNow();
    }

    @AfterEach
    void afterEach(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetNetworkInterface(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.get(METRIC_API_ENDPOINT + "/" + networkId, testContext.succeeding(response ->
        {
            assertEquals(SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            var result = JsonObject.mapFrom(body.getValue(RESULT));

            assertTrue(result != null && !result.isEmpty());

            METRICS.put(RESULT, result);

            var metric = JsonObject.mapFrom(body.getValue(RESULT));

            var items = metric.getJsonArray(NMSConstants.RediscoverJob.NETWORK_INTERFACE.getName()).getJsonObject(0).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS);

            for (var index = 0; index < items.size(); index++)
            {
                if (items.getJsonObject(index).getString(STATUS).equals(STATUS_UP))
                {
                    INTERFACES.add(JsonObject.mapFrom(metric.getJsonArray(NMSConstants.RediscoverJob.NETWORK_INTERFACE.getName()).getJsonObject(0).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS).remove(index)));

                    break;
                }
            }

            TestAPIUtil.put(METRIC_API_ENDPOINT + "/" + networkId, new JsonObject().put(REQUEST_PARAMS, new JsonArray().add(metric.getJsonArray(NMSConstants.RediscoverJob.NETWORK_INTERFACE.getName()).getJsonObject(0))), testContext.succeeding(urlResult ->
            {
                assertEquals(SC_OK, urlResult.statusCode());

                Assertions.assertNotNull(urlResult.bodyAsJsonObject());

                assertEquals(SC_OK, urlResult.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                TestAPIUtil.get(METRIC_API_ENDPOINT + "/" + networkId, testContext.succeeding(httpResponse ->
                {
                    assertEquals(SC_OK, httpResponse.statusCode());

                    var output = httpResponse.bodyAsJsonObject();

                    Assertions.assertNotNull(output);

                    assertEquals(SC_OK, output.getInteger(RESPONSE_CODE));

                    output = JsonObject.mapFrom(output.getValue(RESULT));

                    assertTrue(output != null && !output.isEmpty());

                    testContext.completeNow();

                }));

            }));

        }));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testRediscoverNetworkInterface(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        var jsonObject = prepareParams(testInfo.getTestMethod().get().getName()).getJsonObject(Scheduler.SCHEDULER_CONTEXT).put(NMSConstants.OBJECTS, new JsonArray().add(networkId));

        TestNMSUtil.testRediscover(testContext, prepareParams(testInfo.getTestMethod().get().getName()).mergeIn(jsonObject), INTERFACES.getJsonObject(0), false);

        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_UI, message ->
        {
            try
            {
                var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                if (message.body().getString(EVENT_TYPE) != null && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_REDISCOVER_STOP))
                {
                    var params = new JsonArray();

                    params.add(new JsonObject().put(ID, eventContext.getLong(ID))
                            .put(NMSConstants.REDISCOVER_JOB, eventContext.getString(NMSConstants.REDISCOVER_JOB))
                            .put(SESSION_ID, TestUtil.getSessionId())
                            .put(NMSConstants.OBJECT, INTERFACES.getJsonObject(0)).put(NMSConstants.OBJECTS, new JsonArray().add(INTERFACES.getJsonObject(0)))
                            .put(EVENT_SCHEDULER, TestNMSUtil.getSchedulerId()));

                    TestAPIUtil.post(TestAPIConstants.OBJECT_PROVISION_API_ENDPOINT, new JsonObject().put(REQUEST_PARAMS, params), testContext.succeeding(response ->
                    {
                        Assertions.assertEquals(SC_OK, response.statusCode());

                        var result = response.bodyAsJsonObject();

                        Assertions.assertEquals(SC_OK, result.getInteger(RESPONSE_CODE));

                        Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                        Assertions.assertEquals(InfoMessageConstants.OBJECT_PROVISION_START_SUCCEEDED, result.getString(MESSAGE));

                        messageConsumer.unregister();

                        testContext.completeNow();
                    }));
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testValidateDeviceMetricsAfterRediscoverNetworkInterface(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        TestAPIUtil.get(METRIC_API_ENDPOINT + "/" + networkId, testContext.succeeding(response ->
        {
            assertEquals(SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            body = JsonObject.mapFrom(body.getValue(RESULT));

            assertTrue(body != null && !body.isEmpty());

            testContext.completeNow();
        }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testRediscoverNetworkInterfaceAutoProvision(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        var objects = ObjectConfigStore.getStore().getItemsByTypes(new JsonArray().add(NMSConstants.Type.SWITCH.getName()).add(NMSConstants.Type.FIREWALL.getName()));

        TestNMSUtil.testRediscover(testContext, prepareParams(testInfo.getTestMethod().get().getName()).put(OBJECTS, objects), new JsonObject().put(AUTO_PROVISION_STATUS, YES), false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testRediscoverNetworkInterfaceWithoutAutoProvision(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        var id = ObjectConfigStore.getStore().getItemByIP("***********");

        var metrics = MetricConfigStore.getStore().getItemsByObjectId(id);

        var metricId = 0L;

        for (var index = 0; index < metrics.size(); index++)
        {
            if (metrics.getJsonObject(index).getString(Metric.METRIC_NAME).equalsIgnoreCase("Network Interface"))
            {
                metricId = metrics.getJsonObject(index).getLong(ID);
            }
        }

        var existingObjects = MetricConfigStore.getStore().getObjects(metricId);

        LOGGER.debug("Existing objects : " + existingObjects);

        var param = new JsonObject("{\"id\":32140998272556,\"rediscover.job\":\"Network Interface\",\"event.scheduler\":32140998272576,\"metric.name\":\"NetworkInterface\",\"object.name\":\"HB1.hb1.com\"}").put(ID, metricId).put(AIOpsObject.OBJECT_NAME, "***********");

        param.put(OBJECT, new JsonObject(String.valueOf(existingObjects.get("1"))).put("interface.alias", "New-Name"));

        var objectName = param.getJsonObject(OBJECT).getString(AIOpsObject.OBJECT_NAME);

        TestAPIUtil.post(TestAPIConstants.OBJECT_PROVISION_API_ENDPOINT, new JsonObject().put(REQUEST_PARAMS, new JsonArray().add(param)), testContext.succeeding(response ->
        {
            Assertions.assertEquals(SC_OK, response.statusCode());

            var result = response.bodyAsJsonObject();

            LOGGER.debug(String.format("Received response %s for %s", result, testInfo.getTestMethod().get().getName()));

            Assertions.assertEquals(SC_OK, result.getInteger(RESPONSE_CODE));

            Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

            Assertions.assertEquals(InfoMessageConstants.OBJECT_PROVISION_START_SUCCEEDED, result.getString(MESSAGE));

        }));

        var metric = metricId;

        TestUtil.vertx().setTimer(5000, handler ->
        {

            var objects = MetricConfigStore.getStore().getObjects(metric);

            Assertions.assertNotEquals(existingObjects.get(objectName).getString("interface.alias"), objects.get(objectName).getString("interface.alias"));

            Assertions.assertEquals("New-Name", objects.get(objectName).getString("interface.alias"));

            testContext.completeNow();

        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testRerunRediscoverNetworkInterfaceAutoProvision(VertxTestContext testContext)
    {
        var sent = new AtomicBoolean(false);

        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {

                if (message.body().getString(EVENT_TYPE) != null && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_REDISCOVER_ERROR))
                {
                    sent.set(true);

                    assertEquals(ErrorMessageConstants.REDISCOVERY_RUN_FAILED_ALREADY_RUNNING,
                            CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT)).getString(MESSAGE));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }

                if (!sent.get())
                {
                    Bootstrap.vertx().eventBus().send(EventBusConstants.UI_ACTION_SCHEDULER_RUN, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER).put(ID, TestNMSUtil.getSchedulerId()));
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });

        Bootstrap.vertx().eventBus().send(EventBusConstants.UI_ACTION_SCHEDULER_RUN, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER).put(ID, TestNMSUtil.getSchedulerId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testAbortRediscoverNetworkInterface(VertxTestContext testContext)
    {
        var context = prepareParams("testRediscoverNetworkInterface").put(NMSConstants.OBJECTS, new JsonArray().add(networkId));

        context.getJsonObject(Scheduler.SCHEDULER_CONTEXT).put(NMSConstants.OBJECTS, new JsonArray().add(networkId));

        TestNMSUtil.testRediscover(testContext, context, new JsonObject(), true);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testCreateDiscoverySchedulerMonthly(VertxTestContext testContext)
    {
        var scheduler = new JsonObject().put(SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date())).put(SCHEDULER_DATES, new JsonArray().add("1")).put(SCHEDULER_MONTHS, new JsonArray().add("january")).put(SCHEDULER_TIMES, new JsonArray().add("00:05")).put(SCHEDULER_TIMELINE, CronExpressionUtil.CRON_MONTHLY).put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.DISCOVERY.getName()).put(SCHEDULER_CONTEXT, new JsonObject().put(AUTO_PROVISION_STATUS, NO).put(OBJECTS, new JsonArray().add(CommonUtil.getLong(CONTEXTS.get("discovery")))));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                                {
                                    TestAPIUtil.assertMultiCreateEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(), SCHEDULER_ENTITY_NAME);

                                    CONTEXTS.put("scheduler-monthly", response.bodyAsJsonObject().getJsonArray(ID).getLong(0));

                                    testContext.completeNow();
                                }))));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testCreateDiscoverySchedulerDaily(VertxTestContext testContext)
    {
        var scheduler = new JsonObject().put(SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date())).put(SCHEDULER_TIMES, new JsonArray().add("00:05").add("00:10")).put(SCHEDULER_TIMELINE, CronExpressionUtil.CRON_DAILY).put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.DISCOVERY.getName()).put(SCHEDULER_CONTEXT, new JsonObject().put(AUTO_PROVISION_STATUS, NO).put(OBJECTS, new JsonArray().add(CommonUtil.getLong(CONTEXTS.get("discovery")))));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                                {
                                    TestAPIUtil.assertMultiCreateEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(), SCHEDULER_ENTITY_NAME);

                                    CONTEXTS.put("scheduler-daily", response.bodyAsJsonObject().getJsonArray(ID).getLong(0));

                                    testContext.completeNow();
                                }))));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testGetDiscoverySchedulerDaily(VertxTestContext testContext)
    {
        var scheduler = new JsonObject().put(SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date())).put(SCHEDULER_TIMES, new JsonArray().add("00:05").add("00:10")).put(SCHEDULER_TIMELINE, CronExpressionUtil.CRON_DAILY).put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.DISCOVERY.getName()).put(SCHEDULER_CONTEXT, new JsonObject().put(AUTO_PROVISION_STATUS, NO).put(OBJECTS, new JsonArray().add(CommonUtil.getLong(CONTEXTS.get("discovery")))));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                                {
                                    TestAPIUtil.assertMultiCreateEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(), SCHEDULER_ENTITY_NAME);

                                    CONTEXTS.put("scheduler-daily", response.bodyAsJsonObject().getJsonArray(ID).getLong(0));

                                    testContext.completeNow();
                                }))));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testGetScheduler(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(SCHEDULER_API_ENDPOINT + "/" + CONTEXTS.get("scheduler-daily") + "?filter=" + new JsonObject()
                        .put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.DISCOVERY.getName())
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var result = response.bodyAsJsonObject();

                            Assertions.assertNotNull(result);

                            Assertions.assertFalse(result.isEmpty());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testGetRediscoverScheduler(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(SCHEDULER_API_ENDPOINT + "?filter=" + new JsonObject()
                        .put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.REDISCOVER.getName()).put(NMSConstants.REDISCOVER_JOB, NMSConstants.RediscoverJob.APP.getName())
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var items = response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT);

                            Assertions.assertNotNull(items);

                            Assertions.assertFalse(items.isEmpty());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testCreateDiscoveryScheduler(VertxTestContext testContext)
    {
        var scheduler = new JsonObject().put(SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date())).put(SCHEDULER_TIMES, new JsonArray().add("00:05")).put(SCHEDULER_TIMELINE, CronExpressionUtil.CRON_ONCE).put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.DISCOVERY.getName()).put(SCHEDULER_CONTEXT, new JsonObject().put(AUTO_PROVISION_STATUS, NO).put(OBJECTS, new JsonArray().add(CommonUtil.getLong(CONTEXTS.get("discovery")))));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                                {
                                    TestAPIUtil.assertMultiCreateEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(), SCHEDULER_ENTITY_NAME);

                                    CONTEXTS.put("scheduler", response.bodyAsJsonObject().getJsonArray(ID).getLong(0));

                                    testContext.completeNow();
                                }))));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testGetAllDiscoveryScheduler(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(SCHEDULER_API_ENDPOINT + "?filter=" + new JsonObject().put(ID, CommonUtil.getLong(CONTEXTS.get("discovery"))).put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.DISCOVERY.getName())
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var items = response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT);

                            assertTrue(items != null && !items.isEmpty());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testUpdateDiscoveryScheduler(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = new JsonObject().put(SCHEDULER_TIMELINE, CronExpressionUtil.CRON_MONTHLY).put(SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date())).put(SCHEDULER_TIMES, new JsonArray().add("00:05")).put(SCHEDULER_EMAIL_RECIPIENTS, new JsonArray()).put(SCHEDULER_SMS_RECIPIENTS, new JsonArray()).put(SCHEDULER_DATES, new JsonArray().add("1")).put(SCHEDULER_MONTHS, new JsonArray().add("January")).put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.DISCOVERY.getName()).put(SCHEDULER_CONTEXT, new JsonObject().put(AUTO_PROVISION_STATUS, NO).put(OBJECTS, new JsonArray().add(CommonUtil.getLong(CONTEXTS.get("discovery")))));

        TestAPIUtil.put(SCHEDULER_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXTS.get("scheduler")), item,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(SchedulerConfigStore.getStore(), item, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, SCHEDULER_ENTITY_NAME), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testUpdateDiscoverySchedulerState(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = new JsonObject().put(SCHEDULER_STATE, NO).put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.DISCOVERY.getName());

        TestAPIUtil.put(SCHEDULER_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXTS.get("scheduler")) + "/state", item,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            testContext.awaitCompletion(2, TimeUnit.SECONDS);

                            TestAPIUtil.assertUpdateEntityTestResult(SchedulerConfigStore.getStore(), item, response.bodyAsJsonObject(),
                                    SCHEDULER_ENTITY_NAME + " Disabled successfully...", LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testDeleteDiscoveryScheduler(VertxTestContext testContext)
    {
        TestAPIUtil.delete(SCHEDULER_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXTS.get("scheduler")) + "?" + SCHEDULER_JOB_TYPE + "=" + JobScheduler.JobType.DISCOVERY.getName(),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertDeleteEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_DELETED, SCHEDULER_ENTITY_NAME));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testConfigureInterfaceSpeed(VertxTestContext testContext)
    {
        var context = new JsonObject("{\"_type\":\"0\",\"auto.provision.status\":\"no\",\"credential.profile.name\":\"Default SNMP\",\"credential.profile.protocol\":\"SNMP V1/V2c\",\"discover.down.interface.status\":\"no\",\"discovered.objects\":[{\"interface\":\"Gi0/0-1\",\"interface.address\":\"4C:E1:76:40:B4:80\",\"interface.alias\":\"\",\"interface.bit.type\":\"0\",\"interface.description\":\"GigabitEthernet0/0\",\"interface.index\":\"1\",\"interface.name\":\"Gi0/0\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"1\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/1/1-32\",\"interface.address\":\"4C:E1:76:40:B4:99\",\"interface.alias\":\"\",\"interface.bit.type\":\"0\",\"interface.description\":\"GigabitEthernet1/1/1\",\"interface.index\":\"32\",\"interface.name\":\"Gi1/1/1\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"32\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/1/2-33\",\"interface.address\":\"4C:E1:76:40:B4:9A\",\"interface.alias\":\"\",\"interface.bit.type\":\"0\",\"interface.description\":\"GigabitEthernet1/1/2\",\"interface.index\":\"33\",\"interface.name\":\"Gi1/1/2\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"33\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/1/3-34\",\"interface.address\":\"4C:E1:76:40:B4:9B\",\"interface.alias\":\"\",\"interface.bit.type\":\"0\",\"interface.description\":\"GigabitEthernet1/1/3\",\"interface.index\":\"34\",\"interface.name\":\"Gi1/1/3\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"34\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/1/4-35\",\"interface.address\":\"4C:E1:76:40:B4:9C\",\"interface.alias\":\"\",\"interface.bit.type\":\"0\",\"interface.description\":\"GigabitEthernet1/1/4\",\"interface.index\":\"35\",\"interface.name\":\"Gi1/1/4\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"35\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Fo1/1/1-44\",\"interface.address\":\"4C:E1:76:40:B4:A5\",\"interface.alias\":\"Test12345\",\"interface.bit.type\":\"0\",\"interface.description\":\"FortyGigabitEthernet1/1/1\",\"interface.index\":\"44\",\"interface.name\":\"Fo1/1/1\",\"interface.speed.bytes.per.sec\":536870911,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"44\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Fo1/1/2-45\",\"interface.address\":\"4C:E1:76:40:B4:A6\",\"interface.alias\":\"\",\"interface.bit.type\":\"0\",\"interface.description\":\"FortyGigabitEthernet1/1/2\",\"interface.index\":\"45\",\"interface.name\":\"Fo1/1/2\",\"interface.speed.bytes.per.sec\":536870911,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"45\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Twe1/1/1-46\",\"interface.address\":\"4C:E1:76:40:B4:A7\",\"interface.alias\":\"\",\"interface.bit.type\":\"0\",\"interface.description\":\"TwentyFiveGigE1/1/1\",\"interface.index\":\"46\",\"interface.name\":\"Twe1/1/1\",\"interface.speed.bytes.per.sec\":536870911,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"46\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Twe1/1/2-47\",\"interface.address\":\"4C:E1:76:40:B4:A8\",\"interface.alias\":\"\",\"interface.bit.type\":\"0\",\"interface.description\":\"TwentyFiveGigE1/1/2\",\"interface.index\":\"47\",\"interface.name\":\"Twe1/1/2\",\"interface.speed.bytes.per.sec\":536870911,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"47\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/1-8\",\"interface.address\":\"4C:E1:76:40:B4:81\",\"interface.alias\":\"L3_To_Firewall_Port1_DMZ\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/1\",\"interface.index\":\"8\",\"interface.name\":\"Gi1/0/1\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"8\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Gi1/0/3-10\",\"interface.address\":\"4C:E1:76:40:B4:83\",\"interface.alias\":\"123453\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/3\",\"interface.index\":\"10\",\"interface.name\":\"Gi1/0/3\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"10\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/4-11\",\"interface.address\":\"4C:E1:76:40:B4:84\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/4\",\"interface.index\":\"11\",\"interface.name\":\"Gi1/0/4\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"11\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/6-13\",\"interface.address\":\"4C:E1:76:40:B4:86\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/6\",\"interface.index\":\"13\",\"interface.name\":\"Gi1/0/6\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"13\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/7-14\",\"interface.address\":\"4C:E1:76:40:B4:87\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/7\",\"interface.index\":\"14\",\"interface.name\":\"Gi1/0/7\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"14\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/10-17\",\"interface.address\":\"4C:E1:76:40:B4:8A\",\"interface.alias\":\"L3_To_Firewall_Port5_ScalabilitySNMP\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/10\",\"interface.index\":\"17\",\"interface.name\":\"Gi1/0/10\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"17\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/11-18\",\"interface.address\":\"4C:E1:76:40:B4:8B\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/11\",\"interface.index\":\"18\",\"interface.name\":\"Gi1/0/11\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"18\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/13-20\",\"interface.address\":\"4C:E1:76:40:B4:8D\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/13\",\"interface.index\":\"20\",\"interface.name\":\"Gi1/0/13\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"20\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/15-22\",\"interface.address\":\"4C:E1:76:40:B4:8F\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/15\",\"interface.index\":\"22\",\"interface.name\":\"Gi1/0/15\",\"interface.speed.bytes.per.sec\":1310750000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"22\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/16-23\",\"interface.address\":\"4C:E1:76:40:B4:90\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/16\",\"interface.index\":\"23\",\"interface.name\":\"Gi1/0/16\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"23\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/19-26\",\"interface.address\":\"4C:E1:76:40:B4:93\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/19\",\"interface.index\":\"26\",\"interface.name\":\"Gi1/0/19\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"26\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/23-30\",\"interface.address\":\"4C:E1:76:40:B4:97\",\"interface.alias\":\"D-link_172.16.10.48\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/23\",\"interface.index\":\"30\",\"interface.name\":\"Gi1/0/23\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"30\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Te1/1/1-36\",\"interface.address\":\"4C:E1:76:40:B4:9D\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"TenGigabitEthernet1/1/1\",\"interface.index\":\"36\",\"interface.name\":\"Te1/1/1\",\"interface.speed.bytes.per.sec\":1250000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"36\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Te1/1/2-37\",\"interface.address\":\"4C:E1:76:40:B4:9E\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"TenGigabitEthernet1/1/2\",\"interface.index\":\"37\",\"interface.name\":\"Te1/1/2\",\"interface.speed.bytes.per.sec\":1250000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"37\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Te1/1/3-38\",\"interface.address\":\"4C:E1:76:40:B4:9F\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"TenGigabitEthernet1/1/3\",\"interface.index\":\"38\",\"interface.name\":\"Te1/1/3\",\"interface.speed.bytes.per.sec\":1250000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"38\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Te1/1/4-39\",\"interface.address\":\"4C:E1:76:40:B4:A0\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"TenGigabitEthernet1/1/4\",\"interface.index\":\"39\",\"interface.name\":\"Te1/1/4\",\"interface.speed.bytes.per.sec\":1250000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"39\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Te1/1/6-41\",\"interface.address\":\"4C:E1:76:40:B4:A2\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"TenGigabitEthernet1/1/6\",\"interface.index\":\"41\",\"interface.name\":\"Te1/1/6\",\"interface.speed.bytes.per.sec\":1250000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"41\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Te1/1/7-42\",\"interface.address\":\"4C:E1:76:40:B4:A3\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"TenGigabitEthernet1/1/7\",\"interface.index\":\"42\",\"interface.name\":\"Te1/1/7\",\"interface.speed.bytes.per.sec\":1250000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"42\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Te1/1/8-43\",\"interface.address\":\"4C:E1:76:40:B4:A4\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"TenGigabitEthernet1/1/8\",\"interface.index\":\"43\",\"interface.name\":\"Te1/1/8\",\"interface.speed.bytes.per.sec\":1250000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"43\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Vl1-51\",\"interface.address\":\"4C:E1:76:40:B4:C7\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"Vlan1\",\"interface.index\":\"51\",\"interface.name\":\"Vl1\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"propVirtual (53)\",\"object.name\":\"51\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Tu20-55\",\"interface.address\":\"\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"Tunnel20\",\"interface.index\":\"55\",\"interface.name\":\"Tu20\",\"interface.speed.bytes.per.sec\":0,\"interface.type\":\"tunnel (131)\",\"object.name\":\"55\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Vl30-57\",\"interface.address\":\"4C:E1:76:40:B4:E5\",\"interface.alias\":\"2nd_Flr\",\"interface.bit.type\":\"1\",\"interface.description\":\"Vlan30\",\"interface.index\":\"57\",\"interface.ip.address\":\"**********\",\"interface.link.type\":\"LAN\",\"interface.name\":\"Vl30\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"propVirtual (53)\",\"object.name\":\"57\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Vl50-59\",\"interface.address\":\"4C:E1:76:40:B4:E8\",\"interface.alias\":\"LAB\",\"interface.bit.type\":\"1\",\"interface.description\":\"Vlan50\",\"interface.index\":\"59\",\"interface.name\":\"Vl50\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"propVirtual (53)\",\"object.name\":\"59\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Vl100-60\",\"interface.address\":\"4C:E1:76:40:B4:D1\",\"interface.alias\":\"Mgmt\",\"interface.bit.type\":\"1\",\"interface.description\":\"Vlan100\",\"interface.index\":\"60\",\"interface.name\":\"Vl100\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"propVirtual (53)\",\"object.name\":\"60\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Vl300-62\",\"interface.address\":\"4C:E1:76:40:B4:C7\",\"interface.alias\":\"Wi-Fi\",\"interface.bit.type\":\"1\",\"interface.description\":\"Vlan300\",\"interface.index\":\"62\",\"interface.name\":\"Vl300\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"propVirtual (53)\",\"object.name\":\"62\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Tu10-71\",\"interface.address\":\"\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"Tunnel10\",\"interface.index\":\"71\",\"interface.name\":\"Tu10\",\"interface.speed.bytes.per.sec\":0,\"interface.type\":\"tunnel (131)\",\"object.name\":\"71\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Vl200-61\",\"interface.address\":\"4C:E1:76:40:B4:E2\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"Vlan200\",\"interface.index\":\"61\",\"interface.name\":\"Vl200\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"propVirtual (53)\",\"interface~speed.bytes.per.sec\":1222,\"object.name\":\"61\",\"object.type\":\"interface\",\"status\":\"Up\"}],\"event.id\":***********,\"event.scheduler\":***********,\"event.topic\":\"remote.event.processor \",\"id\":***********,\"interface.discovery\":\"yes\",\"metric.category\":\"Network\",\"metric.credential.profile\":10000000000001,\"metric.credential.profile.protocol\":\"SNMP V1/V2c\",\"metric.discovery.method\":\"REMOTE\",\"metric.instances\":[],\"metric.name\":\"Network Interface\",\"metric.object\":***********,\"metric.plugin\":\"snmpinterface\",\"metric.polling.min.time\":300,\"metric.type\":\"Switch\",\"object.business.hour.profile\":10000000000001,\"object.category\":\"Network\",\"object.creation.time\":\"2024/08/09 16:57:46\",\"object.creation.time.seconds\":1723202866,\"object.discovery.method\":\"REMOTE\",\"object.event.processors\":[],\"object.groups\":[10000000000004,10000000000002,10000000000003],\"object.host\":\"cisco_core.motadata.local\",\"object.id\":3,\"object.ip\":\"**********\",\"object.make.model\":\"Cisco Catalyst 93xx Switch Stack\",\"object.name\":\"cisco_core.motadata.local\",\"object.snmp.device.catalog\":10000000011869,\"object.state\":\"ENABLE\",\"object.system.oid\":\".*******.*******.2494\",\"object.target\":\"**********\",\"object.type\":\"Switch\",\"object.user.tags\":[],\"object.vendor\":\"Cisco Systems\",\"ping.check.status\":\"yes\",\"plugin.engine\":\"go\",\"plugin.id\":201,\"port\":161,\"rediscover.job\":\"Network Interface\",\"remote.address\":\"0:0:0:0:0:0:0:1\",\"remote.event.processor.uuid\":\"fe\",\"session-id\":\"1817acd9-850b-451a-b676-3446269f9259\",\"snmp.check.retries\":2,\"snmp.community\":\"public\",\"snmp.version\":\"v2c\",\"status\":\"succeed\",\"timeout\":120,\"topology.plugin.discovery\":\"no\",\"ui.event.uuid\":\"568e5970-e407-4bbf-a048-c3ce27d1ee9e\",\"user.name\":\"admin\",\"latency.ms\":9172.0,\"result\":{\"objects\":[{\"interface\":\"Gi0/0-1\",\"interface.address\":\"4C:E1:76:40:B4:80\",\"interface.alias\":\"\",\"interface.bit.type\":\"0\",\"interface.description\":\"GigabitEthernet0/0\",\"interface.index\":\"1\",\"interface.name\":\"Gi0/0\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"1\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/1/1-32\",\"interface.address\":\"4C:E1:76:40:B4:99\",\"interface.alias\":\"\",\"interface.bit.type\":\"0\",\"interface.description\":\"GigabitEthernet1/1/1\",\"interface.index\":\"32\",\"interface.name\":\"Gi1/1/1\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"32\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/1/2-33\",\"interface.address\":\"4C:E1:76:40:B4:9A\",\"interface.alias\":\"\",\"interface.bit.type\":\"0\",\"interface.description\":\"GigabitEthernet1/1/2\",\"interface.index\":\"33\",\"interface.name\":\"Gi1/1/2\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"33\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/1/3-34\",\"interface.address\":\"4C:E1:76:40:B4:9B\",\"interface.alias\":\"\",\"interface.bit.type\":\"0\",\"interface.description\":\"GigabitEthernet1/1/3\",\"interface.index\":\"34\",\"interface.name\":\"Gi1/1/3\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"34\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/1/4-35\",\"interface.address\":\"4C:E1:76:40:B4:9C\",\"interface.alias\":\"\",\"interface.bit.type\":\"0\",\"interface.description\":\"GigabitEthernet1/1/4\",\"interface.index\":\"35\",\"interface.name\":\"Gi1/1/4\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"35\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Fo1/1/1-44\",\"interface.address\":\"4C:E1:76:40:B4:A5\",\"interface.alias\":\"Test12345\",\"interface.bit.type\":\"0\",\"interface.description\":\"FortyGigabitEthernet1/1/1\",\"interface.index\":\"44\",\"interface.name\":\"Fo1/1/1\",\"interface.speed.bytes.per.sec\":536870911,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"44\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Fo1/1/2-45\",\"interface.address\":\"4C:E1:76:40:B4:A6\",\"interface.alias\":\"\",\"interface.bit.type\":\"0\",\"interface.description\":\"FortyGigabitEthernet1/1/2\",\"interface.index\":\"45\",\"interface.name\":\"Fo1/1/2\",\"interface.speed.bytes.per.sec\":536870911,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"45\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Twe1/1/1-46\",\"interface.address\":\"4C:E1:76:40:B4:A7\",\"interface.alias\":\"\",\"interface.bit.type\":\"0\",\"interface.description\":\"TwentyFiveGigE1/1/1\",\"interface.index\":\"46\",\"interface.name\":\"Twe1/1/1\",\"interface.speed.bytes.per.sec\":536870911,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"46\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Twe1/1/2-47\",\"interface.address\":\"4C:E1:76:40:B4:A8\",\"interface.alias\":\"\",\"interface.bit.type\":\"0\",\"interface.description\":\"TwentyFiveGigE1/1/2\",\"interface.index\":\"47\",\"interface.name\":\"Twe1/1/2\",\"interface.speed.bytes.per.sec\":536870911,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"47\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"StackPort1-48\",\"interface.address\":\"\",\"interface.alias\":\"\",\"interface.bit.type\":\"0\",\"interface.description\":\"StackPort1\",\"interface.index\":\"48\",\"interface.name\":\"StackPort1\",\"interface.speed.bytes.per.sec\":536870911,\"interface.type\":\"propVirtual (53)\",\"object.name\":\"48\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Nu0-2\",\"interface.address\":\"\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"Null0\",\"interface.index\":\"2\",\"interface.name\":\"Nu0\",\"interface.speed.bytes.per.sec\":1250000000,\"interface.type\":\"other (1)\",\"object.name\":\"2\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Gi1/0/1-8\",\"interface.address\":\"4C:E1:76:40:B4:81\",\"interface.alias\":\"L3_To_Firewall_Port1_DMZ\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/1\",\"interface.index\":\"8\",\"interface.name\":\"Gi1/0/1\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"8\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Gi1/0/2-9\",\"interface.address\":\"4C:E1:76:40:B4:82\",\"interface.alias\":\"L3_To_Firewall_Port2_LAN\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/2\",\"interface.index\":\"9\",\"interface.name\":\"Gi1/0/2\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"9\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Gi1/0/3-10\",\"interface.address\":\"4C:E1:76:40:B4:83\",\"interface.alias\":\"123453\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/3\",\"interface.index\":\"10\",\"interface.name\":\"Gi1/0/3\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"10\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/4-11\",\"interface.address\":\"4C:E1:76:40:B4:84\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/4\",\"interface.index\":\"11\",\"interface.name\":\"Gi1/0/4\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"11\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/5-12\",\"interface.address\":\"4C:E1:76:40:B4:85\",\"interface.alias\":\"L3_To_Firewall_Port3_wifi\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/5\",\"interface.index\":\"12\",\"interface.name\":\"Gi1/0/5\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"12\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Gi1/0/6-13\",\"interface.address\":\"4C:E1:76:40:B4:86\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/6\",\"interface.index\":\"13\",\"interface.name\":\"Gi1/0/6\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"13\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/7-14\",\"interface.address\":\"4C:E1:76:40:B4:87\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/7\",\"interface.index\":\"14\",\"interface.name\":\"Gi1/0/7\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"14\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/8-15\",\"interface.address\":\"4C:E1:76:40:B4:88\",\"interface.alias\":\"Netgear_Switch_g23_10.20.40.10\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/8\",\"interface.index\":\"15\",\"interface.name\":\"Gi1/0/8\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"15\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Gi1/0/9-16\",\"interface.address\":\"4C:E1:76:40:B4:89\",\"interface.alias\":\"Huawei_Switch_172.16.12.5\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/9\",\"interface.index\":\"16\",\"interface.name\":\"Gi1/0/9\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"16\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Gi1/0/10-17\",\"interface.address\":\"4C:E1:76:40:B4:8A\",\"interface.alias\":\"L3_To_Firewall_Port5_ScalabilitySNMP\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/10\",\"interface.index\":\"17\",\"interface.name\":\"Gi1/0/10\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"17\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/11-18\",\"interface.address\":\"4C:E1:76:40:B4:8B\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/11\",\"interface.index\":\"18\",\"interface.name\":\"Gi1/0/11\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"18\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/12-19\",\"interface.address\":\"4C:E1:76:40:B4:8C\",\"interface.alias\":\"cisco_sg350x_8.5\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/12\",\"interface.index\":\"19\",\"interface.name\":\"Gi1/0/12\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"19\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Gi1/0/13-20\",\"interface.address\":\"4C:E1:76:40:B4:8D\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/13\",\"interface.index\":\"20\",\"interface.name\":\"Gi1/0/13\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"20\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/14-21\",\"interface.address\":\"4C:E1:76:40:B4:8E\",\"interface.alias\":\"L3_To_Firewall_Port4_QA_8.O\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/14\",\"interface.index\":\"21\",\"interface.name\":\"Gi1/0/14\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"21\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Gi1/0/15-22\",\"interface.address\":\"4C:E1:76:40:B4:8F\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/15\",\"interface.index\":\"22\",\"interface.name\":\"Gi1/0/15\",\"interface.speed.bytes.per.sec\":1310750000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"22\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/16-23\",\"interface.address\":\"4C:E1:76:40:B4:90\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/16\",\"interface.index\":\"23\",\"interface.name\":\"Gi1/0/16\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"23\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/17-24\",\"interface.address\":\"4C:E1:76:40:B4:91\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/17\",\"interface.index\":\"24\",\"interface.name\":\"Gi1/0/17\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"24\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Gi1/0/18-25\",\"interface.address\":\"4C:E1:76:40:B4:92\",\"interface.alias\":\"CiscoSwitch350X_8.4\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/18\",\"interface.index\":\"25\",\"interface.name\":\"Gi1/0/18\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"25\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Gi1/0/19-26\",\"interface.address\":\"4C:E1:76:40:B4:93\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/19\",\"interface.index\":\"26\",\"interface.name\":\"Gi1/0/19\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"26\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/20-27\",\"interface.address\":\"4C:E1:76:40:B4:94\",\"interface.alias\":\"CiscoSwitch350_8.3\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/20\",\"interface.index\":\"27\",\"interface.name\":\"Gi1/0/20\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"27\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Gi1/0/21-28\",\"interface.address\":\"4C:E1:76:40:B4:95\",\"interface.alias\":\"HPE_Switch_10.47\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/21\",\"interface.index\":\"28\",\"interface.name\":\"Gi1/0/21\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"28\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Gi1/0/22-29\",\"interface.address\":\"4C:E1:76:40:B4:96\",\"interface.alias\":\"cisco2960_10.43\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/22\",\"interface.index\":\"29\",\"interface.name\":\"Gi1/0/22\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"29\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Gi1/0/23-30\",\"interface.address\":\"4C:E1:76:40:B4:97\",\"interface.alias\":\"D-link_172.16.10.48\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/23\",\"interface.index\":\"30\",\"interface.name\":\"Gi1/0/23\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"30\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Gi1/0/24-31\",\"interface.address\":\"4C:E1:76:40:B4:98\",\"interface.alias\":\"CiscoStackSwitch_12.2\",\"interface.bit.type\":\"1\",\"interface.description\":\"GigabitEthernet1/0/24\",\"interface.index\":\"31\",\"interface.name\":\"Gi1/0/24\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"31\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Te1/1/1-36\",\"interface.address\":\"4C:E1:76:40:B4:9D\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"TenGigabitEthernet1/1/1\",\"interface.index\":\"36\",\"interface.name\":\"Te1/1/1\",\"interface.speed.bytes.per.sec\":1250000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"36\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Te1/1/2-37\",\"interface.address\":\"4C:E1:76:40:B4:9E\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"TenGigabitEthernet1/1/2\",\"interface.index\":\"37\",\"interface.name\":\"Te1/1/2\",\"interface.speed.bytes.per.sec\":1250000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"37\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Te1/1/3-38\",\"interface.address\":\"4C:E1:76:40:B4:9F\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"TenGigabitEthernet1/1/3\",\"interface.index\":\"38\",\"interface.name\":\"Te1/1/3\",\"interface.speed.bytes.per.sec\":1250000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"38\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Te1/1/4-39\",\"interface.address\":\"4C:E1:76:40:B4:A0\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"TenGigabitEthernet1/1/4\",\"interface.index\":\"39\",\"interface.name\":\"Te1/1/4\",\"interface.speed.bytes.per.sec\":1250000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"39\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Te1/1/5-40\",\"interface.address\":\"4C:E1:76:40:B4:A1\",\"interface.alias\":\"D-Link_FirstFloorUser_FiberPort51_172.16.10.48\",\"interface.bit.type\":\"1\",\"interface.description\":\"TenGigabitEthernet1/1/5\",\"interface.index\":\"40\",\"interface.name\":\"Te1/1/5\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"40\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Te1/1/6-41\",\"interface.address\":\"4C:E1:76:40:B4:A2\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"TenGigabitEthernet1/1/6\",\"interface.index\":\"41\",\"interface.name\":\"Te1/1/6\",\"interface.speed.bytes.per.sec\":1250000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"41\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Te1/1/7-42\",\"interface.address\":\"4C:E1:76:40:B4:A3\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"TenGigabitEthernet1/1/7\",\"interface.index\":\"42\",\"interface.name\":\"Te1/1/7\",\"interface.speed.bytes.per.sec\":1250000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"42\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Te1/1/8-43\",\"interface.address\":\"4C:E1:76:40:B4:A4\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"TenGigabitEthernet1/1/8\",\"interface.index\":\"43\",\"interface.name\":\"Te1/1/8\",\"interface.speed.bytes.per.sec\":1250000000,\"interface.type\":\"ethernetCsmacd (6)\",\"object.name\":\"43\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Vl1-51\",\"interface.address\":\"4C:E1:76:40:B4:C7\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"Vlan1\",\"interface.index\":\"51\",\"interface.name\":\"Vl1\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"propVirtual (53)\",\"object.name\":\"51\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Lo0-52\",\"interface.address\":\"\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"Loopback0\",\"interface.index\":\"52\",\"interface.ip.address\":\"***********\",\"interface.name\":\"Lo0\",\"interface.speed.bytes.per.sec\":1000000000,\"interface.type\":\"softwareLoopback (24)\",\"object.name\":\"52\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Lo1-53\",\"interface.address\":\"\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"Loopback1\",\"interface.index\":\"53\",\"interface.ip.address\":\"***********\",\"interface.name\":\"Lo1\",\"interface.speed.bytes.per.sec\":1000000000,\"interface.type\":\"softwareLoopback (24)\",\"object.name\":\"53\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Lo10-54\",\"interface.address\":\"\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"Loopback10\",\"interface.index\":\"54\",\"interface.ip.address\":\"***********\",\"interface.name\":\"Lo10\",\"interface.speed.bytes.per.sec\":1000000000,\"interface.type\":\"softwareLoopback (24)\",\"object.name\":\"54\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Tu20-55\",\"interface.address\":\"\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"Tunnel20\",\"interface.index\":\"55\",\"interface.name\":\"Tu20\",\"interface.speed.bytes.per.sec\":0,\"interface.type\":\"tunnel (131)\",\"object.name\":\"55\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Vl20-56\",\"interface.address\":\"4C:E1:76:40:B4:D6\",\"interface.alias\":\"1st_Flr\",\"interface.bit.type\":\"1\",\"interface.description\":\"Vlan20\",\"interface.index\":\"56\",\"interface.ip.address\":\"**********\",\"interface.name\":\"Vl20\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"propVirtual (53)\",\"object.name\":\"56\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Vl30-57\",\"interface.address\":\"4C:E1:76:40:B4:E5\",\"interface.alias\":\"2nd_Flr\",\"interface.bit.type\":\"1\",\"interface.description\":\"Vlan30\",\"interface.index\":\"57\",\"interface.ip.address\":\"**********\",\"interface.name\":\"Vl30\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"propVirtual (53)\",\"object.name\":\"57\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Vl40-58\",\"interface.address\":\"4C:E1:76:40:B4:E4\",\"interface.alias\":\"3rd_Flr\",\"interface.bit.type\":\"1\",\"interface.description\":\"Vlan40\",\"interface.index\":\"58\",\"interface.name\":\"Vl40\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"propVirtual (53)\",\"object.name\":\"58\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Vl50-59\",\"interface.address\":\"4C:E1:76:40:B4:E8\",\"interface.alias\":\"LAB\",\"interface.bit.type\":\"1\",\"interface.description\":\"Vlan50\",\"interface.index\":\"59\",\"interface.name\":\"Vl50\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"propVirtual (53)\",\"object.name\":\"59\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Vl100-60\",\"interface.address\":\"4C:E1:76:40:B4:D1\",\"interface.alias\":\"Mgmt\",\"interface.bit.type\":\"1\",\"interface.description\":\"Vlan100\",\"interface.index\":\"60\",\"interface.name\":\"Vl100\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"propVirtual (53)\",\"object.name\":\"60\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Vl200-61\",\"interface.address\":\"4C:E1:76:40:B4:E2\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"Vlan200\",\"interface.index\":\"61\",\"interface.name\":\"Vl200\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"propVirtual (53)\",\"object.name\":\"61\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Vl300-62\",\"interface.address\":\"4C:E1:76:40:B4:C7\",\"interface.alias\":\"Wi-Fi\",\"interface.bit.type\":\"1\",\"interface.description\":\"Vlan300\",\"interface.index\":\"62\",\"interface.name\":\"Vl300\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"propVirtual (53)\",\"object.name\":\"62\",\"object.type\":\"interface\",\"status\":\"Down\"},{\"interface\":\"Vl500-63\",\"interface.address\":\"4C:E1:76:40:B4:D0\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"Vlan500\",\"interface.index\":\"63\",\"interface.name\":\"Vl500\",\"interface.speed.bytes.per.sec\":125000000,\"interface.type\":\"propVirtual (53)\",\"object.name\":\"63\",\"object.type\":\"interface\",\"status\":\"Up\"},{\"interface\":\"Tu10-71\",\"interface.address\":\"\",\"interface.alias\":\"\",\"interface.bit.type\":\"1\",\"interface.description\":\"Tunnel10\",\"interface.index\":\"71\",\"interface.name\":\"Tu10\",\"interface.speed.bytes.per.sec\":0,\"interface.type\":\"tunnel (131)\",\"object.name\":\"71\",\"object.type\":\"interface\",\"status\":\"Down\"}]},\"event.type\":\"rediscover\"}");

        var schedulerId = new AtomicLong(CommonUtil.newEventId());

        var id = ObjectConfigStore.getStore().getItemByIP("**********");

        Assertions.assertNotNull(id);

        context.put(EVENT_SCHEDULER, schedulerId.get());

        var items = MetricConfigStore.getStore().getItemsByObject(id).stream().filter(item -> item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName())).map(item -> item.getLong(ID)).toList();

        Assertions.assertNotNull(items);

        Assertions.assertNotNull(MetricConfigStore.getStore().getObjects(items.getFirst()).get("56"));

        var result = context.getJsonObject(RESULT);

        var objects = result.getJsonArray(OBJECTS);

        var currentSpeed = new AtomicLong();

        for (var index = 0; index < objects.size(); index++)
        {
            if (objects.getJsonObject(index).getString("interface.index").equalsIgnoreCase("56"))
            {
                currentSpeed.set(objects.getJsonObject(index).getLong("interface.speed.bytes.per.sec"));

                objects.getJsonObject(index).put("interface.speed.bytes.per.sec", 123);

                break;
            }
        }

        result.put(OBJECTS, objects);

        context.put(RESULT, result);

        TestUtil.vertx().eventBus().send(EVENT_REDISCOVER_RESPONSE, context.put(Metric.METRIC_OBJECT, id));

        TestUtil.vertx().eventBus().<JsonObject>localConsumer(EVENT_UI, message ->
        {
            var event = message.body();

            if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_REDISCOVER_STOP))
            {
                event = CodecUtil.toJSONObject(event.getBinary(EVENT_CONTEXT));

                LOGGER.info(String.format("rediscover response ; %s ", event.encode()));

                if (schedulerId.get() == event.getLong(EVENT_SCHEDULER))
                {
                    var objectSpeed = MetricConfigStore.getStore().getObjects(MetricConfigStore.getStore().getItemsByObject(id).stream().filter(item -> item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName())).map(item -> item.getLong(ID)).toList().getFirst()).get("56").getValue("interface.speed.bytes.per.sec");

                    LOGGER.info(String.format("object speed : %s and configured speed : %s ", CommonUtil.getLong(objectSpeed), currentSpeed));

                    Assertions.assertEquals(CommonUtil.getLong(objectSpeed), currentSpeed.get());

                    testContext.completeNow();
                }
            }
        });
    }
}
