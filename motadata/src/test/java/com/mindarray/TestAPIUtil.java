/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray;

import com.mindarray.api.APIConstants;
import com.mindarray.api.APIUtil;
import com.mindarray.api.Tag;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.store.AbstractConfigStore;
import com.mindarray.store.TagConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.netty.handler.codec.http.HttpHeaderNames;
import io.vertx.core.AsyncResult;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.http.Cookie;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.client.HttpResponse;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;

import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.AIOpsObject.OBJECT_TAGS;
import static com.mindarray.api.APIConstants.*;
import static io.vertx.ext.mail.impl.Utils.jsonToMultiMap;
import static org.apache.http.HttpStatus.*;
import static org.junit.jupiter.api.Assertions.*;

public class TestAPIUtil
{
    private static final JsonObject HTTP_HEADERS = new JsonObject().put(HttpHeaderNames.COOKIE.toString(), Cookie.cookie(CLIENT_ID, UI_CLIENT_ID).encode());

    private static final Logger LOGGER = new Logger(TestAPIUtil.class, MOTADATA_API, "Test API Util");

    public static Future<Long> createCredentialProfile(JsonObject credentials, VertxTestContext vertxTestContext)
    {
        var promise = Promise.<Long>promise();

        if (credentials != null && !credentials.isEmpty())
        {
            TestAPIUtil.post(TestAPIConstants.CREDENTIAL_PROFILE_API_ENDPOINT, credentials, vertxTestContext.succeeding(result ->
                    vertxTestContext.verify(() ->
                    {
                        assertEquals(SC_OK, result.statusCode());

                        promise.complete(result.bodyAsJsonObject().getLong(ID));

                    })));
        }
        else
        {
            promise.complete();
        }

        return promise.future();
    }

    /*------------------------------------------------ API CREATE/UPDATE/GET/DELETE ----------------------------------------------*/

    public static void post(String endpoint, JsonObject data, Handler<AsyncResult<HttpResponse<Buffer>>> handler)
    {
        Assertions.assertNotNull(TestUtil.getWebClient());

        Assertions.assertNotNull(TestUtil.getMultiMap());

        TestUtil.getWebClient().post(endpoint).putHeaders(TestUtil.getMultiMap()).sendJsonObject(data, response ->
        {
            if (response.succeeded())
            {
                LOGGER.info(endpoint + ":::" + response.result().statusCode());

                if (validateUnauthorized(response, handler))
                {
                    TestUtil.generateNewToken().onComplete(result ->
                    {
                        if (result.succeeded())
                        {
                            post(endpoint, handler);
                        }
                    });
                }
            }
            else
            {
                LOGGER.warn(response.cause());
            }
        });
    }

    public static void post(String endpoint, Handler<AsyncResult<HttpResponse<Buffer>>> handler)
    {
        Assertions.assertNotNull(TestUtil.getWebClient());

        Assertions.assertNotNull(TestUtil.getMultiMap());

        TestUtil.getWebClient().post(endpoint).putHeaders(TestUtil.getMultiMap()).send(response ->
        {
            if (response.succeeded())
            {
                LOGGER.info(endpoint + ":::" + response.result().statusCode());

                if (validateUnauthorized(response, handler))
                {
                    TestUtil.generateNewToken().onComplete(result ->
                    {
                        if (result.succeeded())
                        {
                            post(endpoint, handler);
                        }
                        else
                        {
                            LOGGER.warn(result.cause());
                        }
                    });
                }
            }
            else
            {
                LOGGER.warn(response.cause());
            }
        });
    }

    private static boolean validateUnauthorized(AsyncResult<HttpResponse<Buffer>> httpResponseAsyncResult, Handler<AsyncResult<HttpResponse<Buffer>>> handler)
    {
        if (httpResponseAsyncResult.result().statusCode() != SC_UNAUTHORIZED)
        {
            handler.handle(httpResponseAsyncResult);

            return false;
        }

        return true;
    }

    public static void delete(String endpoint, Handler<AsyncResult<HttpResponse<Buffer>>> handler)
    {
        Assertions.assertNotNull(TestUtil.getWebClient());

        Assertions.assertNotNull(TestUtil.getMultiMap());

        TestUtil.getWebClient().delete(endpoint).putHeaders(TestUtil.getMultiMap()).send(response ->
        {
            if (response.succeeded())
            {
                LOGGER.info(endpoint + ":::" + response.result().statusCode());

                if (validateUnauthorized(response, handler))
                {
                    TestUtil.generateNewToken().onComplete(result ->
                    {
                        if (!result.succeeded())
                        {
                            return;
                        }

                        delete(endpoint, handler);
                    });
                }
            }
            else
            {
                LOGGER.warn(response.cause());
            }
        });
    }

    public static void deleteAll(String endpoint, JsonObject data, Handler<AsyncResult<HttpResponse<Buffer>>> handler)
    {
        Assertions.assertNotNull(TestUtil.getWebClient());

        Assertions.assertNotNull(TestUtil.getMultiMap());

        TestUtil.getWebClient().delete(endpoint).putHeaders(TestUtil.getMultiMap()).sendJsonObject(data, response ->
        {
            if (response.succeeded())
            {
                LOGGER.info(endpoint + ":::" + response.result().statusCode());

                if (validateUnauthorized(response, handler))
                {
                    TestUtil.generateNewToken().onComplete(result ->
                    {
                        if (!result.succeeded())
                        {
                            return;
                        }

                        deleteAll(endpoint, data, handler);
                    });
                }
            }
            else
            {
                LOGGER.warn(response.cause());
            }
        });
    }

    public static void put(String endpoint, JsonObject data, Handler<AsyncResult<HttpResponse<Buffer>>> handler)
    {
        Assertions.assertNotNull(TestUtil.getWebClient());

        Assertions.assertNotNull(TestUtil.getMultiMap());

        TestUtil.getWebClient().put(endpoint).putHeaders(TestUtil.getMultiMap()).sendJsonObject(data, response ->
        {
            if (response.succeeded())
            {
                LOGGER.info(endpoint + ":::" + response.result().statusCode());

                if (validateUnauthorized(response, handler))
                {
                    TestUtil.generateNewToken().onComplete(result ->
                    {
                        if (!result.succeeded())
                        {
                            return;
                        }

                        put(endpoint, data, handler);
                    });
                }
            }
            else
            {
                LOGGER.warn(response.cause());
            }
        });
    }

    public static void get(String endpoint, Handler<AsyncResult<HttpResponse<Buffer>>> handler)
    {
        Assertions.assertNotNull(TestUtil.getWebClient());

        Assertions.assertNotNull(TestUtil.getMultiMap());

        TestUtil.getWebClient().get(endpoint).putHeaders(TestUtil.getMultiMap()).send(response ->
        {
            if (response.succeeded())
            {
                LOGGER.info(endpoint + ":::" + response.result().statusCode());

                if (validateUnauthorized(response, handler))
                {
                    TestUtil.generateNewToken().onComplete(result ->
                    {
                        if (!result.succeeded())
                        {
                            return;
                        }

                        get(endpoint, handler);
                    });
                }
            }
            else
            {
                LOGGER.warn(response.cause());
            }
        });
    }

    public static void create(String endpoint, JsonObject data, Handler<AsyncResult<HttpResponse<Buffer>>> handler)
    {
        Assertions.assertNotNull(TestUtil.getWebClient());

        Assertions.assertNotNull(TestUtil.getMultiMap());

        TestUtil.getWebClient().post(endpoint).putHeaders(TestUtil.getMultiMap()).sendJsonObject(data, handler);
    }

    public static void get(String endpoint, JsonObject header, Handler<AsyncResult<HttpResponse<Buffer>>> handler)
    {
        Assertions.assertNotNull(TestUtil.getWebClient());

        var multiMap = HttpHeaders.headers();

        HTTP_HEADERS.mergeIn(header).getMap().forEach((key, value) -> multiMap.add(CommonUtil.getString(key), CommonUtil.getString(value)));

        TestUtil.getWebClient().get(endpoint).putHeaders(multiMap).send(handler);

    }

    public static void post(String endpoint, JsonObject header, JsonObject data, Handler<AsyncResult<HttpResponse<Buffer>>> handler)
    {
        Assertions.assertNotNull(TestUtil.getWebClient());

        var multiMap = HttpHeaders.headers();

        HTTP_HEADERS.mergeIn(header).getMap().forEach((key, value) -> multiMap.add(CommonUtil.getString(key), CommonUtil.getString(value)));

        TestUtil.getWebClient().post(endpoint).putHeaders(multiMap).sendJsonObject(data, handler);

    }

    public static void postForm(String endpoint, JsonObject header, JsonObject data, Handler<AsyncResult<HttpResponse<Buffer>>> handler)
    {
        Assertions.assertNotNull(TestUtil.getWebClient());

        var multiMap = HttpHeaders.headers();

        HTTP_HEADERS.mergeIn(header).getMap().forEach((key, value) -> multiMap.add(CommonUtil.getString(key), CommonUtil.getString(value)));

        TestUtil.getWebClient().post(endpoint).putHeaders(multiMap).sendForm(jsonToMultiMap(data), handler);

    }

    public static void delete(String endpoint, JsonObject header, Handler<AsyncResult<HttpResponse<Buffer>>> handler)
    {
        Assertions.assertNotNull(TestUtil.getWebClient());

        var multiMap = HttpHeaders.headers();

        HTTP_HEADERS.mergeIn(header).getMap().forEach((key, value) -> multiMap.add(CommonUtil.getString(key), CommonUtil.getString(value)));

        TestUtil.getWebClient().delete(endpoint).putHeaders(multiMap).send(handler);

    }

    public static void put(String endpoint, JsonObject header, JsonObject data, Handler<AsyncResult<HttpResponse<Buffer>>> handler)
    {
        Assertions.assertNotNull(TestUtil.getWebClient());

        var multiMap = HttpHeaders.headers();

        HTTP_HEADERS.mergeIn(header).getMap().forEach((key, value) -> multiMap.add(CommonUtil.getString(key), CommonUtil.getString(value)));

        TestUtil.getWebClient().put(endpoint).putHeaders(multiMap).sendJsonObject(data, handler);

    }

    //API Create Assertions
    public static void assertCreateEntityTestResult(AbstractConfigStore store, JsonObject context, JsonObject response, String message, JsonArray filters, Logger logger, String methodName)
    {
        logger.info(methodName + ": response: " + response.encode());

        assertEquals(message, response.getString(GlobalConstants.MESSAGE));

        assertEquals(HttpStatus.SC_OK, response.getInteger(APIConstants.RESPONSE_CODE));

        var id = response.getLong(GlobalConstants.ID);

        assertNotNull(id);

        var item = store.getItem(id);

        assertNotNull(item);

        assertFalse(item.isEmpty());

        APIUtil.removeDefaultParameters(item);

        APIUtil.removeDefaultParameters(context);

        if (filters != null)
        {
            var iterator = item.getMap().keySet().iterator();

            while (iterator.hasNext())
            {
                String key = CommonUtil.getString(iterator.next());

                if (filters.contains(key))
                {
                    iterator.remove();
                }
            }
        }

        assertEquals(CommonUtil.removeSensitiveFields(item, true), CommonUtil.removeSensitiveFields(context, true));
    }

    public static void assertCreateEntityTestResult(AbstractConfigStore store, JsonObject response, String message, Logger logger, String methodName)
    {
        logger.info(methodName + ": response: " + response.encode());

        assertEquals(message, response.getString(GlobalConstants.MESSAGE));

        assertEquals(HttpStatus.SC_OK, response.getInteger(APIConstants.RESPONSE_CODE));

        var id = response.getLong(GlobalConstants.ID);

        assertNotNull(id);

        var item = store.getItem(id);

        assertNotNull(item);

        assertFalse(item.isEmpty());
    }

    //API Update Assertions
    public static void assertUpdateEntityTestResult(AbstractConfigStore store, JsonObject context, JsonObject response, String message, Logger logger, String methodName)
    {
        logger.info(methodName + ": response: " + response.encode());

        assertEquals(message.trim(), response.getString(GlobalConstants.MESSAGE).trim());

        assertEquals(HttpStatus.SC_OK, response.getInteger(APIConstants.RESPONSE_CODE));

        var id = response.getLong(GlobalConstants.ID);

        assertNotNull(id);

        var item = store.getItem(id);

        assertNotNull(item);

        assertFalse(item.isEmpty());

        context = CommonUtil.removeSensitiveFields(context, true);

        item = CommonUtil.removeSensitiveFields(item, true);

        for (var entry : item.getMap().entrySet())
        {
            if (context.containsKey(entry.getKey()) && context.getValue(entry.getKey()) != null)
            {
                assertEquals(CommonUtil.getString(context.getValue(entry.getKey())).trim(), CommonUtil.getString(entry.getValue()).trim()); //compare updated value with store value
            }
        }
    }

    public static void assertUpdateEntityTestResult(AbstractConfigStore store, JsonObject context, JsonObject response, String message, JsonArray filters, Logger logger, String methodName)
    {
        logger.info(methodName + ": response: " + response.encode());

        assertEquals(message, response.getString(GlobalConstants.MESSAGE));

        assertEquals(HttpStatus.SC_OK, response.getInteger(APIConstants.RESPONSE_CODE));

        var id = response.getLong(GlobalConstants.ID);

        assertNotNull(id);

        var item = store.getItem(id);

        assertNotNull(item);

        assertFalse(item.isEmpty());

        context = CommonUtil.removeSensitiveFields(context, true);

        item = CommonUtil.removeSensitiveFields(item, true);

        for (var entry : item.getMap().entrySet())
        {
            if (context.containsKey(entry.getKey()) && !filters.contains(entry.getKey()))
            {
                assertEquals(context.getValue(entry.getKey()), entry.getValue()); //compare updated value with store value
            }
        }
    }

    //API Delete Assertions
    public static void assertDeleteEntityTestResult(AbstractConfigStore store, JsonObject response, String message)
    {
        assertEquals(message, response.getString(GlobalConstants.MESSAGE));

        assertEquals(HttpStatus.SC_OK, response.getInteger(APIConstants.RESPONSE_CODE));

        if (response.containsKey(ID))
        {
            var id = response.getLong(GlobalConstants.ID);

            assertNotNull(id);

            Assertions.assertNull(store.getItem(id));
        }
        else if (response.containsKey(REQUEST_PARAM_IDS))
        {
            var ids = response.getJsonArray(REQUEST_PARAM_IDS);

            assertNotNull(ids);

            var items = store.getItems(ids);

            Assertions.assertTrue(items == null || items.isEmpty());
        }

    }

    //Unique error assertion
    public static void assertAlreadyExistedEntityTestResult(JsonObject response, String message, AbstractConfigStore store, String key, String value)
    {
        assertEquals(message, response.getString(GlobalConstants.MESSAGE));

        assertEquals(HttpStatus.SC_BAD_REQUEST, response.getInteger(APIConstants.RESPONSE_CODE));

        var item = store.getItemsByValue(key, value);

        Assertions.assertNotNull(item);

        assertFalse(item.isEmpty());
    }

    //Entity not exist delete error
    public static void assertNotExistEntityDeleteTestResult(HttpResponse response, String entity, String reason)
    {
        assertEquals(SC_INTERNAL_SERVER_ERROR, response.statusCode());

        var body = response.bodyAsJsonObject();

        Assertions.assertNotNull(body);

        assertEquals(String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, entity, reason), body.getString(MESSAGE));

        assertEquals(SC_INTERNAL_SERVER_ERROR, body.getInteger(RESPONSE_CODE));
    }

    //Entity in used delete
    public static void assertEntityInUsedDeleteTestResult(HttpResponse response, String entity)
    {
        assertEquals(SC_BAD_REQUEST, response.statusCode());

        var body = response.bodyAsJsonObject();

        Assertions.assertNotNull(body);

        assertTrue(String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED_IN_USED, entity).equalsIgnoreCase(body.getString(MESSAGE)) ||

                String.format(ErrorMessageConstants.ENTITY_DELETE_NOT_ALLOWED, entity).equalsIgnoreCase(body.getString(MESSAGE)));

        Assertions.assertNotNull(body.getJsonObject(RESULT));

        assertFalse(body.getJsonObject(RESULT).isEmpty());

        assertEquals(SC_BAD_REQUEST, body.getInteger(RESPONSE_CODE));
    }

    //Entity reference count
    public static void assertEntityUsedCountTestResult(HttpResponse response, long id, int expectedCount, Logger logger, String methodName)
    {
        logger.info(methodName + ": response: " + response.bodyAsJsonObject().encode());

        assertEquals(SC_OK, response.statusCode());

        var body = response.bodyAsJsonObject();

        Assertions.assertNotNull(body);

        assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

        var entities = body.getJsonArray(RESULT);

        assertTrue(entities != null && !entities.isEmpty());

        var valid = false;

        for (var index = 0; index < entities.size(); index++)
        {
            if (entities.getJsonObject(index).getLong(ID).equals(id))
            {
                assertTrue(entities.getJsonObject(index).containsKey(ENTITY_PROPERTY_COUNT));

                assertTrue(entities.getJsonObject(index).getInteger(APIConstants.ENTITY_PROPERTY_COUNT) >= expectedCount);

                valid = true;
            }
        }

        assertTrue(valid);
    }

    public static void assertEntityReferenceTestResult(HttpResponse response, String entity, int expectedCount)
    {
        assertEquals(SC_OK, response.statusCode());

        var body = response.bodyAsJsonObject();

        Assertions.assertNotNull(body);

        assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

        Assertions.assertNotNull(body.getJsonObject(RESULT));

        var items = body.getJsonObject(RESULT).getJsonArray(entity);

        assertNotNull(items);

        assertFalse(items.isEmpty());

        assertEquals(expectedCount, items.size());
    }

    //response
    public static void assertValidResponseTestResult(HttpResponse response, Logger logger, String methodName)
    {
        logger.info(methodName + ": response: " + response.bodyAsJsonObject().encode());

        assertEquals(SC_OK, response.statusCode());

        var body = response.bodyAsJsonObject();

        Assertions.assertNotNull(body);

        assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));
    }

    //single get api response assertions
    public static void assertGETRequestTestResult(HttpResponse response, long id, AbstractConfigStore store, JsonArray filters, Logger logger, String methodName)
    {
        logger.info(methodName + ": response: " + response.bodyAsJsonObject().encode());

        assertEquals(SC_OK, response.statusCode());

        var body = response.bodyAsJsonObject();

        Assertions.assertNotNull(body);

        assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

        body = body.getJsonObject(RESULT);

        Assertions.assertNotNull(body);

        assertFalse(body.isEmpty());

        assertEquals(id, body.getLong(GlobalConstants.ID));

        var item = store.getItem(id);

        Assertions.assertNotNull(item);

        assertFalse(item.isEmpty());

        APIUtil.removeDefaultParameters(body);

        APIUtil.removeDefaultParameters(item);

        if (filters != null)
        {
            for (var filter : filters)
            {
                if (body.containsKey(CommonUtil.getString(filter)))
                {
                    body.remove(CommonUtil.getString(filter));
                }
            }
        }

        Assertions.assertEquals(CommonUtil.removeSensitiveFields(body, true), CommonUtil.removeSensitiveFields(item, true));
    }

    //get all response assertions
    public static void assertGETAllRequestTestResult(HttpResponse response, AbstractConfigStore store, JsonArray filters)
    {

        assertEquals(SC_OK, response.statusCode());

        var body = response.bodyAsJsonObject();

        Assertions.assertNotNull(body);

        assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

        var entities = body.getJsonArray(RESULT);

        Assertions.assertNotNull(entities);

        assertFalse(entities.isEmpty());

        var items = store.getItems();

        Assertions.assertNotNull(items);

        assertFalse(items.isEmpty());

        assertTestResult(entities, items, filters);
    }

    //get all response assertions with filters
    public static void assertGETAllRequestFilterTestResult(HttpResponse response, AbstractConfigStore store, String field, JsonArray values, JsonArray filters)
    {

        assertEquals(SC_OK, response.statusCode());

        var body = response.bodyAsJsonObject();

        Assertions.assertNotNull(body);

        assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

        JsonArray entities = body.getJsonArray(RESULT);

        Assertions.assertNotNull(entities);

        assertFalse(entities.isEmpty());

        var items = store.getItemsByValues(field, values);

        Assertions.assertNotNull(items);

        assertFalse(items.isEmpty());

        assertTestResult(entities, enrich(items), filters);
    }

    public static JsonArray enrich(JsonArray entities) // need to differentiate user.tags and system.tags from object.tags
    {
        for (var index = 0; index < entities.size(); index++)
        {
            var entity = entities.getJsonObject(index);

            if (entity.getJsonArray(OBJECT_TAGS) != null && !entity.getJsonArray(OBJECT_TAGS).isEmpty())
            {
                entity.put(OBJECT_TAGS, TagConfigStore.getStore().getItems(entity.getJsonArray(OBJECT_TAGS), Tag.TagType.OBJECT, EMPTY_VALUE));
            }
        }

        return entities;
    }

    //compare database and store entities
    public static void assertTestResult(JsonArray entities, JsonArray items, JsonArray filters)
    {
        var filteredItems = new JsonArray();

        for (var index = 0; index < entities.size(); index++)
        {
            var entity = entities.getJsonObject(index);

            entity.remove(ENTITY_PROPERTY_COUNT);

            entity.remove(ConfigDBConstants.FIELD_TYPE);

            if (filters != null)
            {
                entity.getMap().keySet().removeIf(filters::contains);
            }
            filteredItems.add(CommonUtil.removeSensitiveFields(entity, true));
        }

        var records = new JsonArray();

        for (var index = 0; index < items.size(); index++)
        {
            var item = items.getJsonObject(index);

            item.remove(ENTITY_PROPERTY_COUNT);

            item.remove(ConfigDBConstants.FIELD_TYPE);

            records.add(CommonUtil.removeSensitiveFields(item, true));
        }

        Assertions.assertEquals(filteredItems.stream().collect(Collectors.groupingBy(item -> JsonObject.mapFrom(item).getLong(ID), Collectors.toList()))
                , records.stream().collect(Collectors.groupingBy(item -> JsonObject.mapFrom(item).getLong(ID), Collectors.toList())));
    }

    //forbidden
    public static void assertForbiddenRequestTestResult(HttpResponse response, String moduleType)
    {
        assertEquals(SC_FORBIDDEN, response.statusCode());

        var body = response.bodyAsJsonObject();

        Assertions.assertNotNull(body);

        assertEquals(String.format(ErrorMessageConstants.API_ACCESS_FAILED_NOT_AUTHORIZED, moduleType), body.getString(MESSAGE));
    }

    public static void assertMultiUpdateEntityTestResult(HttpResponse response, String entity)
    {
        assertEquals(SC_OK, response.statusCode());

        var body = response.bodyAsJsonObject();

        Assertions.assertNotNull(body);

        assertEquals(String.format(InfoMessageConstants.ENTITY_UPDATED, entity), body.getString(MESSAGE));

        assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));
    }

    public static void assertMultiCreateEntityTestResult(AbstractConfigStore store, JsonObject response, String fieldName)
    {
        assertEquals(String.format(InfoMessageConstants.ENTITY_CREATED, fieldName), response.getString(GlobalConstants.MESSAGE));

        assertEquals(HttpStatus.SC_OK, response.getInteger(APIConstants.RESPONSE_CODE));

        assertNotNull(response.getJsonArray(ID));

        var items = store.getItems(response.getJsonArray(ID));

        assertNotNull(items);

        assertFalse(items.isEmpty());
    }

    public static void assertDeleteDefaultEntityTestResult(JsonObject response, String message)
    {
        assertEquals(message, response.getString(GlobalConstants.MESSAGE));

        assertEquals(SC_BAD_REQUEST, response.getInteger(APIConstants.RESPONSE_CODE));

        assertEquals(STATUS_FAIL, response.getString(STATUS));
    }
}
