/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.processor;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.TestUtil;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.RemoteEventForwarder;
import com.mindarray.store.FlowSamplingRateConfigStore;
import com.mindarray.util.CommonUtil;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.zeromq.SocketType;
import org.zeromq.ZMQ;

import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(70 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "FLOW_COLLECTOR")
public class TestFlowCollector
{
    private static MessageConsumer<JsonObject> eventDBWriteTestConsumer;
    private static ZMQ.Socket producer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            testContext.awaitCompletion(10, TimeUnit.SECONDS);

            var deploymentId = Bootstrap.getDeployedVerticles().get(RemoteEventForwarder.class.getSimpleName() + " 9450");

            Bootstrap.vertx().undeploy(deploymentId, result ->
            {
                if (result.succeeded())
                {
                    var deploymentID = Bootstrap.getDeployedVerticles().get(RemoteEventForwarder.class.getSimpleName() + " 9449");

                    Bootstrap.vertx().undeploy(deploymentID, asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            Bootstrap.vertx().eventBus().publish(EVENT_CONFIG_INIT, new JsonObject().put(STATUS, STATUS_SUCCEED));

                            testContext.completeNow();
                        }
                    });
                }
            });

            producer = Bootstrap.zcontext().socket(SocketType.PUSH);

            producer.connect("tcp://*:" + CommonUtil.getEventSubscriberPort());

        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    @Timeout(5000)
    void testFlowCollector(VertxTestContext testContext) throws InterruptedException
    {

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        eventDBWriteTestConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_REMOTE, message -> //this event will be
        {
            try
            {
                var event = message.body();

                if (EVENT_FLOW.equalsIgnoreCase(event.getString(EVENT_TYPE)))
                {
                    Assertions.assertNotNull(event);

                    Assertions.assertEquals(EVENT_FLOW, event.getString(EVENT_TYPE));

                    Assertions.assertEquals(467, event.getInteger(EVENT_VOLUME_BYTES));

                    Assertions.assertEquals(DatastoreConstants.PluginId.FLOW_EVENT.getName(), event.getInteger(PLUGIN_ID));

                    eventDBWriteTestConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });

        producer.send(Buffer.buffer().appendShortLE(CommonUtil.getShort(EventBusConstants.FLOW_TOPIC.length())).appendString(EventBusConstants.FLOW_TOPIC).appendBytes("{\"tag\": 1, \"class\": \"unknown\", \"peer_ip_src\": \"*******\", \"peer_ip_dst\": \"\", \"iface_in\": 0, \"iface_out\": 0, \"ip_src\": \"*******\", \"ip_dst\": \"*******\", \"port_src\": 40, \"port_dst\": 80, \"tcp_flags\": \"0\", \"ip_proto\": \"6\", \"tos\": 0, \"sampling_rate\": 10, \"timestamp_start\": \"2022-09-07 14:56:11.000000\", \"timestamp_end\": \"2022-09-07 14:56:11.000000\", \"export_proto_seqno\": 1, \"export_proto_version\": 5, \"flows\": 1, \"packets\": 902, \"bytes\": 393, \"flow_type\": \"NetFlow\"}".getBytes()).getBytes());

        Assertions.assertTrue(testContext.awaitCompletion(20, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    public void testRemoteEventConfigChange(VertxTestContext testContext) throws InterruptedException
    {
        var id = 49983344835L;

        var context = new JsonObject("{\"event.type\":\"change.notification\",\"event.topic\":\"remote.event.processor\",\"collection\":\"flow.sampling.rate\",\"change.notification.type\":\"CONFIG_CHANGE\",\"request\":\"create\",\"id\":49983344835,\"result\":{\"event.source\":\"***********\",\"interface.index\":31,\"interface.sampling.rate\":1,\"event.id\":49983344889,\"_type\":\"1\",\"interface.custom.sampling.rate\":10}}").put(GlobalConstants.ID, id);

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_CHANGE_NOTIFICATION, context);

        TestUtil.vertx().setTimer(5000, handler ->
        {
            var item = FlowSamplingRateConfigStore.getStore().getItem(id);

            Assertions.assertNotNull(item);

            testContext.completeNow();
        });

        Assertions.assertTrue(testContext.awaitCompletion(20, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");

    }
}
