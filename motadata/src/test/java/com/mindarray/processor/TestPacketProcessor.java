/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.processor;

import com.mindarray.Bootstrap;
import com.mindarray.TestUtil;
import com.mindarray.agent.AgentConstants;
import com.mindarray.api.Agent;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.AgentConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CipherUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.xerial.snappy.Snappy;
import org.zeromq.SocketType;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import static com.mindarray.eventbus.EventBusConstants.EVENT_AGENT_RESPONSE_STREAM;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(100 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
@Disabled
//Disabled functionality not available current build
public class TestPacketProcessor
{
    private static final CipherUtil CIPHER_UTIL = new CipherUtil();
    private static final Logger LOGGER = new Logger(TestPacketProcessor.class, "Packet Processor", "Test Packet Processor");
    private static MessageConsumer<JsonObject> eventDBWriteTestConsumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {

        AgentConfigStore.getStore().updateItem(1L, new JsonObject().put(Agent.AGENT_STATE, "enable").put(Agent.AGENT_UUID, "6A48DA800746472D9B3C69488CD36629"));

        ObjectConfigStore.getStore().addItem(2L, new JsonObject("{\"object.host\":\"DESKTOP-VINHN6V\",\"object.name\":\"DESKTOP-VINHN6V\",\"object.ip\":\"***********\",\"object.type\":\"Windows\",\"object.category\":\"Server\",\"object.target\":\"DESKTOP-VINHN6V\",\"object.groups\":[10000000000018,10000000000017],\"object.discovery.method\":\"AGENT\",\"object.business.hour.profile\":10000000000001,\"object.agent\":149349660759625,\"object.creation.time\":\"2022/10/04 11:22:02\",\"object.state\":\"ENABLE\",\"object.id\":1,\"_type\":\"1\",\"id\":149349660759626}"));

        try
        {
            // reflection to mock inject the required objects for test cases.

            var field = AgentConfigStore.getStore().getClass().getDeclaredField("itemsByUUID");

            field.setAccessible(true);

            var itemsByUUID = new ConcurrentHashMap<>();

            itemsByUUID.put("6A48DA800746472D9B3C69488CD36629", 1L);

            field.set(AgentConfigStore.getStore(), itemsByUUID);

            field = ObjectConfigStore.getStore().getClass().getDeclaredField("itemsByAgent");

            field.setAccessible(true);

            var itemsByAgent = new ConcurrentHashMap<>();

            itemsByAgent.put(1L, 2L);

            field.set(ObjectConfigStore.getStore(), itemsByAgent);

            testContext.completeNow();
        }
        catch (NoSuchFieldException | IllegalAccessException exception)
        {
            testContext.failNow(exception);
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        if (eventDBWriteTestConsumer != null)
        {
            eventDBWriteTestConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @Test
    @Order(1)
    @Timeout(5000)
    void testAgentListener(VertxTestContext testContext) throws InterruptedException
    {
        var expectedOutput = List.of("0§***********§source.ip§************", "0§***********§source.country§Unknown", "0§***********§source.city§Unknown", "0§***********§destination.ip§*******", "0§***********§destination.country§United States", "0§***********§destination.city§Unknown", "1§***********§packets§6", "1§***********§volume.bytes§922", "0§***********§application.protocol§Unknown", "0§***********§application§Unknown", "1§***********§source.port§50880", "1§***********§destination.port§443", "0§***********§tcp.flags§PSH|ACK", "0§***********§destination.ip.as§", "0§***********§destination.as§", "0§***********§source.as§", "0§***********§tos§-1 (Unknown)", "1§***********§volume.bytes.per.packet§153.0", "0§***********§protocol§6 (TCP)", "0§***********§peer.source§Unknown", "0§***********§peer.destination§Unknown", "0§***********§source.isp§Unknown", "0§***********§destination.isp§Unknown", "0§***********§source.domain§Unknown", "0§***********§destination.domain§Unknown", "0§***********§source.asn§Unknown", "0§***********§destination.asn§Unknown", "0§***********§source.threat§Unknown", "0§***********§destination.threat§Unknown", "0§***********§event.source§***********");

        assertFlowProcessorResult(testContext, expectedOutput);

        try (var producer = Bootstrap.zcontext().socket(SocketType.PUSH))
        {
            producer.connect("tcp://*:" + CommonUtil.getEventSubscriberPort());

            producer.send("agent " + CIPHER_UTIL.encrypt(new JsonObject()
                    .put("event.type", EVENT_AGENT_RESPONSE_STREAM)
                    .put("agent.id", "6A48DA800746472D9B3C69488CD36629")
                    .put("agent.type", AgentConstants.Agent.PACKET.getName())
                    .put("event.context", Snappy.compress("{\"protocol\":\"tcp\",\"source.ip\":\"************\",\"source.port\":50880,\"destination.ip\":\"*******\",\"destination.port\":443,\"tcp.flags\":\"PSH|ACK\",\"tcp.retransmissions\":3,\"volume.bytes\":922,\"event.timestamp\":1661771400000,\"peer.source\":\"b0:be:83:58:f7:f7\",\"peer.destination\":\"90:6c:ac:e6:1e:87\",\"packets\":6}")).encode()));
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }

        Assertions.assertTrue(testContext.awaitCompletion(5, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    private void assertFlowProcessorResult(VertxTestContext testContext, List<String> values)
    {
        eventDBWriteTestConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
        {
            try
            {
                var event = message.body();

                testContext.verify(() ->
                {

                    var batches = event.getJsonArray(DatastoreConstants.BATCHES);

                    Assertions.assertNotNull(batches);

                    for (var item : values)
                    {
                        Assertions.assertTrue(batches.contains(item), "doesn't contain " + item);
                    }
                });

                testContext.completeNow();
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });
    }
}
