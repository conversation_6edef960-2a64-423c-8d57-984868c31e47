/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.processor;

import com.mindarray.*;
import com.mindarray.api.APIConstants;
import com.mindarray.api.User;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.SNMPTrapProcessor;
import com.mindarray.store.*;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.snmp4j.PDU;
import org.snmp4j.PDUv1;
import org.snmp4j.ScopedPDU;
import org.snmp4j.mp.SnmpConstants;
import org.snmp4j.smi.*;

import java.util.Date;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.SNMPTrapForwarder.*;
import static com.mindarray.api.SNMPTrapListenerConfiguration.*;
import static com.mindarray.api.SNMPTrapProfile.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.SNMP_TRAP_VERSION;
import static com.mindarray.nms.SNMPTrapProcessor.SNMP_TRAP_OID;
import static com.mindarray.notification.Notification.NOTIFICATION_TYPE;
import static com.mindarray.notification.Notification.NotificationType.SNMP_TRAP;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(60 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestTrapProcessor
{
    private static final String SNMP_TRAP_PROFILE = "SNMP Trap Profile";

    private static final String SNMP_TRAP_LISTENER = "SNMP Trap Listener";

    private static final int TRAP_FORWARDER_PORT = 7896;

    private static final JsonObject CONTEXT = new JsonObject();

    private static final JsonObject CONTEXT1 = new JsonObject().put(SNMP_TRAP_PROFILE_NAME, "testProfileDrop")
            .put(SNMP_TRAP_PROFILE_OID, ".*******.4.1.321.10.11147")
            .put(SNMP_TRAP_PROFILE_DROP_STATUS, YES)
            .put(SNMP_TRAP_PROFILE_SEVERITY, "Warning")
            .put(SNMP_TRAP_PROFILE_TRANSLATOR, "Received trap from motadata $1 is $2");

    private static final JsonObject CONTEXT3 = new JsonObject().put(SNMP_TRAP_PROFILE_NAME, "testClearProfile")
            .put(SNMP_TRAP_PROFILE_OID, ".*******.4.1.644.2.4.0.1021")
            .put(SNMP_TRAP_PROFILE_DROP_STATUS, NO)
            .put(SNMP_TRAP_PROFILE_AUTO_CLEAR_STATUS, NO)
            .put(SNMP_TRAP_PROFILE_AUTO_CLEAR_STATUS, NO)
            .put(SNMP_TRAP_PROFILE_SEVERITY, "Warning")
            .put(SNMP_TRAP_PROFILE_TRANSLATOR, "Received trap from motadata $1 is $2");

    private static final JsonObject CONTEXT4 = new JsonObject().put(SNMP_TRAP_PROFILE_NAME, "testAutoClearTimerProfile")
            .put(SNMP_TRAP_PROFILE_OID, ".*******.4.1.644.2.4.0.1023")
            .put(SNMP_TRAP_PROFILE_DROP_STATUS, NO)
            .put(SNMP_TRAP_PROFILE_AUTO_CLEAR_STATUS, YES)
            .put(SNMP_TRAP_PROFILE_AUTO_CLEAR_TIMER, 5)
            .put(SNMP_TRAP_PROFILE_SEVERITY, "Clear")
            .put(SNMP_TRAP_PROFILE_TRANSLATOR, "Received trap from motadata $1 is $2");

    private static final JsonObject CONTEXT2 = new JsonObject().put(SNMP_TRAP_PROFILE_NAME, "testProfileAutoClear")
            .put(SNMP_TRAP_PROFILE_OID, ".*******.4.1.644.2.4.0.1020")
            .put(SNMP_TRAP_PROFILE_DROP_STATUS, NO)
            .put(SNMP_TRAP_PROFILE_AUTO_CLEAR_STATUS, YES)
            .put(SNMP_TRAP_PROFILE_AUTO_CLEAR_OID, ".*******.4.1.644.2.4.0.1021")
            .put(SNMP_TRAP_PROFILE_AUTO_CLEAR_TIMER, 5)
            .put(SNMP_TRAP_PROFILE_SEVERITY, "Warning")
            .put(SNMP_TRAP_PROFILE_TRANSLATOR, "Received trap from motadata $1 is $2");

    private static final JsonObject TRAP_LISTENER_CONTEXT1 = new JsonObject()
            .put(SNMP_TRAP_LISTENER_V1_V2_PORT, SNMP_TRAP_V1_V2_LISTENER_PORT)
            .put(SNMP_TRAP_LISTENER_V1_V2_STATUS, YES)
            .put(SNMP_TRAP_LISTENER_V3_STATUS, NO)
            .put(SNMP_COMMUNITY, "public");

    private static final JsonObject TRAP_LISTENER_CONTEXT2 = new JsonObject()
            .put(SNMP_TRAP_LISTENER_V3_PORT, SNMP_TRAP_V3_LISTENER_PORT)
            .put(SNMP_TRAP_LISTENER_V1_V2_STATUS, NO)
            .put(SNMP_TRAP_LISTENER_V3_STATUS, YES)
            .put(SNMP_V3_SECURITY_USERNAME, "public")
            .put(SNMP_V3_SECURITY_LEVEL, AUTHENTICATION_NO_PRIVACY)
            .put(SNMP_V3_AUTHENTICATION_PROTOCOL, MD5)
            .put(SNMP_V3_AUTHENTICATION_PASSWORD, "Mind@#123");
    private static final Logger LOGGER = new Logger(TestTrapProcessor.class, MOTADATA_API, "Test Trap Processor");
    private static MessageConsumer<byte[]> messageConsumer;

    public static void assertSNMPTrapForwarderTestResult(VertxTestContext testContext, int trapVersion, JsonObject context)
    {
        try
        {
            TestUtil.vertx().eventBus().<JsonObject>request(EVENT_NOTIFICATION, context.put(EventBusConstants.EVENT_REPLY, YES), new DeliveryOptions().setSendTimeout(60000L), reply ->
            {
                try
                {
                    if (reply.failed())
                    {
                        testContext.failNow(reply.cause());
                    }
                    else
                    {
                        var result = reply.result().body();

                        Assertions.assertNotNull(result);

                        Assertions.assertFalse(result.isEmpty());

                        Assertions.assertTrue(result.containsKey(STATUS));

                        Assertions.assertTrue(result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED));

                        Assertions.assertTrue(result.containsKey(ERROR_CODE));

                        Assertions.assertTrue(result.getString(ERROR_CODE).equalsIgnoreCase(ErrorCodes.ERROR_CODE_SUCCESS));

                        Assertions.assertTrue(result.containsKey(EVENT_ID));

                        assertEquals(context.getLong(EVENT_ID), result.getLong(EVENT_ID));

                        Assertions.assertTrue(result.containsKey(SNMP_TRAP_VERSION));

                        assertEquals(trapVersion, (int) result.getInteger(SNMP_TRAP_VERSION));

                        Assertions.assertTrue(result.containsKey(TARGET));

                        Assertions.assertTrue(result.getString(TARGET).equalsIgnoreCase(context.getString(TARGET)));

                        Assertions.assertTrue(result.containsKey(EVENT));

                        Assertions.assertTrue(result.getString(EVENT).equalsIgnoreCase(context.getString(EVENT)));

                        testContext.completeNow();
                    }
                }
                catch (Exception ignored)
                {

                }
            });
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateSNMPTrapProfileHavingFilter(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {

        TestAPIUtil.post(SNMP_TRAP_PROFILE_API_ENDPOINT, CONTEXT1,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            testContext.awaitCompletion(2, TimeUnit.SECONDS);

                            TestAPIUtil.assertCreateEntityTestResult(SNMPTrapProfileConfigStore.getStore(), CONTEXT1, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, SNMP_TRAP_PROFILE), null, LOGGER, testInfo.getTestMethod().get().getName());

                            CONTEXT.put("snmp.trap.drop.profile", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateSNMPTrapProfileHavingAutoClearOID(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.post(SNMP_TRAP_PROFILE_API_ENDPOINT, CONTEXT2,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {

                            TestAPIUtil.assertCreateEntityTestResult(SNMPTrapProfileConfigStore.getStore(), CONTEXT2, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, SNMP_TRAP_PROFILE), null, LOGGER, testInfo.getTestMethod().get().getName());

                            CONTEXT.put("snmp.trap.profile.auto.clear.v1", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testUpdateSNMPTrapProfileDuplicateName(VertxTestContext testContext)
    {
        var context = CONTEXT1.copy().put("snmp.trap.profile.name", "testProfileDropUpdate");

        TestAPIUtil.post(SNMP_TRAP_PROFILE_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(), String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, "Trap OID"),
                            SNMPTrapProfileConfigStore.getStore(), SNMP_TRAP_PROFILE_OID, CONTEXT1.getString(SNMP_TRAP_PROFILE_OID));

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testCreateTrapProfileDuplicateName(VertxTestContext testContext)
    {
        TestAPIUtil.post(SNMP_TRAP_PROFILE_API_ENDPOINT, CONTEXT1.put(SNMP_TRAP_PROFILE_OID, "1.2.3.4.56.7.8"),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {

                            TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(), String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, SNMP_TRAP_PROFILE_NAME),
                                    SNMPTrapProfileConfigStore.getStore(), SNMP_TRAP_PROFILE_NAME, CONTEXT1.getString(SNMP_TRAP_PROFILE_NAME));

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testCreateSNMPTrapProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = CONTEXT2.copy().put(SNMP_TRAP_PROFILE_NAME, "trap dummy " + System.currentTimeMillis()).put(SNMP_TRAP_PROFILE_OID, ".*******.4.1.644.2.4.0.1040");

        context.put(SNMP_TRAP_PROFILE_AUTO_CLEAR_OID, ".*******.4.1.644.2.4.0.1029");

        TestAPIUtil.post(SNMP_TRAP_PROFILE_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    TestAPIUtil.assertCreateEntityTestResult(SNMPTrapProfileConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, SNMP_TRAP_PROFILE), null, LOGGER, testInfo.getTestMethod().get().getName());

                    TestTrapProcessor.CONTEXT.put("snmp.trap.profile.auto.clear.v3", response.bodyAsJsonObject().getLong(ID));

                    testContext.completeNow();


                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testGetSNMPv1TrapListenerSettings(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.get(SNMP_TRAP_LISTENER_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {

            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            var items = body.getJsonArray(GlobalConstants.RESULT);

            Assertions.assertNotNull(items);

            assertFalse(items.isEmpty());

            CONTEXT.put("snmp.trap.listener.v1", items.getJsonObject(0).getLong(ID));

            testContext.completeNow();
        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testUpdateSNMPv1TrapListenerSettings(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.put(SNMP_TRAP_LISTENER_API_ENDPOINT + "/" + CONTEXT.getLong("snmp.trap.listener.v1"), TRAP_LISTENER_CONTEXT1, testContext.succeeding(response -> testContext.verify(() ->
        {

            TestAPIUtil.assertUpdateEntityTestResult(SNMPTrapListenerConfigStore.getStore(), TRAP_LISTENER_CONTEXT1, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, SNMP_TRAP_LISTENER), LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testCreateSNMPTrapForwarderV1(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertTrue(CONTEXT.getLong("snmp.trap.profile.auto.clear.v1") != null && CONTEXT.getLong("snmp.trap.profile.auto.clear.v3") != null);

        var context = new JsonObject().put(SNMP_TRAP_FORWARDER_NAME, "trap-forwarder-auto-clear")
                .put(SNMP_TRAP_FORWARDER_PROFILES, new JsonArray()
                        .add(TestTrapProcessor.CONTEXT.getLong("snmp.trap.profile.auto.clear.v1"))
                        .add(TestTrapProcessor.CONTEXT.getLong("snmp.trap.profile.auto.clear.v3")))
                .put(SNMP_TRAP_FORWARDER_DESTINATION_HOST, "localhost")
                .put(SNMP_TRAP_FORWARDER_DESTINATION_PORT, TRAP_FORWARDER_PORT);

        TestAPIUtil.post(SNMP_TRAP_FORWARDER_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    TestAPIUtil.assertCreateEntityTestResult(SNMPTrapForwarderConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.SNMP_TRAP_FORWARDER.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                    TestTrapProcessor.CONTEXT.put("snmp.trap.forwarder", response.bodyAsJsonObject().getLong(ID));

                    Bootstrap.vertx().eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
                    {
                        if (message.body() != null && message.body().containsKey(EventBusConstants.CHANGE_NOTIFICATION_TYPE) && message.body().getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(ChangeNotificationType.SNMP_TRAP_FORWARDER.name()))
                        {
                            testContext.completeNow();
                        }
                    });

                    TestAPIUtil.put(SNMP_TRAP_FORWARDER_API_ENDPOINT + "/" + response.bodyAsJsonObject().getLong(ID),
                            context, testContext.succeeding(httpResponse ->
                                    testContext.verify(() -> TestAPIUtil.assertUpdateEntityTestResult(SNMPTrapForwarderConfigStore.getStore(), context, httpResponse.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.SNMP_TRAP_FORWARDER.getName()), LOGGER, testInfo.getTestMethod().get().getName()))));

                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testForwardSNMPv1Trap(VertxTestContext testContext)
    {
        try
        {
            PDU pdu;
            var pdu1 = new PDUv1();
            pdu1.setType(PDU.V1TRAP);
            pdu1.setAgentAddress(new IpAddress("localhost"));     //SET THIS. This is the sender address
            pdu1.setSpecificTrap(5);
            pdu1.setGenericTrap(0);
            pdu = pdu1;
            pdu.add(new VariableBinding(SnmpConstants.sysUpTime));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapOID, new OID(CONTEXT2.getString(SNMP_TRAP_PROFILE_OID))));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapAddress, new IpAddress("localhost")));

            assertSNMPTrapForwarderTestResult(testContext, SnmpConstants.version1, new JsonObject().put(EVENT, CodecUtil.toBytes(pdu)).put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                    .put(TARGET, "localhost/" + TRAP_FORWARDER_PORT)
                    .put(SNMP_TRAP_VERSION, SnmpConstants.version1)
                    .put(NOTIFICATION_TYPE, SNMP_TRAP.getName())
                    .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()));
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testCreateClearTimerSNMPTrapProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = CONTEXT4.copy();

        TestAPIUtil.post(SNMP_TRAP_PROFILE_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    TestAPIUtil.assertCreateEntityTestResult(SNMPTrapProfileConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, SNMP_TRAP_PROFILE), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();


                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testForwardSNMPv2Trap(VertxTestContext testContext)
    {
        try
        {
            PDU pdu = new PDU();
            pdu.setType(PDU.V1TRAP);
            pdu.add(new VariableBinding(SnmpConstants.sysUpTime, new OctetString(new Date().toString())));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapOID, new OID(CONTEXT2.getString(SNMP_TRAP_PROFILE_OID))));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapAddress, new IpAddress("localhost")));
            pdu.setType(PDU.NOTIFICATION);

            assertSNMPTrapForwarderTestResult(testContext, SnmpConstants.version2c, new JsonObject().put(EVENT, CodecUtil.toBytes(pdu)).put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                    .put(TARGET, "localhost/" + TRAP_FORWARDER_PORT)
                    .put(SNMP_TRAP_VERSION, SnmpConstants.version2c)
                    .put(NOTIFICATION_TYPE, SNMP_TRAP.getName())
                    .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()));
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testSNMPv1Trap(VertxTestContext testContext)
    {
        try
        {
            PDU pdu;
            var pdu1 = new PDUv1();
            pdu1.setType(PDU.V1TRAP);
            pdu1.setAgentAddress(new IpAddress("localhost"));     //SET THIS. This is the sender address
            pdu1.setSpecificTrap(5);
            pdu1.setGenericTrap(0);
            pdu = pdu1;
            pdu.add(new VariableBinding(SnmpConstants.sysUpTime));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapOID, new OID(CONTEXT4.getString(SNMP_TRAP_PROFILE_OID))));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapAddress, new IpAddress("localhost")));

            TestUtil.vertx().eventBus().send(EVENT_NOTIFICATION, new JsonObject().put(EVENT, CodecUtil.toBytes(pdu)).put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                    .put(TARGET, "localhost/" + 1620)
                    .put(SNMP_TRAP_VERSION, SnmpConstants.version1)
                    .put(NOTIFICATION_TYPE, SNMP_TRAP.getName())
                    .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()));

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
            {
                try
                {

                    var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                    if (event.containsKey(PLUGIN_ID) && (CommonUtil.getInteger(event.getString(PLUGIN_ID).split("-")[0]) == DatastoreConstants.PluginId.TRAP_EVENT.getName()))
                    {
                        if (event.containsKey("trap.oid"))
                        {
                            assertEquals(CONTEXT4.getString(SNMP_TRAP_PROFILE_OID), event.getString("trap.oid"));
                        }

                        TestUtil.vertx().eventBus().send(UI_ACTION_TRAP_ACKNOWLEDGE, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(ACKNOWLEDGED, YES).put(EVENT_CONTEXT, new JsonObject().put(User.USER_NAME, UserConfigStore.getStore().getItems().getJsonObject(0).getString(User.USER_NAME)).put(SNMPTrapProcessor.SNMP_TRAP_OID, null)));

                        messageConsumer.unregister(result -> testContext.completeNow());

                    }
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister(result -> testContext.failNow(exception));

                }
            });
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testSNMPv1ColdStartTrap(VertxTestContext testContext)
    {
        try
        {
            PDU pdu;
            var pdu1 = new PDUv1();
            pdu1.setType(PDU.V1TRAP);
            pdu1.setAgentAddress(new IpAddress("localhost"));     //SET THIS. This is the sender address
            pdu1.setSpecificTrap(5);
            pdu1.setGenericTrap(0);
            pdu = pdu1;
            pdu.add(new VariableBinding(SnmpConstants.sysUpTime));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapOID, new OID(CONTEXT2.getString(SNMP_TRAP_PROFILE_OID))));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapAddress, new IpAddress("localhost")));


            TestUtil.vertx().eventBus().send(EVENT_NOTIFICATION, new JsonObject().put(EVENT, CodecUtil.toBytes(pdu)).put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                    .put(TARGET, "localhost/" + 1620)
                    .put(SNMP_TRAP_VERSION, SnmpConstants.version1)
                    .put(NOTIFICATION_TYPE, SNMP_TRAP.getName())
                    .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()));

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
            {
                try
                {
                    var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                    if (event.containsKey(PLUGIN_ID) && (CommonUtil.getInteger(event.getString(PLUGIN_ID).split("-")[0]) == DatastoreConstants.PluginId.TRAP_EVENT.getName()))
                    {
                        if (event.containsKey("trap.oid"))
                        {
                            assertEquals(CONTEXT2.getString(SNMP_TRAP_PROFILE_OID), event.getString("trap.oid"));
                        }

                        messageConsumer.unregister(result -> testContext.completeNow());
                    }
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister(result -> testContext.failNow(exception));

                }
            });
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testSNMPv1WarmStartTrap(VertxTestContext testContext)
    {
        try
        {
            PDU pdu;
            var pdu1 = new PDUv1();
            pdu1.setType(PDU.V1TRAP);
            pdu1.setAgentAddress(new IpAddress("localhost"));     //SET THIS. This is the sender address
            pdu1.setSpecificTrap(5);
            pdu1.setGenericTrap(1);
            pdu = pdu1;
            pdu.add(new VariableBinding(SnmpConstants.sysUpTime));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapOID, new OID(CONTEXT2.getString(SNMP_TRAP_PROFILE_OID))));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapAddress, new IpAddress("localhost")));


            TestUtil.vertx().eventBus().send(EVENT_NOTIFICATION, new JsonObject().put(EVENT, CodecUtil.toBytes(pdu)).put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                    .put(TARGET, "localhost/" + 1620)
                    .put(SNMP_TRAP_VERSION, SnmpConstants.version1)
                    .put(NOTIFICATION_TYPE, SNMP_TRAP.getName())
                    .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()));

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
            {
                try
                {
                    var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                    if (event.containsKey(PLUGIN_ID) && (CommonUtil.getInteger(event.getString(PLUGIN_ID).split("-")[0]) == DatastoreConstants.PluginId.TRAP_EVENT.getName()))
                    {

                        if (event.containsKey("trap.oid"))
                        {
                            assertEquals(CONTEXT2.getString(SNMP_TRAP_PROFILE_OID), event.getString("trap.oid"));
                        }

                        messageConsumer.unregister(result -> testContext.completeNow());
                    }
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister(result -> testContext.failNow(exception));

                }
            });
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testSNMPv1LinkUpTrap(VertxTestContext testContext)
    {
        try
        {
            PDU pdu;
            var pdu1 = new PDUv1();
            pdu1.setType(PDU.V1TRAP);
            pdu1.setAgentAddress(new IpAddress("localhost"));     //SET THIS. This is the sender address
            pdu1.setSpecificTrap(5);
            pdu1.setGenericTrap(3);
            pdu = pdu1;
            pdu.add(new VariableBinding(SnmpConstants.sysUpTime));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapOID, new OID(CONTEXT2.getString(SNMP_TRAP_PROFILE_OID))));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapAddress, new IpAddress("localhost")));


            TestUtil.vertx().eventBus().send(EVENT_NOTIFICATION, new JsonObject().put(EVENT, CodecUtil.toBytes(pdu)).put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                    .put(TARGET, "localhost/" + 1620)
                    .put(SNMP_TRAP_VERSION, SnmpConstants.version1)
                    .put(NOTIFICATION_TYPE, SNMP_TRAP.getName())
                    .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()));

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
            {
                try
                {
                    var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                    if (event.containsKey(PLUGIN_ID) && (CommonUtil.getInteger(event.getString(PLUGIN_ID).split("-")[0]) == DatastoreConstants.PluginId.TRAP_EVENT.getName()))
                    {
                        if (event.containsKey("trap.oid"))
                        {
                            assertEquals(CONTEXT2.getString(SNMP_TRAP_PROFILE_OID), event.getString("trap.oid"));
                        }

                        messageConsumer.unregister(result -> testContext.completeNow());

                    }
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister(result -> testContext.failNow(exception));

                }
            });
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testSNMPv1LinkDownTrap(VertxTestContext testContext)
    {
        try
        {
            PDU pdu;
            var pdu1 = new PDUv1();
            pdu1.setType(PDU.V1TRAP);
            pdu1.setAgentAddress(new IpAddress("localhost"));     //SET THIS. This is the sender address
            pdu1.setSpecificTrap(5);
            pdu1.setGenericTrap(2);
            pdu = pdu1;
            pdu.add(new VariableBinding(SnmpConstants.sysUpTime));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapOID, new OID(CONTEXT2.getString(SNMP_TRAP_PROFILE_OID))));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapAddress, new IpAddress("localhost")));


            TestUtil.vertx().eventBus().send(EVENT_NOTIFICATION, new JsonObject().put(EVENT, CodecUtil.toBytes(pdu)).put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                    .put(TARGET, "localhost/" + 1620)
                    .put(SNMP_TRAP_VERSION, SnmpConstants.version1)
                    .put(NOTIFICATION_TYPE, SNMP_TRAP.getName())
                    .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()));

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
            {
                try
                {
                    var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                    if (event.containsKey(PLUGIN_ID) && (CommonUtil.getInteger(event.getString(PLUGIN_ID).split("-")[0]) == DatastoreConstants.PluginId.TRAP_EVENT.getName()))
                    {
                        if (event.containsKey("trap.oid"))
                        {
                            assertEquals(CONTEXT2.getString(SNMP_TRAP_PROFILE_OID), event.getString("trap.oid"));
                        }

                        messageConsumer.unregister(result -> testContext.completeNow());

                    }
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister(result -> testContext.failNow(exception));

                }
            });
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testSNMPv1AuthenticationFailureTrap(VertxTestContext testContext)
    {
        try
        {
            PDU pdu;
            var pdu1 = new PDUv1();
            pdu1.setType(PDU.V1TRAP);
            pdu1.setAgentAddress(new IpAddress("localhost"));     //SET THIS. This is the sender address
            pdu1.setSpecificTrap(5);
            pdu1.setGenericTrap(4);
            pdu = pdu1;
            pdu.add(new VariableBinding(SnmpConstants.sysUpTime));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapCommunity, new OID(CONTEXT2.getString(SNMP_TRAP_PROFILE_OID))));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapAddress, new IpAddress("localhost")));


            TestUtil.vertx().eventBus().send(EVENT_NOTIFICATION, new JsonObject().put(EVENT, CodecUtil.toBytes(pdu)).put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                    .put(TARGET, "localhost/" + 1620)
                    .put(SNMP_TRAP_VERSION, SnmpConstants.version1)
                    .put(NOTIFICATION_TYPE, SNMP_TRAP.getName())
                    .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()));

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
            {
                try
                {
                    var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                    if (event.containsKey(PLUGIN_ID) && (CommonUtil.getInteger(event.getString(PLUGIN_ID).split("-")[0]) == DatastoreConstants.PluginId.TRAP_EVENT.getName()))
                    {
                        if (event.containsKey("trap.oid"))
                        {
                            assertEquals(".*******.*******.5.5", event.getString("trap.oid"));
                        }

                        messageConsumer.unregister(result -> testContext.completeNow());

                    }
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister(result -> testContext.failNow(exception));

                }
            });
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testCreateClearSNMPTrapProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = CONTEXT3.copy();

        TestAPIUtil.post(SNMP_TRAP_PROFILE_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    TestAPIUtil.assertCreateEntityTestResult(SNMPTrapProfileConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, SNMP_TRAP_PROFILE), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();


                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testSNMPv1EnterpriseSpecificTrap(VertxTestContext testContext)
    {
        try
        {
            PDU pdu;
            var pdu1 = new PDUv1();
            pdu1.setType(PDU.V1TRAP);
            pdu1.setAgentAddress(new IpAddress("localhost"));     //SET THIS. This is the sender address
            pdu1.setSpecificTrap(5);
            pdu1.setGenericTrap(6);
            pdu = pdu1;
            pdu.add(new VariableBinding(SnmpConstants.sysUpTime));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapEnterprise, new OID(CONTEXT2.getString(SNMP_TRAP_PROFILE_OID))));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapAddress, new IpAddress("localhost")));


            TestUtil.vertx().eventBus().send(EVENT_NOTIFICATION, new JsonObject().put(EVENT, CodecUtil.toBytes(pdu)).put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                    .put(TARGET, "localhost/" + 1620)
                    .put(SNMP_TRAP_VERSION, SnmpConstants.version1)
                    .put(NOTIFICATION_TYPE, SNMP_TRAP.getName())
                    .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()));

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
            {
                try
                {
                    var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                    if (event.containsKey(PLUGIN_ID) && (CommonUtil.getInteger(event.getString(PLUGIN_ID).split("-")[0]) == DatastoreConstants.PluginId.TRAP_EVENT.getName()))
                    {

                        if (event.containsKey("trap.enterprise.id"))
                        {
                            assertEquals(CONTEXT2.getString(SNMP_TRAP_PROFILE_OID), "." + event.getString("trap.enterprise.id"));
                        }

                        messageConsumer.unregister(result -> testContext.completeNow());
                    }

                }
                catch (Exception exception)
                {
                    messageConsumer.unregister(result -> testContext.failNow(exception));

                }
            });
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testClearSNMPv1Trap(VertxTestContext testContext)
    {
        try
        {
            PDU pdu;
            var pdu1 = new PDUv1();
            pdu1.setType(PDU.V1TRAP);
            pdu1.setAgentAddress(new IpAddress("localhost"));     //SET THIS. This is the sender address
            pdu1.setSpecificTrap(5);
            pdu1.setGenericTrap(0);
            pdu = pdu1;
            pdu.add(new VariableBinding(SnmpConstants.sysUpTime));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapOID, new OID(CONTEXT2.getString(SNMP_TRAP_PROFILE_AUTO_CLEAR_OID))));
            pdu.add(new VariableBinding(SnmpConstants.snmpTrapAddress, new IpAddress("localhost")));


            TestUtil.vertx().eventBus().send(EVENT_NOTIFICATION, new JsonObject().put(EVENT, CodecUtil.toBytes(pdu)).put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                    .put(TARGET, "localhost/" + 1620)
                    .put(SNMP_TRAP_VERSION, SnmpConstants.version1)
                    .put(NOTIFICATION_TYPE, SNMP_TRAP.getName())
                    .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()));

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
            {
                try
                {
                    var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                    if (event.containsKey(PLUGIN_ID) && (CommonUtil.getInteger(event.getString(PLUGIN_ID).split("-")[0]) == DatastoreConstants.PluginId.TRAP_EVENT.getName()))
                    {
                        if (event.containsKey("trap.oid"))
                        {
                            assertEquals(CONTEXT2.getString(SNMP_TRAP_PROFILE_AUTO_CLEAR_OID), event.getString("trap.oid"));
                        }

                        messageConsumer.unregister(result -> testContext.completeNow());

                    }

                }
                catch (Exception exception)
                {
                    messageConsumer.unregister(result -> testContext.failNow(exception));

                }
            });
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testUpdateSNMPv3TrapListenerSettingsNoAuthPrivacy(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.put(SNMP_TRAP_LISTENER_API_ENDPOINT + "/" + CONTEXT.getLong("snmp.trap.listener.v1"), TRAP_LISTENER_CONTEXT2, testContext.succeeding(response -> testContext.verify(() ->
        {

            TestAPIUtil.assertUpdateEntityTestResult(SNMPTrapListenerConfigStore.getStore(), TRAP_LISTENER_CONTEXT2, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, SNMP_TRAP_LISTENER), LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));

    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testForwardSNMPv3TrapNoAuthPrivacy(VertxTestContext testContext)
    {
        //due to backend bug of SNMP_V3_PRIVATE_PASSWORD key in TRAP LISTENER

        try
        {
            // Create PDU for V3
            ScopedPDU pdu3 = new ScopedPDU();
            pdu3.setType(ScopedPDU.TRAP);
            pdu3.setRequestID(new Integer32(1234));
            pdu3.add(new VariableBinding(SnmpConstants.sysUpTime));
            pdu3.add(new VariableBinding(SnmpConstants.snmpTrapOID,
                    new OID(".*******.4.1.644.2.4.0.1040")));

            assertSNMPTrapForwarderTestResult(testContext, SnmpConstants.version3, new JsonObject().put(EVENT, CodecUtil.toBytes(pdu3)).put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                    .put(TARGET, "localhost/" + TRAP_FORWARDER_PORT)
                    .put(SNMP_TRAP_VERSION, SnmpConstants.version3)
                    .put(NOTIFICATION_TYPE, SNMP_TRAP.getName())
                    .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()));

        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testUpdateSNMPv3TrapListenerSettingsAuthPrivacy(VertxTestContext testContext, TestInfo testInfo)
    {

        var context = TRAP_LISTENER_CONTEXT2.copy().put(SNMP_V3_PRIVATE_PASSWORD, "Mind@#123").put(SNMP_V3_PRIVACY_PROTOCOL, AES_128);

        TestAPIUtil.put(SNMP_TRAP_LISTENER_API_ENDPOINT + "/" + TestTrapProcessor.CONTEXT.getLong("snmp.trap.listener.v1"), context, testContext.succeeding(response -> testContext.verify(() ->
        {

            TestAPIUtil.assertUpdateEntityTestResult(SNMPTrapListenerConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, SNMP_TRAP_LISTENER), LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));

    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testForwardSNMPv3TrapAuthPrivacy(VertxTestContext testContext)
    {
        //due to backend bug of SNMP_V3_PRIVATE_PASSWORD key in TRAP LISTENER

        try
        {
            // Create PDU for V3
            ScopedPDU pdu3 = new ScopedPDU();
            pdu3.setType(ScopedPDU.TRAP);
            pdu3.setRequestID(new Integer32(1234));
            pdu3.add(new VariableBinding(SnmpConstants.sysUpTime));
            pdu3.add(new VariableBinding(SnmpConstants.snmpTrapOID,
                    new OID(".*******.4.1.644.2.4.0.1040")));

            assertSNMPTrapForwarderTestResult(testContext, SnmpConstants.version3, new JsonObject().put(EVENT, CodecUtil.toBytes(pdu3)).put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                    .put(TARGET, "localhost/" + TRAP_FORWARDER_PORT)
                    .put(SNMP_TRAP_VERSION, SnmpConstants.version3)
                    .put(NOTIFICATION_TYPE, SNMP_TRAP.getName())
                    .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()));
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testUpdateSNMPv3TrapListenerSettingsNoAuthNoPrivacy(VertxTestContext testContext, TestInfo testInfo)
    {

        var context = new JsonObject()
                .put(SNMP_TRAP_LISTENER_V3_PORT, SNMP_TRAP_V3_LISTENER_PORT)
                .put(SNMP_TRAP_LISTENER_V1_V2_STATUS, NO)
                .put(SNMP_TRAP_LISTENER_V3_STATUS, YES)
                .put(SNMP_V3_SECURITY_USERNAME, "public");

        TestAPIUtil.put(SNMP_TRAP_LISTENER_API_ENDPOINT + "/" + TestTrapProcessor.CONTEXT.getLong("snmp.trap.listener.v1"), context, testContext.succeeding(response -> testContext.verify(() ->
        {

            TestAPIUtil.assertUpdateEntityTestResult(SNMPTrapListenerConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, SNMP_TRAP_LISTENER), LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testGetSNMPTrapForwarder(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(SNMP_TRAP_FORWARDER_API_ENDPOINT + "/" + CONTEXT.getLong("snmp.trap.forwarder"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETRequestTestResult(response, CONTEXT.getLong("snmp.trap.forwarder"), SNMPTrapForwarderConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                    assertEquals("trap-forwarder-auto-clear", response.bodyAsJsonObject().getJsonObject(RESULT).getString(SNMP_TRAP_FORWARDER_NAME));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testGetAllSNMPTrapForwarder(VertxTestContext testContext)
    {
        TestAPIUtil.get(SNMP_TRAP_FORWARDER_API_ENDPOINT, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETAllRequestTestResult(response, SNMPTrapForwarderConfigStore.getStore(), null);

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testGetSNMPTrapProfileReference(VertxTestContext testContext)
    {
        TestAPIUtil.get(SNMP_TRAP_PROFILE_API_ENDPOINT + "/" + CONTEXT.getLong("snmp.trap.profile.auto.clear.v3") + "/references",
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    TestAPIUtil.assertEntityReferenceTestResult(response, APIConstants.Entity.SNMP_TRAP_FORWARDER.getName(), 1);

                    testContext.completeNow();

                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testForwardSNMPv3TrapNoAuthNoPrivacy(VertxTestContext testContext)
    {
        //due to backend bug of SNMP_V3_PRIVATE_PASSWORD key in TRAP LISTENER

        try
        {
            // Create PDU for V3
            ScopedPDU pdu3 = new ScopedPDU();
            pdu3.setType(ScopedPDU.TRAP);
            pdu3.setRequestID(new Integer32(1234));
            pdu3.add(new VariableBinding(SnmpConstants.sysUpTime));
            pdu3.add(new VariableBinding(SnmpConstants.snmpTrapOID,
                    new OID(".*******.4.1.644.2.4.0.1040")));

            assertSNMPTrapForwarderTestResult(testContext, SnmpConstants.version3, new JsonObject().put(EVENT, CodecUtil.toBytes(pdu3)).put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                    .put(TARGET, "localhost/" + TRAP_FORWARDER_PORT)
                    .put(SNMP_TRAP_VERSION, SnmpConstants.version3)
                    .put(NOTIFICATION_TYPE, SNMP_TRAP.getName())
                    .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()));
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }

    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testDeleteSNMPTrapProfileEntityInUsed(VertxTestContext testContext)
    {
        TestAPIUtil.delete(SNMP_TRAP_PROFILE_API_ENDPOINT + "/" + CONTEXT.getLong("snmp.trap.profile.auto.clear.v3"),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    TestAPIUtil.assertEntityInUsedDeleteTestResult(response, SNMP_TRAP_PROFILE);

                    testContext.completeNow();

                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    void testDeleteSNMPTrapForwarder(VertxTestContext testContext)
    {
        TestAPIUtil.delete(SNMP_TRAP_FORWARDER_API_ENDPOINT + "/" + CONTEXT.getLong("snmp.trap.forwarder"), testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    TestAPIUtil.assertDeleteEntityTestResult(SNMPTrapForwarderConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_DELETED, APIConstants.Entity.SNMP_TRAP_FORWARDER.getName()));

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    void testDeleteSNMPTrapForwarderNotExist(VertxTestContext testContext)
    {
        TestAPIUtil.delete(SNMP_TRAP_FORWARDER_API_ENDPOINT + "/" + 5666789, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    TestAPIUtil.assertNotExistEntityDeleteTestResult(response, APIConstants.Entity.SNMP_TRAP_FORWARDER.getName(), String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.SNMP_TRAP_FORWARDER.getName()));

                    testContext.completeNow();

                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(34)
    void testDeleteSNMPTrapProfile(VertxTestContext testContext)
    {
        TestAPIUtil.delete(SNMP_TRAP_PROFILE_API_ENDPOINT + "/" + CONTEXT.getLong("snmp.trap.profile.auto.clear.v3"), testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    TestAPIUtil.assertDeleteEntityTestResult(SNMPTrapProfileConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_DELETED, SNMP_TRAP_PROFILE));

                    testContext.completeNow();

                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testSNMPv3TrapAuthPrivacy(VertxTestContext testContext)
    {
        try
        {
            // Create PDU for V3
            ScopedPDU pdu3 = new ScopedPDU();
            pdu3.setType(ScopedPDU.TRAP);
            pdu3.setRequestID(new Integer32(1234));
            pdu3.add(new VariableBinding(SnmpConstants.sysUpTime));
            pdu3.add(new VariableBinding(SnmpConstants.snmpTrapOID,
                    new OID(".*******.4.1.644.2.4.0.1040")));

            TestUtil.vertx().eventBus().send(EVENT_NOTIFICATION, new JsonObject().put(EVENT, CodecUtil.toBytes(pdu3)).put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                    .put(TARGET, "localhost/" + 1630)
                    .put(SNMP_TRAP_VERSION, SnmpConstants.version3)
                    .put(NOTIFICATION_TYPE, SNMP_TRAP.getName())
                    .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()));

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
            {
                try
                {
                    var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                    if (event.containsKey(PLUGIN_ID) && (CommonUtil.getInteger(event.getString(PLUGIN_ID).split("-")[0]) == DatastoreConstants.PluginId.TRAP_EVENT.getName()))
                    {
                        if (event.containsKey("trap.oid"))
                        {
                            assertEquals(".*******.4.1.644.2.4.0.1040", event.getString("trap.oid"));
                        }

                        messageConsumer.unregister(result -> testContext.completeNow());

                    }
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister(result -> testContext.failNow(exception));

                }
            });
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(35)
    void testUpdateSNMPv3TrapListenerAuthPrivacyAES192MD5(VertxTestContext testContext, TestInfo testInfo)
    {

        var context = TRAP_LISTENER_CONTEXT2.copy().put(SNMP_V3_PRIVATE_PASSWORD, "Mind@#123").put(SNMP_V3_PRIVACY_PROTOCOL, AES_192).put(SNMP_V3_SECURITY_LEVEL, AUTHENTICATION_PRIVACY);

        TestAPIUtil.put(SNMP_TRAP_LISTENER_API_ENDPOINT + "/" + TestTrapProcessor.CONTEXT.getLong("snmp.trap.listener.v1"), context, testContext.succeeding(response -> testContext.verify(() ->
        {

            TestAPIUtil.assertUpdateEntityTestResult(SNMPTrapListenerConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, SNMP_TRAP_LISTENER), LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(36)
    void testSNMPv3TrapAuthPrivacyAES192MD5(VertxTestContext testContext)
    {
        try
        {
            // Create PDU for V3
            ScopedPDU pdu3 = new ScopedPDU();
            pdu3.setType(ScopedPDU.TRAP);
            pdu3.setRequestID(new Integer32(1234));
            pdu3.add(new VariableBinding(SnmpConstants.sysUpTime));
            pdu3.add(new VariableBinding(SnmpConstants.snmpTrapOID,
                    new OID(".*******.4.1.644.2.4.0.1040")));

            TestUtil.vertx().eventBus().send(EVENT_NOTIFICATION, new JsonObject().put(EVENT, CodecUtil.toBytes(pdu3)).put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                    .put(TARGET, "localhost/" + 1630)
                    .put(SNMP_TRAP_VERSION, SnmpConstants.version3)
                    .put(NOTIFICATION_TYPE, SNMP_TRAP.getName())
                    .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()));

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
            {
                try
                {
                    var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                    if (event.containsKey(PLUGIN_ID) && (CommonUtil.getInteger(event.getString(PLUGIN_ID).split("-")[0]) == DatastoreConstants.PluginId.TRAP_EVENT.getName()))
                    {
                        if (event.containsKey("trap.oid"))
                        {
                            assertEquals(".*******.4.1.644.2.4.0.1040", event.getString("trap.oid"));
                        }

                        messageConsumer.unregister(result -> testContext.completeNow());

                    }
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister(result -> testContext.failNow(exception));
                }
            });
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }

    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(37)
    void testUpdateSNMPv3TrapListenerAuthPrivacyAES256MD5(VertxTestContext testContext, TestInfo testInfo)
    {

        var context = TRAP_LISTENER_CONTEXT2.copy().put(SNMP_V3_PRIVATE_PASSWORD, "Mind@#123").put(SNMP_V3_PRIVACY_PROTOCOL, AES_256).put(SNMP_V3_SECURITY_LEVEL, AUTHENTICATION_PRIVACY);

        TestAPIUtil.put(SNMP_TRAP_LISTENER_API_ENDPOINT + "/" + TestTrapProcessor.CONTEXT.getLong("snmp.trap.listener.v1"), context, testContext.succeeding(response -> testContext.verify(() ->
        {

            TestAPIUtil.assertUpdateEntityTestResult(SNMPTrapListenerConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, SNMP_TRAP_LISTENER), LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));

    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(38)
    void testSNMPv3TrapAuthPrivacyAES256MD5(VertxTestContext testContext)
    {
        try
        {
            // Create PDU for V3
            ScopedPDU pdu3 = new ScopedPDU();
            pdu3.setType(ScopedPDU.TRAP);
            pdu3.setRequestID(new Integer32(1234));
            pdu3.add(new VariableBinding(SnmpConstants.sysUpTime));
            pdu3.add(new VariableBinding(SnmpConstants.snmpTrapOID,
                    new OID(".*******.4.1.644.2.4.0.1040")));

            TestUtil.vertx().eventBus().send(EVENT_NOTIFICATION, new JsonObject().put(EVENT, CodecUtil.toBytes(pdu3)).put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                    .put(TARGET, "localhost/" + 1630)
                    .put(SNMP_TRAP_VERSION, SnmpConstants.version3)
                    .put(NOTIFICATION_TYPE, SNMP_TRAP.getName())
                    .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()));

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
            {
                try
                {
                    var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                    if (event.containsKey(PLUGIN_ID) && (CommonUtil.getInteger(event.getString(PLUGIN_ID).split("-")[0]) == DatastoreConstants.PluginId.TRAP_EVENT.getName()))
                    {
                        if (event.containsKey("trap.oid"))
                        {
                            assertEquals(".*******.4.1.644.2.4.0.1040", event.getString("trap.oid"));
                        }

                        messageConsumer.unregister(result -> testContext.completeNow());

                    }
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister(result -> testContext.failNow(exception));
                }
            });
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(39)
    void testAckTrap(VertxTestContext testContext)
    {
        var event = new JsonObject("{\"trap.severity\":\"unknown\",\"trap.name\":\"coldStart\",\"trap.oid\":\".*******.4.1.644.2.4.0.1040\",\"event.source\":\"127.0.0.1\",\"trap.enterprise\":\"\",\"trap.enterprise.id\":\"0\",\"trap.vendor\":\"NXNETWORK\",\"trap.version\":\"v1\",\"trap.type\":\"coldStart\",\"trap.message.count\":44,\"trap.message\":\"A coldStart trap signifies that the SNMP entity, supporting a notification originator application, is reinitializing itself and that its configuration may have been altered.\",\"trap.acknowledged\":\"no\",\"event.timestamp\":1708506771,\"user.id\":10000000000001,\"acknowledge\":\"yes\",\"session-id\":\"9c47848a-c252-49bf-8280-1a9bde94afb5\",\"user.name\":\"admin\"}");

        TestUtil.vertx().eventBus().send(EventBusConstants.UI_ACTION_TRAP_ACKNOWLEDGE, event);

        TestUtil.vertx().setTimer(3 * 1000, handler ->
        {
            Assertions.assertTrue(TrapCacheStore.getStore().getItems().containsKey(event.getString(EVENT_SOURCE) + DASH_SEPARATOR + event.getString(SNMP_TRAP_OID)));

            Assertions.assertTrue(TrapCacheStore.getStore().getItems().get(event.getString(EVENT_SOURCE) + DASH_SEPARATOR + event.getString(SNMP_TRAP_OID)).equalsIgnoreCase(YES));

            testContext.completeNow();
        });
    }
}

