/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray;

import io.vertx.core.json.JsonObject;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

public class TestConstants
{
    public static final String TOPOLOGY_PLUGIN_UNASSIGNED_SUCCEEDED = "Topology Plugin unassigned successfully";
    private static final JsonObject PARAMETERS = new JsonObject();

    static
    {
        try
        {
            if (PARAMETERS.isEmpty())
            {
                var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                        + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "test-parameters.json");

                if (file.exists())
                {
                    PARAMETERS.mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));
                }
            }
        }
        catch (Exception exception)
        {
            System.exit(0);
        }
    }

    public static JsonObject prepareParams(String parameter)
    {
        return new JsonObject().mergeIn(PARAMETERS.getJsonObject(parameter));
    }
}
