/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;

public class MiscConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(MiscConfigStore.class, GlobalConstants.MOTADATA_STORE, "Misc Config Store");

    private static final MiscConfigStore STORE = new MiscConfigStore();

    private MiscConfigStore()
    {
        super(LOGGER);
    }

    public static MiscConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {

        var promise = Promise.<Void>promise();

        try
        {
            Bootstrap.configDBService().getAll(ConfigDBConstants.COLLECTION_TEMPLATE, result ->
            {
                if (result.succeeded())
                {
                    items.clear();

                    if (result.result() != null && !result.result().isEmpty())
                    {
                        for (var index = 0; index < result.result().size(); index++)
                        {
                            var item = result.result().getJsonObject(index);

                            items.put(item.getLong(GlobalConstants.ID), item);

                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("item added %s into the store", CommonUtil.removeSensitiveFields(item, false).encodePrettily()));
                            }
                        }
                    }

                    promise.complete();

                    LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));

                }

                else
                {
                    promise.fail(result.cause());

                    LOGGER.fatal(String.format("failed to init store %s , reason : %s...", this.getClass().getSimpleName(), result.cause()));
                }

            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getMessage());
        }

        return promise.future();
    }

}
