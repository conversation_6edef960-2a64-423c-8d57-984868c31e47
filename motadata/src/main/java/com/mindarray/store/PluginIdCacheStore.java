/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.Metric;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class PluginIdCacheStore extends AbstractCacheStore
{
    private static final Logger LOGGER = new Logger(PluginIdCacheStore.class, GlobalConstants.MOTADATA_STORE, "Plugin Id Cache Store");

    private static final PluginIdCacheStore STORE = new PluginIdCacheStore();

    private final Map<String, Integer> items = new ConcurrentHashMap<>();

    private PluginIdCacheStore()
    {
    }

    public static PluginIdCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        Bootstrap.configDBService().getAll(ConfigDBConstants.COLLECTION_PLUGIN_ID, future ->
        {

            if (future.succeeded())
            {
                for (var index = 0; index < future.result().size(); index++)
                {
                    var item = future.result().getJsonObject(index);

                    items.put(item.getString(NMSConstants.OBJECT), item.getInteger(GlobalConstants.PLUGIN_ID));

                    ObjectManagerCacheStore.getStore().addMetricName(item.getInteger(GlobalConstants.PLUGIN_ID), item.containsKey(Metric.METRIC_NAME) ? item.getString(Metric.METRIC_NAME) : item.getString(NMSConstants.OBJECT).split(GlobalConstants.SEPARATOR)[0].trim());//as per backward compability handling
                }

                promise.complete();

                LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
            }
            else
            {
                LOGGER.error(future.cause());

                promise.fail(future.cause());
            }
        });

        return promise.future();
    }

    public void addItem(String key, int pluginId, String metricName)
    {
        items.putIfAbsent(key, pluginId);

        ObjectManagerCacheStore.getStore().addMetricName(pluginId, metricName);

        Bootstrap.configDBService().save(ConfigDBConstants.COLLECTION_PLUGIN_ID,
                new JsonObject().put(NMSConstants.OBJECT, key).put(GlobalConstants.PLUGIN_ID, pluginId).put(Metric.METRIC_NAME, metricName),
                GlobalConstants.DEFAULT_USER, GlobalConstants.SYSTEM_REMOTE_ADDRESS,
                result ->
                {
                });
    }

    public Integer getPluginId(String key)
    {
        return items.getOrDefault(key, GlobalConstants.NOT_AVAILABLE);
    }

    public Integer generateNextPluginId()
    {
        return items.values().stream().mapToInt(id -> id).max().orElse(500) + 1;
    }
}
