/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Discovery;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.mindarray.api.APIConstants.*;
import static com.mindarray.eventbus.EventBusConstants.EVENT_DATASTORE_REGISTER;

public class RemoteEventProcessorConfigStore extends AbstractConfigStore
{

    private static final Logger LOGGER = new Logger(RemoteEventProcessorConfigStore.class, GlobalConstants.MOTADATA_STORE, "Remote Event Processor Config Store");
    private static final RemoteEventProcessorConfigStore STORE = new RemoteEventProcessorConfigStore();
    private final Map<String, Long> itemsByUUID = new ConcurrentHashMap<>();
    private final Map<GlobalConstants.InstallationMode, Long> datastores = new ConcurrentHashMap<>();                         // separate map because we will query frequently
    private final Map<String, String> itemTypes = new ConcurrentHashMap<>();

    private RemoteEventProcessorConfigStore()
    {

        super(ConfigDBConstants.COLLECTION_REMOTE_EVENT_PROCESSOR, LOGGER, false, List.of(
                Map.of(
                        REFERENCE_ENTITY, APIConstants.Entity.DISCOVERY,
                        REFERENCE_ENTITY_PROPERTY, Discovery.DISCOVERY_EVENT_PROCESSORS,
                        REFERENCE_ENTITY_STORE, ConfigStore.DISCOVERY,
                        REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTI_VALUE
                ),
                Map.of(
                        REFERENCE_ENTITY, APIConstants.Entity.OBJECT,
                        REFERENCE_ENTITY_PROPERTY, AIOpsObject.OBJECT_EVENT_PROCESSORS,
                        REFERENCE_ENTITY_STORE, ConfigStore.OBJECT,
                        REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTI_VALUE
                )
        ));
    }

    public static RemoteEventProcessorConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> addItem(long id)
    {
        var promise = Promise.<Void>promise();

        super.addItem(id).onComplete(result ->
        {
            if (result.succeeded())
            {
                var item = getItem(id);

                itemsByUUID.put(item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID).concat(GlobalConstants.VALUE_SEPARATOR).concat(item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE))
                        .concat(GlobalConstants.VALUE_SEPARATOR)
                        .concat(item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE)), id);

                itemTypes.put(item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP), item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE));

                if (item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(GlobalConstants.BootstrapType.DATASTORE.name()))
                {
                    datastores.put(GlobalConstants.InstallationMode.valueOf(item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE)), id);
                }
            }

            promise.complete();
        });

        return promise.future();
    }

    @Override
    public void deleteItem(long id)
    {
        if (items.containsKey(id))
        {
            super.deleteItem(id);

            RemoteEventProcessorCacheStore.getStore().deleteItem(id);

            init();
        }

    }

    @Override
    public void deleteItems(JsonArray ids)
    {
        super.deleteItems(ids);

        init();
    }

    public Long getItemByMode(GlobalConstants.InstallationMode installationMode)
    {
        return datastores.getOrDefault(installationMode, null);
    }

    public Long getItemIdByUUID(String uuid, String remoteEventProcessorType, String installationMode)
    {
        if (remoteEventProcessorType == null)
        {
            remoteEventProcessorType = GlobalConstants.BootstrapType.COLLECTOR.name();
        }

        if (installationMode == null)
        {
            installationMode = GlobalConstants.InstallationMode.STANDALONE.name();
        }

        return itemsByUUID.get(uuid.concat(GlobalConstants.VALUE_SEPARATOR).concat(remoteEventProcessorType.toUpperCase()).concat(GlobalConstants.VALUE_SEPARATOR).concat(installationMode.toUpperCase()));
    }

    public String getItemType(String ip)
    {
        return itemTypes.getOrDefault(ip, null);
    }

    private void init()
    {
        try
        {
            itemsByUUID.clear();

            itemTypes.clear();

            items.values().stream().filter(item -> item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID) != null)
                    .collect(Collectors.groupingBy(item ->
                                    item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID).concat(GlobalConstants.VALUE_SEPARATOR)
                                            .concat(item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE))
                                            .concat(GlobalConstants.VALUE_SEPARATOR).concat(item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, GlobalConstants.InstallationMode.STANDALONE.name())),
                            Collectors.mapping(item -> item.getLong(GlobalConstants.ID), Collectors.toList())))
                    .forEach((key, value) -> itemsByUUID.put(key, value.getFirst()));

            items.values().stream().filter(item -> item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP) != null)
                    .collect(Collectors.groupingBy(item -> item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP),
                            Collectors.mapping(item -> item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE), Collectors.toList())))
                    .forEach((key, value) -> itemTypes.put(key, value.getFirst()));

            for (var entry : items.entrySet())
            {
                if (entry.getValue().getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(GlobalConstants.BootstrapType.DATASTORE.name()))
                {
                    datastores.put(GlobalConstants.InstallationMode.valueOf(entry.getValue().getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE)), CommonUtil.getLong(entry.getKey()));

                    Bootstrap.vertx().eventBus().send(EVENT_DATASTORE_REGISTER, entry.getValue());
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    public Future<Void> initStore()
    {

        var promise = Promise.<Void>promise();

        super.initStore().onComplete(result ->
        {
            if (result.succeeded())
            {
                try
                {
                    init();

                    promise.complete();

                    LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
                }

                catch (Exception exception)
                {
                    promise.fail(exception);

                    LOGGER.warn("failed to init remote event processor config store...");

                    LOGGER.error(exception);
                }
            }

            else
            {
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

}
