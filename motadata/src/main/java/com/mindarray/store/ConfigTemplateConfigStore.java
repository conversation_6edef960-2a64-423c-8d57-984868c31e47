/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.ConfigTemplate;
import com.mindarray.api.Configuration;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;

import java.util.List;
import java.util.Map;

import static com.mindarray.api.APIConstants.*;

public class ConfigTemplateConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(ConfigTemplateConfigStore.class, GlobalConstants.MOTADATA_STORE, "Config Template Config Store");

    private static final ConfigTemplateConfigStore STORE = new ConfigTemplateConfigStore();

    private ConfigTemplateConfigStore()
    {
        super(ConfigDBConstants.COLLECTION_CONFIG_TEMPLATE, LOGGER, false,
                List.of(
                        Map.of(
                                REFERENCE_ENTITY, Entity.CONFIGURATION,
                                REFERENCE_ENTITY_STORE, ConfigStore.CONFIGURATION,
                                REFERENCE_ENTITY_PROPERTY, Configuration.CONFIG_TEMPLATE,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.VALUE
                        )
                )
        );
    }

    public static ConfigTemplateConfigStore getStore()
    {
        return STORE;
    }

    /*
     * @param catalog ID
     * @return JsonArray of Config Template using catalog ID
     */
    public JsonArray getConfigTemplates(Long deviceCatalogId)
    {
        return getStore().getItemsByMultiValueField(ConfigTemplate.CONFIG_TEMPLATE_CATALOG_IDS, deviceCatalogId);
    }
}
