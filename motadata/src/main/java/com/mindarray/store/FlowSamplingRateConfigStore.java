/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.flow.FlowEngineConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class FlowSamplingRateConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(FlowSamplingRateConfigStore.class, GlobalConstants.MOTADATA_STORE, "Flow Sampling Rate Config Store");

    private static final FlowSamplingRateConfigStore STORE = new FlowSamplingRateConfigStore();

    private final Map<String, Integer> samplingRates = new ConcurrentHashMap<>();

    private FlowSamplingRateConfigStore()
    {
        super(ConfigDBConstants.COLLECTION_FLOW_SAMPLING_RATE, LOGGER, true);
    }

    public static FlowSamplingRateConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        if (GlobalConstants.BootstrapType.COLLECTOR == Bootstrap.bootstrapType())
        {
            promise.fail("remote event processor can not init store... it must be master or default boot sequence...");
        }
        else
        {
            Bootstrap.configDBService().getAll(ConfigDBConstants.COLLECTION_FLOW_SAMPLING_RATE, result ->
            {
                if (result.succeeded())
                {
                    for (var index = 0; index < result.result().size(); index++)
                    {
                        var item = result.result().getJsonObject(index);

                        this.items.put(item.getLong(GlobalConstants.ID), item);

                        if (item.getInteger(FlowEngineConstants.INTERFACE_CUSTOM_SAMPLING_RATE) != null)
                        {
                            samplingRates.put(item.getString(EventBusConstants.EVENT_SOURCE) + GlobalConstants.SEPARATOR + item.getString(NMSConstants.INTERFACE_INDEX), item.getInteger(FlowEngineConstants.INTERFACE_CUSTOM_SAMPLING_RATE));
                        }

                        if (CommonUtil.debugEnabled())
                        {
                            LOGGER.debug(String.format("item added %s into the store", CommonUtil.removeSensitiveFields(item, false).encodePrettily()));
                        }
                    }

                    promise.complete();

                    LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
                }
                else
                {
                    LOGGER.fatal(String.format("failed to init store %s , reason : %s...", this.getClass().getSimpleName(), result.cause()));

                    promise.fail(result.cause());
                }
            });
        }

        return promise.future();
    }


    @Override
    public Future<Void> updateItem(long id)
    {
        return super.updateItem(id).onComplete(result ->
        {
            var item = items.get(id);

            if (item.getInteger(FlowEngineConstants.INTERFACE_CUSTOM_SAMPLING_RATE) != null)
            {
                samplingRates.put(item.getString(EventBusConstants.EVENT_SOURCE) + GlobalConstants.SEPARATOR + item.getString(NMSConstants.INTERFACE_INDEX), item.getInteger(FlowEngineConstants.INTERFACE_CUSTOM_SAMPLING_RATE));
            }
        });
    }

    @Override
    public void addItem(long id, JsonObject item)
    {
        if (item != null)
        {
            this.items.put(id, item);

            if (item.getInteger(FlowEngineConstants.INTERFACE_CUSTOM_SAMPLING_RATE) != null)
            {
                samplingRates.put(item.getString(EventBusConstants.EVENT_SOURCE) + GlobalConstants.SEPARATOR + item.getString(NMSConstants.INTERFACE_INDEX), item.getInteger(FlowEngineConstants.INTERFACE_CUSTOM_SAMPLING_RATE));
            }
        }

    }

    public Integer getSamplingRate(String key)
    {
        return samplingRates.getOrDefault(key, null);
    }
}
