/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.APIUtil;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;

import java.net.InetAddress;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.api.FlowIPGroup.FLOW_IP_GROUP;
import static com.mindarray.api.FlowIPGroup.FLOW_IP_GROUP_NAME;

public class FlowIPGroupConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(FlowIPGroupConfigStore.class, GlobalConstants.MOTADATA_STORE, "Flow IP Group Config Store");

    private static final FlowIPGroupConfigStore STORE = new FlowIPGroupConfigStore();

    private final Map<String, String> domains = new ConcurrentHashMap<>();

    private FlowIPGroupConfigStore()
    {
        super(ConfigDBConstants.COLLECTION_FLOW_IP_GROUP, LOGGER, false);
    }

    public static FlowIPGroupConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        try
        {
            Bootstrap.configDBService().getAll(ConfigDBConstants.COLLECTION_FLOW_IP_GROUP, result ->
            {
                if (result.succeeded())
                {
                    items.clear();

                    domains.clear();

                    if (result.result() != null && !result.result().isEmpty())
                    {
                        for (var index = 0; index < result.result().size(); index++)
                        {
                            var item = result.result().getJsonObject(index);

                            items.put(item.getLong(GlobalConstants.ID), item);

                            update(item.getLong(GlobalConstants.ID), true);
                        }
                    }

                    promise.complete();

                    LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
                }
                else
                {
                    promise.fail(result.cause());

                    LOGGER.fatal(String.format("failed to init store %s , reason : %s...", this.getClass().getSimpleName(), result.cause()));
                }

            });
        }
        catch (Exception exception)
        {
            promise.fail(exception);

            LOGGER.error(exception);
        }

        return promise.future();
    }

    public String getDomain(String ipAddress)
    {
        return domains.get(ipAddress);
    }

    @Override
    public Future<Void> addItem(long id)
    {
        return super.addItem(id).onComplete(result ->
        {
            if (result.succeeded())
            {
                update(id, true);
            }
        });
    }

    @Override
    public Future<Void> updateItem(long id)
    {
        update(id, false);        // delete item first from Cache as we don't know what user has deleted from range

        return super.updateItem(id).onComplete(result ->
        {
            if (result.succeeded())
            {
                update(id, true);
            }
        });
    }

    @Override
    public void deleteItem(long id)
    {
        update(id, false);

        super.deleteItem(id);

    }

    @Override
    public void deleteItems(JsonArray ids)
    {
        if (ids != null && !ids.isEmpty())
        {
            for (var index = 0; index < ids.size(); index++)
            {
                this.deleteItem(ids.getLong(index));
            }
        }
    }

    /**
     * update cache
     * if IP update directly
     * if Range - Calculate Range and update
     *
     * @param id
     */
    private void update(long id, boolean add)
    {
        try
        {
            var item = items.get(id);

            if (item != null)
            {
                var groups = item.getJsonArray(FLOW_IP_GROUP);

                for (var index = 0; index < groups.size(); index++)
                {
                    var ipGroup = groups.getString(index);

                    if (ipGroup.contains("-"))
                    {
                        var tokens = ipGroup.split("-");

                        var start = tokens[0].trim();

                        if (tokens[1].trim().matches("\\d+"))//in case user provides range like ***********-55
                        {
                            update(start, start.substring(0, start.lastIndexOf('.')) + "." + tokens[1].trim(), item.getString(FLOW_IP_GROUP_NAME), add);
                        }
                        else
                        {
                            //in case user provides range like ***********-*********** or xx.xx.10.1-xx.1.2.5
                            update(start, ipGroup.split("-")[1].trim(), item.getString(FLOW_IP_GROUP_NAME), add);
                        }
                    }
                    else if (ipGroup.contains("xx"))
                    {
                        update(ipGroup, ipGroup, item.getString(FLOW_IP_GROUP_NAME), add);
                    }
                    else
                    {
                        if (add)
                        {
                            domains.put(ipGroup, item.getString(FLOW_IP_GROUP_NAME));
                        }
                        else
                        {
                            domains.remove(ipGroup);
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void update(String start, String end, String ipGroupName, boolean add)
    {
        try
        {
            if (start.contains("xx"))
            {
                start = start.replaceAll("xx", "0");
            }
            if (end.contains("xx"))
            {
                end = end.replaceAll("xx", "255");
            }

            if (APIUtil.validateRange(start, end))
            {
                if (add)
                {
                    CommonUtil.calculateIPRange(InetAddress.getByName(start), InetAddress.getByName(end)).forEach(ip -> domains.put(ip, ipGroupName));
                }
                else
                {
                    CommonUtil.calculateIPRange(InetAddress.getByName(start), InetAddress.getByName(end)).forEach(domains::remove);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

}
