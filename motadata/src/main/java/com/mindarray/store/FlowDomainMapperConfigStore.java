/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *     Change Logs:
 *     Date            Author         Notes
 *     2-Apr-2025      Bharat         MOTADATA-5637: Domain Mapping in Flow - Initial Version
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.FlowDomainMapper;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class FlowDomainMapperConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(FlowDomainMapperConfigStore.class, GlobalConstants.MOTADATA_STORE, "Flow Domain Mapper Config Store");

    private static final FlowDomainMapperConfigStore STORE = new FlowDomainMapperConfigStore();

    private final Map<String, String> mappings = new ConcurrentHashMap<>();  //{"127.0.0.1" : yahoo.com}

    private FlowDomainMapperConfigStore()
    {
        super(ConfigDBConstants.COLLECTION_FLOW_DOMAIN_MAPPER, LOGGER, true);
    }

    public static FlowDomainMapperConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        try
        {
            Bootstrap.configDBService().getAll(ConfigDBConstants.COLLECTION_FLOW_DOMAIN_MAPPER, result ->
            {
                if (result.succeeded())
                {
                    items.clear();

                    mappings.clear();

                    if (result.result() != null && !result.result().isEmpty())
                    {
                        for (var index = 0; index < result.result().size(); index++)
                        {
                            var item = result.result().getJsonObject(index);

                            items.put(item.getLong(GlobalConstants.ID), item);

                            update(item.getLong(GlobalConstants.ID), true);
                        }
                    }

                    promise.complete();

                    LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
                }
                else
                {
                    promise.fail(result.cause());

                    LOGGER.fatal(String.format("failed to init store %s , reason : %s...", this.getClass().getSimpleName(), result.cause()));
                }

            });
        }
        catch (Exception exception)
        {
            promise.fail(exception);

            LOGGER.error(exception);
        }

        return promise.future();
    }

    @Override
    public Future<Void> addItem(long id)
    {
        return super.addItem(id).onComplete(result ->
        {
            if (result.succeeded())
            {
                update(id, true);
            }
        });
    }

    @Override
    public void addItem(long id, JsonObject item)
    {
        if (item != null)
        {
            this.items.put(id, item);

            update(id, true);
        }
    }

    @Override
    public Future<Void> updateItem(long id)
    {
        update(id, false);        // delete item first from Cache as we don't know what user has deleted from range

        return super.updateItem(id).onComplete(result ->
        {
            if (result.succeeded())
            {
                update(id, true);
            }
        });
    }

    @Override
    public void deleteItem(long id)
    {
        update(id, false);

        super.deleteItem(id);

    }

    @Override
    public void deleteItems(JsonArray ids)
    {
        if (ids != null && !ids.isEmpty())
        {
            for (var index = 0; index < ids.size(); index++)
            {
                this.deleteItem(ids.getLong(index));
            }
        }
    }

    /**
     * update cache
     * if IP update directly
     *
     * @param id
     */
    private void update(long id, boolean add)
    {
        try
        {
            var item = items.get(id);

            if (item != null)
            {
                var ipAddresses = item.getJsonArray(FlowDomainMapper.FLOW_DOMAIN_MAPPER_GROUP);

                for (var index = 0; index < ipAddresses.size(); index++)
                {
                    var ipAddress = ipAddresses.getString(index);

                    if (add)
                    {
                        mappings.put(ipAddress, item.getString(FlowDomainMapper.FLOW_DOMAIN_MAPPER_NAME));
                    }
                    else
                    {
                        mappings.remove(ipAddress);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /*
        Set Domain Name in flow as per Domain Mapping.
     */
    public void setDomain(String ip, JsonObject flow, String flowType)
    {

        var mapping = mappings.get(ip);

        if (mapping != null)
        {
            flow.put(flowType + ".domain", mapping);  //name
        }
    }

}
