/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.ConfigTemplate;
import com.mindarray.api.SNMPDeviceCatalog;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.util.Logger;

import java.util.List;
import java.util.Map;

import static com.mindarray.api.APIConstants.*;

public class SNMPDeviceCatalogConfigStore extends AbstractConfigStore
{

    private static final Logger LOGGER = new Logger(SNMPDeviceCatalogConfigStore.class, GlobalConstants.MOTADATA_STORE, "SNMP Device Catalog Config Store");

    private static final SNMPDeviceCatalogConfigStore STORE = new SNMPDeviceCatalogConfigStore();

    private SNMPDeviceCatalogConfigStore()
    {
        super(ConfigDBConstants.COLLECTION_SNMP_DEVICE_CATALOG, LOGGER, false, List.of(
                Map.of(
                        REFERENCE_ENTITY, APIConstants.Entity.OBJECT,
                        REFERENCE_ENTITY_PROPERTY, AIOpsObject.OBJECT_SNMP_DEVICE_CATALOG,
                        REFERENCE_ENTITY_STORE, ConfigStore.OBJECT,
                        REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.VALUE
                ),
                Map.of(
                        REFERENCE_ENTITY, Entity.CONFIG_TEMPLATE,
                        REFERENCE_ENTITY_PROPERTY, ConfigTemplate.CONFIG_TEMPLATE_CATALOG_IDS,
                        REFERENCE_ENTITY_STORE, ConfigStore.CONFIG_TEMPLATE,
                        REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTI_VALUE
                )
        ));
    }

    public static SNMPDeviceCatalogConfigStore getStore()
    {
        return STORE;
    }

    public String getSNMPDeviceType(String oid)
    {
        return items.values().parallelStream().filter(item -> item.getString(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_OID) != null && item.getString(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_OID).equalsIgnoreCase(oid))
                .map(item -> item.getString(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_TYPE))
                .findFirst().orElse(null);
    }
}
