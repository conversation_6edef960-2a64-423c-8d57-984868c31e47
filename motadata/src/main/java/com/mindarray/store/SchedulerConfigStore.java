/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.Scheduler;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;

import java.util.stream.Collectors;

public class SchedulerConfigStore extends AbstractConfigStore
{

    private static final Logger LOGGER = new Logger(SchedulerConfigStore.class, GlobalConstants.MOTADATA_STORE, "Scheduler Config Store");

    private static final SchedulerConfigStore STORE = new SchedulerConfigStore();

    private SchedulerConfigStore()
    {
        super(ConfigDBConstants.COLLECTION_SCHEDULER, LOGGER, false);
    }

    public static SchedulerConfigStore getStore()
    {
        return STORE;
    }

    //Will return object ids like discovery/scheduler etc
    public JsonArray getSchedulerObjects(String key, String field, JsonArray value, String jobType)
    {
        return new JsonArray(items.values().stream()
                .filter(item -> item.getJsonObject(key) != null && item.getString(Scheduler.SCHEDULER_JOB_TYPE).equalsIgnoreCase(jobType) && item.getJsonObject(key).getJsonArray(field) != null && !item.getJsonObject(key).getJsonArray(field).isEmpty() && value.contains(item.getJsonObject(key).getJsonArray(field).getLong(0)))
                .map(item -> item.getJsonObject(key).getJsonArray(field).getLong(0))
                .distinct().collect(Collectors.toList()));
    }

    //will return scheduler ids
    public JsonArray getItems(String key, String field, JsonArray value, String jobType)
    {

        return new JsonArray(items.values().stream()
                .filter(item -> item.getJsonObject(key) != null && item.getString(Scheduler.SCHEDULER_JOB_TYPE).equalsIgnoreCase(jobType) && item.getJsonObject(key).getJsonArray(field) != null && !item.getJsonObject(key).getJsonArray(field).isEmpty() && value.contains(item.getJsonObject(key).getJsonArray(field).getLong(0)))
                .map(item -> item.getLong(GlobalConstants.ID))
                .distinct().collect(Collectors.toList()));

    }
}
