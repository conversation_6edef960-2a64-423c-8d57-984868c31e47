/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*  Change Logs:
 *  Date			Author		    Notes
 *  23-Apr-2025		sankalp		    MOTADATA-4883 : Added Method to get qualified groups based on user groups
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mindarray.api.APIConstants.*;

public class GroupConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(GroupConfigStore.class, GlobalConstants.MOTADATA_STORE, "Group Config Store");

    private static final GroupConfigStore STORE = new GroupConfigStore();

    private GroupConfigStore()
    {
        super(ConfigDBConstants.COLLECTION_GROUP, LOGGER, false,
                List.of(
                        Map.of(
                                REFERENCE_ENTITY, APIConstants.Entity.OBJECT,
                                REFERENCE_ENTITY_PROPERTY, AIOpsObject.OBJECT_GROUPS,
                                REFERENCE_ENTITY_STORE, ConfigStore.OBJECT,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTI_VALUE
                        ),
                        Map.of(
                                REFERENCE_ENTITY, Entity.DISCOVERY,
                                REFERENCE_ENTITY_PROPERTY, Discovery.DISCOVERY_GROUPS,
                                REFERENCE_ENTITY_STORE, ConfigStore.DISCOVERY,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTI_VALUE
                        ),
                        Map.of(
                                REFERENCE_ENTITY, Entity.USER,
                                REFERENCE_ENTITY_PROPERTY, User.USER_GROUPS,
                                REFERENCE_ENTITY_STORE, ConfigStore.USER,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTI_VALUE
                        ),
                        Map.of(
                                REFERENCE_ENTITY, Entity.METRIC_PLUGIN,
                                REFERENCE_ENTITY_PROPERTY, MetricPlugin.METRIC_PLUGIN_ENTITIES,
                                REFERENCE_ENTITY_STORE, ConfigStore.METRIC_PLUGIN,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTI_VALUE
                        ),
                        Map.of(
                                REFERENCE_ENTITY, Entity.RUNBOOK_PLUGIN,
                                REFERENCE_ENTITY_PROPERTY, RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES,
                                REFERENCE_ENTITY_STORE, ConfigStore.RUNBOOK_PLUGIN,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTI_VALUE
                        ),
                        Map.of(
                                REFERENCE_ENTITY, Entity.TOPOLOGY_PLUGIN,
                                REFERENCE_ENTITY_PROPERTY, TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES,
                                REFERENCE_ENTITY_STORE, ConfigStore.TOPOLOGY_PLUGIN,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTI_VALUE
                        ),
                        Map.of(
                                REFERENCE_ENTITY, Entity.METRIC_POLICY,
                                REFERENCE_ENTITY_PROPERTY, PolicyEngineConstants.POLICY_CONTEXT,
                                REFERENCE_ENTITY_NESTED_PROPERTY, GlobalConstants.ENTITIES,
                                REFERENCE_ENTITY_STORE, ConfigStore.METRIC_POLICY,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTIMAP_VALUE
                        ),
                        Map.of(
                                REFERENCE_ENTITY, Entity.EVENT_POLICY,
                                REFERENCE_ENTITY_PROPERTY, PolicyEngineConstants.POLICY_CONTEXT,
                                REFERENCE_ENTITY_NESTED_PROPERTY, GlobalConstants.ENTITIES,
                                REFERENCE_ENTITY_STORE, ConfigStore.EVENT_POLICY,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTIMAP_VALUE
                        )
                )
        );

    }

    public static GroupConfigStore getStore()
    {
        return STORE;
    }

    public JsonArray getChildren(String field, long value)
    {

        var groups = new JsonArray(items.values().stream()
                .filter(item -> item.getLong(field) == value)
                .map(item -> item.getLong(GlobalConstants.ID))
                .distinct().collect(Collectors.toList()));

        return !groups.isEmpty() ? groups.addAll(new JsonArray(items.values().stream().filter(item -> groups.contains(item.getLong(field))).map(item -> item.getLong(GlobalConstants.ID))
                .distinct().collect(Collectors.toList()))) : new JsonArray();
    }

    public JsonArray getItemsByGroups(JsonArray groups)
    {
        return new JsonArray(items.values().stream()
                .filter(item -> groups.contains(item.getLong(GlobalConstants.ID)))
                .map(JsonObject::copy)
                .collect(Collectors.toList()));
    }

    @Override
    public JsonArray getItems(JsonArray ids)
    {
        var items = new JsonArray();

        if (ids != null && !ids.isEmpty())
        {
            items.addAll(new JsonArray(ids.stream().filter(this.items::containsKey).map(id -> this.items.get(id).copy()).collect(Collectors.toList())));

        }

        return items;
    }

    /**
     * Compares the groups attached to the entity with the user's groups,
     * and returns array of groups that are common to both the entity and the user.
     */
    public JsonArray getQualifiedGroups(JsonArray groups, JsonArray userGroups)
    {
        var items = new JsonArray();

        try
        {
            for (var group : userGroups)
            {
                if (groups.contains(group))
                {
                    items.add(group);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return items;
    }
}
