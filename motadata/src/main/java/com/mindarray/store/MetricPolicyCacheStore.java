/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	16-Mar-2025		Pruthviraj		    get severity count method added for multiple entities
*/


package com.mindarray.store;


import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Metric;
import com.mindarray.api.MetricPolicy;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.aiops.AIOpsConstants.CORRELATION_PROBE;
import static com.mindarray.api.AIOpsObject.*;
import static com.mindarray.api.APIConstants.ENTITY_ID;

public class MetricPolicyCacheStore extends AbstractCacheStore
{
    private static final MetricPolicyCacheStore STORE = new MetricPolicyCacheStore();

    private static final Logger LOGGER = new Logger(MetricPolicyCacheStore.class, GlobalConstants.MOTADATA_STORE, "Metric Policy Cache Store");
    private final Map<String, Integer> policyKeys = new ConcurrentHashMap<>();//containing policy keys with its hashcodes
    private final Map<Integer, String> policyKeyHashCodes = new ConcurrentHashMap<>();// containing policy keys against hashcode opposite of previous one to get key value
    private final Map<Long, String> policyKeysByObject = new ConcurrentHashMap<>(); //unique policy keys or policy keys against monitor id or entityid
    private final Map<Integer, String> objectCategories = new ConcurrentHashMap<>(); //policy key hashcode with its objectcategory
    private final Map<Integer, Long> timestamps = new ConcurrentHashMap<>();//policy key hashcode with its policy triggered time
    private final Map<Integer, Integer> plugins = new ConcurrentHashMap<>();//policy key hashcode with its policy triggered pluginId
    private final Map<Integer, Long> policyIds = new ConcurrentHashMap<>();//policy key hashcode with its policy triggered policyId
    private final Map<Integer, Severity> severities = new ConcurrentHashMap<>(); //policy key hashcode with its policy triggered severity
    private final Map<Integer, String> values = new ConcurrentHashMap<>();//policy key hashcode with its policy triggered value
    private final Map<Integer, String> metricTypes = new ConcurrentHashMap<>();//policy key hashcode with its policy triggered metrictype only if its metric type or application qualified for application severity
    private final Map<Integer, String> correlatedPolicies = new ConcurrentHashMap<>();//policy key hashcode with its policy triggered metrictype only if its metric type or application qualified for application severity
    private final Map<Integer, String> policyThresholds = new ConcurrentHashMap<>();//policy key hashcode with its policy triggered threshold value
    private final Map<Long, String> objects = new ConcurrentHashMap<>();
    private final Map<String, String> apps = new ConcurrentHashMap<>();
    private final Map<String, String> instances = new ConcurrentHashMap<>();
    private final Map<Long, Integer> warningSeverities = new ConcurrentHashMap<>();
    private final Map<Long, Integer> criticalSeverities = new ConcurrentHashMap<>();
    private final Map<Long, Integer> clearSeverities = new ConcurrentHashMap<>();
    private final Map<Long, Integer> downSeverities = new ConcurrentHashMap<>();
    private final Map<Long, Integer> majorSeverities = new ConcurrentHashMap<>();
    private final Map<Long, Integer> unreachableSeverities = new ConcurrentHashMap<>();

    private MetricPolicyCacheStore()
    {
    }

    public static MetricPolicyCacheStore getStore()
    {
        return STORE;
    }

    public void updateItem(int hashCode, JsonObject context)
    {
        try
        {
            if (policyKeys.getOrDefault(context.getString(PolicyEngineConstants.POLICY_KEY), null) == null)
            {
                policyKeys.put(context.getString(PolicyEngineConstants.POLICY_KEY), hashCode);

                objectCategories.put(hashCode, context.getString(AIOpsObject.OBJECT_CATEGORY));

                plugins.put(hashCode, context.getInteger(GlobalConstants.PLUGIN_ID));

                policyKeyHashCodes.put(hashCode, context.getString(PolicyEngineConstants.POLICY_KEY));
            }

            if (context.containsKey(CORRELATION_PROBE) && context.getString(CORRELATION_PROBE).equalsIgnoreCase(YES))
            {
                if (context.getJsonArray(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS) != null)
                {
                    correlatedPolicies.put(hashCode, StringUtils.join(context.getJsonArray(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS), GlobalConstants.COMMA_SEPARATOR));
                }
            }
            else
            {

                correlatedPolicies.remove(hashCode);
            }

            var keys = policyKeysByObject.getOrDefault(context.getLong(ENTITY_ID), null);

            var items = new HashSet<String>();

            items.add(CommonUtil.getString(hashCode));

            if (keys != null && !keys.isEmpty())
            {
                items.addAll(Arrays.asList(keys.split(GlobalConstants.COMMA_SEPARATOR)));

                policyKeysByObject.put(context.getLong(ENTITY_ID), StringUtils.join(items, GlobalConstants.COMMA_SEPARATOR));
            }

            else
            {
                policyKeysByObject.put(context.getLong(ENTITY_ID), CommonUtil.getString(hashCode));
            }

            var severity = severities.getOrDefault(hashCode, Severity.UNKNOWN);

            policyIds.put(hashCode, context.getLong(ID));

            severities.put(hashCode, Severity.valueOf(context.getString(GlobalConstants.SEVERITY)));

            values.put(hashCode, CommonUtil.getString(context.getValue(VALUE)));

            timestamps.put(hashCode, context.getLong(EventBusConstants.EVENT_TIMESTAMP));

            policyThresholds.put(hashCode, CommonUtil.getString(context.getValue(MetricPolicy.POLICY_THRESHOLD, EMPTY_VALUE)));

            if (!severity.equals(Severity.valueOf(context.getString(GlobalConstants.SEVERITY))))
            {
                update(hashCode, context, items);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void update(int hashCode, JsonObject context, Set<String> items)
    {
        try
        {

            var severities = new TreeSet<Severity>();

            TreeSet<Severity> apps = null;

            TreeSet<Severity> instances = null;

            var entityId = context.getLong(ENTITY_ID);

            var policyKey = context.getString(PolicyEngineConstants.POLICY_KEY);

            var instance = EMPTY_VALUE;

            var app = EMPTY_VALUE;

            if (policyKey != null && policyKey.split(SEPARATOR_WITH_ESCAPE).length > 3)
            {
                var tokens = policyKey.split(SEPARATOR_WITH_ESCAPE);

                instance = tokens[0] + SEPARATOR + tokens[3];

                instances = new TreeSet<>();
            }

            if (NMSConstants.APPLICATION_TYPES.contains(context.getString(Metric.METRIC_TYPE)))
            {
                apps = new TreeSet<>();

                metricTypes.put(hashCode, entityId + GlobalConstants.SEPARATOR + context.getString(Metric.METRIC_TYPE));

                app = entityId + GlobalConstants.SEPARATOR + context.getString(Metric.METRIC_TYPE);
            }

            var clearSeverities = 0;

            var criticalSeverities = 0;

            var downSeverities = 0;

            var majorSeverities = 0;

            var unreachableSeverities = 0;

            var warningSeverities = 0;

            for (var item : items)
            {
                if (!item.isEmpty())
                {
                    var policyHashCode = CommonUtil.getInteger(item);

                    var severity = this.severities.get(policyHashCode);

                    policyKey = policyKeyHashCodes.get(policyHashCode);

                    if (policyKey != null)
                    {
                        var tokens = policyKey.split(SEPARATOR_WITH_ESCAPE);

                        if (tokens.length > 3)                              // for instance
                        {
                            if (severity.equals(Severity.DOWN))             // if instance down , then add critical which will affect the object severity
                            {
                                severities.add(Severity.CRITICAL);

                                if (!app.isEmpty() && metricTypes.containsKey(policyHashCode) && metricTypes.get(policyHashCode).equalsIgnoreCase(app))                         // normal flow
                                {
                                    apps.add(severity);
                                }
                                else
                                {
                                    var splittedTokens = instance.split(SEPARATOR_WITH_ESCAPE);

                                    var appItems = MetricConfigStore.getStore().getAppsByObjectId(CommonUtil.getLong(splittedTokens[0]));

                                    if (appItems != null)                   //  if instance down , then also update the severity of app as well.
                                    {
                                        this.apps.put(splittedTokens[0] + SEPARATOR + appItems.getString(splittedTokens[1]), Severity.DOWN.name());
                                    }
                                }
                            }
                            else
                            {
                                severities.add(severity);
                            }

                            if ((tokens[0] + SEPARATOR + tokens[3]).equalsIgnoreCase(instance))
                            {
                                instances.add(severity);
                            }
                        }
                        else
                        {
                            severities.add(severity);
                        }

                        if (!app.isEmpty() && metricTypes.containsKey(policyHashCode) && metricTypes.get(policyHashCode).equalsIgnoreCase(app))
                        {
                            apps.add(severity);
                        }

                        if (severity.equals(Severity.CLEAR))
                        {
                            clearSeverities++;
                        }
                        else if (severity.equals(Severity.DOWN))
                        {
                            downSeverities++;
                        }
                        else if (severity.equals(Severity.UNREACHABLE))
                        {
                            unreachableSeverities++;
                        }
                        else if (severity.equals(Severity.WARNING))
                        {
                            warningSeverities++;
                        }
                        else if (severity.equals(Severity.MAJOR))
                        {
                            majorSeverities++;
                        }
                        else if (severity.equals(Severity.CRITICAL))
                        {
                            criticalSeverities++;
                        }
                    }
                }
            }

            this.clearSeverities.put(entityId, clearSeverities);

            this.criticalSeverities.put(entityId, criticalSeverities);

            this.majorSeverities.put(entityId, majorSeverities);

            this.warningSeverities.put(entityId, warningSeverities);

            this.unreachableSeverities.put(entityId, unreachableSeverities);

            this.downSeverities.put(entityId, downSeverities);

            if (!objects.getOrDefault(entityId, Severity.UNKNOWN.name()).equalsIgnoreCase(severities.last().name()))
            {
                //publish to UI
                objects.put(entityId, severities.last().name());

                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_SEVERITY_CHANGE, new JsonObject().put(ENTITY_ID, entityId).put(PolicyEngineConstants.POLICY_KEY, entityId).put(SEVERITY, objects.get(entityId)).put(PolicyEngineConstants.SEVERITY_TYPE, "object.severity"));
            }

            if (!app.isEmpty() && !this.apps.getOrDefault(app, GlobalConstants.Severity.UNKNOWN.name()).equalsIgnoreCase(apps.last().name()))
            {
                this.apps.put(app, apps.last().name());

                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_SEVERITY_CHANGE, new JsonObject().put(ENTITY_ID, entityId)
                        .put(PolicyEngineConstants.POLICY_KEY, app)
                        .put(INSTANCE, context.getString(Metric.METRIC_TYPE))
                        .put(SEVERITY, this.apps.get(app)).put(PolicyEngineConstants.SEVERITY_TYPE, "application.severity"));
            }

            if (!instance.isEmpty() && !this.instances.getOrDefault(instance, GlobalConstants.Severity.UNKNOWN.name()).equalsIgnoreCase(instances.last().name()))
            {
                this.instances.put(instance, instances.last().name());

                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_SEVERITY_CHANGE, new JsonObject().put(ENTITY_ID, entityId)
                        .put(PolicyEngineConstants.POLICY_KEY, instance)
                        .put(INSTANCE, instance.split(SEPARATOR_WITH_ESCAPE)[1].trim())
                        .put(SEVERITY, this.instances.get(instance)).put(PolicyEngineConstants.SEVERITY_TYPE, "instance.severity"));
            }

            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_POLICY_NOTIFICATION, context.put(PolicyEngineConstants.POLICY_ID, context.getLong(ID)));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public Map<String, JsonObject> getItems(JsonArray entities)
    {
        var items = new HashMap<String, JsonObject>();

        try
        {
            for (var index = 0; index < entities.size(); index++)
            {
                if (policyKeysByObject.containsKey(entities.getLong(index)))
                {
                    for (var key : policyKeysByObject.get(entities.getLong(index)).split(COMMA_SEPARATOR))
                    {
                        try
                        {
                            if (CommonUtil.isNotNullOrEmpty(key))
                            {
                                var hashCode = CommonUtil.getInteger(key);

                                if (policyKeyHashCodes.containsKey(hashCode))
                                {
                                    items.put(policyKeyHashCodes.get(hashCode), getItem(hashCode));
                                }
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.warn(String.format("exception occurred for key %s for monitor %s", policyKeysByObject.get(entities.getLong(index)), ObjectConfigStore.getStore().getObjectName(entities.getLong(index))));

                            LOGGER.error(exception);
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return items;
    }

    public JsonObject getItem(int hashCode)
    {
        JsonObject item = null;

        try
        {
            if (policyKeyHashCodes.containsKey(hashCode))
            {
                var policyKey = policyKeyHashCodes.get(hashCode);

                item = new JsonObject().put(METRIC, policyKey.split(SEPARATOR_WITH_ESCAPE)[2].trim()).put(INSTANCE, policyKey.split(SEPARATOR_WITH_ESCAPE).length > 3 ? policyKey.split(SEPARATOR_WITH_ESCAPE)[3].trim() : null).put(ID, policyIds.get(hashCode)).put(ENTITY_ID, CommonUtil.getLong(policyKey.split(SEPARATOR_WITH_ESCAPE)[0]))
                        .put(AIOpsObject.OBJECT_CATEGORY, objectCategories.get(hashCode)).put(EventBusConstants.EVENT_TIMESTAMP, timestamps.get(hashCode))
                        .put(VALUE, values.get(hashCode)).put(SEVERITY, severities.get(hashCode))
                        .put(Metric.METRIC_TYPE, metricTypes.getOrDefault(hashCode, EMPTY_VALUE))
                        .put(MetricPolicy.POLICY_THRESHOLD, policyThresholds.getOrDefault(hashCode, EMPTY_VALUE))
                        .put(PLUGIN_ID, plugins.get(hashCode));

                if (correlatedPolicies.containsKey(hashCode))
                {
                    item.put(CORRELATION_PROBE, YES).put(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS, new ArrayList<>(Arrays.stream(correlatedPolicies.get(hashCode).split(COMMA_SEPARATOR)).toList()));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return item;
    }

    public JsonObject getSeverities()
    {
        return new JsonObject().put("object.severity", JsonObject.mapFrom(objects)).put("instance.severity", JsonObject.mapFrom(instances)).put("application.severity", JsonObject.mapFrom(apps));
    }

    public JsonObject getSeveritiesByObject(long id)
    {
        return new JsonObject().put(Severity.CRITICAL.name().toLowerCase(), criticalSeverities.getOrDefault(id, 0)).put(Severity.CLEAR.name().toLowerCase(), clearSeverities.getOrDefault(id, 0))
                .put(Severity.WARNING.name().toLowerCase(), warningSeverities.getOrDefault(id, 0)).put(Severity.UNREACHABLE.name().toLowerCase(), unreachableSeverities.getOrDefault(id, 0))
                .put(Severity.DOWN.name().toLowerCase(), downSeverities.getOrDefault(id, 0)).put(Severity.MAJOR.name().toLowerCase(), majorSeverities.getOrDefault(id, 0));
    }

    public JsonObject getSeveritiesByObject(JsonArray entities)
    {
        var items = new JsonObject();

        for (var index = 0; index < entities.size(); index++)
        {
            var id = entities.getLong(index);

            items.put(CommonUtil.getString(id), new JsonObject().put(Severity.CRITICAL.name().toLowerCase(), criticalSeverities.getOrDefault(id, 0)).put(Severity.CLEAR.name().toLowerCase(), clearSeverities.getOrDefault(id, 0))
                    .put(Severity.WARNING.name().toLowerCase(), warningSeverities.getOrDefault(id, 0)).put(Severity.UNREACHABLE.name().toLowerCase(), unreachableSeverities.getOrDefault(id, 0))
                    .put(Severity.DOWN.name().toLowerCase(), downSeverities.getOrDefault(id, 0)).put(Severity.MAJOR.name().toLowerCase(), majorSeverities.getOrDefault(id, 0)));
        }

        return items;
    }

    public void deleteItem(long id)
    {
        objects.remove(id);

        apps.keySet().removeIf(key -> CommonUtil.getLong(key.split(SEPARATOR_WITH_ESCAPE)[0]) == id);

        instances.keySet().removeIf(key -> CommonUtil.getLong(key.split(SEPARATOR_WITH_ESCAPE)[0]) == id);

        var keys = policyKeysByObject.remove(id);

        if (keys != null)
        {
            Arrays.stream(keys.split(COMMA_SEPARATOR)).forEach(key -> cleanup(CommonUtil.getInteger(key)));
        }

        clearSeverities.remove(id);

        criticalSeverities.remove(id);

        majorSeverities.remove(id);

        warningSeverities.remove(id);

        unreachableSeverities.remove(id);

        downSeverities.remove(id);
    }

    public void deleteItem(long id, String instance, String policyKey)
    {
        cleanup(policyKey.hashCode());

        instances.remove(id + SEPARATOR + instance);
    }

    public void cleanup(long id)
    {
        try
        {
            var hashCodes = new HashSet<Integer>();

            var entities = new HashMap<Long, Set<String>>();

            var iterator = this.policyIds.entrySet().iterator();

            while (iterator.hasNext())
            {
                var entry = iterator.next();

                if (entry.getValue().equals(id) && policyKeyHashCodes.containsKey(entry.getKey()))
                {
                    hashCodes.add(entry.getKey());

                    var entity = CommonUtil.getLong(policyKeyHashCodes.get(entry.getKey()).split(SEPARATOR_WITH_ESCAPE)[0]);

                    entities.computeIfAbsent(entity, value -> new HashSet<>()).add(CommonUtil.getString(entry.getKey()));

                    iterator.remove();
                }
            }

            if (!hashCodes.isEmpty())
            {
                hashCodes.forEach(this::cleanup);

                for (var entry : entities.entrySet())
                {
                    var items = new HashSet<>(Arrays.asList(policyKeysByObject.get(entry.getKey()).split(COMMA_SEPARATOR)));

                    items.removeAll(entry.getValue());

                    if (!items.isEmpty())
                    {
                        policyKeysByObject.put(entry.getKey(), StringUtils.join(items, COMMA_SEPARATOR));
                    }

                    notify(entry.getKey(), items);

                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void notify(long id, Set<String> items)
    {
        try
        {
            if (!items.isEmpty())
            {
                var severities = new TreeSet<Severity>();

                var apps = new HashMap<String, TreeSet<Severity>>();

                for (var item : items)
                {
                    if (CommonUtil.isNotNullOrEmpty(item))
                    {
                        if (this.severities.containsKey(CommonUtil.getInteger(item)))
                        {
                            severities.add(this.severities.get(CommonUtil.getInteger(item)));
                        }

                        if (metricTypes.containsKey(CommonUtil.getInteger(item)))
                        {
                            apps.computeIfAbsent(metricTypes.get(CommonUtil.getInteger(item)), metricType -> new TreeSet<>());

                            apps.get(metricTypes.get(CommonUtil.getInteger(item))).add(this.severities.get(CommonUtil.getInteger(item)));
                        }
                    }
                }

                if (!severities.isEmpty() && !objects.getOrDefault(id, Severity.UNKNOWN.name()).equalsIgnoreCase(severities.last().name()))
                {
                    objects.put(id, severities.last().name());
                }
                else if (severities.isEmpty())
                {
                    objects.put(id, Severity.UNKNOWN.name());
                }

                apps.keySet().forEach(metricType ->
                {
                    this.apps.put(metricType, apps.get(metricType).last().name());

                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_SEVERITY_CHANGE, new JsonObject().put(ENTITY_ID, id).put(PolicyEngineConstants.POLICY_KEY, metricType).put(SEVERITY, this.apps.get(metricType)).put(PolicyEngineConstants.SEVERITY_TYPE, "application.severity"));
                });
            }
            else
            {
                objects.put(id, Severity.UNKNOWN.name());
            }

            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_SEVERITY_CHANGE, new JsonObject().put(ENTITY_ID, id).put(PolicyEngineConstants.POLICY_KEY, id).put(SEVERITY, objects.get(id)).put(PolicyEngineConstants.SEVERITY_TYPE, "object.severity"));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void cleanup(int hashCode)
    {
        var type = metricTypes.remove(hashCode);

        if (type != null)
        {
            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_SEVERITY_CHANGE, new JsonObject().put(PolicyEngineConstants.POLICY_KEY, type).put(SEVERITY, Severity.UNKNOWN).put(PolicyEngineConstants.SEVERITY_TYPE, "application.severity"));

            this.apps.remove(type);
        }

        severities.remove(hashCode);

        plugins.remove(hashCode);

        objectCategories.remove(hashCode);

        policyIds.remove(hashCode);

        values.remove(hashCode);

        timestamps.remove(hashCode);

        policyKeys.entrySet().removeIf(entry -> entry.getValue() == hashCode);

        policyKeyHashCodes.remove(hashCode);

        correlatedPolicies.remove(hashCode);

        policyThresholds.remove(hashCode);
    }

    public String getSeverity(long entity)
    {
        return objects.getOrDefault(entity, null);
    }

    public String getInstanceSeverity(String entity)
    {
        return instances.getOrDefault(entity, null);
    }

    public String getAppSeverity(String entity)
    {
        return apps.getOrDefault(entity, null);
    }

    public JsonArray getItemsBySeverity(String severity)
    {
        var items = new JsonArray();

        for (var entry : objects.entrySet())
        {
            if (severity.equalsIgnoreCase(objects.get(entry.getKey())))
            {
                var object = ObjectConfigStore.getStore().getItem(entry.getKey());

                if (object != null)
                {
                    items.add(new JsonObject().put(ID, entry.getKey()).put(OBJECT_NAME, object.getString(AIOpsObject.OBJECT_NAME)).put(OBJECT_IP, object.getString(OBJECT_IP, object.getString(OBJECT_TARGET))).put(SEVERITY, severity).put(OBJECT_ID, object.getInteger(OBJECT_ID)));
                }
            }
        }

        return items;
    }
}
