/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.Integration;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;

public class IntegrationConfigStore extends AbstractConfigStore
{

    private static final Logger LOGGER = new Logger(IntegrationConfigStore.class, GlobalConstants.MOTADATA_STORE, "Integration Config Store");

    private static final IntegrationConfigStore STORE = new IntegrationConfigStore();

    private IntegrationConfigStore()
    {

        super(ConfigDBConstants.COLLECTION_INTEGRATION, LOGGER, false);
    }

    public static IntegrationConfigStore getStore()
    {
        return STORE;
    }

    //returns list of Active Integrations
    public JsonArray getActiveIntegrations()
    {
        var items = new JsonArray();

        for (var entry : this.items.entrySet())
        {
            var item = entry.getValue();

            if (item.containsKey(Integration.INTEGRATION_CONTEXT)
                    && !item.getJsonObject(Integration.INTEGRATION_CONTEXT).isEmpty()
                    && item.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(GlobalConstants.TARGET)
                    && CommonUtil.isNotNullOrEmpty(item.getJsonObject(Integration.INTEGRATION_CONTEXT).getString(GlobalConstants.TARGET)))
            {
                item = item.copy();

                CommonUtil.removeSensitiveFields(item, true);

                // remove it to reduce api payload
                item.remove(Integration.INTEGRATION_ATTRIBUTES);

                items.add(item);
            }
        }

        return items;
    }
}
