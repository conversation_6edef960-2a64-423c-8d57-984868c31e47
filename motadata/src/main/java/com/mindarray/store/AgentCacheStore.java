/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.agent.AgentConstants;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class AgentCacheStore extends AbstractCacheStore
{
    private static final Logger LOGGER = new Logger(AgentCacheStore.class, GlobalConstants.MOTADATA_STORE, "Agent Cache Store");

    private static final AgentCacheStore STORE = new AgentCacheStore();

    private final Map<Long, JsonObject> items = new ConcurrentHashMap<>();

    private final Map<Long, Long> durations = new ConcurrentHashMap<>();

    private final Map<Long, Integer> progresses = new ConcurrentHashMap<>();


    private AgentCacheStore()
    {
    }

    public static AgentCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        Bootstrap.vertx().<Void>executeBlocking(future ->
        {
            try
            {
                var buffer = Bootstrap.vertx().fileSystem().existsBlocking(AgentConstants.AGENT_CACHE_PATH) ? Bootstrap.vertx().fileSystem().readFileBlocking(AgentConstants.AGENT_CACHE_PATH) : null;

                if (buffer != null && buffer.length() > 0)
                {
                    var caches = new JsonArray(new String(CodecUtil.toBytes(buffer.getBytes())));

                    for (var index = 0; index < caches.size(); index++)
                    {
                        var cache = caches.getJsonObject(index);

                        if (AgentConfigStore.getStore().existItem(cache.getLong(GlobalConstants.ID)))
                        {
                            items.put(cache.getLong(GlobalConstants.ID), cache);
                        }
                    }
                }

                future.complete();

                LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
            }
            catch (Exception exception)
            {
                future.fail(exception);

                LOGGER.error(exception);
            }
        }, result ->
        {
            if (result.succeeded())
            {
                promise.complete();
            }
            else
            {
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    public void updateItem(long id, JsonObject context)
    {
        items.put(id, context);
    }

    public void updateDuration(long id, String state)
    {
        if (state.equalsIgnoreCase(GlobalConstants.YES))
        {
            durations.put(id, DateTimeUtil.currentSeconds());
        }
        else
        {
            durations.remove(id);
        }
    }

    public void updateProgress(long id, int progress)
    {
        progresses.put(id, progress);
    }

    public Integer getProgress(long id)
    {
        return progresses.getOrDefault(id, null);
    }

    public void clearProgress(long id)
    {
        progresses.remove(id);
    }

    public long getDuration(long id)
    {
        return durations.getOrDefault(id, 0L);
    }

    public JsonObject getItem(long id)
    {
        return items.containsKey(id) ? items.get(id).copy() : new JsonObject();
    }

    public void deleteItem(long id)
    {
        items.remove(id);

        durations.remove(id);
    }
}
