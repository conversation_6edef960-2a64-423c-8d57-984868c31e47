/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.CustomMonitoringField;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.util.Logger;

import java.util.List;
import java.util.stream.Collectors;

public class CustomMonitoringFieldConfigStore extends AbstractConfigStore
{

    private static final Logger LOGGER = new Logger(CustomMonitoringFieldConfigStore.class, GlobalConstants.MOTADATA_STORE, "Custom Monitoring Field Config Store");

    private static final CustomMonitoringFieldConfigStore STORE = new CustomMonitoringFieldConfigStore();

    private CustomMonitoringFieldConfigStore()
    {

        super(ConfigDBConstants.COLLECTION_CUSTOM_MONITORING_FIELD, LOGGER, false);
    }

    public static CustomMonitoringFieldConfigStore getStore()
    {
        return STORE;
    }

    public List<String> getFieldNames()
    {
        return this.items.values().stream().map(item -> item.getString(CustomMonitoringField.CUSTOM_MONITORING_FIELD_NAME)).collect(Collectors.toList());
    }

    public List<String> getFieldIds()
    {
        return this.items.values().stream().map(item -> item.getString(GlobalConstants.ID)).collect(Collectors.toList());
    }
}
