/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.nms.SNMPTrapProcessor;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

public class TrapCacheStore extends AbstractCacheStore
{
    private static final TrapCacheStore STORE = new TrapCacheStore();

    private static final Logger LOGGER = new Logger(TrapCacheStore.class, GlobalConstants.MOTADATA_STORE, "Trap Cache Store");

    private final Map<String, String> items = new ConcurrentHashMap<>();

    private final AtomicBoolean dirty = new AtomicBoolean();
    private boolean updated = false;

    private TrapCacheStore()
    {
    }

    public static TrapCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.APP)
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + SNMPTrapProcessor.SNMP_TRAP_ACKS);

            if (file.exists())
            {
                var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath());

                if (buffer != null && buffer.length() > 0)
                {

                    ((Map<String, Object>) new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))).getMap().get("trap.acks")).forEach((key, value) -> items.put(key, CommonUtil.getString(value)));

                    LOGGER.info(String.format("%s loaded from the backup file...", "trap"));
                }
            }
            else
            {
                Bootstrap.vertx().fileSystem().createFileBlocking(file.getPath());
            }

            promise.complete();

            LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
        }
        else
        {
            promise.fail(String.format("failed to init store %s, reason: invalid boot sequence...", this.getClass().getSimpleName()));
        }

        return promise.future();
    }

    public void updateItem(String trapId, String value)
    {
        items.put(trapId, value);

        updated = true;
    }

    public boolean dirty()
    {
        return dirty.get();
    }

    public void setDirty(boolean value)
    {
        dirty.set(value);
    }


    public Map<String, String> getItems()
    {
        return new HashMap<>(items);
    }

    public void dump(String path)
    {
        if (updated)
        {
            updated = false;

            dirty.set(true);

            Bootstrap.vertx().fileSystem().writeFileBlocking(path,
                    Buffer.buffer(CodecUtil.compress(new JsonObject().put("trap.acks", new HashMap<>(items)).encode().getBytes(StandardCharsets.UTF_8))));
        }
    }
}
