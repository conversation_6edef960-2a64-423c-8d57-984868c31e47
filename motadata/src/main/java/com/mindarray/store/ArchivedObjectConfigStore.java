/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *  Date			Author			Notes
 *  05-Mar-2025		<PERSON><PERSON>		MOTADATA-5414: refactored getItemByIP(ip,type) method & instead of throwing exception we will return null.
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.DUMMY_ID;
import static com.mindarray.GlobalConstants.ID;
import static com.mindarray.api.AIOpsObject.*;

public class ArchivedObjectConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(ArchivedObjectConfigStore.class, GlobalConstants.MOTADATA_STORE, "Archived Object Config Store");
    private static final ArchivedObjectConfigStore STORE = new ArchivedObjectConfigStore();
    private final Map<Integer, Long> itemsByObjectId = new ConcurrentHashMap<>();
    private final Map<Long, Integer> itemsById = new ConcurrentHashMap<>();

    private ArchivedObjectConfigStore()
    {

        super(ConfigDBConstants.COLLECTION_OBJECT, LOGGER, false, new JsonArray().add(ID).add(AIOpsObject.OBJECT_GROUPS)
                .add(AIOpsObject.OBJECT_IP).add(OBJECT_TARGET).add(AIOpsObject.OBJECT_NAME).add(AIOpsObject.OBJECT_HOST).add(AIOpsObject.OBJECT_TYPE)
                .add(AIOpsObject.OBJECT_CATEGORY).add(AIOpsObject.OBJECT_AGENT).add(AIOpsObject.OBJECT_ID).add(AIOpsObject.OBJECT_VENDOR).add(AIOpsObject.OBJECT_STATE).add(AIOpsObject.OBJECT_TAGS), new ArrayList<>());
    }

    public static ArchivedObjectConfigStore getStore()
    {
        return STORE;
    }


    @Override
    public Future<Void> initStore()
    {

        var promise = Promise.<Void>promise();

        super.initStore().onComplete(result ->
        {
            if (result.succeeded())
            {
                try
                {
                    if (!items.isEmpty())
                    {
                        for (var item : items.entrySet())
                        {
                            itemsByObjectId.put(item.getValue().getInteger(OBJECT_ID), item.getValue().getLong(ID));

                            itemsById.put(item.getValue().getLong(ID), item.getValue().getInteger(OBJECT_ID));
                        }
                    }

                    promise.complete();
                }

                catch (Exception exception)
                {
                    promise.fail(exception);

                    LOGGER.warn("failed to init Archived config store...");

                    LOGGER.error(exception);
                }
            }

            else
            {
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    @Override
    public Future<Void> addItem(long id)
    {
        return super.addItem(id).onComplete(result ->
        {
            if (result.succeeded())
            {
                itemsByObjectId.put(items.get(id).getInteger(OBJECT_ID), id);

                itemsById.put(id, items.get(id).getInteger(OBJECT_ID));
            }
        });
    }

    public int getNextObjectId()
    {
        return items.values().stream().mapToInt(item -> item.getInteger(AIOpsObject.OBJECT_ID)).max().orElse(0) + 1;
    }

    public String getObjectName(long id)
    {
        return items.containsKey(id) ? items.get(id).getString(AIOpsObject.OBJECT_NAME) : GlobalConstants.EMPTY_VALUE;
    }

    public JsonObject getItemByIP(String ip, NMSConstants.Type type)
    {
        return items.values().parallelStream().filter(item -> item.getString(AIOpsObject.OBJECT_IP) != null && item.getString(AIOpsObject.OBJECT_IP).equalsIgnoreCase(ip))
                .map(item -> item.getLong(GlobalConstants.ID)).toList().stream().filter(id -> items.get(id).getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(type.getName()))
                .map(items::get).findFirst().orElse(null);
    }

    public JsonObject getItemByIP(String ip)
    {
        return items.values().parallelStream().filter(item -> item.getString(AIOpsObject.OBJECT_IP) != null && item.getString(AIOpsObject.OBJECT_IP).equalsIgnoreCase(ip))
                .map(item -> item.getLong(GlobalConstants.ID)).toList().stream()
                .map(items::get).findFirst().orElse(null);
    }

    public int getObjectIdByIP(String ip, NMSConstants.Type type)
    {
        var id = GlobalConstants.NOT_AVAILABLE;

        try
        {
            if (ip != null)
            {
                id = getItemByIP(ip, type).getInteger(AIOpsObject.OBJECT_ID);
            }
        }

        catch (Exception exception)
        {
            // ignored
        }

        return id;

    }

    public JsonObject getItemByIP(String ip, String category)
    {
        return items.values().parallelStream().filter(item -> item.getString(AIOpsObject.OBJECT_IP) != null && item.getString(AIOpsObject.OBJECT_IP).equalsIgnoreCase(ip))
                .map(item -> item.getLong(GlobalConstants.ID)).toList().stream().filter(id -> items.get(id).getString(OBJECT_CATEGORY).equalsIgnoreCase(category))
                .map(items::get).findFirst().orElse(null);
    }

    public JsonObject getItemByTarget(String target, NMSConstants.Type type)
    {
        return items.values().parallelStream().filter(item -> item.getString(AIOpsObject.OBJECT_TARGET) != null && item.getString(AIOpsObject.OBJECT_TARGET).equalsIgnoreCase(target))
                .map(item -> item.getLong(GlobalConstants.ID)).toList().stream().filter(id -> items.get(id).getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(type.getName()))
                .map(items::get).findFirst().orElse(null);
    }

    public JsonObject getItemByTarget(String target, String category)
    {
        return items.values().parallelStream().filter(item -> item.getString(AIOpsObject.OBJECT_TARGET) != null && item.getString(AIOpsObject.OBJECT_TARGET).equalsIgnoreCase(target))
                .map(item -> item.getLong(GlobalConstants.ID)).toList().stream().filter(id -> items.get(id).getString(OBJECT_CATEGORY).equalsIgnoreCase(category))
                .map(items::get).findFirst().orElse(null);
    }

    public JsonObject getItemByTarget(String target)
    {
        return items.values().parallelStream().filter(item -> item.getString(AIOpsObject.OBJECT_TARGET) != null && item.getString(AIOpsObject.OBJECT_TARGET).equalsIgnoreCase(target))
                .map(item -> item.getLong(GlobalConstants.ID)).toList().stream()
                .map(items::get).findFirst().orElse(null);
    }

    @Override
    public JsonArray getItems(JsonArray ids)
    {
        var items = new JsonArray();

        if (ids != null && !ids.isEmpty())
        {
            items.addAll(new JsonArray(ids.stream().filter(this.items::containsKey).map(id -> this.items.get(id).copy()).collect(Collectors.toList())));

        }

        return items;
    }

    public List<JsonObject> getItemsByType(NMSConstants.Type type, String discoveryMethod)
    {
        return items.values().parallelStream().filter(item -> item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(type.getName()) && item.getString(AIOpsObject.OBJECT_DISCOVERY_METHOD).equalsIgnoreCase(discoveryMethod))
                .map(JsonObject::copy).collect(Collectors.toList());
    }


    public int getObjectIdByTarget(String target, NMSConstants.Type type)
    {
        var id = GlobalConstants.NOT_AVAILABLE;

        try
        {
            if (target != null)
            {
                id = getItemByTarget(target, type).getInteger(AIOpsObject.OBJECT_ID);
            }
        }

        catch (Exception exception)
        {
            // ignored
        }

        return id;
    }

    public JsonArray getObjectIdsByIds(JsonArray ids)
    {
        var objectIds = new JsonArray();

        for (var i = 0; i < ids.size(); i++)
        {
            objectIds.add(items.get(ids.getLong(i)).getInteger(AIOpsObject.OBJECT_ID));
        }

        return objectIds;
    }

    public JsonArray getArchiveObjectIds()
    {
        var objectIds = new JsonArray();

        for (var entry : items.entrySet())
        {
            if (entry.getValue().getString(OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ARCHIVE.name()))
            {
                objectIds.add(entry.getValue().getInteger(OBJECT_ID));
            }
        }

        return objectIds;
    }

    public JsonArray getArchivedObjects()
    {
        var items = new JsonArray();

        for (var entry : this.items.entrySet())
        {
            if (entry.getValue().getString(OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ARCHIVE.name()))
            {
                items.add(entry.getValue());
            }
        }

        return items;
    }

    public List<Integer> getItemsByPlugin(Set<Integer> objects, int pluginId)
    {
        var items = new ArrayList<Integer>();

        for (var object : objects)
        {
            if (this.items.get(itemsByObjectId.get(object)).getJsonArray("plugins").contains(pluginId))
            {
                items.add(object);
            }
        }

        return items;
    }


    public long getIdByObjectId(int id)
    {
        return itemsByObjectId.getOrDefault(id, DUMMY_ID);
    }
}
