/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *  Date			Author			Notes
 *  5-Feb-2025		<PERSON><PERSON>		MOTADATA-5128: added the support to use the delete monitor name in service check category in case of updation from service monitor settings
 *  24-Apr-2025      Chopra Deven    Initialized TagConfigStore before ObjectConfigStore as in rare cases ObjectConfigStore update item method is getting called before the TagConfigStore initialized and that causing issue.
 *  23-Apr-2025		sankalp		    MOTADATA-4883 : Added filter based on ID field in getItemsByGroups
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.AIOpsObject.OBJECT_ID;
import static com.mindarray.api.AIOpsObject.OBJECT_TARGET;
import static com.mindarray.api.APIConstants.*;

public class ObjectConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(ObjectConfigStore.class, GlobalConstants.MOTADATA_STORE, "Object Config Store");

    private static final ObjectConfigStore STORE = new ObjectConfigStore();

    private final Map<Long, Long> itemsByAgent = new ConcurrentHashMap<>(); //This data-structure is used frequently in agents

    // warning -> if you add service check monitors in below defined two datastructures then dependencies will not work properly and it impact on correlation, topology
    // if you need data-structure for service check monitors also then create separate data structure for it
    private final Map<String, Long> itemsByIP = new ConcurrentHashMap<>(); // do not store service check monitor

    private final Map<String, Long> itemsByTarget = new ConcurrentHashMap<>(); // do not store service check monitor

    private final Map<String, Long> itemsByObjectName = new ConcurrentHashMap<>(); // this will store monitor name which will be unique regardless of category

    private final Map<Integer, JsonArray> itemsByPlugin = new ConcurrentHashMap<>();

    private final Map<Integer, Long> itemsByObjectId = new ConcurrentHashMap<>();

    private final Map<String, JsonArray> itemsByCategory = new ConcurrentHashMap<>();

    private final Map<String, JsonArray> itemsByType = new ConcurrentHashMap<>();

    private final Map<Long, Integer> itemsById = new ConcurrentHashMap<>();

    private ObjectConfigStore()
    {

        super(ConfigDBConstants.COLLECTION_OBJECT, LOGGER, true,
                new JsonArray().add(ID)
                        .add(AIOpsObject.OBJECT_GROUPS)
                        .add(AIOpsObject.OBJECT_IP)
                        .add(OBJECT_TARGET)
                        .add(AIOpsObject.OBJECT_NAME)
                        .add(AIOpsObject.OBJECT_HOST)
                        .add(AIOpsObject.OBJECT_TYPE)
                        .add(AIOpsObject.OBJECT_CATEGORY)
                        .add(AIOpsObject.OBJECT_AGENT)
                        .add(AIOpsObject.OBJECT_STATE)
                        .add(AIOpsObject.OBJECT_ID)
                        .add(AIOpsObject.OBJECT_VENDOR)
                        .add(AIOpsObject.OBJECT_TAGS),
                List.of(
                        Map.of(
                                REFERENCE_ENTITY, Entity.METRIC_PLUGIN,
                                REFERENCE_ENTITY_PROPERTY, MetricPlugin.METRIC_PLUGIN_ENTITIES,
                                REFERENCE_ENTITY_STORE, ConfigStore.METRIC_PLUGIN,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTI_VALUE
                        ),
                        Map.of(
                                REFERENCE_ENTITY, Entity.RUNBOOK_PLUGIN,
                                REFERENCE_ENTITY_PROPERTY, RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES,
                                REFERENCE_ENTITY_STORE, ConfigStore.RUNBOOK_PLUGIN,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTI_VALUE
                        ),
                        Map.of(
                                REFERENCE_ENTITY, Entity.TOPOLOGY_PLUGIN,
                                REFERENCE_ENTITY_PROPERTY, TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES,
                                REFERENCE_ENTITY_STORE, ConfigStore.TOPOLOGY_PLUGIN,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTI_VALUE
                        ),
                        Map.of(
                                REFERENCE_ENTITY, Entity.DISCOVERY,
                                REFERENCE_ENTITY_PROPERTY, Discovery.DISCOVERY_TARGET,
                                REFERENCE_ENTITY_STORE, ConfigStore.DISCOVERY,
                                REFERENCE_ENTITY_CONTEXT_PROP_KEY, Discovery.DISCOVERY_TARGET_TYPE,
                                REFERENCE_ENTITY_CONTEXT_PROP_VALUE, NMSConstants.DiscoveryTargetType.OBJECT.name(),
                                REFERENCE_ENTITY_CONTEXT_PROP_CONDITION, Discovery.DISCOVERY_TARGET,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.CONTEXT_VALUE
                        ),
                        Map.of(
                                REFERENCE_ENTITY, Entity.SCHEDULER,
                                REFERENCE_ENTITY_PROPERTY, Scheduler.SCHEDULER_CONTEXT,
                                REFERENCE_ENTITY_NESTED_PROPERTY, NMSConstants.OBJECTS,
                                REFERENCE_ENTITY_STORE, ConfigStore.SCHEDULER,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTIMAP_VALUE
                        ),
                        Map.of(
                                REFERENCE_ENTITY, Entity.METRIC_POLICY,
                                REFERENCE_ENTITY_PROPERTY, PolicyEngineConstants.POLICY_CONTEXT,
                                REFERENCE_ENTITY_NESTED_PROPERTY, ENTITIES,
                                REFERENCE_ENTITY_STORE, ConfigStore.METRIC_POLICY,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTIMAP_VALUE
                        ),
                        Map.of(
                                REFERENCE_ENTITY, Entity.COMPLIANCE_POLICY,
                                REFERENCE_ENTITY_PROPERTY, CompliancePolicy.COMPLIANCE_POLICY_CONTEXT,
                                REFERENCE_ENTITY_NESTED_PROPERTY, ENTITIES,
                                REFERENCE_ENTITY_STORE, ConfigStore.COMPLIANCE_POLICY,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTIMAP_VALUE
                        )
                ));
    }

    public static ObjectConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {

        var promise = Promise.<Void>promise();

        try
        {
            TagConfigStore.getStore().initStore().onComplete(asyncResult ->
            {
                LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));

                Bootstrap.configDBService().get(ConfigDBConstants.COLLECTION_OBJECT,
                        new JsonObject().put(ConfigDBConstants.FIELD_NAME, AIOpsObject.OBJECT_STATE).put(VALUE, new JsonArray().add(NMSConstants.State.ENABLE.name()).add(NMSConstants.State.DISABLE.name()).add(NMSConstants.State.MAINTENANCE.name())),
                        result ->
                        {
                            if (result.succeeded())
                            {
                                items.clear();

                                itemsByAgent.clear();

                                itemsByIP.clear();

                                itemsByObjectName.clear();

                                itemsByTarget.clear();

                                itemsByObjectId.clear();

                                itemsById.clear();

                                itemsByCategory.clear();

                                itemsByType.clear();

                                ObjectManagerCacheStore.getStore().initStore().onComplete(response ->
                                {
                                    if (result.result() != null && !result.result().isEmpty())
                                    {
                                        for (var index = 0; index < result.result().size(); index++)
                                        {
                                            var item = result.result().getJsonObject(index);

                                            items.put(item.getLong(GlobalConstants.ID), item);

                                            if (CommonUtil.debugEnabled())
                                            {
                                                LOGGER.debug(String.format("item added %s into the store", CommonUtil.removeSensitiveFields(item, false).encodePrettily()));
                                            }

                                            if (!item.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()))
                                            {
                                                var status = switch (NMSConstants.State.valueOf(item.getString(AIOpsObject.OBJECT_STATE)))
                                                {
                                                    case DISABLE -> STATUS_DISABLE;

                                                    case MAINTENANCE -> STATUS_MAINTENANCE;

                                                    case SUSPEND -> STATUS_SUSPEND;

                                                    default -> STATUS_UNKNOWN;
                                                };

                                                LOGGER.info(String.format("updating state for object : %s with status : %s ", item.getString(AIOpsObject.OBJECT_STATE), status));

                                                ObjectStatusCacheStore.getStore().updateItem(item.getLong(ID), status, DateTimeUtil.currentSeconds());
                                            }

                                            init(item, false);
                                        }
                                    }

                                    MetricConfigStore.getStore().initStore().onComplete(asyncResponse ->
                                    {
                                        LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));

                                        promise.complete();
                                    });
                                });
                            }
                            else
                            {
                                LOGGER.fatal(String.format("failed to init store %s , reason : %s...", this.getClass().getSimpleName(), result.cause()));

                                promise.fail(result.cause());
                            }
                        });
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getMessage());
        }

        return promise.future();
    }

    public int getObjectId(long id)
    {
        return items.containsKey(id) ? CommonUtil.getInteger(items.get(id).getValue(AIOpsObject.OBJECT_ID)) : NOT_AVAILABLE;
    }

    public int getNextObjectId()
    {
        return items.values().stream().mapToInt(item -> item.getInteger(AIOpsObject.OBJECT_ID)).max().orElse(0) + 1;
    }

    public String getObjectName(long id)
    {
        var objectName = items.containsKey(id) ? items.get(id).getString(AIOpsObject.OBJECT_NAME) : GlobalConstants.EMPTY_VALUE;

        if (CommonUtil.isNullOrEmpty(objectName))
        {
            objectName = ArchivedObjectConfigStore.getStore().getObjectName(id);
        }

        return objectName;
    }

    public JsonArray getObjectNames(JsonArray ids)
    {
        var objects = new JsonArray();

        for (var index = 0; index < ids.size(); index++)
        {
            var id = ids.getLong(index);

            var objectName = items.containsKey(id) ? items.get(id).getString(AIOpsObject.OBJECT_NAME) : GlobalConstants.EMPTY_VALUE;

            if (CommonUtil.isNullOrEmpty(objectName))
            {
                objectName = ArchivedObjectConfigStore.getStore().getObjectName(id);
            }

            if (!objectName.isEmpty())
            {
                objects.add(objectName);
            }
        }

        return objects;
    }

    public String getAgentObjectName(long id)
    {
        var objectName = itemsByAgent.containsKey(id) && items.get(itemsByAgent.get(id)) != null ? items.get(itemsByAgent.get(id)).getString(AIOpsObject.OBJECT_NAME) : GlobalConstants.EMPTY_VALUE;

        if (CommonUtil.isNullOrEmpty(objectName))
        {
            var item = ArchivedObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_AGENT, id);

            if (item != null)
            {
                objectName = item.getString(AIOpsObject.OBJECT_NAME);
            }
        }

        return objectName;
    }

    public JsonArray getItemsByGroup(long groupId)
    {
        return new JsonArray(items.values().parallelStream().filter(item -> item.getJsonArray(AIOpsObject.OBJECT_GROUPS) != null)
                .flatMap(item -> item.getJsonArray(AIOpsObject.OBJECT_GROUPS)
                        .stream().map(value -> new AbstractMap.SimpleEntry<>(item, value)))
                .filter(item -> item.getValue().equals(groupId))
                .map(item -> item.getKey().getLong(GlobalConstants.ID))
                .collect(Collectors.toList()));
    }

    public JsonArray getItemsByGroups(JsonArray groups)
    {
        return new JsonArray(items.values().parallelStream().filter(item -> item.getJsonArray(AIOpsObject.OBJECT_GROUPS) != null)
                .flatMap(item -> item.getJsonArray(AIOpsObject.OBJECT_GROUPS)
                        .stream().filter(groups::contains).map(value -> new AbstractMap.SimpleEntry<>(item, value)))
                .map(item -> item.getKey().getLong(GlobalConstants.ID))
                .distinct()
                .collect(Collectors.toList()));
    }

    public JsonArray getItemsByGroups(JsonArray groups, String field, JsonArray values)
    {
        return new JsonArray(items.values().parallelStream().filter(item -> item.getJsonArray(AIOpsObject.OBJECT_GROUPS) != null && values.contains(item.getString(field)))
                .flatMap(item -> item.getJsonArray(AIOpsObject.OBJECT_GROUPS)
                        .stream().filter(groups::contains).map(value -> new AbstractMap.SimpleEntry<>(item, value)))
                .map(item -> item.getKey().getLong(ID))
                .distinct()
                .collect(Collectors.toList()));
    }

    public JsonArray getIdsByGroups(JsonArray groups, JsonArray values)
    {
        return new JsonArray(items.values().parallelStream().filter(item -> item.getJsonArray(AIOpsObject.OBJECT_GROUPS) != null && values.contains(item.getLong(ID)))
                .flatMap(item -> item.getJsonArray(AIOpsObject.OBJECT_GROUPS)
                        .stream().filter(groups::contains).map(value -> new AbstractMap.SimpleEntry<>(item, value)))
                .map(item -> item.getKey().getLong(ID))
                .distinct()
                .collect(Collectors.toList()));
    }

    public JsonArray getItemsByGroups(JsonArray groups, String field, JsonArray values, String flatField, JsonArray flatValues)
    {
        return new JsonArray(items.values().parallelStream().filter(item -> item.getJsonArray(AIOpsObject.OBJECT_GROUPS) != null &&
                        values.contains(item.getString(field)) && flatValues.contains(item.getString(flatField)))
                .flatMap(item -> item.getJsonArray(AIOpsObject.OBJECT_GROUPS)
                        .stream().filter(groups::contains).map(value -> new AbstractMap.SimpleEntry<>(item, value)))
                .map(item -> item.getKey().getLong(ID))
                .distinct()
                .collect(Collectors.toList()));
    }

    @Override
    public JsonArray getItems(JsonArray ids)
    {
        var items = new JsonArray();

        if (ids != null && !ids.isEmpty())
        {
            items.addAll(new JsonArray(ids.stream().filter(this.items::containsKey).map(id -> this.items.get(id).copy()).collect(Collectors.toList())));
        }

        return items;
    }

    public JsonArray getItemsByGroups(JsonArray groups, JsonArray types)
    {
        return new JsonArray(items.values().stream().filter(item -> item.getJsonArray(AIOpsObject.OBJECT_GROUPS) != null && types.contains(item.getString(AIOpsObject.OBJECT_TYPE)))
                .flatMap(item -> item.getJsonArray(AIOpsObject.OBJECT_GROUPS)
                        .stream().filter(groups::contains).map(value -> new AbstractMap.SimpleEntry<>(item, value)))
                .map(item -> item.getKey().getLong(GlobalConstants.ID))
                .collect(Collectors.toList()));
    }


    public JsonArray getItemsByType(NMSConstants.Type type)
    {
        return new JsonArray(items.values().parallelStream().filter(item -> item.getString(AIOpsObject.OBJECT_TYPE) != null
                        && item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(type.getName()))
                .map(item -> item.getLong(GlobalConstants.ID))
                .collect(Collectors.toList()));
    }

    public List<JsonObject> getItemsByType(NMSConstants.Type type, String discoveryMethod)
    {
        return items.values().parallelStream().filter(item -> item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(type.getName()) && item.getString(AIOpsObject.OBJECT_DISCOVERY_METHOD).equalsIgnoreCase(discoveryMethod))
                .map(JsonObject::copy).collect(Collectors.toList());
    }

    public JsonArray getItemsByTypes(JsonArray types)
    {
        return new JsonArray(items.values().stream().filter(item -> item.getString(AIOpsObject.OBJECT_TYPE) != null
                        && types.contains(item.getString(AIOpsObject.OBJECT_TYPE)))
                .map(item -> item.getLong(GlobalConstants.ID))
                .collect(Collectors.toList()));
    }

    public JsonArray getTypes()
    {
        return new JsonArray(items.values().stream().filter(item -> item.getString(AIOpsObject.OBJECT_TYPE) != null)
                .map(item -> NMSConstants.Type.valueOfName(item.getString(AIOpsObject.OBJECT_TYPE)))
                .distinct().collect(Collectors.toList()));
    }

    public JsonArray getItemsByCategory(NMSConstants.Category category)
    {
        return new JsonArray(items.values().stream().filter(item -> item.getString(AIOpsObject.OBJECT_CATEGORY) != null
                        && item.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(category.getName()))
                .map(item -> item.getLong(GlobalConstants.ID))
                .collect(Collectors.toList()));
    }

    public JsonArray getItemsByCategories(JsonArray categories)
    {
        return new JsonArray(items.values().stream().filter(item -> item.getString(AIOpsObject.OBJECT_CATEGORY) != null
                        && categories.contains(NMSConstants.Category.valueOfName(item.getString(AIOpsObject.OBJECT_CATEGORY))))
                .map(item -> item.getLong(GlobalConstants.ID))
                .collect(Collectors.toList()));
    }

    public JsonArray getItemsByState(NMSConstants.State state)
    {
        return new JsonArray(items.values().stream().filter(item -> item.getString(AIOpsObject.OBJECT_STATE) != null
                        && item.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(state.name()))
                .map(item -> item.getLong(GlobalConstants.ID))
                .collect(Collectors.toList()));
    }

    public JsonArray getItemsByStates(JsonArray states)
    {
        return new JsonArray(items.values().stream().filter(item -> item.getString(AIOpsObject.OBJECT_STATE) != null
                        && states.contains(NMSConstants.State.valueOf(item.getString(AIOpsObject.OBJECT_STATE))))
                .map(item -> item.getLong(GlobalConstants.ID))
                .collect(Collectors.toList()));
    }

    public JsonArray getItemsByRemoteEventProcessor(long remoteEventProcessor)
    {
        return new JsonArray(items.values().stream().filter(item -> item.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS) != null && !item.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS).isEmpty())
                .flatMap(item -> item.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS)
                        .stream().map(value -> new AbstractMap.SimpleEntry<>(item, value)))
                .filter(item -> CommonUtil.getLong(item.getValue()) == remoteEventProcessor)
                .map(item -> item.getKey().getLong(GlobalConstants.ID))
                .collect(Collectors.toList()));
    }

    public JsonObject getItemByIP(String ip, NMSConstants.Type type)
    {
        return items.values().parallelStream().filter(item -> item.getString(AIOpsObject.OBJECT_IP) != null && item.getString(AIOpsObject.OBJECT_IP).equalsIgnoreCase(ip))
                .map(item -> item.getLong(GlobalConstants.ID)).toList().stream().filter(id -> items.get(id).getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(type.getName()))
                .map(items::get).findFirst().orElse(null);
    }

    public JsonObject getItemByIP(String ip, NMSConstants.Category category)
    {
        return items.values().parallelStream().filter(item -> item.getString(AIOpsObject.OBJECT_IP) != null && item.getString(AIOpsObject.OBJECT_IP).equalsIgnoreCase(ip))
                .map(item -> item.getLong(GlobalConstants.ID)).toList().stream().filter(id -> items.get(id).getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(category.getName()))
                .map(items::get).findFirst().orElse(null);
    }

    public JsonArray getAgents()
    {
        return new JsonArray(new ArrayList<>(itemsByAgent.keySet()));
    }

    public JsonObject getItemByAgentId(long id)
    {
        return itemsByAgent.get(id) != null ? getItem(itemsByAgent.get(id)) : null;
    }

    public JsonArray getItemsByAgentIds(JsonArray ids)
    {
        var items = new JsonArray();

        if (ids != null && !ids.isEmpty())
        {
            items = new JsonArray(itemsByAgent.keySet().stream().filter(id -> existItem(itemsByAgent.get(id)))
                    .map(id -> getItem(itemsByAgent.get(id))).collect(Collectors.toList()));
        }

        return items;
    }

    public List<JsonObject> getItemsByAgentIds()
    {
        return itemsByAgent.keySet().stream().filter(id -> existItem(itemsByAgent.get(id))).map(id -> getItem(itemsByAgent.get(id))).collect(Collectors.toList());
    }

    public JsonObject getItemByTarget(String target, NMSConstants.Type type)
    {
        return items.values().parallelStream().filter(item -> item.getString(AIOpsObject.OBJECT_TARGET) != null && item.getString(AIOpsObject.OBJECT_TARGET).equalsIgnoreCase(target))
                .map(item -> item.getLong(GlobalConstants.ID)).toList().stream().filter(id -> items.get(id).getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(type.getName()))
                .map(items::get).findFirst().orElse(null);
    }

    public JsonObject getItemByTarget(String target, NMSConstants.Category category)
    {
        return items.values().parallelStream().filter(item -> item.getString(AIOpsObject.OBJECT_TARGET) != null && item.getString(AIOpsObject.OBJECT_TARGET).equalsIgnoreCase(target))
                .map(item -> item.getLong(GlobalConstants.ID)).toList().stream().filter(id -> items.get(id).getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(category.getName()))
                .map(items::get).findFirst().orElse(null);
    }

    private void init(JsonObject item, boolean update)
    {
        try
        {
            if (item.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVER.getName())
                    && item.getLong(AIOpsObject.OBJECT_AGENT) != null)
            {
                itemsByAgent.put(item.getLong(AIOpsObject.OBJECT_AGENT), item.getLong(GlobalConstants.ID));
            }

            if (item.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CLOUD.getName()))
            {
                itemsByTarget.put(item.getString(AIOpsObject.OBJECT_TARGET), item.getLong(ID));
            }
            else if (!item.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVICE_CHECK.getName()))
            {
                itemsByIP.put(item.getString(AIOpsObject.OBJECT_IP), item.getLong(ID));
            }

            if (item.getString(AIOpsObject.OBJECT_NAME) != null)
            {
                itemsByObjectName.put(item.getString(AIOpsObject.OBJECT_NAME), item.getLong(ID));
            }

            if (!update)
            {
                if (item.getString(AIOpsObject.OBJECT_CATEGORY) != null)
                {
                    itemsByCategory.computeIfAbsent(item.getString(AIOpsObject.OBJECT_CATEGORY), value -> new JsonArray()).add(item.getInteger(OBJECT_ID));
                }

                if (item.getString(AIOpsObject.OBJECT_TYPE) != null)
                {
                    itemsByType.computeIfAbsent(item.getString(AIOpsObject.OBJECT_TYPE), value -> new JsonArray()).add(item.getInteger(OBJECT_ID));
                }

                itemsById.put(item.getLong(ID), item.getInteger(OBJECT_ID));

                itemsByObjectId.put(item.getInteger(OBJECT_ID), item.getLong(ID));
            }

            TagCacheStore.getStore().init(item);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    public Future<Void> addItem(long id)
    {
        return super.addItem(id).onComplete(result ->
        {

            if (result.succeeded())
            {
                try
                {
                    var item = items.get(id).copy();

                    init(item, false);

                    notify(item, REQUEST_CREATE);

                    //if there is any old entry found for same object.id in Archived config store,then we will drop that and insert new object
                    if (ArchivedObjectConfigStore.getStore().getIdByObjectId(ObjectConfigStore.getStore().getObjectIdById(id)) != DUMMY_ID && ArchivedObjectConfigStore.getStore().getIdByObjectId(ObjectConfigStore.getStore().getObjectIdById(id)) != id)
                    {
                        Bootstrap.configDBService().delete(ConfigDBConstants.COLLECTION_OBJECT,
                                new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, ArchivedObjectConfigStore.getStore().getIdByObjectId(ObjectConfigStore.getStore().getObjectIdById(id))),
                                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                asyncResult ->
                                {

                                    if (asyncResult.succeeded())
                                    {
                                        ArchivedObjectConfigStore.getStore().deleteItem(ArchivedObjectConfigStore.getStore().getIdByObjectId(ObjectConfigStore.getStore().getObjectIdById(id)));

                                        ArchivedObjectConfigStore.getStore().addItem(id);
                                    }
                                    else
                                    {
                                        LOGGER.warn("Duplicate entry is not deleted from object collection...");
                                    }

                                });
                    }
                    else
                    {
                        ArchivedObjectConfigStore.getStore().addItem(id);
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }
        });
    }

    @Override
    public void deleteItem(long id)
    {
        var item = items.get(id);

        super.deleteItem(id);

        ArchivedObjectConfigStore.getStore().updateItem(id);

        TagCacheStore.getStore().deleteTagsByMonitor(itemsById.get(id));

        ObjectCacheStore.getStore().delete(id);

        itemsById.remove(id);

        itemsByObjectId.values().remove(id);

        if (item.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVER.getName()) && item.getLong(AIOpsObject.OBJECT_AGENT) != null)
        {
            itemsByAgent.remove(item.getLong(AIOpsObject.OBJECT_AGENT));
        }

        if (item.getString(AIOpsObject.OBJECT_NAME) != null)
        {
            itemsByObjectName.remove(item.getString(AIOpsObject.OBJECT_NAME));
        }

        if (!item.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVICE_CHECK.getName()))
        {
            if (item.getString(AIOpsObject.OBJECT_IP) != null)
            {
                itemsByIP.remove(item.getString(AIOpsObject.OBJECT_IP));
            }

            if (item.getString(AIOpsObject.OBJECT_TARGET) != null)
            {
                itemsByTarget.remove(item.getString(AIOpsObject.OBJECT_TARGET));
            }
        }

        for (var items : itemsByPlugin.values())
        {
            items.remove(id);
        }

        itemsByCategory.get(item.getString(AIOpsObject.OBJECT_CATEGORY)).remove(item.getInteger(OBJECT_ID));

        itemsByType.get(item.getString(AIOpsObject.OBJECT_TYPE)).remove(item.getLong(OBJECT_ID));

        notify(item, REQUEST_DELETE);
    }

    @Override
    public Future<Void> updateItem(long id)
    {
        return super.updateItem(id).onComplete(result ->
        {
            notify(items.get(id).copy(), REQUEST_UPDATE);

            init(items.get(id), true);

            ArchivedObjectConfigStore.getStore().updateItem(id);
        });
    }

    public Long getItemByIP(String ip)
    {
        return itemsByIP.getOrDefault(ip, null);
    }

    public Long getItemByTarget(String target)
    {
        return itemsByTarget.getOrDefault(target, null);
    }

    private void notify(JsonObject item, String request)
    {
        var context = new JsonObject().put(REQUEST, request)
                .put(GlobalConstants.ID, item.getLong(GlobalConstants.ID));

        if (!request.equalsIgnoreCase(REQUEST_DELETE))
        {
            context.mergeIn(filter(item));
        }

//        EventBusConstants.publish(EventBusConstants.CONFIG_CHANGE, context.put(ENTITY_NAME, Entity.OBJECT.getName()));
    }

    public int getObjectIdByIP(String ip, NMSConstants.Type type)
    {
        var id = GlobalConstants.NOT_AVAILABLE;

        try
        {
            if (ip != null)
            {
                id = getItemByIP(ip, type).getInteger(AIOpsObject.OBJECT_ID);
            }
        }

        catch (Exception exception)
        {
            // ignored
        }

        return id;

    }

    public int getObjectIdByIP(String ip, NMSConstants.Category category)
    {
        var id = GlobalConstants.NOT_AVAILABLE;

        try
        {
            if (ip != null)
            {
                id = getItemByIP(ip, category).getInteger(AIOpsObject.OBJECT_ID);
            }
        }

        catch (Exception exception)
        {
            // ignored
        }

        return id;

    }

    public Long getIdByObjectName(String name)
    {
        return itemsByObjectName.getOrDefault(name, null);
    }

    // The object name will always be unique, regardless of the any type/category.
    public int getObjectIdByObjectName(String name)
    {
        var id = GlobalConstants.NOT_AVAILABLE;

        try
        {
            if (name != null && itemsByObjectName.containsKey(name))
            {
                id = getItem(itemsByObjectName.get(name)).getInteger(AIOpsObject.OBJECT_ID);
            }

        }

        catch (Exception exception)
        {
            // ignored
        }

        return id;
    }

    public JsonObject getItemByObjectName(String name)
    {
        return itemsByObjectName.containsKey(name) ? getItem(itemsByObjectName.get(name)) : null;
    }

    public int getObjectIdByAccountId(String accountId, NMSConstants.Type type)
    {
        var objectId = GlobalConstants.NOT_AVAILABLE;

        try
        {
            objectId = items.values().stream().filter(item -> item.getString(AIOpsObject.OBJECT_TYPE) != null
                            && item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(type.getName()))
                    .map(item -> item.getLong(GlobalConstants.ID)).toList().stream().filter(id -> items.get(id).getString(AIOpsObject.OBJECT_ACCOUNT_ID).equalsIgnoreCase(accountId))
                    .map(items::get)
                    .findFirst().orElseThrow().getInteger(AIOpsObject.OBJECT_ID);
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return objectId;
    }

    public int getObjectIdByTarget(String target, NMSConstants.Type type)
    {
        var id = GlobalConstants.NOT_AVAILABLE;

        try
        {
            if (target != null)
            {
                id = getItemByTarget(target, type).getInteger(AIOpsObject.OBJECT_ID);
            }
        }

        catch (Exception exception)
        {
            // ignored
        }

        return id;
    }

    public JsonArray flatItemsByMapValueField(NMSConstants.Type type, String key, String value, String flatField, String flatValue, Integer field)
    {
        return new JsonArray(items.values().stream()
                .filter(item -> item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(type.getName()) &&
                        item.getString(key).equalsIgnoreCase(value) && item.getJsonObject(flatField) != null && item.getJsonObject(flatField).containsKey(flatValue)
                        && item.getJsonObject(flatField).getValue(flatValue).equals(field))
                .map(JsonObject::copy)
                .collect(Collectors.toList()));
    }

    public JsonArray getObjectTypes()
    {
        return new JsonArray(this.items.values().parallelStream().map(item -> item.getString(AIOpsObject.OBJECT_TYPE)).distinct().collect(Collectors.toList()));
    }

    public JsonArray getObjectIdsByIds(JsonArray ids)
    {
        var objectIds = new JsonArray();

        for (var i = 0; i < ids.size(); i++)
        {
            objectIds.add(itemsById.get(ids.getLong(i)));
        }

        return objectIds;
    }

    public JsonArray getItemsByObjectIds(List objectIds)
    {
        return new JsonArray((ArrayList<Long>) objectIds.stream().map(CommonUtil::getInteger).filter(itemsByObjectId::containsKey).map(itemsByObjectId::get).collect(Collectors.toList()));
    }

    public long getIdByObjectId(int id)
    {
        return itemsByObjectId.containsKey(id) ? itemsByObjectId.get(id) : NOT_AVAILABLE;
    }

    public JsonArray getObjectIdsByGroups(JsonArray groups)
    {
        return new JsonArray(items.values().parallelStream().filter(item -> item.getJsonArray(AIOpsObject.OBJECT_GROUPS) != null)
                .flatMap(item -> item.getJsonArray(AIOpsObject.OBJECT_GROUPS)
                        .stream().filter(groups::contains).map(value -> new AbstractMap.SimpleEntry<>(item, value)))
                .map(item -> item.getKey().getInteger(AIOpsObject.OBJECT_ID))
                .distinct()
                .collect(Collectors.toList()));
    }

    public Set<Integer> getUniqueObjectIdsByGroups(JsonArray groups)
    {
        return items.values().parallelStream().filter(item -> item.getJsonArray(AIOpsObject.OBJECT_GROUPS) != null)
                .flatMap(item -> item.getJsonArray(AIOpsObject.OBJECT_GROUPS)
                        .stream().filter(groups::contains).map(value -> new AbstractMap.SimpleEntry<>(item, value)))
                .map(item -> item.getKey().getInteger(AIOpsObject.OBJECT_ID))
                .collect(Collectors.toSet());
    }

    public JsonArray getObjectIds()
    {
        var result = new JsonArray();

        for (var entry : itemsByObjectId.entrySet())
        {
            result.add(entry.getKey());
        }

        return result;
    }

    public JsonArray getObjectIdsByCategories(JsonArray categories)
    {
        var objectIds = new JsonArray();

        for (var i = 0; i < categories.size(); i++)
        {
            objectIds.addAll(itemsByCategory.get(categories.getString(i)));
        }

        return objectIds;
    }

    public JsonArray getObjectIdsByTypes(JsonArray types)
    {
        var objectIds = new JsonArray();

        for (var i = 0; i < types.size(); i++)
        {
            if (itemsByType.containsKey(types.getString(i)))
            {
                objectIds.addAll(itemsByType.get(types.getString(i)));
            }
        }

        return objectIds;
    }

    //will return all object level tags
    public JsonArray getTags(String tagType, String type)
    {
        var result = new JsonArray();

        for (var entity : items.entrySet())
        {
            var item = entity.getValue();

            if (item.getJsonArray(AIOpsObject.OBJECT_TAGS) != null && !item.getJsonArray(AIOpsObject.OBJECT_TAGS).isEmpty())
            {
                if (Tag.TagType.OBJECT.getName().equalsIgnoreCase(tagType))
                {
                    result.addAll(TagConfigStore.getStore().getItems(item.getJsonArray(AIOpsObject.OBJECT_TAGS), Tag.TagType.OBJECT, type));
                }
                else if (Tag.TagType.INSTANCE.getName().equalsIgnoreCase(tagType))
                {
                    result.addAll(TagConfigStore.getStore().getItems(item.getJsonArray(AIOpsObject.OBJECT_TAGS), Tag.TagType.INSTANCE, type));
                }
                else
                {
                    //retrieving all the available tags(system tags + user tags)
                    result.addAll(TagConfigStore.getStore().getItems(item.getJsonArray(AIOpsObject.OBJECT_TAGS)));
                }
            }
        }

        return result;
    }

    public int getProvisionedItems()
    {
        return items.size();
    }

    public JsonArray getObjectIdsByTags(JsonArray tags)
    {
        return new JsonArray(items.values().parallelStream().filter(item -> (item.getJsonArray(AIOpsObject.OBJECT_TAGS) != null && TagConfigStore.getStore().getItems(item.getJsonArray(AIOpsObject.OBJECT_TAGS)).stream().anyMatch(tags::contains))).map(item -> item.getInteger(AIOpsObject.OBJECT_ID))
                .distinct()
                .collect(Collectors.toList()));
    }

    public Map<String, JsonArray> getTagsByObjects(JsonArray ids)
    {
        var items = new HashMap<String, JsonArray>();

        try
        {
            for (var id : ids)
            {
                var item = this.items.get(itemsByObjectId.get(CommonUtil.getInteger(id)));

                if (item.getJsonArray(AIOpsObject.OBJECT_TAGS) != null)
                {
                    var tags = item.getJsonArray(AIOpsObject.OBJECT_TAGS);

                    for (var i = 0; i < tags.size(); i++)
                    {
                        var tag = TagConfigStore.getStore().getTag(tags.getLong(i));

                        items.computeIfAbsent(tag, value -> new JsonArray().add(item.getInteger(AIOpsObject.OBJECT_ID)));

                        if (!items.get(tag).contains(item.getInteger(AIOpsObject.OBJECT_ID)))
                        {
                            items.get(tag).add(item.getInteger(AIOpsObject.OBJECT_ID));
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return items;
    }

    public Map<String, JsonArray> getTagsByGroupObjects(JsonArray ids, String tagKey)
    {
        var result = new HashMap<String, JsonArray>();

        try
        {
            for (var id : ids)
            {
                var item = this.items.get(itemsByObjectId.get(CommonUtil.getInteger(id)));

                if (item.getJsonArray(AIOpsObject.OBJECT_TAGS) != null)
                {
                    var tags = item.getJsonArray(AIOpsObject.OBJECT_TAGS);

                    for (var i = 0; i < tags.size(); i++)
                    {
                        var tag = TagConfigStore.getStore().getTag(tags.getLong(i));

                        if (tag.contains(COLON_SEPARATOR) && tag.split(COLON_SEPARATOR)[0].equalsIgnoreCase(tagKey))
                        {
                            result.computeIfAbsent(tag, value -> new JsonArray().add(item.getInteger(AIOpsObject.OBJECT_ID)));

                            if (!result.get(tag).contains(item.getInteger(AIOpsObject.OBJECT_ID)))
                            {
                                result.get(tag).add(item.getInteger(AIOpsObject.OBJECT_ID));
                            }
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    public void updateItemPlugin(int pluginId, long objectId)
    {
        itemsByPlugin.computeIfAbsent(pluginId, value -> new JsonArray());

        if (!itemsByPlugin.get(pluginId).contains(objectId))
        {
            itemsByPlugin.get(pluginId).add(objectId);
        }
    }

    public List<Integer> getItemsByPlugin(Set<Integer> objects, int pluginId)
    {
        var items = new ArrayList<Integer>();

        if (itemsByPlugin.containsKey(pluginId))
        {
            items.addAll(itemsByPlugin.get(pluginId).stream().map(object -> itemsById.get(CommonUtil.getLong(object))).filter(objects::contains).toList());
        }

        return items;
    }

    public JsonArray getItemsByPlugin(JsonArray pluginIds, boolean idRequired)
    {
        var items = new JsonArray();

        for (var plugin : pluginIds)
        {
            var pluginId = CommonUtil.getInteger(plugin);

            if (itemsByPlugin.containsKey(pluginId) && !itemsByPlugin.get(pluginId).isEmpty())
            {
                items.addAll(new JsonArray(itemsByPlugin.get(pluginId).stream().map(object -> idRequired ? CommonUtil.getLong(object) : ObjectConfigStore.getStore().getItem(CommonUtil.getLong(object), false)).toList()));
            }
        }

        return items;
    }

    public int getItemsByPlugin(int pluginId)
    {
        return itemsByPlugin.getOrDefault(pluginId, new JsonArray()).size();
    }

    public JsonArray getPluginsById(long id)
    {
        var items = new JsonArray();

        for (var plugins : itemsByPlugin.entrySet())
        {
            if (plugins.getValue().contains(id))
            {
                items.add(plugins.getKey());
            }
        }
        return items;
    }

    public JsonObject getItemByObjectId(int objectId)
    {
        return itemsByObjectId.get(objectId) != null ? items.get(itemsByObjectId.get(objectId)).copy() : null;
    }

    public int getObjectIdById(long id)
    {
        return itemsById.get(id);
    }

    public void updateItem(String name, String updatedName)
    {
        var id = itemsByObjectName.remove(name);

        if (id != null)
        {
            itemsByObjectName.put(updatedName, id);
        }
    }

    public JsonArray getObjectIdsByCategories(JsonArray categories, JsonArray ids)
    {
        var objectIds = new JsonArray();

        for (var index = 0; index < categories.size(); index++)
        {
            if (itemsByCategory.containsKey(categories.getString(index)))
            {
                objectIds.addAll(new JsonArray(getItemsByCategory(categories.getString(index), true).stream().filter(ids::contains).toList()));
            }
        }

        return objectIds;
    }

    public JsonArray getItemsByCategory(String category, boolean copy)
    {
        return itemsByCategory.containsKey(category) ? (copy ? itemsByCategory.get(category).copy() : itemsByCategory.get(category)) : new JsonArray(new ArrayList<>(1));
    }

    public void deleteItem(String ip)
    {
        itemsByIP.remove(ip);
    }
}
