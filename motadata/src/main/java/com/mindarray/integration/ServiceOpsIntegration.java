/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  07-Mar-2025     Chandresh           username fixes
 */
package com.mindarray.integration;

import com.mindarray.*;
import com.mindarray.api.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.notification.Notification;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.vertx.circuitbreaker.CircuitBreaker;
import io.vertx.circuitbreaker.CircuitBreakerOptions;
import io.vertx.circuitbreaker.OpenCircuitException;
import io.vertx.circuitbreaker.RetryPolicy;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.http.HttpMethod;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.client.HttpResponse;
import io.vertx.ext.web.client.WebClient;
import io.vertx.ext.web.multipart.MultipartForm;
import org.apache.commons.text.StringSubstitutor;
import org.apache.http.HttpStatus;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import static com.mindarray.ErrorMessageConstants.*;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.CLIENT_ID;
import static com.mindarray.api.APIConstants.ENTITY_ID;
import static com.mindarray.api.Integration.*;
import static com.mindarray.db.ConfigDBConstants.COLLECTION_INTEGRATION;
import static com.mindarray.db.ConfigDBConstants.FIELD_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.policy.PolicyEngineConstants.POLICY_ID;
import static com.mindarray.policy.PolicyEngineConstants.POLICY_NAME;

/**
 * Integration class for ServiceOps ticketing system.
 * <p>
 * This class provides functionality to:
 * - Create and update tickets in ServiceOps
 * - Handle authentication with ServiceOps using OAuth tokens
 * - Handle circuit breaking for reliable communication with ServiceOps
 * - Process integration events from the event bus
 * - Send notifications when integration operations succeed or fail
 * <p>
 * The integration uses ServiceOps' REST API to communicate with the ServiceOps instance.
 * It implements a circuit breaker pattern to handle connection issues and retries.
 */
public class ServiceOpsIntegration extends AbstractVerticle
{
    public static final String STATUS_RESOLVED = "Resolved";
    public static final String STATUS_CLOSED = "Closed";
    public static final String STATUS_OPEN = "Open";
    public static final String CLIENT_SECRET = "client.secret";
    private static final Logger LOGGER = new Logger(ServiceOpsIntegration.class, INTEGRATION_DIR, "ServiceOps Integration");
    private static final String REPLY_TOPIC = IntegrationConstants.IntegrationType.SERVICEOPS.getName() + DOT_SEPARATOR + EVENT_INTEGRATION + EventBusConstants.EVENT_REPLY;
    // constants
    private static final String DESCRIPTION_TEMPLATE = "<ul><li><p>Object Name: <strong>$$$object.name$$$</strong></p></li><li><p>IP / Host: <strong>$$$object.ip$$$</strong></p></li><li><p>Object Type: <strong>$$$object.type$$$</strong></p></li><li><p>Metric: <strong>$$$counter$$$</strong></p></li><li><p>Metric Value: <strong>$$$value$$$</strong></p></li><li><p>Severity: <strong>$$$severity$$$</strong></p></li><li><p>Policy Name: <strong>$$$policy.name$$$</strong></p></li><li><p>Policy Type: <strong>$$$policy.type$$$</strong></p></li><li><p>Message: <strong>$$$policy.message$$$</strong></p></li></ul>";
    // url
    private static final String PING_URL = "api/public/ping";
    // api payload related constants
    private static final String STATUS_NAME = "statusName";
    private static final String SUBJECT = "subject";
    private static final String DESCRIPTION = "description";
    private static final String REQUESTER_EMAIL = "requesterEmail";
    private static final String REQUESTER_NAME = "requesterName";
    private static final String DETAILS = "details";
    private static final String NAME = "name";
    private static final String SOURCE = "source";
    private final StringBuilder builder = new StringBuilder(0);
    private final Map<Long, JsonObject> integrationProfiles = new HashMap<>();
    private final CircuitBreaker breaker = CircuitBreaker.create("serviceops-integration-circuit-breaker", Bootstrap.vertx(), new CircuitBreakerOptions()
                    .setMaxFailures(1)
                    .setMaxRetries(MotadataConfigUtil.getIntegrationMaxRetries())
                    .setTimeout(TimeUnit.SECONDS.toMillis(MotadataConfigUtil.getIntegrationTimeoutSeconds()))
                    .setResetTimeout(TimeUnit.SECONDS.toMillis(MotadataConfigUtil.getIntegrationPingTimerSeconds()))
            )
            .retryPolicy(RetryPolicy.constantDelay(TimeUnit.SECONDS.toMillis(MotadataConfigUtil.getIntegrationRetryTimerSeconds())));
    private WebClient webClient = WebClientUtil.getWebClient();
    private EventEngine eventEngine;
    private Set<String> mappers;
    private JsonObject integration;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        mappers = new HashSet<>();

        integration = IntegrationConfigStore.getStore().getItem(IntegrationConstants.IntegrationId.SERVICEOPS.getName());

        var eventEnginePromise = Promise.<Void>promise();

        eventEngine = new EventEngine().setEventType(IntegrationConstants.IntegrationType.SERVICEOPS.getName() + DOT_SEPARATOR + EVENT_INTEGRATION)
                .setEventQueueSize(MotadataConfigUtil.getIntegrationEventQueueSize())
                .setBlockingEvent(true).setLogger(LOGGER).setEventHandler(this::process).start(vertx, eventEnginePromise);

        eventEnginePromise.future().onComplete(result ->
        {
            if (result.succeeded())
            {
                try
                {
                    // register circuit breaker handlers
                    breaker.openHandler(handler ->
                            LOGGER.warn("connection break! circuit is in open state!"));

                    breaker.halfOpenHandler(handler ->
                    {
                        if (integration != null && !integration.isEmpty())
                        {
                            var context = integration.getJsonObject(Integration.INTEGRATION_CONTEXT);

                            if (context != null && !context.isEmpty())
                            {
                                var url = context.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                                        ? context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + PING_URL
                                        : context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/" + PING_URL;

                                LOGGER.info(String.format("reconnecting to : %s", url));

                                webClient.requestAbs(HttpMethod.GET, url)
                                        .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                                        .send(asyncResult ->
                                        {
                                            try
                                            {
                                                if (asyncResult.succeeded())
                                                {
                                                    var response = asyncResult.result();

                                                    if (CommonUtil.traceEnabled())
                                                    {
                                                        LOGGER.trace(String.format("received response with status code : %s and response body %s", response.statusCode(), response.bodyAsString()));
                                                    }

                                                    if (response.statusCode() == HttpStatus.SC_OK)
                                                    {
                                                        LOGGER.info(String.format("connected to : %s", url));

                                                        breaker.reset();
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(asyncResult.result().statusCode());

                                                        breaker.open();
                                                    }
                                                }
                                                else
                                                {
                                                    LOGGER.error(asyncResult.cause());

                                                    breaker.open();
                                                }
                                            }
                                            catch (Exception exception)
                                            {
                                                LOGGER.error(exception);

                                                breaker.open();
                                            }
                                        });
                            }
                            else
                            {
                                LOGGER.warn("invalid serviceops context");

                                breaker.open();
                            }
                        }
                        else
                        {
                            LOGGER.warn("invalid serviceops configuration");

                            breaker.open();
                        }
                    });

                    breaker.closeHandler(handler ->
                    {
                        LOGGER.info("processing pending events");

                        var pendingEvents = IntegrationCacheStore.getStore().getPendingEvents(IntegrationConstants.IntegrationType.SERVICEOPS);

                        if (pendingEvents != null && !pendingEvents.isEmpty())
                        {
                            IntegrationCacheStore.getStore().setUpdate(true);

                            while (!pendingEvents.isEmpty())
                            {
                                process(pendingEvents.removeFirst());
                            }
                        }
                    });

                    vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
                    {
                        switch (EventBusConstants.ChangeNotificationType.valueOf(message.body().getString(CHANGE_NOTIFICATION_TYPE)))
                        {
                            case UPDATE_INTEGRATION ->
                            {
                                try
                                {
                                    var event = message.body();

                                    if (event.containsKey(ID) && event.getValue(ID) != null
                                            && event.getLong(ID).equals(IntegrationConstants.IntegrationId.SERVICEOPS.getName()))
                                    {
                                        integration = IntegrationConfigStore.getStore().getItem(IntegrationConstants.IntegrationId.SERVICEOPS.getName());

                                        if (event.containsKey(Integration.INTEGRATION_CONTEXT))
                                        {
                                            var context = event.getJsonObject(INTEGRATION_CONTEXT);

                                            if (context.getValue(ProxyServer.PROXY_ENABLED) != null && context.getString(ProxyServer.PROXY_ENABLED).equalsIgnoreCase(YES)
                                                    && WebClientUtil.getProxyOptions() != null)
                                            {
                                                webClient = WebClientUtil.getProxyWebClient();
                                            }
                                            else
                                            {
                                                webClient = WebClientUtil.getWebClient();
                                            }
                                        }
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            }

                            case ADD_INTEGRATION_PROFILE, UPDATE_INTEGRATION_PROFILE ->
                            {
                                try
                                {
                                    var event = message.body();

                                    if (event.containsKey(ID) && event.getValue(ID) != null && event.containsKey(IntegrationProfile.INTEGRATION)
                                            && event.getLong(IntegrationProfile.INTEGRATION).equals(IntegrationConstants.IntegrationId.SERVICEOPS.getName()))
                                    {
                                        integrationProfiles.put(event.getLong(ID), IntegrationProfileConfigStore.getStore().getItem(event.getLong(ID)));
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            }

                            case DELETE_INTEGRATION_PROFILE ->
                            {
                                try
                                {
                                    var event = message.body();

                                    if (event.containsKey(ID) && event.getValue(ID) != null)
                                    {
                                        integrationProfiles.remove(event.getLong(ID));
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            }

                            default ->
                            {
                                // do nothing
                            }
                        }
                    });

                    var items = IntegrationProfileConfigStore.getStore().getItems();

                    for (var index = 0; index < items.size(); index++)
                    {
                        var item = items.getJsonObject(index);

                        if (item.containsKey(IntegrationProfile.INTEGRATION)
                                && item.getLong(IntegrationProfile.INTEGRATION).equals(IntegrationConstants.IntegrationId.SERVICEOPS.getName()))
                        {
                            integrationProfiles.put(item.getLong(ID), item);
                        }
                    }

                    if (integration != null && !integration.isEmpty()
                            && integration.getValue(ProxyServer.PROXY_ENABLED) != null
                            && integration.getString(ProxyServer.PROXY_ENABLED).equalsIgnoreCase(YES) && WebClientUtil.getProxyOptions() != null)
                    {
                        webClient = WebClientUtil.getProxyWebClient();
                    }

                    var pendingEvents = IntegrationCacheStore.getStore().getPendingEvents(IntegrationConstants.IntegrationType.SERVICEOPS);

                    if (pendingEvents != null && !pendingEvents.isEmpty())
                    {
                        IntegrationCacheStore.getStore().setUpdate(true);

                        while (!pendingEvents.isEmpty())
                        {
                            // when AIOps restart we need to enqueue this pending events into event engine we can't directly call process method otherwise event engine pending event count will go below 0
                            vertx.eventBus().send(IntegrationConstants.IntegrationType.SERVICEOPS.getName() + DOT_SEPARATOR + EVENT_INTEGRATION, JsonObject.mapFrom(pendingEvents.removeFirst()));
                        }
                    }

                    // get integration
                    vertx.eventBus().<JsonObject>localConsumer(IntegrationConstants.IntegrationType.SERVICEOPS.getName() + DOT_SEPARATOR + EVENT_INTEGRATION_RESPONSE_GET, message ->
                    {
                        var event = message.body();

                        try
                        {
                            if (integration != null && !integration.isEmpty())
                            {
                                var context = integration.getJsonObject(Integration.INTEGRATION_CONTEXT);

                                var url = context.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                                        ? context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "api/v1/request/" + event.getString(IntegrationEngine.ACK_ID)
                                        : context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/api/v1/request/" + event.getString(IntegrationEngine.ACK_ID);

                                var redirectionUrl = context.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                                        ? context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "t/request/" + event.getString(IntegrationEngine.ACK_ID)
                                        : context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/t/request/" + event.getString(IntegrationEngine.ACK_ID);

                                webClient.requestAbs(HttpMethod.GET, url)
                                        .bearerTokenAuthentication(integration.getString(INTEGRATION_ACCESS_TOKEN))
                                        .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                                        .send(asyncResult ->
                                        {
                                            if (asyncResult.succeeded())
                                            {
                                                var response = asyncResult.result();

                                                if (CommonUtil.traceEnabled())
                                                {
                                                    LOGGER.trace(String.format("received response with status code : %s and response body %s", response.statusCode(), response.bodyAsString()));
                                                }

                                                if (response.statusCode() == HttpStatus.SC_OK)
                                                {
                                                    if (CommonUtil.validJSONResponse(response))
                                                    {
                                                        message.reply(event.mergeIn(response.bodyAsJsonObject()).put(STATUS, STATUS_SUCCEED).put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICEOPS.getName()).put(GlobalConstants.TARGET, redirectionUrl));
                                                    }
                                                    else
                                                    {
                                                        message.fail(HttpStatus.SC_BAD_REQUEST, "Invalid Json response!");
                                                    }
                                                }
                                                else if (response.statusCode() == HttpStatus.SC_UNAUTHORIZED)
                                                {
                                                    generateToken();

                                                    message.fail(response.statusCode(), String.format("Failed to get incident with status code : %s", response.statusCode()));
                                                }
                                                else
                                                {
                                                    message.fail(response.statusCode(), String.format("Failed to get incident with status code : %s", response.statusCode()));
                                                }
                                            }
                                            else
                                            {
                                                LOGGER.error(asyncResult.cause());

                                                message.fail(HttpStatus.SC_INTERNAL_SERVER_ERROR, asyncResult.cause().getMessage());
                                            }
                                        });
                            }
                            else
                            {
                                LOGGER.warn(String.format("Invalid integration type : %s", event.getString(Integration.INTEGRATION_TYPE)));

                                message.fail(HttpStatus.SC_BAD_REQUEST, String.format("Invalid integration type : %s", event.getString(INTEGRATION_TYPE)));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            message.fail(HttpStatus.SC_INTERNAL_SERVER_ERROR, exception.getMessage());
                        }
                    });

                    // test integration
                    vertx.eventBus().<JsonObject>localConsumer(IntegrationConstants.IntegrationType.SERVICEOPS.getName() + DOT_SEPARATOR + EVENT_INTEGRATION_TEST, message ->
                    {
                        var event = message.body();

                        try
                        {
                            if (event.containsKey(Integration.INTEGRATION_CONTEXT) && !event.getJsonObject(Integration.INTEGRATION_CONTEXT).isEmpty())
                            {
                                var context = event.getJsonObject(Integration.INTEGRATION_CONTEXT);

                                var item = CredentialProfileConfigStore.getStore().getItem(context.getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE), true);

                                if (item.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
                                {
                                    item.mergeIn(item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                                    item.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                                }

                                var form = MultipartForm.create().attribute(USERNAME, item.getString(USERNAME)).attribute(PASSWORD, item.getString(PASSWORD)).attribute("grant_type", PASSWORD);

                                var url = context.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                                        ? context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "api/oauth/token"
                                        : context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/api/oauth/token";

                                LOGGER.info(String.format("generating token using : %s", url));

                                var webClient = context.getValue(ProxyServer.PROXY_ENABLED) != null && context.getString(ProxyServer.PROXY_ENABLED).equalsIgnoreCase(YES) && WebClientUtil.getProxyOptions() != null ? WebClientUtil.getProxyWebClient() : WebClientUtil.getWebClient();

                                webClient.requestAbs(HttpMethod.POST, url)
                                        .basicAuthentication(item.getString(CLIENT_ID), item.getString(CLIENT_SECRET))
                                        .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                                        .sendMultipartForm(form, asyncResult ->
                                        {
                                            try
                                            {
                                                if (asyncResult.succeeded())
                                                {
                                                    var response = asyncResult.result();

                                                    if (CommonUtil.traceEnabled())
                                                    {
                                                        LOGGER.trace(String.format("received response with status code : %s and response body %s", response.statusCode(), response.bodyAsString()));
                                                    }

                                                    if (response.statusCode() == HttpStatus.SC_OK)
                                                    {
                                                        if (CommonUtil.validJSONResponse(response) &&
                                                                response.bodyAsJsonObject().containsKey("access_token"))
                                                        {
                                                            message.reply(event.put(STATUS, STATUS_SUCCEED).put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_SUCCEEDED, IntegrationConstants.IntegrationType.SERVICEOPS.getName())));

                                                            if (integration != null && !integration.isEmpty())
                                                            {
                                                                integration.put(INTEGRATION_ACCESS_TOKEN, response.bodyAsJsonObject().getString("access_token"));

                                                                Bootstrap.configDBService().update(COLLECTION_INTEGRATION,
                                                                        new JsonObject().put(FIELD_NAME, ID).put(VALUE, integration.getLong(ID)), integration,
                                                                        DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, handler -> IntegrationConfigStore.getStore().updateItem(integration.getLong(ID)));
                                                            }
                                                        }
                                                        else
                                                        {
                                                            LOGGER.warn("invalid json response");

                                                            message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                                                    .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.SERVICEOPS.getName(), INVALID_JSON)));
                                                        }
                                                    }
                                                    else if (response.statusCode() == HttpStatus.SC_BAD_REQUEST)
                                                    {
                                                        message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                                                .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.SERVICEOPS.getName(), CREDENTIAL_ERROR)));
                                                    }
                                                    else if (response.statusCode() == HttpStatus.SC_UNAUTHORIZED)
                                                    {
                                                        message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                                                .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.SERVICEOPS.getName(), String.format(API_ACCESS_FAILED_NOT_AUTHORIZED, url))));
                                                    }
                                                    else if (response.statusCode() == HttpStatus.SC_NOT_FOUND)
                                                    {
                                                        message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                                                .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.SERVICEOPS.getName(), UNABLE_TO_CONNECT)));
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(asyncResult.result().statusCode());

                                                        message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                                                .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.SERVICEOPS.getName(), asyncResult.result().statusCode())));
                                                    }
                                                }
                                                else
                                                {
                                                    LOGGER.error(asyncResult.cause());

                                                    message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                                            .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.SERVICEOPS.getName(), asyncResult.cause().getMessage())));
                                                }
                                            }
                                            catch (Exception exception)
                                            {
                                                message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                                        .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.SERVICEOPS.getName(), exception.getMessage())));

                                                LOGGER.error(exception);
                                            }
                                        });
                            }
                            else
                            {
                                message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                        .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.SERVICEOPS.getName(), INTERNAL_ERROR)));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            message.fail(HttpStatus.SC_INTERNAL_SERVER_ERROR, exception.getMessage());
                        }
                    });

                    promise.complete();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    promise.fail(exception);
                }
            }
            else
            {
                LOGGER.error(result.cause());

                promise.fail(result.cause());
            }
        });
    }

    /**
     * Processes integration events and performs appropriate actions in ServiceOps.
     * <p>
     * This method:
     * 1. Validates the integration and profile configurations
     * 2. Determines the appropriate action based on event data:
     * - Creates a new ticket
     * - Updates an existing ticket
     * - Closes a ticket when severity is CLEAR
     * - Reopens a previously closed ticket if configured
     * 3. Sends a response back to the event bus when processing is complete
     * <p>
     * The method handles various scenarios including:
     * - Auto-closing tickets based on configuration
     * - Reopening tickets for recurring alerts
     * - Creating new tickets for first-time alerts
     *
     * @param event The event object containing information for ServiceOps integration
     */
    private void process(JsonObject event)
    {
        try
        {
            if (integration != null && !integration.isEmpty())
            {
                var item = integrationProfiles.get(event.getLong(ID));

                if (item != null && !item.isEmpty())
                {
                    // merge configuration
                    event.mergeIn(integration.getJsonObject(Integration.INTEGRATION_CONTEXT));

                    // merge profile context
                    event.put(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT, item.getJsonObject(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT));

                    var autoClose = CommonUtil.getString(event.getJsonObject(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT).getString(IntegrationProfile.AUTO_CLOSE_TICKET_STATUS)).equalsIgnoreCase(YES);

                    var ackId = IntegrationCacheStore.getStore().getItem(IntegrationConstants.IntegrationType.SERVICEOPS.getName(), event.getString(POLICY_ID) + SEPARATOR + event.getString(PolicyEngineConstants.POLICY_KEY));

                    if (ackId != null && !CommonUtil.getString(ackId).isEmpty() && !ackId.equals(NOT_AVAILABLE)) //Update Ticket
                    {
                        if (Severity.CLEAR.name().equalsIgnoreCase(event.getString(SEVERITY)))
                        {
                            LOGGER.info(String.format("closing ticket with id : %s", ackId));

                            if (autoClose)
                            {
                                patch(event, CommonUtil.getString(ackId), new JsonObject().put(STATUS_NAME, event.getJsonObject(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT).getString(IntegrationProfile.AUTO_CLOSE_TICKET_STATE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.TicketState.CLOSED.getName()) ? STATUS_CLOSED : STATUS_RESOLVED));
                            }
                            else
                            {
                                IntegrationCacheStore.getStore().remove(IntegrationConstants.IntegrationType.SERVICEOPS.getName(), event.getString(POLICY_ID) + SEPARATOR + event.getString(PolicyEngineConstants.POLICY_KEY));

                                vertx.eventBus().send(REPLY_TOPIC, event);

                                if (MotadataConfigUtil.devMode())
                                {
                                    vertx.eventBus().send("test.integration", event.put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICEOPS.getName()));
                                }
                            }
                        }
                        else
                        {
                            LOGGER.info(String.format("updating ticket with id : %s", ackId));

                            post(event, CommonUtil.getString(ackId));
                        }
                    }
                    else if (event.containsKey(IntegrationEngine.CREATE_TICKET) && event.getBoolean(IntegrationEngine.CREATE_TICKET)) // Create Ticket
                    {
                        var reopen = false;

                        if (event.containsKey(Integration.ALERT_REOCCURRENCE_ACTION)
                                && event.getString(Integration.ALERT_REOCCURRENCE_ACTION).equalsIgnoreCase(IntegrationConstants.AlertReoccurrenceAction.REOPEN.getName()))
                        {
                            var historyId = IntegrationCacheStore.getStore().getArchivedItem(IntegrationConstants.IntegrationType.SERVICEOPS.getName(), event.getString(POLICY_ID) + SEPARATOR + event.getString(PolicyEngineConstants.POLICY_KEY));

                            if (historyId != null && !CommonUtil.getString(historyId).isEmpty() && !historyId.equals(NOT_AVAILABLE))
                            {
                                reopen = true;

                                LOGGER.info(String.format("reopening ticket with id : %s", historyId));

                                patch(event, CommonUtil.getString(historyId), new JsonObject().put(STATUS_NAME, STATUS_OPEN));
                            }
                        }

                        if (!reopen) // means create ticket
                        {
                            LOGGER.info("creating ticket");

                            post(event, null);
                        }
                    }
                    else
                    {
                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("ticket will be not created for the severity : %s", event.getString(SEVERITY)));
                        }

                        vertx.eventBus().send(REPLY_TOPIC, event);
                    }
                }
                else
                {
                    LOGGER.warn(String.format("integration failed %s, reason: %s", event.getLong(ID), String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.INTEGRATION_PROFILE.getName())));

                    vertx.eventBus().send(REPLY_TOPIC, event.put(STATUS, STATUS_ABORT).put(MESSAGE, String.format(ErrorMessageConstants.RUNBOOK_EXECUTION_FAILED, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.INTEGRATION_PROFILE.getName())))
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST));
                }
            }
            else
            {
                LOGGER.warn(String.format("integration failed %s, reason: %s", event.getLong(ID), String.format("%s integration not configured", IntegrationConstants.IntegrationType.SERVICEOPS.getName())));

                vertx.eventBus().send(REPLY_TOPIC, event.put(STATUS, STATUS_ABORT).put(MESSAGE, String.format("%s integration not configured", IntegrationConstants.IntegrationType.SERVICEOPS.getName()))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Creates or updates a ticket in ServiceOps using a POST request.
     * <p>
     * This method:
     * 1. Retrieves credential information for authentication
     * 2. Determines the appropriate API endpoint based on whether this is a new ticket or an update
     * 3. Prepares the request payload with ticket details
     * 4. Sends the request to ServiceOps using the circuit breaker pattern
     * 5. Processes the response:
     * - Updates the integration cache with ticket information on success
     * - Logs warnings for invalid responses
     * - Sends email notifications for failures
     *
     * @param event The event object containing information for the ticket
     * @param ackId The acknowledgement ID of the existing ticket (null for new tickets)
     */
    private void post(JsonObject event, String ackId)
    {
        var id = EMPTY_VALUE;

        var request = new JsonObject();

        if (ackId != null)
        {
            id = ackId.split(HASH_SEPARATOR)[1];
        }

        var url = event.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                ? event.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "api/"
                : event.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/api/";

        if (CommonUtil.isNotNullOrEmpty(id)) // update ticket
        {
            url += "request/" + id + "/conversation";

            request.mergeIn(prepareUpdateRequestPayload(event));
        }
        else // create ticket
        {
            url += "v1/request/";

            var item = CredentialProfileConfigStore.getStore().getItem(integration.getJsonObject(INTEGRATION_CONTEXT).getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, 0L), true);

            if (item.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
            {
                item.mergeIn(item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                item.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
            }

            request.mergeIn(prepareCreateRequestPayload(event, item.getString(USERNAME, EMPTY_VALUE)));
        }

        var requestUrl = url;

        var requestId = id;

        if (CommonUtil.traceEnabled())
        {
            LOGGER.trace(String.format("executing api : %s with request body : %s", requestUrl, request.encode()));
        }

        execute(() -> webClient.requestAbs(HttpMethod.POST, requestUrl)
                .bearerTokenAuthentication(integration.getString(INTEGRATION_ACCESS_TOKEN))
                .timeout(TimeUnit.SECONDS.toMillis(event.getLong(TIMEOUT, 60L)))
                .putHeader("Content-Type", "application/json")
                .sendJsonObject(request), event).onComplete(asyncResult ->
        {
            try
            {
                if (asyncResult.succeeded())
                {
                    var result = asyncResult.result();

                    if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                    {
                        vertx.eventBus().send(REPLY_TOPIC, result);

                        IntegrationConstants.dump(result.put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICEOPS.getName()), mappers, builder, LOGGER);

                        if (MotadataConfigUtil.devMode())
                        {
                            vertx.eventBus().send("test.integration", event.put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICEOPS.getName()));
                        }

                        var response = result.containsKey(RESULT) ? result.getJsonObject(RESULT) : null;

                        if (CommonUtil.isNullOrEmpty(requestId) && response != null && !response.isEmpty()) // ticket created
                        {
                            if (response.containsKey(NAME) && CommonUtil.isNotNullOrEmpty(response.getString(NAME))
                                    && response.containsKey(ID) && CommonUtil.isNotNullOrEmpty(response.getString(ID)))
                            {
                                IntegrationCacheStore.getStore().updateItem(IntegrationConstants.IntegrationType.SERVICEOPS.getName(), event.getString(POLICY_ID) + SEPARATOR + event.getString(PolicyEngineConstants.POLICY_KEY), response.getString(NAME, EMPTY_VALUE) + HASH_SEPARATOR + response.getString(ID, EMPTY_VALUE), event.getLong(EVENT_TIMESTAMP), event.getString(SEVERITY));
                            }
                            else
                            {
                                LOGGER.warn(String.format("invalid response received : %s", result.encode()));
                            }
                        }
                    }
                    else
                    {
                        IntegrationCacheStore.getStore().addPendingEvent(IntegrationConstants.IntegrationType.SERVICEOPS, event);

                        LOGGER.warn(String.format("received failed response : %s", result.encode()));
                    }
                }
                else
                {
                    LOGGER.warn("integration is down!");

                    IntegrationCacheStore.getStore().addPendingEvent(IntegrationConstants.IntegrationType.SERVICEOPS, event);

                    LOGGER.error(asyncResult.cause());
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });
    }

    /**
     * Updates the status of an existing ticket in ServiceOps using a PATCH request.
     * <p>
     * This method:
     * 1. Extracts the system ID from the acknowledgement ID
     * 2. Constructs the API URL for the specific ticket
     * 3. Sends the update request to ServiceOps using the circuit breaker pattern
     * 4. Processes the response:
     * - Removes the ticket from the integration cache if it's being closed or resolved
     * - Updates the integration cache when reopening a ticket
     * - Adds the event to pending events if the integration is down
     * - Logs warnings for invalid responses
     *
     * @param event   The event object containing information for the ticket update
     * @param ackId   The acknowledgement ID of the ticket to update
     * @param request The request object containing fields to update (primarily status)
     */
    private void patch(JsonObject event, String ackId, JsonObject request)
    {
        var id = ackId.split(HASH_SEPARATOR)[1];

        var url = event.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                ? event.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "api/v1/request/" + id
                : event.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/api/v1/request/" + id;

        if (CommonUtil.traceEnabled())
        {
            LOGGER.trace(String.format("executing api : %s with request body : %s", url, request.encode()));
        }

        execute(() -> webClient.requestAbs(HttpMethod.PATCH, url)
                .bearerTokenAuthentication(integration.getString(INTEGRATION_ACCESS_TOKEN))
                .timeout(TimeUnit.SECONDS.toMillis(event.getLong(TIMEOUT, 60L)))
                .putHeader("Content-Type", "application/json")
                .sendJsonObject(request), event).onComplete(asyncResult ->
        {
            try
            {
                if (asyncResult.succeeded())
                {
                    var result = asyncResult.result();

                    if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                    {
                        vertx.eventBus().send(REPLY_TOPIC, result);

                        IntegrationConstants.dump(result.put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICEOPS.getName()), mappers, builder, LOGGER);

                        if (MotadataConfigUtil.devMode())
                        {
                            vertx.eventBus().send("test.integration", event.put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICEOPS.getName()));
                        }

                        if (request.containsKey(STATUS_NAME) &&
                                (request.getString(STATUS_NAME).equalsIgnoreCase(STATUS_CLOSED) || request.getString(STATUS_NAME).equalsIgnoreCase(STATUS_RESOLVED)))
                        {
                            IntegrationCacheStore.getStore().remove(IntegrationConstants.IntegrationType.SERVICEOPS.getName(), event.getString(POLICY_ID) + SEPARATOR + event.getString(PolicyEngineConstants.POLICY_KEY));
                        }
                        else if (request.containsKey(STATUS_NAME) && request.getString(STATUS_NAME).equalsIgnoreCase(STATUS_OPEN))
                        {
                            IntegrationCacheStore.getStore().updateItem(IntegrationConstants.IntegrationType.SERVICEOPS.getName(), event.getString(POLICY_ID) + SEPARATOR + event.getString(PolicyEngineConstants.POLICY_KEY), ackId, event.getLong(EVENT_TIMESTAMP), event.getString(SEVERITY));
                        }
                    }
                    else
                    {
                        IntegrationCacheStore.getStore().addPendingEvent(IntegrationConstants.IntegrationType.SERVICEOPS, event);

                        LOGGER.warn(String.format("received failed response : %s", result.encode()));
                    }
                }
                else
                {
                    LOGGER.warn("integration is down!");

                    IntegrationCacheStore.getStore().addPendingEvent(IntegrationConstants.IntegrationType.SERVICEOPS, event);

                    LOGGER.error(asyncResult.cause());
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });
    }

    /**
     * Prepares the request payload for creating a ticket in ServiceOps.
     * <p>
     * This method:
     * 1. Sets up the requester information
     * 2. Merges the integration profile context into the request
     * 3. Constructs the subject and detailed description based on event data
     * 4. Uses placeholder substitution to include dynamic information in the description
     * <p>
     * The method handles different scenarios:
     * - Including instance information when available
     * - Setting appropriate source information
     * - Formatting the description in HTML for better readability in ServiceOps
     *
     * @param event    The event object containing information for the ticket
     * @param username The username to use as the requester email
     * @return A JsonObject containing the prepared request payload
     */
    private JsonObject prepareCreateRequestPayload(JsonObject event, String username)
    {
        var context = new JsonObject();

        try
        {
            context.put(REQUESTER_NAME, "Motadata");

            context.mergeIn(event.getJsonObject(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT));

            if (event.containsKey(SOURCE) && CommonUtil.isNotNullOrEmpty(event.getString(SOURCE)))
            {
                context.put(SOURCE, event.getString(SOURCE));
            }

            // remove the keys that are used by us.
            context.remove(IntegrationProfile.AUTO_CLOSE_TICKET_STATE);

            context.remove(IntegrationProfile.AUTO_CLOSE_TICKET_STATUS);

            var item = MetricPolicyConfigStore.getStore().getItem(event.getLong(PolicyEngineConstants.POLICY_ID));

            if (item != null)
            {
                if (event.containsKey(INSTANCE))
                {
                    context.put(SUBJECT, event.getString(SUBJECT, item.getString(POLICY_NAME, EMPTY_VALUE) + " - " + event.getString(AIOpsObject.OBJECT_NAME, EMPTY_VALUE) + " (" + event.getString(AIOpsObject.OBJECT_IP, EMPTY_VALUE) + ")" + " - " + event.getString(METRIC, EMPTY_VALUE) + " - " + event.getString(INSTANCE, EMPTY_VALUE)));

                    context.put(DESCRIPTION, PolicyEngineConstants.replaceMetricPolicyPlaceholders(item, event, event.getString(DESCRIPTION, DESCRIPTION_TEMPLATE.replace("</ul>", EMPTY_VALUE) + "<li><p>Instance: <strong>$$$instance$$$</strong></p></li>"), event.getString(MESSAGE, EMPTY_VALUE)));
                }
                else
                {
                    context.put(SUBJECT, event.getString(SUBJECT, item.getString(POLICY_NAME) + " - " + event.getString(AIOpsObject.OBJECT_NAME, EMPTY_VALUE) + " (" + event.getString(AIOpsObject.OBJECT_IP, EMPTY_VALUE) + ")" + " - " + event.getString(METRIC, EMPTY_VALUE)));

                    context.put(DESCRIPTION, PolicyEngineConstants.replaceMetricPolicyPlaceholders(item, event, event.getString(DESCRIPTION, DESCRIPTION_TEMPLATE), event.getString(MESSAGE, EMPTY_VALUE)));
                }
            }
            else
            {
                LOGGER.warn(String.format("incorrect policy id request received : %s", event.getLong(POLICY_ID)));
            }

            context.put(REQUESTER_EMAIL, username);

            var object = ObjectConfigStore.getStore().getItem(event.getLong(ENTITY_ID));

            if (object != null && object.getJsonArray(AIOpsObject.OBJECT_TAGS) != null && !object.getJsonArray(AIOpsObject.OBJECT_TAGS).isEmpty())
            {
                context.put(TAGS, TagConfigStore.getStore().getItems(object.getJsonArray(AIOpsObject.OBJECT_TAGS), Tag.TagType.OBJECT, EMPTY_VALUE));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return context;
    }

    /**
     * Prepares the request payload for updating an existing ticket in ServiceOps.
     * <p>
     * This method:
     * 1. Retrieves the policy information associated with the event
     * 2. Constructs a conversation note containing details about the event
     * 3. Uses placeholder substitution to include dynamic information in the note
     * <p>
     * The method configures the update as a published note in the ticket conversation,
     * which allows for tracking the history of updates to the ticket.
     *
     * @param event The event object containing information for the ticket update
     * @return A JsonObject containing the prepared update payload with conversation details
     */
    private JsonObject prepareUpdateRequestPayload(JsonObject event)
    {
        var context = new JsonObject()
                .put("conversationType", "note")
                .put("noteAsFirstResponse", false)
                .put("conversationState", "published");

        try
        {
            var item = MetricPolicyConfigStore.getStore().getItem(event.getLong(PolicyEngineConstants.POLICY_ID));

            if (item != null)
            {
                context.put(DETAILS, PolicyEngineConstants.replaceMetricPolicyPlaceholders(item, event, event.getString(DESCRIPTION, DESCRIPTION_TEMPLATE), event.getString(MESSAGE, EMPTY_VALUE)));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return context;
    }

    /**
     * Executes an HTTP request to ServiceOps using the circuit breaker pattern.
     * <p>
     * This method:
     * 1. Wraps the HTTP request in a circuit breaker to handle connection issues
     * 2. Processes the response from ServiceOps:
     * - Validates the HTTP status code (200 OK or 201 Created)
     * - Validates that the response is valid JSON
     * - Extracts the result from the response
     * 3. Handles various error scenarios:
     * - Circuit open exceptions (connection issues)
     * - Invalid responses
     * - HTTP errors
     * - Token expiration (triggers token refresh)
     * 4. Sends email notifications for failures when configured
     * <p>
     * The circuit breaker pattern helps prevent cascading failures when
     * ServiceOps is unavailable by failing fast and allowing for retries.
     *
     * @param supplier A supplier function that returns a Future with the HTTP request
     * @param event    The event object containing information for error handling
     * @return A Future containing the processed response as a JsonObject
     */
    private Future<JsonObject> execute(Supplier<Future<HttpResponse<Buffer>>> supplier, JsonObject event)
    {
        var future = Promise.<JsonObject>promise();

        try
        {
            // execute
            breaker.<JsonObject>execute(promise -> supplier.get().onComplete(result ->
            {
                try
                {
                    if (result.succeeded())
                    {
                        var response = result.result();

                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("received response with status code : %s and response body %s", response.statusCode(), response.bodyAsString()));
                        }

                        if (response.statusCode() == HttpStatus.SC_OK || response.statusCode() == HttpStatus.SC_CREATED)
                        {
                            if (CommonUtil.validJSONResponse(response))
                            {
                                promise.complete(event.put(STATUS, STATUS_SUCCEED).put(RESULT, response.bodyAsJsonObject()));
                            }
                            else
                            {
                                promise.fail(String.format("ticket creation failed with status code %s and response : %s", response.statusCode(), response.bodyAsBuffer().toString()));
                            }
                        }
                        else if (response.statusCode() == HttpStatus.SC_UNAUTHORIZED)
                        {
                            generateToken();

                            promise.complete(event.put(STATUS, STATUS_FAIL));

                            breaker.open();
                        }
                        else
                        {
                            promise.fail(String.format("ticket creation failed with status code %s and response : %s", response.statusCode(), response.bodyAsBuffer().toString()));
                        }
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        promise.fail(result.cause());
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    promise.fail(exception.getCause());
                }
            })).onComplete(result ->
            {
                if (result.succeeded())
                {
                    future.complete(result.result());
                }
                else
                {
                    sendMail(result.cause().getMessage(), event);

                    future.fail(result.cause());
                }
            });
        }
        catch (OpenCircuitException exception) // if the circuit is in open state in that case if any event reaches here to execute then circuit will raise this exception hence will add into pending events
        {
            LOGGER.error(exception);

            IntegrationCacheStore.getStore().addPendingEvent(IntegrationConstants.IntegrationType.SERVICEOPS, event);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            future.fail(exception.getCause());
        }

        return future.future();
    }

    /**
     * Sends an email notification when a ServiceOps integration operation fails.
     * <p>
     * This method:
     * 1. Saves the error message to a temporary file
     * 2. Constructs an email notification with:
     * - The error message as an attachment
     * - Standard icons and images
     * - A formatted HTML template with error details
     * 3. Sends the email using the email notification event bus
     * 4. Cleans up the temporary file after sending
     * 5. Sends a test integration event in development mode
     * <p>
     * The email includes details such as:
     * - Timestamp of the failure
     * - Integration type (ServiceOps)
     * - Error message
     * - Target ServiceOps instance URL
     *
     * @param message The error message to include in the notification
     * @param event   The event object containing recipient information and other details
     */
    private void sendMail(String message, JsonObject event)
    {
        var fileName = DateTimeUtil.currentSeconds() + "-response.txt";

        vertx.fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + fileName, Buffer.buffer(message));

        // send fail over email
        Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_EMAIL_NOTIFICATION, new JsonObject()
                        .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline")
                        .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                        .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(fileName).add("critical.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                        .put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format(INTEGRATION_FAILED_NOTIFICATION_SUBJECT, IntegrationConstants.IntegrationType.SERVICEOPS.getName()))
                        .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, event.getJsonArray(Notification.EMAIL_NOTIFICATION_RECIPIENTS))
                        .put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject().put(TIME_STAMP, DateTimeUtil.timestamp(System.currentTimeMillis())).put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICEOPS.getName()).put(MESSAGE, "Failed to create ticket!").put(GlobalConstants.TARGET, event.getString(GlobalConstants.TARGET)).getMap()).replace(Notification.EMAIL_INTEGRATION_FAILED_HTML_TEMPLATE).getBytes(StandardCharsets.UTF_8)), new DeliveryOptions().setSendTimeout(600000L),
                reply ->
                        vertx.fileSystem().deleteBlocking(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + fileName));

        if (MotadataConfigUtil.devMode())
        {
            vertx.eventBus().send("test.integration", event.put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICEOPS.getName()).put(MESSAGE, message));
        }
    }

    /**
     * Generates or refreshes the OAuth access token for ServiceOps API authentication.
     * <p>
     * This method:
     * 1. Retrieves client ID and secret from the integration configuration
     * 2. Constructs a multipart form request with the OAuth grant parameters
     * 3. Sends the token request to the ServiceOps token endpoint
     * 4. Processes the response:
     * - Extracts the access token and refresh token
     * - Updates the integration configuration with the new tokens
     * - Persists the updated configuration to the database
     * 5. Sets up a timer to automatically refresh the token before it expires
     * <p>
     * The method handles token expiration by automatically refreshing tokens
     * and ensuring continuous authentication with the ServiceOps API.
     */
    private void generateToken()
    {
        try
        {
            if (integration != null && !integration.isEmpty())
            {
                var context = integration.getJsonObject(Integration.INTEGRATION_CONTEXT);


                if (context != null && !context.isEmpty() && context.containsKey(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE))
                {
                    var item = CredentialProfileConfigStore.getStore().getItem(context.getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE), true);

                    if (item.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
                    {
                        item.mergeIn(item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                        item.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                    }

                    var form = MultipartForm.create().attribute(USERNAME, item.getString(USERNAME)).attribute(PASSWORD, item.getString(PASSWORD)).attribute("grant_type", PASSWORD);

                    var url = context.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                            ? context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "api/oauth/token"
                            : context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/api/oauth/token";

                    LOGGER.info(String.format("generating token using : %s", url));

                    webClient.requestAbs(HttpMethod.POST, url)
                            .basicAuthentication(item.getString(CLIENT_ID), item.getString(CLIENT_SECRET))
                            .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                            .sendMultipartForm(form, asyncResult ->
                            {
                                try
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        var response = asyncResult.result();

                                        if (CommonUtil.traceEnabled())
                                        {
                                            LOGGER.trace(String.format("received response with status code : %s and response body %s", response.statusCode(), response.bodyAsString()));
                                        }

                                        if (response.statusCode() == HttpStatus.SC_OK)
                                        {
                                            if (CommonUtil.validJSONResponse(response)
                                                    && response.bodyAsJsonObject().containsKey("access_token"))
                                            {
                                                LOGGER.info("token generated successfully");

                                                integration.put(INTEGRATION_ACCESS_TOKEN, response.bodyAsJsonObject().getString("access_token"));

                                                Bootstrap.configDBService().update(COLLECTION_INTEGRATION,
                                                        new JsonObject().put(FIELD_NAME, ID).put(VALUE, integration.getLong(ID)), integration,
                                                        DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result -> IntegrationConfigStore.getStore().updateItem(integration.getLong(ID)));

                                                if (MotadataConfigUtil.devMode())
                                                {
                                                    vertx.eventBus().send("test.token", response.bodyAsJsonObject().put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICEOPS.getName()));
                                                }
                                            }
                                            else
                                            {
                                                LOGGER.warn("invalid json response");
                                            }
                                        }
                                        else
                                        {
                                            LOGGER.warn(asyncResult.result().statusCode());
                                        }
                                    }
                                    else
                                    {
                                        LOGGER.error(asyncResult.cause());
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            });
                }
                else
                {
                    LOGGER.warn("invalid serviceops context");
                }
            }
            else
            {
                LOGGER.warn("invalid serviceops configuration");
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        eventEngine.stop(vertx, promise);
    }
}
