/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	24-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
	07-Mar-2025     Chandresh           username fixes
*/

package com.mindarray.integration;

import com.mindarray.*;
import com.mindarray.api.*;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.notification.Notification;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.vertx.circuitbreaker.CircuitBreaker;
import io.vertx.circuitbreaker.CircuitBreakerOptions;
import io.vertx.circuitbreaker.OpenCircuitException;
import io.vertx.circuitbreaker.RetryPolicy;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.http.HttpMethod;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.client.HttpResponse;
import io.vertx.ext.web.client.WebClient;
import org.apache.commons.text.StringSubstitutor;
import org.apache.http.HttpStatus;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Supplier;

import static com.mindarray.ErrorMessageConstants.*;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.ENTITY_ID;
import static com.mindarray.api.Integration.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.policy.PolicyEngineConstants.POLICY_ID;
import static com.mindarray.policy.PolicyEngineConstants.POLICY_NAME;

/**
 * Integration class for ServiceNow ticketing system.
 * <p>
 * This class provides functionality to:
 * - Create and update incidents and events in ServiceNow
 * - Synchronize attributes (categories, services, technicians, impacts, urgencies) with ServiceNow
 * - Handle circuit breaking for reliable communication with ServiceNow
 * - Process integration events from the event bus
 * - Send notifications when integration operations succeed or fail
 * <p>
 * The integration uses ServiceNow's REST API to communicate with the ServiceNow instance.
 * It implements a circuit breaker pattern to handle connection issues and retries.
 */
public class ServiceNowIntegration extends AbstractVerticle
{
    /**
     * Logger for ServiceNow integration operations
     */
    private static final Logger LOGGER = new Logger(ServiceNowIntegration.class, INTEGRATION_DIR, "ServiceNow Integration");

    /**
     * Topic for replying to integration events
     */
    private static final String REPLY_TOPIC = IntegrationConstants.IntegrationType.SERVICENOW.getName() + DOT_SEPARATOR + EVENT_INTEGRATION + EventBusConstants.EVENT_REPLY;

    /**
     * Template for incident descriptions
     * Uses placeholders that will be replaced with actual values when creating incidents
     */
    private static final String DESCRIPTION_TEMPLATE = "Object Name: $$$object.name$$$\nIP / Host: $$$object.ip$$$\nObject Type: $$$object.type$$$\nMetric: $$$counter$$$\nMetric Value: $$$value$$$\nSeverity: $$$severity$$$\nPolicy Name: $$$policy.name$$$\nPolicy Type: $$$policy.type$$$\nMessage: $$$policy.message$$$";

    /**
     * URL for testing connectivity to ServiceNow
     */
    private static final String PING_URL = "api/now/table/incident?sysparm_limit=1";

    // ServiceNow API field names and values

    /**
     * State field name in ServiceNow
     */
    private static final String STATE = "state";

    /**
     * State value for reopened incidents
     */
    private static final String STATE_REOPEN = "1";

    /**
     * State value for closed incidents
     */
    private static final String STATE_CLOSE = "7";

    /**
     * State value for resolved incidents
     */
    private static final String STATE_RESOLVED = "6";

    /**
     * Description field name in ServiceNow
     */
    private static final String DESCRIPTION = "description";

    /**
     * Short description field name in ServiceNow
     */
    private static final String SHORT_DESCRIPTION = "short_description";

    /**
     * Message key field name in ServiceNow
     */
    private static final String MESSAGE_KEY = "message_key";

    /**
     * Subject field name in ServiceNow
     */
    private static final String SUBJECT = "subject";

    /**
     * Metric name field in ServiceNow
     */
    private static final String METRIC_NAME = "metric_name";

    /**
     * Type field name in ServiceNow
     */
    private static final String TYPE = "type";

    /**
     * Node field name in ServiceNow
     */
    private static final String NODE = "node";

    /**
     * Resource field name in ServiceNow
     */
    private static final String RESOURCE = "resource";

    /**
     * Source field name in ServiceNow
     */
    private static final String SOURCE = "source";

    /**
     * Caller ID field name in ServiceNow
     */
    private static final String CALLER_ID = "caller_id";

    /**
     * Resolution state field name in ServiceNow
     */
    private static final String RESOLUTION_STATE = "resolution_state";

    /**
     * Comments field name in ServiceNow
     */
    private static final String COMMENTS = "comments";

    /**
     * Name field in ServiceNow
     */
    private static final String NAME = "name";

    /**
     * System ID field in ServiceNow
     */
    private static final String SYS_ID = "sys_id";

    /**
     * Label field in ServiceNow
     */
    private static final String LABEL = "label";

    /**
     * Number field in ServiceNow
     */
    private static final String NUMBER = "number";

    /**
     * Dependent value field in ServiceNow
     */
    private static final String DEPENDENT_VALUE = "dependent_value";

    /**
     * Parent field in ServiceNow
     */
    private static final String PARENT = "parent";

    /**
     * Display value field in ServiceNow
     */
    private static final String DISPLAY_VALUE = "display_value";

    /**
     * User field in ServiceNow
     */
    private static final String USER = "user";

    /**
     * StringBuilder used for building response messages
     */
    private final StringBuilder builder = new StringBuilder(0);

    /**
     * Map of integration profiles indexed by their IDs
     * Contains configuration details for each integration profile
     */
    private final Map<Long, JsonObject> integrationProfiles = new HashMap<>();

    /**
     * Flag to track if synchronization is currently running
     */
    private final AtomicBoolean RUNNING = new AtomicBoolean();

    /**
     * Circuit breaker for handling connection issues with ServiceNow
     * Implements retry policy and timeout handling for reliable communication
     */
    private final CircuitBreaker breaker = CircuitBreaker.create("servicenow-integration-circuit-breaker", Bootstrap.vertx(), new CircuitBreakerOptions()
                    .setMaxFailures(1)
                    .setMaxRetries(MotadataConfigUtil.getIntegrationMaxRetries())
                    .setTimeout(TimeUnit.SECONDS.toMillis(MotadataConfigUtil.getIntegrationTimeoutSeconds()))
                    .setResetTimeout(TimeUnit.SECONDS.toMillis(MotadataConfigUtil.getIntegrationPingTimerSeconds()))
            )
            .retryPolicy(RetryPolicy.constantDelay(TimeUnit.SECONDS.toMillis(MotadataConfigUtil.getIntegrationRetryTimerSeconds())));

    /**
     * Event engine for processing integration events
     */
    private EventEngine eventEngine;

    /**
     * Set of mapper identifiers for event processing
     */
    private Set<String> mappers;

    /**
     * Timer ID for periodic synchronization
     */
    private Long timerId;

    /**
     * Integration configuration object
     */
    private JsonObject integration;

    /**
     * Web client for making HTTP requests to ServiceNow
     */
    private WebClient webClient = WebClientUtil.getWebClient();

    /**
     * Initializes the ServiceNow integration.
     * <p>
     * This method:
     * 1. Initializes the mappers set for event processing
     * 2. Loads the ServiceNow integration configuration
     * 3. Sets up the event engine for processing integration events
     * 4. Registers circuit breaker handlers for connection management
     * 5. Sets up periodic synchronization of ServiceNow attributes
     * 6. Completes the initialization promise when setup is complete
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        mappers = new HashSet<>();

        integration = IntegrationConfigStore.getStore().getItem(IntegrationConstants.IntegrationId.SERVICENOW.getName());

        var eventEnginePromise = Promise.<Void>promise();

        eventEngine = new EventEngine().setEventType(IntegrationConstants.IntegrationType.SERVICENOW.getName() + DOT_SEPARATOR + EVENT_INTEGRATION)
                .setEventQueueSize(MotadataConfigUtil.getIntegrationEventQueueSize())
                .setBlockingEvent(true).setLogger(LOGGER).setEventHandler(this::process).start(vertx, eventEnginePromise);

        eventEnginePromise.future().onComplete(result ->
        {
            if (result.succeeded())
            {
                try
                {
                    // register circuit breaker handlers
                    breaker.openHandler(handler ->
                            LOGGER.warn("connection break! circuit is in open state!"));

                    breaker.halfOpenHandler(handler ->
                    {
                        if (integration != null && !integration.isEmpty())
                        {
                            var context = integration.getJsonObject(Integration.INTEGRATION_CONTEXT);

                            if (context != null && !context.isEmpty())
                            {
                                var url = context.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                                        ? context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + PING_URL
                                        : context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/" + PING_URL;

                                LOGGER.info(String.format("reconnecting to : %s", url));

                                var credentialProfile = CredentialProfileConfigStore.getStore().getItem(integration.getJsonObject(INTEGRATION_CONTEXT).getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE), true);

                                if (credentialProfile.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
                                {
                                    credentialProfile.mergeIn(credentialProfile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                                    credentialProfile.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                                }

                                webClient.requestAbs(HttpMethod.GET, url)
                                        .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                                        .basicAuthentication(credentialProfile.getString(USERNAME, EMPTY_VALUE), credentialProfile.getString(PASSWORD, EMPTY_VALUE)).send(asyncResult ->
                                        {
                                            try
                                            {
                                                if (asyncResult.succeeded())
                                                {
                                                    var response = asyncResult.result();

                                                    if (CommonUtil.traceEnabled())
                                                    {
                                                        LOGGER.trace(String.format("received response with status code : %s and response body %s", response.statusCode(), response.bodyAsString()));
                                                    }

                                                    if (response.statusCode() == HttpStatus.SC_OK)
                                                    {
                                                        if (CommonUtil.validJSONResponse(response)
                                                                && response.bodyAsJsonObject().containsKey(RESULT))
                                                        {
                                                            LOGGER.info(String.format("connected to : %s", url));

                                                            breaker.reset();
                                                        }
                                                        else
                                                        {
                                                            LOGGER.warn("invalid json response");

                                                            breaker.open();
                                                        }
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(asyncResult.result().statusCode());

                                                        breaker.open();
                                                    }
                                                }
                                                else
                                                {
                                                    LOGGER.error(asyncResult.cause());

                                                    breaker.open();
                                                }
                                            }
                                            catch (Exception exception)
                                            {
                                                LOGGER.error(exception);

                                                breaker.open();
                                            }
                                        });
                            }
                            else
                            {
                                LOGGER.warn("invalid servicenow context");

                                breaker.open();
                            }
                        }
                        else
                        {
                            LOGGER.warn("invalid servicenow configuration");

                            breaker.open();
                        }
                    });

                    breaker.closeHandler(handler ->
                    {
                        LOGGER.info("processing pending events");

                        var pendingEvents = IntegrationCacheStore.getStore().getPendingEvents(IntegrationConstants.IntegrationType.SERVICENOW);

                        if (pendingEvents != null && !pendingEvents.isEmpty())
                        {
                            IntegrationCacheStore.getStore().setUpdate(true);

                            while (!pendingEvents.isEmpty())
                            {
                                process(pendingEvents.removeFirst());
                            }
                        }
                    });

                    vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
                    {
                        switch (EventBusConstants.ChangeNotificationType.valueOf(message.body().getString(CHANGE_NOTIFICATION_TYPE)))
                        {
                            case UPDATE_INTEGRATION ->
                            {
                                try
                                {
                                    var event = message.body();

                                    if (event.containsKey(ID) && event.getValue(ID) != null
                                            && event.getLong(ID).equals(IntegrationConstants.IntegrationId.SERVICENOW.getName()))
                                    {
                                        integration = IntegrationConfigStore.getStore().getItem(IntegrationConstants.IntegrationId.SERVICENOW.getName());

                                        if (event.containsKey(Integration.INTEGRATION_CONTEXT))
                                        {
                                            var context = event.getJsonObject(INTEGRATION_CONTEXT);

                                            if (context.getValue(ProxyServer.PROXY_ENABLED) != null && context.getString(ProxyServer.PROXY_ENABLED).equalsIgnoreCase(YES)
                                                    && WebClientUtil.getProxyOptions() != null)
                                            {
                                                webClient = WebClientUtil.getProxyWebClient();
                                            }
                                            else
                                            {
                                                webClient = WebClientUtil.getWebClient();
                                            }

                                            // sync will work with proxy also
                                            syncAttributes();

                                            if (context.containsKey(Integration.AUTO_SYNC)
                                                    && context.getString(Integration.AUTO_SYNC).equalsIgnoreCase(YES)
                                                    && context.containsKey(Integration.SYNC_INTERVAL))
                                            {
                                                if (timerId != null)
                                                {
                                                    vertx.cancelTimer(timerId);
                                                }

                                                timerId = vertx.setPeriodic(TimeUnit.HOURS.toMillis(context.getInteger(SYNC_INTERVAL, 8)), timer -> syncAttributes());
                                            }
                                            else if (context.containsKey(AUTO_SYNC) && context.getString(AUTO_SYNC).equalsIgnoreCase(NO))
                                            {
                                                if (timerId != null)
                                                {
                                                    vertx.cancelTimer(timerId);
                                                }
                                            }
                                        }
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            }

                            case ADD_INTEGRATION_PROFILE, UPDATE_INTEGRATION_PROFILE ->
                            {
                                try
                                {
                                    var event = message.body();

                                    if (event.containsKey(ID) && event.getValue(ID) != null && event.containsKey(IntegrationProfile.INTEGRATION)
                                            && event.getLong(IntegrationProfile.INTEGRATION).equals(IntegrationConstants.IntegrationId.SERVICENOW.getName()))
                                    {
                                        integrationProfiles.put(event.getLong(ID), IntegrationProfileConfigStore.getStore().getItem(event.getLong(ID)));
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            }

                            case DELETE_INTEGRATION_PROFILE ->
                            {
                                try
                                {
                                    var event = message.body();

                                    if (event.containsKey(ID) && event.getValue(ID) != null)
                                    {
                                        integrationProfiles.remove(event.getLong(ID));
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            }
                        }
                    });

                    var items = IntegrationProfileConfigStore.getStore().getItems();

                    for (var index = 0; index < items.size(); index++)
                    {
                        var item = items.getJsonObject(index);

                        if (item.containsKey(IntegrationProfile.INTEGRATION)
                                && item.getLong(IntegrationProfile.INTEGRATION).equals(IntegrationConstants.IntegrationId.SERVICENOW.getName()))
                        {
                            integrationProfiles.put(item.getLong(ID), item);
                        }
                    }

                    if (integration != null && !integration.isEmpty()
                            && integration.getValue(ProxyServer.PROXY_ENABLED) != null
                            && integration.getString(ProxyServer.PROXY_ENABLED).equalsIgnoreCase(YES) && WebClientUtil.getProxyOptions() != null)
                    {
                        webClient = WebClientUtil.getProxyWebClient();
                    }

                    var pendingEvents = IntegrationCacheStore.getStore().getPendingEvents(IntegrationConstants.IntegrationType.SERVICENOW);

                    if (pendingEvents != null && !pendingEvents.isEmpty())
                    {
                        IntegrationCacheStore.getStore().setUpdate(true);

                        while (!pendingEvents.isEmpty())
                        {
                            // when AIOps restart we need to enqueue this pending events into event engine we can't directly call process method otherwise event engine pending event count will go below 0
                            vertx.eventBus().send(IntegrationConstants.IntegrationType.SERVICENOW.getName() + DOT_SEPARATOR + EVENT_INTEGRATION, JsonObject.mapFrom(pendingEvents.removeFirst()));
                        }
                    }

                    // get integration
                    vertx.eventBus().<JsonObject>localConsumer(IntegrationConstants.IntegrationType.SERVICENOW.getName() + DOT_SEPARATOR + EVENT_INTEGRATION_RESPONSE_GET, message ->
                    {
                        var event = message.body();

                        try
                        {
                            if (integration != null && !integration.isEmpty())
                            {
                                var context = integration.getJsonObject(Integration.INTEGRATION_CONTEXT);

                                var url = context.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                                        ? context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "api/now/table/incident/" + event.getString(IntegrationEngine.ACK_ID) + "?sysparm_display_value=all"
                                        : context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/api/now/table/incident/" + event.getString(IntegrationEngine.ACK_ID) + "?sysparm_display_value=all";

                                var redirectionUrl = context.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                                        ? context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "nav_to.do?uri=incident.do?sys_id=" + event.getString(IntegrationEngine.ACK_ID)
                                        : context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/nav_to.do?uri=incident.do?sys_id=" + event.getString(IntegrationEngine.ACK_ID);

                                var item = CredentialProfileConfigStore.getStore().getItem(integration.getJsonObject(INTEGRATION_CONTEXT).getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, 0L), true);

                                if (item.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
                                {
                                    item.mergeIn(item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                                    item.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                                }

                                webClient.requestAbs(HttpMethod.GET, url)
                                        .basicAuthentication(item.getString(USERNAME, EMPTY_VALUE), item.getString(PASSWORD, EMPTY_VALUE))
                                        .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                                        .send(asyncResult ->
                                        {
                                            if (asyncResult.succeeded())
                                            {
                                                var response = asyncResult.result();

                                                if (CommonUtil.traceEnabled())
                                                {
                                                    LOGGER.trace(String.format("received response with status code : %s and response body %s", response.statusCode(), response.bodyAsString()));
                                                }

                                                if (response.statusCode() == HttpStatus.SC_OK)
                                                {
                                                    if (CommonUtil.validJSONResponse(response)
                                                            && response.bodyAsJsonObject().containsKey(RESULT))
                                                    {
                                                        message.reply(event.mergeIn(response.bodyAsJsonObject().getJsonObject(RESULT)).put(STATUS, STATUS_SUCCEED).put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICENOW.getName()).put(GlobalConstants.TARGET, redirectionUrl));
                                                    }
                                                    else
                                                    {
                                                        message.fail(HttpStatus.SC_BAD_REQUEST, "Invalid Json response!");
                                                    }
                                                }
                                                else
                                                {
                                                    message.fail(response.statusCode(), String.format("Failed to get incident with status code : %s", response.statusCode()));
                                                }
                                            }
                                            else
                                            {
                                                LOGGER.error(asyncResult.cause());

                                                message.fail(HttpStatus.SC_INTERNAL_SERVER_ERROR, asyncResult.cause().getMessage());
                                            }
                                        });
                            }
                            else
                            {
                                LOGGER.warn(String.format("Invalid integration type : %s", event.getString(Integration.INTEGRATION_TYPE)));

                                message.fail(HttpStatus.SC_BAD_REQUEST, String.format("Invalid integration type : %s", event.getString(INTEGRATION_TYPE)));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            message.fail(HttpStatus.SC_INTERNAL_SERVER_ERROR, exception.getMessage());
                        }
                    });

                    // test integration
                    vertx.eventBus().<JsonObject>localConsumer(IntegrationConstants.IntegrationType.SERVICENOW.getName() + DOT_SEPARATOR + EVENT_INTEGRATION_TEST, message ->
                    {
                        var event = message.body();

                        try
                        {
                            if (event.containsKey(Integration.INTEGRATION_CONTEXT) && !event.getJsonObject(Integration.INTEGRATION_CONTEXT).isEmpty())
                            {
                                var context = event.getJsonObject(Integration.INTEGRATION_CONTEXT);

                                var url = context.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                                        ? context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "api/now/table/%s"
                                        : context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/api/now/table/%s";

                                var request = new JsonObject().put(DESCRIPTION, "This is a test incident.");

                                if (context.containsKey(REQUEST_TYPE) && context.getString(REQUEST_TYPE) != null
                                        && context.getString(Integration.REQUEST_TYPE).equalsIgnoreCase(IntegrationConstants.RequestType.INCIDENT.getName()))
                                {
                                    url = String.format(url, "incident");

                                    request.put(SHORT_DESCRIPTION, "Motadata Incident");
                                }
                                else
                                {
                                    url = String.format(url, "em_event");

                                    request.put(MESSAGE_KEY, "Motadata Incident").put(SOURCE, "Motadata AIOps");
                                }

                                if (CommonUtil.traceEnabled())
                                {
                                    LOGGER.trace(String.format("executing request POST : %s with body : %s", url, request.encode()));
                                }

                                var webClient = context.getValue(ProxyServer.PROXY_ENABLED) != null && context.getString(ProxyServer.PROXY_ENABLED).equalsIgnoreCase(YES) && WebClientUtil.getProxyOptions() != null ? WebClientUtil.getProxyWebClient() : WebClientUtil.getWebClient();

                                var credentialProfile = CredentialProfileConfigStore.getStore().getItem(context.getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE), true);

                                if (credentialProfile.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
                                {
                                    credentialProfile.mergeIn(credentialProfile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                                    credentialProfile.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                                }

                                webClient.requestAbs(HttpMethod.POST, url)
                                        .basicAuthentication(credentialProfile.getString(USERNAME, EMPTY_VALUE), credentialProfile.getString(PASSWORD, EMPTY_VALUE))
                                        .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                                        .sendJsonObject(request).onComplete(asyncResult ->
                                        {
                                            try
                                            {
                                                if (asyncResult.succeeded())
                                                {
                                                    var response = asyncResult.result();

                                                    if (CommonUtil.traceEnabled())
                                                    {
                                                        LOGGER.trace(String.format("integration test response received with status code %s and response %s", response.statusCode(), response.bodyAsString()));
                                                    }

                                                    if (response.statusCode() == HttpStatus.SC_OK || response.statusCode() == HttpStatus.SC_CREATED)
                                                    {
                                                        if (CommonUtil.validJSONResponse(response))
                                                        {
                                                            message.reply(event.put(STATUS, STATUS_SUCCEED)
                                                                    .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_SUCCEEDED, IntegrationConstants.IntegrationType.SERVICENOW.getName())));
                                                        }
                                                        else
                                                        {
                                                            LOGGER.warn("invalid json response");

                                                            message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                                                    .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.SERVICENOW.getName(), INVALID_JSON)));
                                                        }
                                                    }
                                                    else if (response.statusCode() == HttpStatus.SC_UNAUTHORIZED)
                                                    {
                                                        message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                                                .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.SERVICENOW.getName(), CREDENTIAL_ERROR)));
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(asyncResult.result().statusCode());

                                                        message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                                                .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.SERVICENOW.getName(), asyncResult.result().statusCode())));
                                                    }
                                                }
                                                else
                                                {
                                                    LOGGER.error(asyncResult.cause());

                                                    message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                                            .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.SERVICENOW.getName(), asyncResult.cause().getMessage())));
                                                }
                                            }
                                            catch (Exception exception)
                                            {
                                                message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                                        .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.SERVICENOW.getName(), exception.getMessage())));

                                                LOGGER.error(exception);
                                            }
                                        });
                            }
                            else
                            {
                                message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                        .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.SERVICENOW.getName(), INTERNAL_ERROR)));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            message.fail(HttpStatus.SC_INTERNAL_SERVER_ERROR, exception.getMessage());
                        }
                    });

                    promise.complete();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    promise.fail(exception);
                }
            }
            else
            {
                LOGGER.error(result.cause());

                promise.fail(result.cause());
            }
        });
    }

    /**
     * Processes integration events and performs appropriate actions in ServiceNow.
     * <p>
     * This method:
     * 1. Validates the integration and profile configurations
     * 2. Determines the appropriate action based on event data:
     * - Creates a new ticket (incident or event)
     * - Updates an existing ticket
     * - Closes a ticket when severity is CLEAR
     * - Reopens a previously closed ticket if configured
     * 3. Sends a response back to the event bus when processing is complete
     * <p>
     * The method handles various scenarios including:
     * - Auto-closing tickets based on configuration
     * - Reopening tickets for recurring alerts
     * - Creating different types of tickets (incidents or events)
     *
     * @param event The event object containing information for ServiceNow integration
     */
    private void process(JsonObject event)
    {
        try
        {
            if (integration != null && !integration.isEmpty())
            {
                var item = integrationProfiles.get(event.getLong(ID));

                if (item != null && !item.isEmpty())
                {
                    // merge configuration
                    event.mergeIn(integration.getJsonObject(Integration.INTEGRATION_CONTEXT));

                    // merge profile context
                    event.put(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT, item.getJsonObject(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT));

                    var autoClose = CommonUtil.getString(event.getJsonObject(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT).getString(IntegrationProfile.AUTO_CLOSE_TICKET_STATUS)).equalsIgnoreCase(YES);

                    var ackId = IntegrationCacheStore.getStore().getItem(IntegrationConstants.IntegrationType.SERVICENOW.getName(), event.getString(POLICY_ID) + SEPARATOR + event.getString(PolicyEngineConstants.POLICY_KEY));

                    var createIncident = event.containsKey(REQUEST_TYPE) && event.getString(REQUEST_TYPE) != null && event.getString(Integration.REQUEST_TYPE).equalsIgnoreCase(IntegrationConstants.RequestType.INCIDENT.getName());

                    if (ackId != null && !CommonUtil.getString(ackId).isEmpty() && !ackId.equals(NOT_AVAILABLE)) //Update Ticket
                    {
                        if (Severity.CLEAR.name().equalsIgnoreCase(event.getString(SEVERITY)))
                        {
                            LOGGER.info(String.format("closing ticket with id : %s", ackId));

                            if (autoClose)
                            {
                                update(event, CommonUtil.getString(ackId), new JsonObject().put(STATE, event.getJsonObject(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT).getString(IntegrationProfile.AUTO_CLOSE_TICKET_STATE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.TicketState.CLOSED.getName()) ? STATE_CLOSE : STATE_RESOLVED));
                            }
                            else
                            {
                                IntegrationCacheStore.getStore().remove(IntegrationConstants.IntegrationType.SERVICENOW.getName(), event.getString(POLICY_ID) + SEPARATOR + event.getString(PolicyEngineConstants.POLICY_KEY));

                                vertx.eventBus().send(REPLY_TOPIC, event);

                                if (MotadataConfigUtil.devMode())
                                {
                                    vertx.eventBus().send("test.integration", event.put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICENOW.getName()));
                                }
                            }
                        }
                        else
                        {
                            LOGGER.info(String.format("updating ticket with id : %s", ackId));

                            update(event, CommonUtil.getString(ackId), new JsonObject());
                        }
                    }
                    else if (event.containsKey(IntegrationEngine.CREATE_TICKET) && event.getBoolean(IntegrationEngine.CREATE_TICKET)) // Create Ticket
                    {
                        var reopen = false;

                        if (event.containsKey(Integration.ALERT_REOCCURRENCE_ACTION)
                                && event.getString(Integration.ALERT_REOCCURRENCE_ACTION).equalsIgnoreCase(IntegrationConstants.AlertReoccurrenceAction.REOPEN.getName()))
                        {
                            var historyId = IntegrationCacheStore.getStore().getArchivedItem(IntegrationConstants.IntegrationType.SERVICENOW.getName(), event.getString(POLICY_ID) + SEPARATOR + event.getString(PolicyEngineConstants.POLICY_KEY));

                            if (historyId != null && !CommonUtil.getString(historyId).isEmpty() && !historyId.equals(NOT_AVAILABLE))
                            {
                                reopen = true;

                                LOGGER.info(String.format("reopening ticket with id : %s", historyId));

                                update(event, CommonUtil.getString(historyId), new JsonObject().put(STATE, STATE_REOPEN));
                            }
                        }

                        if (!reopen) // means create ticket
                        {
                            LOGGER.info("creating ticket");

                            create(event, createIncident);
                        }
                    }
                    else if (!createIncident) // in case of events we may not always receive create.severity
                    {
                        create(event, false);
                    }
                    else
                    {
                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("ticket will be not created for the severity : %s", event.getString(SEVERITY)));
                        }

                        vertx.eventBus().send(REPLY_TOPIC, event);
                    }
                }
                else
                {
                    LOGGER.warn(String.format("integration failed %s, reason: %s", event.getLong(ID), String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.INTEGRATION_PROFILE.getName())));

                    vertx.eventBus().send(REPLY_TOPIC, event.put(STATUS, STATUS_ABORT).put(MESSAGE, String.format(ErrorMessageConstants.RUNBOOK_EXECUTION_FAILED, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.INTEGRATION_PROFILE.getName())))
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST));
                }
            }
            else
            {
                LOGGER.warn(String.format("integration failed %s, reason: %s", event.getLong(ID), String.format("%s integration not configured", IntegrationConstants.IntegrationType.SERVICENOW.getName())));

                vertx.eventBus().send(REPLY_TOPIC, event.put(STATUS, STATUS_ABORT).put(MESSAGE, String.format("%s integration not configured", IntegrationConstants.IntegrationType.SERVICENOW.getName()))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Creates a ticket in ServiceNow based on the event data.
     * <p>
     * This method:
     * 1. Constructs the appropriate API URL based on ticket type (incident or event)
     * 2. Retrieves credential information for authentication
     * 3. Prepares the request payload with event details
     * 4. Sends the request to ServiceNow using the circuit breaker pattern
     * 5. Processes the response:
     * - Updates the integration cache with ticket information on success
     * - Adds the event to pending events if the integration is down
     * - Logs warnings for invalid responses
     *
     * @param event          The event object containing information for the ticket
     * @param createIncident Flag indicating whether to create an incident (true) or event (false)
     */
    private void create(JsonObject event, boolean createIncident)
    {
        var url = event.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                ? event.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "api/now/table/" + (createIncident ? "incident" : "em_event")
                : event.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/api/now/table/" + (createIncident ? "incident" : "em_event");

        var item = CredentialProfileConfigStore.getStore().getItem(integration.getJsonObject(INTEGRATION_CONTEXT).getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, 0L), true);

        if (item.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
        {
            item.mergeIn(item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

            item.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
        }

        var request = prepareCreateRequestPayload(event, createIncident, item.getString(USERNAME, EMPTY_VALUE));

        if (CommonUtil.traceEnabled())
        {
            LOGGER.trace(String.format("executing api : %s with request body : %s", url, request.encode()));
        }

        execute(() -> webClient.requestAbs(HttpMethod.POST, url)
                .basicAuthentication(item.getString(USERNAME, EMPTY_VALUE), item.getString(PASSWORD, EMPTY_VALUE))
                .timeout(TimeUnit.SECONDS.toMillis(event.getLong(TIMEOUT, 60L)))
                .sendJsonObject(request), event).onComplete(asyncResult ->
        {
            try
            {
                if (asyncResult.succeeded())
                {
                    var result = asyncResult.result();

                    if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED)) // ticket created successfully
                    {
                        vertx.eventBus().send(REPLY_TOPIC, result);

                        IntegrationConstants.dump(result.put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICENOW.getName()), mappers, builder, LOGGER);

                        if (MotadataConfigUtil.devMode())
                        {
                            vertx.eventBus().send("test.integration", event.put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICENOW.getName()));
                        }

                        var response = result.containsKey(RESULT) ? result.getJsonObject(RESULT) : null;

                        if (response != null && !response.isEmpty())
                        {
                            // do not save any result if the alert.type is event
                            if (createIncident)
                            {
                                if (response.containsKey(NUMBER) && CommonUtil.isNotNullOrEmpty(response.getString(NUMBER))
                                        && response.containsKey(SYS_ID) && CommonUtil.isNotNullOrEmpty(response.getString(SYS_ID)))
                                {
                                    IntegrationCacheStore.getStore().updateItem(IntegrationConstants.IntegrationType.SERVICENOW.getName(), event.getString(POLICY_ID) + SEPARATOR + event.getString(PolicyEngineConstants.POLICY_KEY), response.getString(NUMBER, EMPTY_VALUE) + HASH_SEPARATOR + response.getString(SYS_ID, EMPTY_VALUE), event.getLong(EVENT_TIMESTAMP), event.getString(SEVERITY));
                                }
                                else
                                {
                                    LOGGER.warn(String.format("invalid response received : %s", result.encode()));
                                }
                            }
                        }
                        else
                        {
                            LOGGER.warn(String.format("invalid response received : %s", result.encode()));
                        }
                    }
                    else
                    {
                        LOGGER.warn(String.format("received failed response : %s", result.encode()));
                    }
                }
                else
                {
                    LOGGER.warn("integration is down!");

                    IntegrationCacheStore.getStore().addPendingEvent(IntegrationConstants.IntegrationType.SERVICENOW, event);

                    LOGGER.error(asyncResult.cause());
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });
    }

    /**
     * Updates an existing ticket in ServiceNow.
     * <p>
     * This method:
     * 1. Extracts the system ID from the acknowledgement ID
     * 2. Constructs the API URL for the specific incident
     * 3. Merges the provided request with additional update payload
     * 4. Sends the update request to ServiceNow using the circuit breaker pattern
     * 5. Processes the response:
     * - Removes the ticket from the integration cache if it's being closed
     * - Adds the event to pending events if the integration is down
     * - Logs warnings for invalid responses
     *
     * @param event   The event object containing information for the ticket update
     * @param ackId   The acknowledgement ID of the ticket to update
     * @param request The base request object containing fields to update
     */
    private void update(JsonObject event, String ackId, JsonObject request)
    {
        var id = ackId.split(HASH_SEPARATOR)[1];

        var url = event.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                ? event.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "api/now/table/incident/" + id
                : event.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/api/now/table/incident/" + id;

        request.mergeIn(prepareUpdateRequestPayload(event));

        if (CommonUtil.traceEnabled())
        {
            LOGGER.trace(String.format("executing api : %s with request body : %s", url, request.encode()));
        }

        var item = CredentialProfileConfigStore.getStore().getItem(integration.getJsonObject(INTEGRATION_CONTEXT).getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, 0L), true);

        if (item.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
        {
            item.mergeIn(item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

            item.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
        }

        execute(() -> webClient.requestAbs(HttpMethod.PUT, url)
                .basicAuthentication(item.getString(USERNAME, EMPTY_VALUE), item.getString(PASSWORD, EMPTY_VALUE))
                .timeout(TimeUnit.SECONDS.toMillis(event.getLong(TIMEOUT, 60L)))
                .sendJsonObject(request), event).onComplete(asyncResult ->
        {
            try
            {
                if (asyncResult.succeeded())
                {
                    var result = asyncResult.result();

                    vertx.eventBus().send(REPLY_TOPIC, result);

                    IntegrationConstants.dump(result.put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICENOW.getName()), mappers, builder, LOGGER);

                    if (MotadataConfigUtil.devMode())
                    {
                        vertx.eventBus().send("test.integration", event.put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICENOW.getName()));
                    }

                    if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED)) // ticket updated successfully
                    {
                        if (request.containsKey(STATE)
                                && (request.getString(STATE).equalsIgnoreCase(STATE_RESOLVED) || request.getString(STATE).equalsIgnoreCase(STATE_CLOSE))) // means ticket closed/resolved
                        {
                            IntegrationCacheStore.getStore().remove(IntegrationConstants.IntegrationType.SERVICENOW.getName(), event.getString(POLICY_ID) + SEPARATOR + event.getString(PolicyEngineConstants.POLICY_KEY));
                        }
                        else if (request.containsKey(STATE) && request.getString(STATE).equalsIgnoreCase(STATE_REOPEN)) //means ticket reopen
                        {
                            IntegrationCacheStore.getStore().updateItem(IntegrationConstants.IntegrationType.SERVICENOW.getName(), event.getString(POLICY_ID) + SEPARATOR + event.getString(PolicyEngineConstants.POLICY_KEY), ackId, event.getLong(EVENT_TIMESTAMP), event.getString(SEVERITY));
                        }
                    }
                    else
                    {
                        LOGGER.warn(String.format("received failed response : %s", result.encode()));
                    }
                }
                else
                {
                    LOGGER.warn("integration is down!");

                    IntegrationCacheStore.getStore().addPendingEvent(IntegrationConstants.IntegrationType.SERVICENOW, event);

                    LOGGER.error(asyncResult.cause());
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });
    }

    /**
     * Prepares the request payload for creating a ticket in ServiceNow.
     * <p>
     * This method:
     * 1. Sets up the caller ID using the provided username
     * 2. Merges the integration profile context into the request
     * 3. Constructs the short description and detailed description based on event data
     * 4. Adds severity mapping for event-type tickets
     * 5. Adds additional metadata like metric name, type, node, and resource information
     * <p>
     * The method handles different scenarios:
     * - Creating incidents vs. events (different field requirements)
     * - Including instance information when available
     * - Setting appropriate severity levels based on the event severity
     *
     * @param event          The event object containing information for the ticket
     * @param createIncident Flag indicating whether to create an incident (true) or event (false)
     * @param username       The username to use as the caller ID
     * @return A JsonObject containing the prepared request payload
     */
    private JsonObject prepareCreateRequestPayload(JsonObject event, boolean createIncident, String username)
    {
        var context = new JsonObject();

        try
        {
            context.put(CALLER_ID, username);

            context.mergeIn(event.getJsonObject(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT));

            // remove the keys that are used by us.
            context.remove(IntegrationProfile.AUTO_CLOSE_TICKET_STATE);

            context.remove(IntegrationProfile.AUTO_CLOSE_TICKET_STATUS);

            var item = MetricPolicyConfigStore.getStore().getItem(event.getLong(PolicyEngineConstants.POLICY_ID));

            if (item != null)
            {
                if (event.containsKey(INSTANCE))
                {
                    context.put(createIncident ? SHORT_DESCRIPTION : MESSAGE_KEY, event.getString(SUBJECT, item.getString(POLICY_NAME, EMPTY_VALUE) + " - " + event.getString(AIOpsObject.OBJECT_NAME, EMPTY_VALUE) + " (" + event.getString(AIOpsObject.OBJECT_IP, EMPTY_VALUE) + ")" + " - " + event.getString(METRIC, EMPTY_VALUE) + " - " + event.getString(INSTANCE, EMPTY_VALUE)));

                    context.put(DESCRIPTION, PolicyEngineConstants.replaceMetricPolicyPlaceholders(item, event, event.getString(DESCRIPTION, DESCRIPTION_TEMPLATE + "\nInstance: $$$instance$$$"), event.getString(MESSAGE, EMPTY_VALUE)));
                }
                else
                {
                    context.put(createIncident ? SHORT_DESCRIPTION : MESSAGE_KEY, event.getString(SUBJECT, item.getString(POLICY_NAME, EMPTY_VALUE) + " - " + event.getString(AIOpsObject.OBJECT_NAME, EMPTY_VALUE) + " (" + event.getString(AIOpsObject.OBJECT_IP, EMPTY_VALUE) + ")" + " - " + event.getString(METRIC, EMPTY_VALUE)));

                    context.put(DESCRIPTION, PolicyEngineConstants.replaceMetricPolicyPlaceholders(item, event, event.getString(DESCRIPTION, DESCRIPTION_TEMPLATE), event.getString(MESSAGE, EMPTY_VALUE)));
                }
            }
            else
            {
                LOGGER.warn(String.format("incorrect policy id request received : %s", event.getLong(POLICY_ID)));
            }

            if (!createIncident)
            {
                switch (Severity.valueOf(event.getString(SEVERITY)))
                {
                    case CRITICAL, DOWN -> context.put(SEVERITY, 1);
                    case MAJOR -> context.put(SEVERITY, 2);
                    case WARNING -> context.put(SEVERITY, 4);
                    case CLEAR -> context.put(SEVERITY, 0).put(RESOLUTION_STATE, "Closing");
                }

                context.put(METRIC_NAME, event.getString(METRIC, EMPTY_VALUE));

                context.put(TYPE, event.getString(Metric.METRIC_TYPE, EMPTY_VALUE));

                context.put(NODE, event.getString(AIOpsObject.OBJECT_NAME));

                // in case of manual declare incident and alert.type=event then object.ip will be not present in the event
                context.put(RESOURCE, event.getString(AIOpsObject.OBJECT_IP, ObjectConfigStore.getStore().getItem(event.getLong(ENTITY_ID)).getString(AIOpsObject.OBJECT_IP)));

                context.put(SOURCE, "Motadata AIOps");
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return context;
    }

    /**
     * Prepares the request payload for updating an existing ticket in ServiceNow.
     * <p>
     * This method:
     * 1. Retrieves the policy information associated with the event
     * 2. Constructs a comment containing details about the event
     * 3. Uses placeholder substitution to include dynamic information in the comment
     * <p>
     * The comment includes information such as:
     * - Object name and IP
     * - Metric name and value
     * - Severity level
     * - Policy name and type
     * - Custom message
     *
     * @param event The event object containing information for the ticket update
     * @return A JsonObject containing the prepared update payload with comments
     */
    private JsonObject prepareUpdateRequestPayload(JsonObject event)
    {
        var context = new JsonObject();

        try
        {
            var item = MetricPolicyConfigStore.getStore().getItem(event.getLong(PolicyEngineConstants.POLICY_ID));

            if (item != null)
            {
                context.put(COMMENTS, PolicyEngineConstants.replaceMetricPolicyPlaceholders(item, event, event.getString(DESCRIPTION, DESCRIPTION_TEMPLATE), event.getString(MESSAGE)));
            }
        }
        catch (Exception exception)
        {
            LOGGER.warn(exception);
        }

        return context;
    }

    /**
     * Executes an HTTP request to ServiceNow using the circuit breaker pattern.
     * <p>
     * This method:
     * 1. Wraps the HTTP request in a circuit breaker to handle connection issues
     * 2. Processes the response from ServiceNow:
     * - Validates the HTTP status code (200 OK or 201 Created)
     * - Validates that the response is valid JSON
     * - Extracts the result from the response
     * 3. Handles various error scenarios:
     * - Circuit open exceptions (connection issues)
     * - Invalid responses
     * - HTTP errors
     * 4. Sends email notifications for failures when configured
     * <p>
     * The circuit breaker pattern helps prevent cascading failures when
     * ServiceNow is unavailable by failing fast and allowing for retries.
     *
     * @param supplier A supplier function that returns a Future with the HTTP request
     * @param event    The event object containing information for error handling
     * @return A Future containing the processed response as a JsonObject
     */
    private Future<JsonObject> execute(Supplier<Future<HttpResponse<Buffer>>> supplier, JsonObject event)
    {
        var future = Promise.<JsonObject>promise();

        try
        {
            // execute
            breaker.<JsonObject>execute(promise -> supplier.get().onComplete(result ->
            {
                try
                {
                    if (result.succeeded())
                    {
                        var response = result.result();

                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("received response with status code : %s and response body %s", response.statusCode(), response.bodyAsString()));
                        }

                        if (response.statusCode() == HttpStatus.SC_OK || response.statusCode() == HttpStatus.SC_CREATED)
                        {
                            if (CommonUtil.validJSONResponse(response))
                            {
                                promise.complete(event.put(STATUS, STATUS_SUCCEED).mergeIn(response.bodyAsJsonObject()));
                            }
                            else
                            {
                                promise.fail(String.format("ticket creation failed with status code %s and response : %s", response.statusCode(), response.bodyAsBuffer().toString()));
                            }
                        }
                        else
                        {
                            promise.fail(String.format("ticket creation failed with status code %s and response : %s", response.statusCode(), response.bodyAsBuffer().toString()));
                        }
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        promise.fail(result.cause());
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    promise.fail(exception.getCause());
                }
            })).onComplete(result ->
            {
                if (result.succeeded())
                {
                    future.complete(result.result());
                }
                else
                {
                    sendMail(result.cause().getMessage(), event);

                    future.fail(result.cause());
                }
            });
        }
        catch (OpenCircuitException exception) // if the circuit is in open state in that case if any event reaches here to execute then circuit will raise this exception hence will add into pending events
        {
            LOGGER.error(exception);

            IntegrationCacheStore.getStore().addPendingEvent(IntegrationConstants.IntegrationType.SERVICENOW, event);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            future.fail(exception.getCause());
        }

        return future.future();
    }

    /**
     * Sends an email notification when a ServiceNow integration operation fails.
     * <p>
     * This method:
     * 1. Saves the error message to a temporary file
     * 2. Constructs an email notification with:
     * - The error message as an attachment
     * - Standard icons and images
     * - A formatted HTML template with error details
     * 3. Sends the email using the email notification event bus
     * 4. Cleans up the temporary file after sending
     * 5. Sends a test integration event in development mode
     * <p>
     * The email includes details such as:
     * - Timestamp of the failure
     * - Integration type (ServiceNow)
     * - Error message
     * - Target ServiceNow instance URL
     *
     * @param message The error message to include in the notification
     * @param event   The event object containing recipient information and other details
     */
    private void sendMail(String message, JsonObject event)
    {
        var fileName = DateTimeUtil.currentSeconds() + "-response.txt";

        vertx.fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + fileName, Buffer.buffer(message));

        // send fail over email
        Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_EMAIL_NOTIFICATION, new JsonObject()
                        .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline")
                        .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                        .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(fileName).add("critical.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                        .put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format(INTEGRATION_FAILED_NOTIFICATION_SUBJECT, IntegrationConstants.IntegrationType.SERVICENOW.getName()))
                        .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, event.getJsonArray(Notification.EMAIL_NOTIFICATION_RECIPIENTS))
                        .put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject().put(TIME_STAMP, DateTimeUtil.timestamp(System.currentTimeMillis())).put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICENOW.getName()).put(MESSAGE, "Failed to create ticket!").put(GlobalConstants.TARGET, event.getString(GlobalConstants.TARGET)).getMap()).replace(Notification.EMAIL_INTEGRATION_FAILED_HTML_TEMPLATE).getBytes(StandardCharsets.UTF_8)), new DeliveryOptions().setSendTimeout(600000L),
                reply ->
                        vertx.fileSystem().deleteBlocking(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + fileName));

        if (MotadataConfigUtil.devMode())
        {
            vertx.eventBus().send("test.integration", event.put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICENOW.getName()).put(MESSAGE, message));
        }
    }

    /**
     * Synchronizes ServiceNow attributes with the local system.
     * <p>
     * This method:
     * 1. Ensures only one synchronization job runs at a time using the RUNNING flag
     * 2. Retrieves credential information for authentication
     * 3. Constructs the base URL for ServiceNow API calls
     * 4. Initiates parallel synchronization of different attribute types:
     * - Categories and subcategories
     * - Services
     * - Technicians
     * - Impact levels
     * - Urgency levels
     * 5. Updates the integration configuration in the database when all synchronizations complete
     * <p>
     * The method uses a Future.join pattern to run all synchronization tasks in parallel
     * and update the database only when all tasks have completed.
     */
    private void syncAttributes()
    {
        try
        {
            if (!RUNNING.get())
            {
                LOGGER.info("Sync job started!");

                RUNNING.set(true);

                var item = CredentialProfileConfigStore.getStore().getItem(integration.getJsonObject(INTEGRATION_CONTEXT).getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, 0L), true);

                if (integration != null && !integration.isEmpty()
                        && integration.containsKey(INTEGRATION_CONTEXT) && !integration.getJsonObject(INTEGRATION_CONTEXT).isEmpty()
                        && integration.getJsonObject(INTEGRATION_CONTEXT).containsKey(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE)
                        && item != null)
                {
                    var context = integration.getJsonObject(INTEGRATION_CONTEXT);

                    if (item.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
                    {
                        item.mergeIn(item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                        item.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                    }

                    var url = context.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                            ? context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "api/now/table/"
                            : context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/api/now/table/";

                    var futures = new ArrayList<Future<Void>>();

                    futures.add(syncCategories(url, context, integration.getJsonObject(INTEGRATION_ATTRIBUTES), item));

                    futures.add(syncServices(url, context, integration.getJsonObject(INTEGRATION_ATTRIBUTES), item));

                    futures.add(syncTechnicians(url, context, integration.getJsonObject(INTEGRATION_ATTRIBUTES), item));

                    futures.add(syncImpacts(url, context, integration.getJsonObject(INTEGRATION_ATTRIBUTES), item));

                    futures.add(syncUrgencies(url, context, integration.getJsonObject(INTEGRATION_ATTRIBUTES), item));

                    Future.join(futures).onComplete(result ->
                            Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_INTEGRATION,
                                    new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, integration.getLong(ID)),
                                    integration, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                                    {
                                        RUNNING.set(false);

                                        if (asyncResult.succeeded())
                                        {
                                            IntegrationConfigStore.getStore().updateItem(integration.getLong(ID));
                                        }
                                        else
                                        {
                                            LOGGER.error(asyncResult.cause());
                                        }
                                    }));
                }
            }
            else
            {
                LOGGER.warn("Sync job is already running!");
            }
        }
        catch (Exception exception)
        {
            RUNNING.set(false);

            LOGGER.error(exception);
        }
    }

    /**
     * Synchronizes categories and subcategories from ServiceNow.
     * <p>
     * This method:
     * 1. Retrieves all categories from ServiceNow's sys_choice table
     * 2. Maps the categories to a local data structure
     * 3. Retrieves all subcategories and associates them with their parent categories
     * 4. Updates the integration attributes with the synchronized category information
     * <p>
     * The method uses asynchronous HTTP requests and returns a Future that completes
     * when the synchronization is finished.
     *
     * @param url               The base URL for ServiceNow API calls
     * @param context           The integration context containing configuration parameters
     * @param attributes        The integration attributes object to be updated
     * @param credentialProfile The credential profile for authentication
     * @return A Future that completes when the synchronization is finished
     */
    private Future<Void> syncCategories(String url, JsonObject context, JsonObject attributes, JsonObject credentialProfile)
    {
        var promise = Promise.<Void>promise();

        // category and sub category sync
        webClient.getAbs(url + "sys_choice?sysparm_query=name=incident^element=category")
                .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                .basicAuthentication(credentialProfile.getString(USERNAME, EMPTY_VALUE), credentialProfile.getString(PASSWORD, EMPTY_VALUE))
                .send(result ->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            var response = result.result();

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("received response for category : %s", response.bodyAsString()));
                            }

                            if (response.statusCode() == HttpStatus.SC_OK)
                            {
                                if (CommonUtil.validJSONResponse(result.result()))
                                {
                                    if (response.bodyAsJsonObject().containsKey(GlobalConstants.RESULT)
                                            && !response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT).isEmpty())
                                    {
                                        var items = response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT);

                                        var categories = new HashMap<String, JsonObject>();

                                        for (var index = 0; index < items.size(); index++)
                                        {
                                            var category = items.getJsonObject(index);

                                            categories.put(category.getString(VALUE), new JsonObject()
                                                    .put(ID, category.getString(VALUE))
                                                    .put(NAME, category.getString(LABEL))
                                                    .put("subcategories", new JsonArray()));
                                        }

                                        webClient.getAbs(url + "sys_choice?sysparm_query=name=incident^element=subcategory")
                                                .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                                                .basicAuthentication(credentialProfile.getString(USERNAME, EMPTY_VALUE), credentialProfile.getString(PASSWORD, EMPTY_VALUE))
                                                .send(asyncResult ->
                                                {
                                                    try
                                                    {
                                                        if (asyncResult.succeeded())
                                                        {
                                                            var httpResponse = asyncResult.result();

                                                            if (CommonUtil.traceEnabled())
                                                            {
                                                                LOGGER.trace(String.format("received response for sub-category : %s", httpResponse.bodyAsString()));
                                                            }

                                                            if (httpResponse.statusCode() == HttpStatus.SC_OK)
                                                            {
                                                                if (CommonUtil.validJSONResponse(httpResponse))
                                                                {
                                                                    if (httpResponse.bodyAsJsonObject().containsKey(GlobalConstants.RESULT)
                                                                            && !httpResponse.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT).isEmpty())
                                                                    {
                                                                        var subCategories = httpResponse.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT);

                                                                        for (var index = 0; index < subCategories.size(); index++)
                                                                        {
                                                                            var subCategory = subCategories.getJsonObject(index);

                                                                            var category = categories.get(subCategory.getString(DEPENDENT_VALUE));

                                                                            if (category != null)
                                                                            {
                                                                                category.getJsonArray("subcategories").add(new JsonObject()
                                                                                        .put(ID, subCategory.getString(VALUE))
                                                                                        .put(NAME, subCategory.getString(LABEL)));
                                                                            }
                                                                        }
                                                                    }
                                                                    else
                                                                    {
                                                                        LOGGER.warn("no sub-categories found!");
                                                                    }
                                                                }
                                                                else
                                                                {
                                                                    LOGGER.warn("invalid json response received in sub-category sync");
                                                                }
                                                            }
                                                            else
                                                            {
                                                                LOGGER.warn(String.format("invalid status code received in sub-category sync : %s", asyncResult.result().statusCode()));
                                                            }

                                                            attributes.put("categories", new JsonArray(categories.values().stream().toList()));

                                                            promise.complete();
                                                        }
                                                        else
                                                        {
                                                            LOGGER.error(asyncResult.cause());

                                                            promise.fail(asyncResult.cause());
                                                        }
                                                    }
                                                    catch (Exception exception)
                                                    {
                                                        LOGGER.error(exception);

                                                        promise.fail(exception);
                                                    }
                                                });
                                    }
                                    else
                                    {
                                        LOGGER.warn("no categories found!");

                                        promise.fail("no categories found!");
                                    }
                                }
                                else
                                {
                                    LOGGER.warn("invalid json response received in category sync");

                                    promise.fail("invalid json response received in category sync");
                                }
                            }
                            else
                            {
                                LOGGER.warn(String.format("invalid status code received in category sync : %s", result.result().statusCode()));

                                promise.fail(String.format("invalid status code received in category sync : %s", result.result().statusCode()));
                            }
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            promise.fail(result.cause());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        promise.fail(exception);
                    }
                });

        return promise.future();
    }

    /**
     * Synchronizes services and service offerings from ServiceNow.
     * <p>
     * This method:
     * 1. Retrieves all services from ServiceNow's cmdb_ci_service table
     * 2. Filters for top-level services (those without a parent)
     * 3. Retrieves all service offerings from the service_offering table
     * 4. Associates service offerings with their parent services
     * 5. Updates the integration attributes with the synchronized service information
     * <p>
     * The method uses asynchronous HTTP requests and returns a Future that completes
     * when the synchronization is finished.
     *
     * @param url               The base URL for ServiceNow API calls
     * @param context           The integration context containing configuration parameters
     * @param attributes        The integration attributes object to be updated
     * @param credentialProfile The credential profile for authentication
     * @return A Future that completes when the synchronization is finished
     */
    private Future<Void> syncServices(String url, JsonObject context, JsonObject attributes, JsonObject credentialProfile)
    {
        var promise = Promise.<Void>promise();

        // services and service offerings sync
        webClient.getAbs(url + "cmdb_ci_service")
                .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                .basicAuthentication(credentialProfile.getString(USERNAME, EMPTY_VALUE), credentialProfile.getString(PASSWORD, EMPTY_VALUE))
                .send(result ->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            var response = result.result();

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("received response for service : %s", response.bodyAsString()));
                            }

                            if (response.statusCode() == HttpStatus.SC_OK)
                            {
                                if (CommonUtil.validJSONResponse(response))
                                {
                                    if (response.bodyAsJsonObject().containsKey(GlobalConstants.RESULT)
                                            && !response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT).isEmpty())
                                    {
                                        var items = response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT);

                                        var services = new HashMap<String, JsonObject>();

                                        for (var index = 0; index < items.size(); index++)
                                        {
                                            var service = items.getJsonObject(index);

                                            if (!service.containsKey(PARENT) || service.getString(PARENT).isEmpty() || service.getJsonObject(PARENT) == null)
                                            {
                                                services.put(service.getString(SYS_ID), new JsonObject()
                                                        .put(ID, service.getString(SYS_ID))
                                                        .put(NAME, service.getString(NAME))
                                                        .put("service.offerings", new JsonArray()));
                                            }
                                        }

                                        webClient.getAbs(url + "service_offering?sysparm_display_value=all")
                                                .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                                                .basicAuthentication(credentialProfile.getString(USERNAME, EMPTY_VALUE), credentialProfile.getString(PASSWORD, EMPTY_VALUE))
                                                .send(asyncResult ->
                                                {
                                                    try
                                                    {
                                                        if (asyncResult.succeeded())
                                                        {
                                                            var httpResponse = asyncResult.result();

                                                            if (CommonUtil.traceEnabled())
                                                            {
                                                                LOGGER.trace(String.format("received response for service-offering : %s", httpResponse.bodyAsString()));
                                                            }

                                                            if (httpResponse.statusCode() == HttpStatus.SC_OK)
                                                            {
                                                                if (CommonUtil.validJSONResponse(httpResponse))
                                                                {
                                                                    if (httpResponse.bodyAsJsonObject().containsKey(GlobalConstants.RESULT)
                                                                            && !httpResponse.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT).isEmpty())
                                                                    {
                                                                        var serviceOfferings = httpResponse.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT);

                                                                        for (var index = 0; index < serviceOfferings.size(); index++)
                                                                        {
                                                                            var serviceOffering = serviceOfferings.getJsonObject(index);

                                                                            var service = services.get(serviceOffering.getJsonObject(PARENT).getString(VALUE));

                                                                            if (service != null)
                                                                            {
                                                                                service.getJsonArray("service.offerings").add(new JsonObject()
                                                                                        .put(ID, serviceOffering.getJsonObject(SYS_ID).getString(VALUE))
                                                                                        .put(NAME, serviceOffering.getJsonObject(NAME).getString(VALUE)));
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                                else
                                                                {
                                                                    LOGGER.warn("invalid json response received in service-offering sync");
                                                                }
                                                            }
                                                            else
                                                            {
                                                                LOGGER.warn(String.format("invalid status code received in service-offering sync : %s", asyncResult.result().statusCode()));
                                                            }

                                                            attributes.put("services", new JsonArray(services.values().stream().toList()));

                                                            promise.complete();
                                                        }
                                                        else
                                                        {
                                                            LOGGER.error(asyncResult.cause());

                                                            promise.fail(asyncResult.cause());
                                                        }
                                                    }
                                                    catch (Exception exception)
                                                    {
                                                        LOGGER.error(exception);

                                                        promise.fail(exception);
                                                    }
                                                });
                                    }
                                    else
                                    {
                                        LOGGER.warn("no services found!");

                                        promise.fail("no services found!");
                                    }
                                }
                                else
                                {
                                    LOGGER.warn("invalid json response received in service sync");

                                    promise.fail("invalid json response received in service sync");
                                }
                            }
                            else
                            {
                                LOGGER.warn(String.format("invalid status code received in service sync : %s", result.result().statusCode()));

                                promise.fail(String.format("invalid status code received in service sync : %s", result.result().statusCode()));
                            }
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            promise.fail(result.cause());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        promise.fail(exception);
                    }
                });

        return promise.future();
    }

    /**
     * Synchronizes technicians and their groups from ServiceNow.
     * <p>
     * This method:
     * 1. Retrieves all user groups from ServiceNow's sys_user_group table
     * 2. For each group, retrieves the technicians (users) that belong to that group
     * 3. Creates a hierarchical structure of groups and their associated technicians
     * 4. Updates the integration attributes with the synchronized technician information
     * <p>
     * The method uses parallel asynchronous HTTP requests for each group to improve performance
     * and returns a Future that completes when all synchronizations are finished.
     *
     * @param url               The base URL for ServiceNow API calls
     * @param context           The integration context containing configuration parameters
     * @param attributes        The integration attributes object to be updated
     * @param credentialProfile The credential profile for authentication
     * @return A Future that completes when the synchronization is finished
     */
    private Future<Void> syncTechnicians(String url, JsonObject context, JsonObject attributes, JsonObject credentialProfile)
    {
        var promise = Promise.<Void>promise();

        var groups = new JsonArray();

        webClient.getAbs(url + "sys_user_group")
                .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                .basicAuthentication(credentialProfile.getString(USERNAME, EMPTY_VALUE), credentialProfile.getString(PASSWORD, EMPTY_VALUE))
                .send(result ->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            var response = result.result();

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("received response for group: %s", result.result().bodyAsString()));
                            }

                            if (response.statusCode() == HttpStatus.SC_OK)
                            {
                                if (CommonUtil.validJSONResponse(response))
                                {
                                    if (response.bodyAsJsonObject().containsKey(GlobalConstants.RESULT)
                                            && !response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT).isEmpty())
                                    {
                                        var items = response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT);

                                        var futures = new ArrayList<Future<Void>>();

                                        for (var i = 0; i < items.size(); i++)
                                        {
                                            var future = Promise.<Void>promise();

                                            futures.add(future.future());

                                            var item = items.getJsonObject(i);

                                            var group = item.getString(NAME);

                                            var userGroup = new JsonObject();

                                            userGroup.put(ID, item.getString(SYS_ID));

                                            userGroup.put(NAME, group);

                                            userGroup.put("technicians", new JsonArray());

                                            webClient.getAbs(url + "sys_user_grmember?sysparm_display_value=all&sysparm_query=group.name=" + URLEncoder.encode(group, StandardCharsets.UTF_8))
                                                    .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                                                    .basicAuthentication(credentialProfile.getString(USERNAME, EMPTY_VALUE), credentialProfile.getString(PASSWORD, EMPTY_VALUE))
                                                    .send(asyncResult ->
                                                    {
                                                        try
                                                        {
                                                            if (asyncResult.succeeded())
                                                            {
                                                                var httpResponse = asyncResult.result();

                                                                if (httpResponse.statusCode() == HttpStatus.SC_OK)
                                                                {
                                                                    if (CommonUtil.validJSONResponse(httpResponse))
                                                                    {
                                                                        if (httpResponse.bodyAsJsonObject().containsKey(GlobalConstants.RESULT)
                                                                                && !httpResponse.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT).isEmpty())
                                                                        {
                                                                            var technicians = httpResponse.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT);

                                                                            for (var j = 0; j < technicians.size(); j++)
                                                                            {
                                                                                var technician = technicians.getJsonObject(j);

                                                                                userGroup.getJsonArray("technicians").add(new JsonObject()
                                                                                        .put(ID, technician.getJsonObject(USER).getString(VALUE))
                                                                                        .put(NAME, technician.getJsonObject(USER).getString(DISPLAY_VALUE)));
                                                                            }
                                                                        }
                                                                        else
                                                                        {
                                                                            LOGGER.warn(String.format("no technician found for group : %s", group));
                                                                        }
                                                                    }
                                                                    else
                                                                    {
                                                                        LOGGER.warn("invalid json response received in technician sync");
                                                                    }
                                                                }
                                                                else
                                                                {
                                                                    LOGGER.warn(String.format("invalid status code received in technician sync : %s", asyncResult.result().statusCode()));
                                                                }

                                                                groups.add(userGroup);

                                                                future.complete();
                                                            }
                                                            else
                                                            {
                                                                LOGGER.error(asyncResult.cause());

                                                                future.fail(asyncResult.cause());
                                                            }
                                                        }
                                                        catch (Exception exception)
                                                        {
                                                            LOGGER.error(exception);

                                                            future.fail(exception);
                                                        }
                                                    });
                                        }

                                        Future.join(futures).onComplete(asyncResult ->
                                        {
                                            if (asyncResult.succeeded())
                                            {
                                                if (!groups.isEmpty())
                                                {
                                                    attributes.put("groups", groups);
                                                }

                                                promise.complete();
                                            }
                                            else
                                            {
                                                promise.fail(asyncResult.cause());
                                            }
                                        });
                                    }
                                    else
                                    {
                                        LOGGER.warn("no groups found!");

                                        promise.fail("no groups found!");
                                    }
                                }
                                else
                                {
                                    LOGGER.warn("invalid json response received in group sync");

                                    promise.fail("invalid json response received in group sync");
                                }
                            }
                            else
                            {
                                LOGGER.warn(String.format("invalid status code received in group sync : %s", result.result().statusCode()));

                                promise.fail(String.format("invalid status code received in group sync : %s", result.result().statusCode()));
                            }
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            promise.fail(result.cause());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        promise.fail(exception);
                    }
                });

        return promise.future();
    }

    /**
     * Synchronizes impact levels from ServiceNow.
     * <p>
     * This method:
     * 1. Retrieves all impact levels from ServiceNow's sys_choice table
     * 2. Maps each impact level to a local data structure with ID and name
     * 3. Updates the integration attributes with the synchronized impact information
     * <p>
     * Impact levels are used in incident creation to specify the severity of an incident.
     *
     * @param url               The base URL for ServiceNow API calls
     * @param context           The integration context containing configuration parameters
     * @param attributes        The integration attributes object to be updated
     * @param credentialProfile The credential profile for authentication
     * @return A Future that completes when the synchronization is finished
     */
    private Future<Void> syncImpacts(String url, JsonObject context, JsonObject attributes, JsonObject credentialProfile)
    {
        var promise = Promise.<Void>promise();

        // impacts sync
        webClient.getAbs(url + "sys_choice?sysparm_query=name=task^element=impact")
                .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                .basicAuthentication(credentialProfile.getString(USERNAME, EMPTY_VALUE), credentialProfile.getString(PASSWORD, EMPTY_VALUE))
                .send(asyncResult ->
                {
                    try
                    {
                        if (asyncResult.succeeded())
                        {
                            var response = asyncResult.result();

                            if (response.statusCode() == HttpStatus.SC_OK)
                            {
                                if (CommonUtil.validJSONResponse(response))
                                {
                                    if (response.bodyAsJsonObject().containsKey(GlobalConstants.RESULT)
                                            && !response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT).isEmpty())
                                    {
                                        var result = response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT);

                                        var impacts = new JsonArray();

                                        for (var index = 0; index < result.size(); index++)
                                        {
                                            var impact = result.getJsonObject(index);

                                            impacts.add(new JsonObject()
                                                    .put(ID, impact.getString(SYS_ID))
                                                    .put(NAME, impact.getString(LABEL)));
                                        }

                                        attributes.put("impacts", impacts);

                                        promise.complete();
                                    }
                                    else
                                    {
                                        LOGGER.warn("no impact found");

                                        promise.fail("no impact found");
                                    }
                                }
                                else
                                {
                                    LOGGER.warn("invalid json response received in impacts sync");

                                    promise.fail("invalid json response received in impacts sync");
                                }
                            }
                            else
                            {
                                LOGGER.warn(String.format("invalid status code received in impact sync : %s", asyncResult.result().statusCode()));

                                promise.fail(String.format("invalid status code received in impact sync : %s", asyncResult.result().statusCode()));
                            }
                        }
                        else
                        {
                            LOGGER.error(asyncResult.cause());

                            promise.fail(asyncResult.cause());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        promise.fail(exception);
                    }
                });

        return promise.future();
    }

    /**
     * Synchronizes urgency levels from ServiceNow.
     * <p>
     * This method:
     * 1. Retrieves all urgency levels from ServiceNow's sys_choice table
     * 2. Maps each urgency level to a local data structure with ID and name
     * 3. Updates the integration attributes with the synchronized urgency information
     * <p>
     * Urgency levels are used in incident creation to specify how quickly an incident
     * needs to be resolved.
     *
     * @param url               The base URL for ServiceNow API calls
     * @param context           The integration context containing configuration parameters
     * @param attributes        The integration attributes object to be updated
     * @param credentialProfile The credential profile for authentication
     * @return A Future that completes when the synchronization is finished
     */
    private Future<Void> syncUrgencies(String url, JsonObject context, JsonObject attributes, JsonObject credentialProfile)
    {
        var promise = Promise.<Void>promise();

        webClient.getAbs(url + "sys_choice?sysparm_query=name=task^element=urgency")
                .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                .basicAuthentication(credentialProfile.getString(USERNAME, EMPTY_VALUE), credentialProfile.getString(PASSWORD, EMPTY_VALUE))
                .send(asyncResult ->
                {
                    try
                    {
                        if (asyncResult.succeeded())
                        {
                            var response = asyncResult.result();

                            if (response.statusCode() == HttpStatus.SC_OK)
                            {
                                if (CommonUtil.validJSONResponse(response))
                                {
                                    if (response.bodyAsJsonObject().containsKey(GlobalConstants.RESULT)
                                            && response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT) != null
                                            && !response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT).isEmpty())
                                    {
                                        var result = response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT);

                                        var urgencies = new JsonArray();

                                        for (var index = 0; index < result.size(); index++)
                                        {
                                            var urgency = result.getJsonObject(index);

                                            urgencies.add(new JsonObject()
                                                    .put(ID, urgency.getString(SYS_ID))
                                                    .put(NAME, urgency.getString(LABEL)));
                                        }

                                        attributes.put("urgencies", urgencies);

                                        promise.complete();
                                    }
                                    else
                                    {
                                        promise.fail("no urgency found!");

                                        LOGGER.warn("no urgency found!");
                                    }
                                }
                                else
                                {
                                    LOGGER.warn("invalid json response received in urgency sync");

                                    promise.fail("invalid json response received in urgency sync");
                                }
                            }
                            else
                            {
                                LOGGER.warn(String.format("invalid status code received in urgency sync : %s", asyncResult.result().statusCode()));

                                promise.fail(String.format("invalid status code received in urgency sync : %s", asyncResult.result().statusCode()));
                            }
                        }
                        else
                        {
                            LOGGER.error(asyncResult.cause());

                            promise.fail(asyncResult.cause());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        promise.fail(exception);
                    }
                });

        return promise.future();
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        eventEngine.stop(vertx, promise);
    }
}
