/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.agent;

import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Agent;
import com.mindarray.api.Metric;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.AgentConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.ObjectManagerCacheStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.EVENT_AGENT_HEALTH_STATS;
import static com.mindarray.eventbus.EventBusConstants.EVENT_TIMESTAMP;

/**
 * The MetricAgentEventProcessor class is responsible for processing metric data from agents.
 * <p>
 * This verticle handles the processing of metric data collected by metric agents, including:
 * <ul>
 *   <li>Receiving raw metric data from agents</li>
 *   <li>Enriching the data with object information</li>
 *   <li>Determining the appropriate metric plugin based on the object type</li>
 *   <li>Processing system-specific metrics (CPU, disk, network, processes, etc.)</li>
 *   <li>Forwarding the enriched data to the metric enricher for further processing</li>
 * </ul>
 * <p>
 * The processor uses an event engine to consume metric events from the event bus and
 * processes them based on the object type and metric type. It handles metrics from
 * different operating systems (Windows, Linux, IBM AIX) and different resource types.
 * <p>
 * This class is a key component in the metric processing pipeline, bridging the gap
 * between raw metric collection and metric enrichment/storage.
 */
public class MetricAgentEventProcessor extends AbstractVerticle
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(MetricAgentEventProcessor.class, MOTADATA_AGENT, "Metric Agent Event Processor");

    /**
     * Flag to indicate when processing of the current event is complete
     */
    private final AtomicBoolean done = new AtomicBoolean(false);

    /**
     * Event engine for consuming metric events from the event bus
     */
    private EventEngine eventEngine;

    /**
     * Initializes the MetricAgentEventProcessor verticle and sets up the event engine.
     * <p>
     * This method configures an event engine to consume metric events from the event bus.
     * The event engine is configured to:
     * <ul>
     *   <li>Listen for metric agent events</li>
     *   <li>Persist event offsets for reliable processing</li>
     *   <li>Process events using the handler defined in this method</li>
     * </ul>
     * <p>
     * The event handler enriches metric data with object information, determines the
     * appropriate metric plugin based on the object type and operating system, and
     * forwards the enriched data to the metric enricher for further processing.
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        // Configure and start the event engine for processing metric agent events
        eventEngine = new EventEngine()
                .setLogger(LOGGER)
                .setEventType(EventBusConstants.EVENT_METRIC_AGENT)
                .setPersistEventOffset(true)
                .setEventHandler(event ->
                {
                    try
                    {
                        var object = AgentConfigStore.getStore().getAgentId(event.getString(Agent.AGENT_UUID)) != null
                                ? ObjectConfigStore.getStore().getItemByAgentId(AgentConfigStore.getStore().getAgentId(event.getString(Agent.AGENT_UUID))) : null;

                        if (object != null)
                        {
                            event.mergeIn(object);

                            var context = new JsonObject(event.getString(EventBusConstants.EVENT_CONTEXT));

                            event.remove(EventBusConstants.EVENT_CONTEXT);

                            if (!context.containsKey(EVENT_TIMESTAMP) || context.getValue(EVENT_TIMESTAMP) == null)
                            {
                                context.put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds());
                            }

                            event.put(EVENT_TIMESTAMP, context.remove(EVENT_TIMESTAMP));

                            event.put(RESULT, context);

                            var objectType = event.getString(AIOpsObject.OBJECT_TYPE);

                            context.stream().takeWhile(entry -> !done.get()).forEach(entry ->
                            {
                                try
                                {
                                    if (!entry.getKey().equalsIgnoreCase(NMSConstants.CORRELATION_METRICS))
                                    {
                                        if (entry.getValue() instanceof JsonArray || entry.getValue() instanceof List)
                                        {
                                            var type = NMSConstants.Type.valueOfName(entry.getKey());

                                            var plugin = switch (type)
                                            {
                                                case SYSTEM_PROCESS, SYSTEM_PROCESS_NETWORK_CONNECTION ->
                                                        objectType.equalsIgnoreCase(NMSConstants.Type.WINDOWS.getName()) ? NMSConstants.MetricPlugin.WINDOWS_PROCESS.getName() :
                                                                objectType.equalsIgnoreCase(NMSConstants.Type.IBM_AIX.getName()) ? NMSConstants.MetricPlugin.IBM_AIX_PROCESS.getName()
                                                                        : NMSConstants.MetricPlugin.LINUX_PROCESS.getName();

                                                case SYSTEM_DISK_VOLUME ->
                                                        objectType.equalsIgnoreCase(NMSConstants.Type.WINDOWS.getName()) ? NMSConstants.MetricPlugin.WINDOWS_DISK.getName() :
                                                                objectType.equalsIgnoreCase(NMSConstants.Type.IBM_AIX.getName()) ? NMSConstants.MetricPlugin.IBM_AIX_DISK.getName()
                                                                        : NMSConstants.MetricPlugin.LINUX_DISK.getName();

                                                case SYSTEM_CPU_CORE ->
                                                        objectType.equalsIgnoreCase(NMSConstants.Type.WINDOWS.getName()) ? NMSConstants.MetricPlugin.WINDOWS_CPU_CORE.getName() :
                                                                objectType.equalsIgnoreCase(NMSConstants.Type.IBM_AIX.getName()) ? NMSConstants.MetricPlugin.IBM_AIX_CPU_CORE.getName()
                                                                        : NMSConstants.MetricPlugin.LINUX_CPU_CORE.getName();

                                                case SYSTEM_SERVICE ->
                                                        NMSConstants.MetricPlugin.WINDOWS_SERVICE.getName();

                                                case SYSTEM_NETWORK_INTERFACE ->
                                                        objectType.equalsIgnoreCase(NMSConstants.Type.WINDOWS.getName()) ? NMSConstants.MetricPlugin.WINDOWS_NETWORK_INTERFACE.getName() :
                                                                objectType.equalsIgnoreCase(NMSConstants.Type.IBM_AIX.getName()) ? NMSConstants.MetricPlugin.IBM_AIX_NETWORK_INTERFACE.getName()
                                                                        : NMSConstants.MetricPlugin.LINUX_NETWORK_INTERFACE.getName();

                                                default -> EMPTY_VALUE;
                                            };

                                            if (CommonUtil.isNotNullOrEmpty(plugin))
                                            {
                                                event.mergeIn(ObjectManagerCacheStore.getStore().getItemByMetricPlugin(plugin));

                                                event.put(Metric.METRIC_CATEGORY, NMSConstants.getCategory(NMSConstants.Type.valueOfName(objectType)).getName());

                                                event.put(Metric.METRIC_OBJECT, event.getLong(ID));

                                                done.set(true);
                                            }
                                        }

                                        else
                                        {
                                            event.mergeIn(ObjectManagerCacheStore.getStore().getItemByMetricPlugin(objectType.equalsIgnoreCase(NMSConstants.Type.WINDOWS.getName()) ? NMSConstants.MetricPlugin.WINDOWS.getName() :
                                                    objectType.equalsIgnoreCase(NMSConstants.Type.IBM_AIX.getName()) ? NMSConstants.MetricPlugin.IBM_AIX.getName()
                                                            : NMSConstants.MetricPlugin.LINUX.getName()));

                                            event.put(Metric.METRIC_CATEGORY, NMSConstants.getCategory(NMSConstants.Type.valueOfName(objectType)).getName());

                                            event.put(Metric.METRIC_OBJECT, event.getLong(ID));

                                            done.set(true);
                                        }
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            });

                            if (done.get())
                            {
                                vertx.eventBus().send(EVENT_AGENT_HEALTH_STATS, event);

                                event.mergeIn(JsonObject.mapFrom(event.remove(Metric.METRIC_CONTEXT)));

                                vertx.eventBus().send(EventBusConstants.EVENT_METRIC_ENRICHER, event);

                                done.set(false);
                            }

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("agent poll response: %s", CommonUtil.removeSensitiveFields(event, false).encodePrettily()));
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }

                }).start(vertx, promise);
    }

    /**
     * Stops the MetricAgentEventProcessor verticle and shuts down the event engine.
     * <p>
     * This method gracefully stops the event engine, ensuring that any in-progress
     * event processing is completed and event offsets are properly persisted before
     * shutting down.
     *
     * @param promise Promise to be completed when shutdown is done
     */
    @Override
    public void stop(Promise<Void> promise)
    {
        // Stop the event engine and complete the promise when done
        eventEngine.stop(vertx, promise);
    }
}
