/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date            Author         Notes
 *  3-Mar-2025      Chandresh      MOTADATA-4822: Added patch 8.0.17
 *  17-Mar-2025     Chandresh      MOTADATA-5379: Added patch 8.0.18
 *   5-Mar-2025     Pruthviraj     Bypass the netroute policy flap duration cache store
 *  24-Apr-2025     Chopra Deven   Bypass the TagConfigStore as we are going to init in ObjectConfigStore
 */

package com.mindarray;

import com.fasterxml.jackson.core.StreamReadConstraints;
import com.mindarray.agent.AgentEventProcessor;
import com.mindarray.agent.MetricAgentEventProcessor;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.cache.AvailabilityCacheService;
import com.mindarray.cache.CacheService;
import com.mindarray.cache.ConfigCacheService;
import com.mindarray.cache.PolicyCacheService;
import com.mindarray.compliance.ComplianceDBService;
import com.mindarray.compliance.ComplianceManager;
import com.mindarray.compliance.ComplianceRuleEngine;
import com.mindarray.config.ConfigConstants;
import com.mindarray.config.ConfigManager;
import com.mindarray.config.ConfigResponseProcessor;
import com.mindarray.config.ConfigUpgradeManager;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.db.ConfigDBService;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.flow.FlowListener;
import com.mindarray.flow.FlowProcessor;
import com.mindarray.ha.HAConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.log.LogProcessor;
import com.mindarray.log.TCPLogListener;
import com.mindarray.log.UDPLogListener;
import com.mindarray.nms.*;
import com.mindarray.patch.*;
import com.mindarray.plugin.PluginEngine;
import com.mindarray.policy.MetricPolicyInspector;
import com.mindarray.policy.MetricPolicyTriggerDurationCalculator;
import com.mindarray.store.AbstractCacheStore;
import com.mindarray.store.AbstractConfigStore;
import com.mindarray.store.LicenseCacheStore;
import com.mindarray.util.*;
import io.vertx.core.*;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.core.json.jackson.DatabindCodec;
import io.vertx.ext.dropwizard.DropwizardMetricsOptions;
import org.apache.commons.io.FileUtils;
import org.zeromq.ZMQ;

import javax.management.Attribute;
import javax.management.ObjectName;
import java.io.File;
import java.lang.management.ManagementFactory;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.LogManager;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.EVENT_TYPE;


/**
 * Central bootstrap and lifecycle management class for the Motadata application.
 * <p>
 * This class is responsible for:
 * 1. Initializing the Vert.x instance and core services
 * 2. Starting and stopping application components (verticles)
 * 3. Managing application lifecycle (startup, shutdown, patching)
 * 4. Providing access to shared services and resources
 * 5. Handling configuration and deployment modes
 * <p>
 * The Bootstrap class follows a singleton pattern with static methods and fields,
 * serving as the central coordination point for the entire application. It manages
 * different deployment modes (standalone, primary, secondary, agent, etc.) and
 * initializes the appropriate components based on the current mode.
 * <p>
 * The application uses a modular architecture based on Vert.x verticles, with
 * event-driven communication through the event bus.
 */
public class Bootstrap
{
    private static final Logger LOGGER = new Logger(Bootstrap.class, GlobalConstants.MOTADATA_SYSTEM, "Bootstrap");
    private static final Vertx VERTX = Vertx.vertx(new VertxOptions().setEventLoopPoolSize(MotadataConfigUtil.getEventLoopWorkers())
            .setWorkerPoolSize(MotadataConfigUtil.getHelperWorkers())
            .setInternalBlockingPoolSize(MotadataConfigUtil.getInternalHelperWorkers())
            .setMaxWorkerExecuteTime(Duration.ofSeconds(600).toNanos())
            .setWarningExceptionTime(Duration.ofSeconds(600).toNanos())
            .setMetricsOptions(new DropwizardMetricsOptions().setEnabled(true).setJmxEnabled(true)));
    private static final ZMQ.Context CONTEXT = ZMQ.context(MotadataConfigUtil.getIOWorkers());
    private static final Map<String, String> deployedVerticles = new ConcurrentHashMap<>();
    private static final List<String> patches = List.of(Patch802.class.getName(), Patch804.class.getName(), Patch806.class.getName(), Patch807.class.getName(), Patch808.class.getName(), Patch809.class.getName(), Patch8010.class.getName(), Patch8011.class.getName(), Patch8012.class.getName(), Patch8013.class.getName(), Patch8014.class.getName(), Patch8015.class.getName(), Patch8016.class.getName(), Patch8017.class.getName(), Patch8018.class.getName(), Patch8019.class.getName(), Patch8020.class.getName(), Patch8021.class.getName(), Patch8022.class.getName());

    private static final Set<AbstractConfigStore.ConfigStore> PASSOVER_CONFIG_STORES = Set.of(AbstractConfigStore.ConfigStore.METRIC, AbstractConfigStore.ConfigStore.TAG);
    private static final Set<AbstractCacheStore.CacheStore> PASSOVER_CACHE_STORES = Set.of(AbstractCacheStore.CacheStore.OBJECT_MANAGER, AbstractCacheStore.CacheStore.TAG, AbstractCacheStore.CacheStore.METRIC_POLICY_FLAP_DURATION, AbstractCacheStore.CacheStore.NETROUTE_POLICY_FLAP_DURATION);
    private static final AtomicBoolean stopped = new AtomicBoolean(false);

    public static ConfigDBService configDBService;
    public static ComplianceDBService complianceDBService;
    public static CacheService cacheService;
    public static ConfigCacheService configCacheService;
    public static PolicyCacheService policyCacheService;
    public static AvailabilityCacheService availabilityCacheService;
    private static String registrationId;
    private static GlobalConstants.BootstrapType bootstrapType;
    private static boolean syncDone;
    private static String installationMode;

    static
    {
        try
        {
            bootstrapType = GlobalConstants.BootstrapType.MANAGER;

            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR);

            if (!file.exists())
            {
                file.mkdir();
            }

            file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CACHE_DIR);

            if (!file.exists())
            {
                file.mkdir();
            }

            file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.UPLOADS);

            if (!file.exists())
            {
                file.mkdir();
            }

            file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.UPLOADS + GlobalConstants.PATH_SEPARATOR + GlobalConstants.REPORTS);

            if (!file.exists())
            {
                file.mkdir();
            }

            file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.DOWNLOADS);

            if (!file.exists())
            {
                file.mkdir();
            }

            file = new File(ConfigConstants.CONFIG_MANAGEMENT_DEFAULT_DIR);

            if (!file.exists())
            {
                file.mkdir();

                CommonUtil.setFilePermissions(file, GlobalConstants.ALL_PERMISSION);
            }

            file = new File(ConfigConstants.CONFIG_FIRMWARE_DEFAULT_DIR);

            if (!file.exists())
            {
                file.mkdir();

                CommonUtil.setFilePermissions(file, GlobalConstants.ALL_PERMISSION);
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            System.exit(0);
        }
    }

    public static ConfigDBService configDBService()
    {
        return configDBService;
    }

    public static ComplianceDBService complianceDBService()
    {
        return complianceDBService;
    }

    public static CacheService cacheService()
    {
        return cacheService;
    }

    public static ConfigCacheService configCacheService()
    {
        return configCacheService;
    }

    public static PolicyCacheService policyCacheService()
    {
        return policyCacheService;
    }

    public static AvailabilityCacheService availabilityCacheService()
    {
        return availabilityCacheService;
    }

    public static ZMQ.Context zcontext()
    {
        return CONTEXT;
    }

    public static Map<String, String> getDeployedVerticles()
    {
        return deployedVerticles;
    }

    public static GlobalConstants.BootstrapType bootstrapType()
    {
        return bootstrapType;
    }

    public static String getInstallationMode()
    {
        return installationMode;
    }

    public static boolean syncDone()
    {
        return syncDone;
    }

    public static void setSyncDone(boolean syncDone)
    {
        Bootstrap.syncDone = syncDone;
    }

    public static String getRegistrationId()
    {
        return registrationId;
    }

    public static void setRegistrationId(String registrationId)
    {
        Bootstrap.registrationId = registrationId;
    }

    /**
     * Returns the shared Vert.x instance used throughout the application.
     * <p>
     * This method provides access to the singleton Vert.x instance that is
     * configured with appropriate thread pool sizes and metrics options.
     *
     * @return The shared Vert.x instance
     */
    public static Vertx vertx()
    {
        return VERTX;
    }

    /**
     * Application entry point that initializes and starts the Motadata application.
     * <p>
     * This method:
     * 1. Determines the bootstrap type from command-line arguments
     * 2. Initializes logging and configuration
     * 3. Loads application settings from motadata.json
     * 4. Verifies the Java agent
     * 5. Starts the appropriate bootstrap implementation based on bootstrap type and installation mode:
     * - MANAGER: Starts BootstrapManager for management operations
     * - OBSERVER: Starts BootstrapObserver for high-availability observation
     * - APP: Starts the main application in one of four modes:
     * - STANDALONE: Single-instance deployment
     * - PRIMARY: Primary node in high-availability setup
     * - SECONDARY: Secondary node in high-availability setup
     * - FAILOVER: Failover node in high-availability setup
     * - AGENT: Starts BootstrapAgent for remote agent functionality
     * - COLLECTOR, EVENT_COLLECTOR, EVENT_PROCESSOR, FLOW_COLLECTOR: Starts specialized collectors
     * 6. Registers a shutdown hook for graceful application termination
     * <p>
     * The method handles initialization errors by logging them and stopping the application
     * with an appropriate error message.
     *
     * @param args Command-line arguments, where the first argument specifies the bootstrap type
     */
    public static void main(String[] args)
    {
        try
        {
            if (args.length > 0 && args[0] != null && !args[0].isEmpty())
            {
                bootstrapType = GlobalConstants.BootstrapType.valueOf(CommonUtil.getString(args[0]).toUpperCase());
            }

            LOGGER.info(String.format("motadata initialization started %s...", bootstrapType.name()));

            // Apply the constraints to Vert.x's JSON codec default limit 50000 character json object key

            DatabindCodec.mapper().getFactory().setStreamReadConstraints(StreamReadConstraints.builder()
                    .maxNameLength(MotadataConfigUtil.getMaxNameLength()) // Set a higher limit if necessary
                    .build());

            LogManager.getLogManager().reset();

            MotadataConfigUtil.loadConfigs(new JsonObject(Files.readString(new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.MOTADATA_CONFIG_FILE).toPath(), StandardCharsets.UTF_8)));

            installationMode = MotadataConfigUtil.getInstallationMode();

            LOGGER.info(String.format("starting %s with mode %s ", bootstrapType, installationMode));

            deleteMetricAgentCacheFiles();

            if (MotadataConfigUtil.loadArtifactVersions())
            {
                if (!verifiedJavaAgent())
                {
                    switch (bootstrapType)
                    {
                        case MANAGER -> new BootstrapManager().start();

                        case OBSERVER -> new BootstrapObserver().start();

                        case APP ->
                        {
                            configDBService = ConfigDBService.createProxy(Bootstrap.vertx(), "config.db.service");

                            switch (GlobalConstants.InstallationMode.valueOf(installationMode))
                            {
                                case STANDALONE ->
                                {
                                    startCacheServices();

                                    new BootstrapStandalone().start();
                                }
                                case PRIMARY ->
                                {
                                    startCacheServices();

                                    new BootstrapPrimary().start();
                                }
                                case SECONDARY -> new BootstrapSecondary().start();

                                case FAILOVER -> new BootstrapFailover().start();
                            }
                        }

                        case AGENT -> new BootstrapAgent().start();

                        case COLLECTOR -> new BootstrapCollector().start();

                        case EVENT_COLLECTOR -> new BootstrapEventCollector().start();

                        case EVENT_PROCESSOR -> new BootstrapEventProcessor().start();

                        case FLOW_COLLECTOR -> new BootstrapFlowCollector().start();

                        default -> stop("Invalid bootstrap type");
                    }

                    Runtime.getRuntime().addShutdownHook(new Thread(() ->
                    {
                        if (bootstrapType.name().equalsIgnoreCase(GlobalConstants.BootstrapType.APP.name()) && (installationMode.equalsIgnoreCase(InstallationMode.PRIMARY.name()) || installationMode.equalsIgnoreCase(InstallationMode.SECONDARY.name())))
                        {
                            LOGGER.info(String.format("deattaching vip when stop signal received ... for bootstrap type %s ", bootstrapType.name()));

                            HAConstants.switchOverIP(HAConstants.HASyncOperation.DETACH.getName());
                        }

                        LOGGER.info("Shutdown request received...");

                        // somehow we need to wait for the stop method to complete.
                        var latch = new CountDownLatch(1);

                        Bootstrap.stop(true, null, false).onComplete(result -> latch.countDown());

                        try
                        {
                            latch.await(CommonUtil.getShutdownHookTimeoutSeconds(), TimeUnit.SECONDS);

                            LOGGER.info("Shutdown Successful!");

//                            Runtime.getRuntime().halt(0);
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    }));
                }
                else
                {
                    stop("motadata could not start...please remove agent or disable java instrumentation....");
                }
            }
            else
            {
                stop("motadata could not start... Invalid version file");
            }
        }
        catch (Exception exception)
        {
            stop(exception);
        }
    }

    private static void startCacheServices()
    {
        cacheService = CacheService.createProxy(Bootstrap.vertx(), "cache.service");

        availabilityCacheService = AvailabilityCacheService.createProxy(Bootstrap.vertx(), "availability.cache.service");

        configCacheService = ConfigCacheService.createProxy(Bootstrap.vertx(), "config.cache.service");

        policyCacheService = PolicyCacheService.createProxy(Bootstrap.vertx(), "policy.cache.service");
    }

    public static void initComplianceDBService()
    {
        complianceDBService = ComplianceDBService.createProxy(Bootstrap.vertx(), "compliance.db.service");
    }

    /**
     * Deploys a standard (non-worker) verticle in the Vert.x environment.
     * <p>
     * This method:
     * 1. Checks if the verticle should be deployed based on the current bootstrap type
     * 2. Verifies that the verticle hasn't already been deployed
     * 3. Deploys the verticle with standard (non-worker) deployment options
     * 4. Tracks the deployment ID in the deployedVerticles map
     * <p>
     * The method is used for verticles that perform non-blocking operations and
     * should run on the Vert.x event loop threads.
     *
     * @param verticle      The AbstractVerticle instance to deploy
     * @param name          A unique name for the verticle (used for logging and tracking)
     * @param bootstrapType The bootstrap type for which this verticle should be deployed,
     *                      or null to deploy regardless of bootstrap type
     * @return A Future that completes when the verticle is deployed, or fails with the cause of deployment failure
     */
    public static Future<Void> startEngine(AbstractVerticle verticle, String name, GlobalConstants.BootstrapType bootstrapType)
    {
        var promise = Promise.<Void>promise();

        try
        {
            if (bootstrapType == null || bootstrapType != Bootstrap.bootstrapType())
            {
                if (!deployedVerticles.containsKey(name))
                {
                    VERTX.deployVerticle(verticle, result ->
                    {
                        if (result.succeeded())
                        {
                            deployedVerticles.put(name, result.result());

                            LOGGER.info(String.format("%s started successfully...", name));

                            promise.complete();
                        }
                        else
                        {
                            LOGGER.fatal(String.format("failed to start %s...", name));

                            promise.fail(result.cause());
                        }
                    });
                }
                else
                {
                    LOGGER.info(String.format("%s is already deployed...", name));

                    promise.complete();
                }
            }
            else
            {
                promise.complete();
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getCause());
        }

        return promise.future();
    }

    /**
     * Deploys a worker verticle in the Vert.x environment.
     * <p>
     * This method:
     * 1. Checks if the verticle should be deployed based on the current bootstrap type
     * 2. Verifies that the verticle hasn't already been deployed
     * 3. Deploys the verticle with worker deployment options (multi-threaded worker)
     * 4. Tracks the deployment ID in the deployedVerticles map
     * <p>
     * Worker verticles are used for operations that might block the event loop,
     * such as CPU-intensive tasks or blocking I/O operations. They run on dedicated
     * worker threads rather than the Vert.x event loop threads.
     *
     * @param verticle      The AbstractVerticle instance to deploy as a worker
     * @param name          A unique name for the verticle (used for logging and tracking)
     * @param bootstrapType The bootstrap type for which this verticle should be deployed,
     *                      or null to deploy regardless of bootstrap type
     * @return A Future that completes when the verticle is deployed, or fails with the cause of deployment failure
     */
    public static Future<Void> startWorkerEngine(AbstractVerticle verticle, String name, GlobalConstants.BootstrapType bootstrapType)
    {
        var promise = Promise.<Void>promise();

        try
        {
            if (bootstrapType == null || bootstrapType != Bootstrap.bootstrapType())
            {
                if (!deployedVerticles.containsKey(name))
                {
                    VERTX.deployVerticle(verticle, new DeploymentOptions().setThreadingModel(ThreadingModel.WORKER).setWorkerPoolSize(1).setWorkerPoolName("wp." + name), result ->
                    {
                        if (result.succeeded())
                        {
                            deployedVerticles.put(name, result.result());

                            LOGGER.info(String.format("%s started successfully...", name));

                            promise.complete();
                        }
                        else
                        {
                            LOGGER.fatal(String.format("failed to start %s...", name));

                            promise.fail(result.cause());
                        }
                    });
                }
                else
                {
                    LOGGER.info(String.format("%s is already deployed...", name));

                    promise.complete();
                }
            }
            else
            {
                promise.complete();
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getCause());
        }

        return promise.future();
    }

    /**
     * Deploys a standard (non-worker) verticle in the Vert.x environment with event type configuration.
     * <p>
     * This method:
     * 1. Checks if the verticle should be deployed based on the current bootstrap type
     * 2. Verifies that the verticle hasn't already been deployed
     * 3. Deploys the verticle with standard (non-worker) deployment options and event type configuration
     * 4. Tracks the deployment ID in the deployedVerticles map
     * <p>
     * This version of startEngine includes an eventType parameter that is passed to the
     * verticle as part of its configuration. This allows the verticle to know which type
     * of events it should process, enabling more specialized behavior.
     *
     * @param verticle      The AbstractVerticle instance to deploy
     * @param name          A unique name for the verticle (used for logging and tracking)
     * @param bootstrapType The bootstrap type for which this verticle should be deployed,
     *                      or null to deploy regardless of bootstrap type
     * @param eventType     The type of events this verticle should process
     * @return A Future that completes when the verticle is deployed, or fails with the cause of deployment failure
     */
    public static Future<Void> startEngine(AbstractVerticle verticle, String name, GlobalConstants.BootstrapType bootstrapType, String eventType)
    {
        var promise = Promise.<Void>promise();

        try
        {
            if (bootstrapType == null || bootstrapType != Bootstrap.bootstrapType())
            {
                if (!deployedVerticles.containsKey(name))
                {
                    VERTX.deployVerticle(verticle, new DeploymentOptions().setConfig(new JsonObject().put(EVENT_TYPE, eventType)), result ->
                    {
                        if (result.succeeded())
                        {
                            deployedVerticles.put(name, result.result());

                            LOGGER.info(String.format("%s started successfully...", name));

                            promise.complete();
                        }
                        else
                        {
                            LOGGER.fatal(String.format("failed to start %s...", name));

                            promise.fail(result.cause());
                        }
                    });
                }
                else
                {
                    LOGGER.info(String.format("%s is already deployed...", name));

                    promise.complete();
                }
            }
            else
            {
                promise.complete();
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getCause());
        }

        return promise.future();
    }

    /**
     * Logs detailed system configuration information for diagnostic purposes.
     * <p>
     * This method retrieves and logs various system attributes through JMX, including:
     * - Operating system name and version
     * - System architecture
     * - Available processors
     * - Memory usage (committed, free physical, free swap)
     * - File descriptor counts
     * - CPU load and time
     * - Total memory and swap space
     * <p>
     * The information is collected using the platform MBean server and the
     * OperatingSystem MBean, then formatted and logged at INFO level.
     * This is useful for troubleshooting performance issues and understanding
     * the environment in which the application is running.
     */
    public static void logSystemConfig()
    {
        var configs = new StringBuilder();

        try
        {
            LOGGER.info("logging system config to check memory/os/cpu details...");

            var platformMBeanServer = ManagementFactory.getPlatformMBeanServer();

            configs.append("System Configurations: ").append(GlobalConstants.NEW_LINE);

            if (platformMBeanServer != null)
            {
                var objectName = ObjectName.getInstance("java.lang:type=OperatingSystem");

                var attributes = platformMBeanServer.getAttributes(objectName, new String[]{
                        "Name", "Arch", "AvailableProcessors", "CommittedVirtualMemorySize", "FreePhysicalMemorySize", "FreeSwapSpaceSize", "MaxFileDescriptorCount", "OpenFileDescriptorCount", "ProcessCpuLoad",

                        "ProcessCpuTime", "SystemCpuLoad", "SystemLoadAverage", "TotalSwapSpaceSize", "TotalPhysicalMemorySize", "Version"
                });

                if (attributes != null && !attributes.isEmpty())
                {
                    attributes.forEach(object ->
                    {
                        var attribute = (Attribute) object;

                        configs.append(attribute.getName().trim()).append(": ").append(attribute.getValue());

                        configs.append(GlobalConstants.NEW_LINE);
                    });

                }
            }

            LOGGER.info(configs.toString());

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Initializes all configuration and cache stores in the application.
     * <p>
     * This method:
     * 1. Iterates through all ConfigStore values and initializes each one
     * that's not in the PASSOVER_CONFIG_STORES set
     * 2. Iterates through all CacheStore values and initializes each one
     * that's not in the PASSOVER_CACHE_STORES set
     * 3. Waits for all store initializations to complete
     * 4. Initializes the LicenseCacheStore after all other stores are initialized
     * <p>
     * The method handles initialization in a non-blocking way, returning a Future
     * that completes when all stores have been initialized. If any store initialization
     * fails, the application is stopped with the cause of the failure.
     * <p>
     * Some stores are deliberately skipped during initialization (defined in
     * PASSOVER_CONFIG_STORES and PASSOVER_CACHE_STORES) because they are either
     * initialized elsewhere or require special handling.
     *
     * @return A Future that completes when all stores have been initialized,
     * or fails with the cause if any store initialization fails
     */
    public static Future<Void> initStores()
    {
        var promise = Promise.<Void>promise();

        var futures = new ArrayList<Future<Void>>();

        for (var configStore : AbstractConfigStore.ConfigStore.values())
        {
            try
            {
                var store = AbstractConfigStore.getConfigStore(configStore);

                if (!PASSOVER_CONFIG_STORES.contains(configStore) && store != null)
                {
                    futures.add(store.initStore());
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

        }

        for (var cacheStore : AbstractCacheStore.CacheStore.values())
        {
            try
            {
                var store = AbstractCacheStore.getCacheStore(cacheStore);

                if (!PASSOVER_CACHE_STORES.contains(cacheStore) && store != null)
                {
                    futures.add(store.initStore());
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }

        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                LicenseCacheStore.getStore().initStore().onComplete(asyncResult -> promise.complete());
            }
            else
            {
                stop(result.cause());
            }
        });

        return promise.future();
    }

    /**
     * Gracefully shuts down the application with configurable options.
     * <p>
     * This method performs a controlled shutdown of the application by:
     * 1. Closing resources (PortUtil, WebClientUtil, GeoDBUtil) if forceFully is true
     * 2. Shutting down the JobScheduler
     * 3. For APP bootstrap type:
     * - Writing license usage information
     * - Persisting dependencies and correlated resources
     * - Undeploying verticles (except those in passOverClass)
     * - Closing database services
     * 4. For other bootstrap types:
     * - Undeploying verticles (except those in passOverClass)
     * 5. Setting the stopped flag to true
     * 6. If forceFully is true:
     * - Closing the Vert.x instance and ZMQ context
     * - Exiting the JVM if exit is true
     * <p>
     * The method handles shutdown in a non-blocking way, returning a Future that
     * completes when the shutdown process is finished.
     *
     * @param forceFully      If true, closes all resources and optionally exits the JVM
     * @param passOverClasses A JsonArray of class names to skip during undeployment (can be null)
     * @param exit            If true and forceFully is true, exits the JVM after shutdown
     * @return A Future that completes when the shutdown process is finished
     */
    public static Future<Void> stop(boolean forceFully, JsonArray passOverClasses, boolean exit)
    {
        var promise = Promise.<Void>promise();

        try
        {
            if (forceFully)
            {
                PortUtil.close();

                WebClientUtil.close();

                WebClientUtil.closeProxy();

                GeoDBUtil.close();
            }

            JobScheduler.shutdownJobScheduler();

            if (Bootstrap.bootstrapType() == BootstrapType.APP)
            {
                LicenseUtil.writeLicenseUsage();

                var asyncFutures = new ArrayList<Future<Void>>();

                for (var dependencyType : AIOpsConstants.DependencyType.values())
                {
                    var future = Promise.<Void>promise();

                    asyncFutures.add(future.future());

                    writeDependencies(dependencyType).onComplete(asyncResult -> future.complete());
                }

                asyncFutures.add(writeCorrelatedResources());

                Future.join(asyncFutures).onComplete(result ->
                {
                    var futures = new ArrayList<Future<Void>>();

                    deployedVerticles.keySet().stream().filter(item -> passOverClasses == null || !passOverClasses.contains(item)).forEach(clazz -> futures.add(undeployVerticle(clazz)));

                    Future.join(futures).onComplete(asyncResult ->
                    {
                        var asyncFuture = Promise.<Void>promise();

                        if (bootstrapType == BootstrapType.APP)
                        {
                            configDBService.close(future -> asyncFuture.complete());

                            if (complianceDBService != null)
                            {
                                complianceDBService.close(future -> asyncFuture.complete());
                            }
                        }
                        else
                        {
                            asyncFuture.complete();
                        }

                        asyncFuture.future().onComplete(response ->
                        {
                            stopped.set(true);

                            if (forceFully)
                            {
                                VERTX.close();

                                CONTEXT.close();

                                promise.complete();

                                if (exit)
                                {
                                    LOGGER.info("motadata stopped forcefully...");

                                    System.exit(0);
                                }
                            }
                            else
                            {
                                promise.complete();
                            }
                        });
                    });
                });
            }
            else
            {
                var futures = new ArrayList<Future<Void>>();

                deployedVerticles.keySet().stream().filter(item -> passOverClasses == null || !passOverClasses.contains(item)).forEach(clazz -> futures.add(undeployVerticle(clazz)));

                Future.join(futures).onComplete(result ->
                {
                    stopped.set(true);

                    if (forceFully)
                    {
                        VERTX.close();

                        CONTEXT.close();

                        promise.complete();

                        if (exit)
                        {
                            LOGGER.info("motadata stopped forcefully...");

                            System.exit(0);
                        }
                    }
                    else
                    {
                        promise.complete();
                    }
                });
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            if (!promise.future().isComplete())
            {
                promise.fail(exception);
            }
        }

        return promise.future();
    }

    /**
     * Performs an emergency shutdown of the application due to an exception.
     * <p>
     * This method is called when a critical error occurs during application startup
     * or operation. It logs the error, then calls the main stop method with parameters
     * that force a full shutdown and exit the JVM.
     * <p>
     * This is a convenience method for handling exception-based shutdown scenarios
     * without having to specify all the parameters of the main stop method.
     *
     * @param cause The Throwable that caused the shutdown
     */
    public static void stop(Throwable cause)
    {
        LOGGER.fatal("failed to start core engine (s)...aborting motadata boot process...");

        LOGGER.error(cause);

        stop(true, null, true);
    }

    /**
     * Performs an emergency shutdown of the application due to an error message.
     * <p>
     * This method is called when a critical error condition is detected during
     * application startup or operation. It logs the error message, then calls
     * the main stop method with parameters that force a full shutdown and exit the JVM.
     * <p>
     * This is a convenience method for handling string-based error shutdown scenarios
     * without having to specify all the parameters of the main stop method.
     *
     * @param error The error message describing the reason for shutdown
     */
    public static void stop(String error)
    {
        LOGGER.fatal("failed to start core engine (s)...aborting motadata boot process...");

        LOGGER.warn(error);

        stop(true, null, true);
    }

    /**
     * Persists correlated availability resources to storage during shutdown.
     * <p>
     * This method sends a request to the availability correlation event bus address
     * to write correlated resources to persistent storage. This ensures that
     * availability correlation data is preserved across application restarts.
     * <p>
     * The method uses a 10-minute timeout (600000ms) to allow sufficient time for
     * the write operation to complete, which may involve a large amount of data.
     * It returns a Future that completes when the write operation is finished.
     *
     * @return A Future that completes when the correlated resources have been written
     */
    public static Future<Void> writeCorrelatedResources()
    {
        var promise = Promise.<Void>promise();

        Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_AVAILABILITY_CORRELATION + ".write", null, new DeliveryOptions().setSendTimeout(600000L), reply -> promise.complete());

        return promise.future();
    }

    /**
     * Persists dependencies of a specific type to storage during shutdown.
     * <p>
     * This method sends a request to the dependency event bus address for the specified
     * dependency type to write dependencies to persistent storage. This ensures that
     * dependency data is preserved across application restarts.
     * <p>
     * The method uses a 10-minute timeout (600000ms) to allow sufficient time for
     * the write operation to complete, which may involve a large amount of data.
     * It returns a Future that completes when the write operation is finished.
     *
     * @param dependencyType The type of dependencies to persist (e.g., LOCAL_DOMAIN, NETWORK, etc.)
     * @return A Future that completes when the dependencies have been written
     */
    public static Future<Void> writeDependencies(AIOpsConstants.DependencyType dependencyType)
    {
        var promise = Promise.<Void>promise();

        Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY + "." + dependencyType.getName() + ".write", null, new DeliveryOptions().setSendTimeout(600000L), reply -> promise.complete());

        return promise.future();
    }

    /**
     * Checks if a Java agent is attached to the JVM.
     * <p>
     * This method examines the JVM runtime arguments to detect if any Java agent
     * is attached using the -javaagent flag. This is a security measure to prevent
     * potential interception of sensitive information like passwords or encryption keys
     * by unauthorized agents.
     * <p>
     * If a Java agent is detected, the application may refuse to start as a security
     * precaution, as agents can potentially modify application behavior or intercept
     * sensitive data.
     *
     * @return true if a Java agent is attached, false otherwise
     */
    public static boolean verifiedJavaAgent()
    {
        var javaAgentAttached = false;   //check for any agent attached to intercept passwords/encryption keys etc...

        var runtimeMXBean = ManagementFactory.getRuntimeMXBean();

        var runtimeArgs = runtimeMXBean.getInputArguments();

        if (runtimeArgs != null && !runtimeArgs.isEmpty())
        {
            for (var runtimeArg : runtimeArgs)
            {
                if (runtimeArg.contains("-javaagent:"))
                {
                    //javaAgentAttached = true;

                    break; //agent already running hence aborting motadata boot process
                }
            }
        }

        return javaAgentAttached;
    }

    /**
     * Undeploys all licensed verticles based on enabled modules.
     * <p>
     * This method selectively undeploys verticles associated with licensed modules:
     * 1. If monitoring (NMS) is enabled, undeploys monitoring-related verticles:
     * - Agent processors
     * - SNMP trap processors
     * - Object managers and status calculators
     * - Metric enrichers and processors
     * - Discovery, topology, and rediscovery engines
     * - Plugin engines
     * - Metric pollers, schedulers, and policy inspectors
     * 2. If configuration management is enabled, undeploys config-related verticles
     * 3. If log management is enabled, undeploys log-related verticles
     * 4. If flow management is enabled, undeploys flow-related verticles
     * <p>
     * The method uses a chain of composed futures to ensure verticles are undeployed
     * in a controlled sequence, and returns a Future that completes when all
     * licensed verticles have been undeployed.
     *
     * @return A Future that completes when all licensed verticles have been undeployed
     */
    public static Future<Void> undeployLicensedVerticle()
    {
        var futures = new ArrayList<Future<Void>>();

        if (LicenseUtil.MONITORING_ENABLED.get()) //check for nms module...
        {
            var promise = Promise.<Void>promise();

            futures.add(promise.future());

            undeployVerticle(AgentEventProcessor.class.getSimpleName())
                    .compose(handler -> undeployVerticle(SNMPTrapProcessor.class.getSimpleName()))
                    .compose(handler -> undeployVerticle(ObjectManager.class.getSimpleName()))
                    .compose(handler -> undeployVerticle(ObjectStatusCalculator.class.getSimpleName()))
                    .compose(handler -> undeployVerticle(MetricEnricher.class.getSimpleName()))
                    .compose(handler -> undeployVerticle(ResponseProcessor.class.getSimpleName()))
                    .compose(handler -> undeployVerticle(MetricAgentEventProcessor.class.getSimpleName()))
                    .compose(handler -> undeployVerticle(DiscoveryEngine.class.getSimpleName()))
                    .compose(handler -> undeployVerticle(TopologyEngine.class.getSimpleName()))
                    .compose(handler -> undeployVerticle(RediscoverEngine.class.getSimpleName()))
                    .compose(handler -> undeployVerticle(PluginEngine.class.getSimpleName()))
                    .compose(handler -> undeployVerticle(MetricPoller.class.getSimpleName()))
                    .compose(handler -> undeployVerticle(MetricScheduler.class.getSimpleName()))
                    .compose(handler -> undeployVerticle(MetricPolicyInspector.class.getSimpleName()))
                    .compose(handler -> undeployVerticle(MetricPolicyTriggerDurationCalculator.class.getSimpleName()))
                    .compose(handler -> undeployVerticle(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()))
                    .onComplete(result ->
                    {
                        if (result.failed())
                        {
                            LOGGER.error(result.cause());
                            promise.fail(result.cause());
                        }
                        else
                        {
                            LOGGER.debug("Motadata engine undeployed successfully..!!");
                            promise.complete();
                        }
                    });
        }

        if (LicenseUtil.CONFIG_MANAGEMENT_ENABLED.get()) //check for config devices module...
        {
            var promise = Promise.<Void>promise();

            futures.add(promise.future());

            undeployVerticle(ConfigManager.class.getSimpleName())
                    .compose(handler -> undeployVerticle(ConfigResponseProcessor.class.getSimpleName()))
                    .compose(handler -> undeployVerticle(ConfigUpgradeManager.class.getSimpleName()))
                    .onComplete(result ->
                    {
                        if (result.failed())
                        {
                            LOGGER.error(result.cause());

                            promise.fail(result.cause());
                        }
                        else
                        {
                            LOGGER.info("Motadata Config engine un-deployed successfully..!!");

                            promise.complete();
                        }
                    });
        }

        if (LicenseUtil.LOG_ENABLED.get()) //check for log module...
        {
            var promise = Promise.<Void>promise();

            futures.add(promise.future());

            undeployVerticle(LogProcessor.class.getSimpleName())
                    .compose(handler -> undeployVerticle(LogProcessor.class.getSimpleName()))
                    .compose(handler -> undeployVerticle(TCPLogListener.class.getSimpleName()))
                    .compose(handler -> undeployVerticle(UDPLogListener.class.getSimpleName()))
                    // .compose(handler -> undeployVerticle(LogAgentEventProcessor.class.getSimpleName()))
                    .onComplete(result ->
                    {
                        if (result.failed())
                        {
                            LOGGER.error(result.cause());
                            promise.fail(result.cause());
                        }
                        else
                        {
                            LOGGER.debug("Log engine undeployed successfully..!!");
                            promise.complete();
                        }
                    });
        }

        if (LicenseUtil.FLOW_ENABLED.get()) //check for flow module...
        {
            var promise = Promise.<Void>promise();

            futures.add(promise.future());

            undeployVerticle(FlowListener.class.getSimpleName())
                    .compose(handler -> undeployVerticle(FlowProcessor.class.getSimpleName()))
                    .onComplete(result ->
                    {
                        if (result.failed())
                        {
                            LOGGER.error(result.cause());
                            promise.fail(result.cause());
                        }
                        else
                        {
                            LOGGER.debug("Flow engine undeployed successfully..!!");
                            promise.complete();
                        }
                    });
        }

        if (LicenseUtil.COMPLIANCE_ENABLED.get()) //check for config devices module...
        {
            var promise = Promise.<Void>promise();

            futures.add(promise.future());

            undeployVerticle(ComplianceManager.class.getSimpleName())
                    .compose(handler -> undeployVerticle(ComplianceRuleEngine.class.getSimpleName()))
                    .onComplete(result ->
                    {
                        if (result.failed())
                        {
                            LOGGER.error(result.cause());

                            promise.fail(result.cause());
                        }
                        else
                        {
                            LOGGER.info("Motadata Compliance engine un-deployed successfully..!!");

                            promise.complete();
                        }
                    });
        }

        var promise = Promise.<Void>promise();

        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                promise.complete();
            }
            else
            {
                promise.fail("Could not undeploy, reason" + result.cause().getMessage());
            }
        });

        return promise.future();
    }

    /**
     * Undeploys a specific verticle by its class name.
     * <p>
     * This method:
     * 1. Checks if the verticle is currently deployed (exists in deployedVerticles map)
     * 2. If deployed, calls Vert.x's undeploy method to stop the verticle
     * 3. Removes the verticle from the deployedVerticles map upon successful undeployment
     * 4. Logs the result of the undeployment operation
     * <p>
     * The method handles both successful and failed undeployment scenarios gracefully.
     * If the verticle is not found in the deployedVerticles map, the method completes
     * successfully without attempting to undeploy anything.
     *
     * @param clazz The class name of the verticle to undeploy
     * @return A Future that completes when the verticle is undeployed, or fails with the cause of undeployment failure
     */
    public static Future<Void> undeployVerticle(String clazz)
    {
        var promise = Promise.<Void>promise();

        if (deployedVerticles.containsKey(clazz))
        {
            VERTX.undeploy(deployedVerticles.remove(clazz), result ->
            {
                if (result.succeeded())
                {
                    LOGGER.info(clazz + " undeployed successfully");

                    promise.complete();
                }
                else
                {
                    LOGGER.warn(String.format("failed to undeploy %s, reason: %s", clazz, result.cause().getMessage()));

                    promise.fail(result.cause());
                }
            });
        }
        else
        {
            promise.complete();
        }

        return promise.future();
    }

    /**
     * Applies patches to the application based on version information.
     * <p>
     * This method is called after the configuration database is loaded and before
     * the system starts up. It handles two scenarios:
     * <p>
     * 1. For existing installations (upgrades):
     * - Executes all applicable patches in sequence based on version comparison
     * - Updates the system version in the database after each successful patch
     * <p>
     * 2. For fresh installations:
     * - Skips the patching process
     * - Sets the current version in the database to the latest version
     * <p>
     * The method uses the patches list defined in the Bootstrap class, which contains
     * the class names of all available patches. Each patch is instantiated and executed
     * only if its version is greater than the current system version.
     *
     * @param freshInstallation If true, indicates this is a fresh installation rather than an upgrade
     * @return A Future that completes when all applicable patches have been applied,
     * or fails with the cause if any patch fails to apply
     */
    public static Future<Void> patch(boolean freshInstallation)
    {
        var promise = Promise.<Void>promise();

        if (!freshInstallation)
        {
            executePatches(patches.toArray(), promise, new AtomicInteger(0));
        }
        else // add the latest version available in VERSION file in case of fresh installation
        {
            Bootstrap.configDBService().getOne(ConfigDBConstants.COLLECTION_SYSTEM, result ->
            {
                if (result.succeeded())
                {
                    Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_SYSTEM,
                            new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, DEFAULT_ID),
                            result.result().put(VERSION, MotadataConfigUtil.getVersion()), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    promise.complete();

                                    LOGGER.info("added a fresh installation version " + MotadataConfigUtil.getVersion());
                                }
                                else
                                {
                                    LOGGER.warn(asyncResult.cause());

                                    promise.fail(asyncResult.cause());
                                }
                            });
                }
                else
                {
                    LOGGER.warn(result.cause());

                    promise.fail(result.cause());
                }
            });
        }

        return promise.future();
    }

    /**
     * Recursively executes patches in sequence based on version comparison.
     * <p>
     * This private helper method for the patch() function:
     * 1. Checks if all patches have been processed (index >= classes.length)
     * 2. If not, instantiates the next patch class
     * 3. Retrieves the current system version from the database
     * 4. Compares the patch version with the current system version
     * 5. If the patch version is greater, executes the patch
     * 6. Updates the system version in the database after successful patch application
     * 7. Recursively calls itself to process the next patch
     * <p>
     * The method uses recursion rather than iteration to ensure patches are applied
     * in sequence, with each patch only starting after the previous one has completed
     * and the system version has been updated.
     *
     * @param classes An array of patch class names to process
     * @param promise The Promise to complete when all patches have been processed
     * @param index   The current index in the classes array
     */
    private static void executePatches(Object[] classes, Promise<Void> promise, AtomicInteger index)
    {
        try
        {
            if (index.get() >= classes.length)
            {
                promise.complete();
            }
            else
            {
                var clazz = classes[index.get()];

                try
                {
                    var patch = (Patch) Class.forName(clazz.toString()).getDeclaredConstructor().newInstance();

                    Bootstrap.configDBService().getOne(ConfigDBConstants.COLLECTION_SYSTEM, asyncResult ->
                    {
                        try
                        {
                            if (asyncResult.succeeded())
                            {
                                var result = asyncResult.result();

                                var currentVersion = result.containsKey(VERSION) ? result.getString(VERSION) : "8.0.0";

                                if (MotadataConfigUtil.upgradable(patch.getVersion(), currentVersion))
                                {
                                    LOGGER.info(String.format("executing patch : %s for current version: %s", clazz, currentVersion));

                                    patch.doPatch().onComplete(response ->
                                    {
                                        if (response.succeeded())
                                        {
                                            Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_SYSTEM,
                                                    new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, DEFAULT_ID),
                                                    result.put(VERSION, patch.getVersion()), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResponse ->
                                                    {
                                                        if (asyncResponse.succeeded())
                                                        {
                                                            index.set(index.incrementAndGet());

                                                            executePatches(classes, promise, index);
                                                        }
                                                        else
                                                        {
                                                            promise.fail(asyncResponse.cause());
                                                        }
                                                    });
                                        }
                                        else
                                        {
                                            promise.fail("failed to execute patch " + patch.getVersion());
                                        }
                                    });
                                }
                                else
                                {
                                    index.set(index.incrementAndGet());

                                    executePatches(classes, promise, index);
                                }
                            }
                            else
                            {
                                promise.fail(asyncResult.cause());
                            }
                        }
                        catch (Exception exception)
                        {
                            promise.fail(exception.getMessage());

                            LOGGER.error(exception);
                        }
                    });
                }
                catch (ClassCastException exception)
                {
                    LOGGER.error(exception);
                }
                catch (Exception exception)
                {
                    promise.fail(exception.getMessage());

                    LOGGER.error(exception);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Hot swaps a verticle by undeploying and redeploying it without stopping the application.
     * <p>
     * This method performs a runtime replacement of a verticle by:
     * 1. Undeploying the existing verticle instance using its class name
     * 2. Creating a new instance of the same verticle class
     * 3. Deploying the new instance with the same configuration
     * <p>
     * Hot swapping is useful for applying changes to a verticle's implementation
     * without restarting the entire application, such as during development or
     * when applying patches to a running system.
     * <p>
     * The method logs the hot swap operation and handles both successful and failed
     * scenarios appropriately.
     *
     * @param clazz The class of the verticle to hot swap
     * @return A Future that completes when the verticle has been successfully restarted,
     * or fails with the cause if either undeployment or deployment fails
     */
    public static Future<Void> restartVerticle(Class<? extends AbstractVerticle> clazz)
    {
        var promise = Promise.<Void>promise();

        LOGGER.info("hot swapping " + clazz.getSimpleName());

        undeployVerticle(clazz.getSimpleName()).onComplete(result ->
        {
            try
            {
                if (result.succeeded())
                {
                    LOGGER.info("starting engine " + clazz.getSimpleName());

                    startEngine(clazz.getConstructor().newInstance(), clazz.getSimpleName(), null).onComplete(asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            promise.complete();
                        }
                        else
                        {
                            promise.fail(asyncResult.cause());
                        }
                    });
                }
                else
                {
                    promise.fail(result.cause());
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                promise.fail(exception);
            }
        });

        return promise.future();
    }

    /**
     * Cleans up metric agent cache files during application startup.
     * <p>
     * This method deletes the cache directory for non-agent bootstrap types to prevent
     * false disk utilization warnings when the system restarts. The issue addressed is:
     * <p>
     * When a health agent reports disk utilization above 90% and the Motadata service
     * is stopped, the cache folder retains this data. Upon restart, the agent might
     * report disk utilization above 90% again based on this stale data, even if the
     * actual disk utilization has changed.
     * <p>
     * By clearing the cache directory during startup (except for AGENT bootstrap type),
     * the method ensures that disk utilization reports are based on fresh data rather
     * than stale cached information.
     * <p>
     * This functionality was implemented as part of MOTADATA-970.
     */
    private static void deleteMetricAgentCacheFiles()
    {
        if (!MotadataConfigUtil.getSystemBootstrapType().equalsIgnoreCase(GlobalConstants.BootstrapType.AGENT.name()))
        {
            LOGGER.info(String.format("Deleting cache dir for %s Bootstrap type", MotadataConfigUtil.getSystemBootstrapType()));

            FileUtils.deleteQuietly(new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.MOTADATA_CACHE));
        }
    }

}
