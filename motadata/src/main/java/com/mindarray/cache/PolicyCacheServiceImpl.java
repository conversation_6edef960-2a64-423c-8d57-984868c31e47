/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			        Notes
 *  13-Mar-2025		Pruthviraj Jadeja		MOTADATA-5331 : Get NetRoute policy added
 */

package com.mindarray.cache;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.integration.IntegrationEngine;
import com.mindarray.netroute.NetRouteConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.*;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.ENTITY_ID;
import static com.mindarray.api.MetricPolicy.POLICY_THRESHOLD;
import static com.mindarray.eventbus.EventBusConstants.EVENT_TIMESTAMP;
import static com.mindarray.policy.PolicyEngineConstants.*;
import static com.mindarray.policy.PolicyEngineConstants.DURATION;

/**
 * Implementation of the {@link PolicyCacheService} interface that provides methods for retrieving
 * cached data related to policies, including active policies, policy groups, and policy heatmaps.
 * <p>
 * This class reads cached data from various policy stores, processes the data as needed, and returns it
 * to the caller. It uses Vert.x's executeBlocking method to perform operations without blocking
 * the event loop.
 * <p>
 * The policy data is organized by policy type, severity, object, and other identifiers to allow
 * for efficient retrieval and visualization of specific policy information.
 *
 * @see PolicyCacheService
 * @see CacheServiceProvider
 */
public class PolicyCacheServiceImpl implements PolicyCacheService
{
    /**
     * Logger instance for this class.
     */
    private static final Logger LOGGER = new Logger(PolicyCacheServiceImpl.class, MOTADATA_CACHE, "Policy Cache Service");

    /**
     * The Vert.x instance used for executing blocking operations.
     */
    private final Vertx vertx;

    /**
     * Constructs a new PolicyCacheServiceImpl instance.
     *
     * @param vertx   The Vert.x instance
     * @param handler The handler to be called when the service is created
     */
    PolicyCacheServiceImpl(Vertx vertx, Handler<AsyncResult
            <PolicyCacheService>> handler)
    {
        this.vertx = vertx;

        handler.handle(Future.succeededFuture(this));
    }

    /**
     * {@inheritDoc}
     * <p>
     * This implementation executes a blocking operation to retrieve policy counts grouped by
     * the criteria specified in the context. It retrieves policy data from the MetricPolicyCacheStore
     * and groups the policies based on the specified grouping field or by severity.
     */
    @Override
    public PolicyCacheService getPoliciesByGroup(JsonObject context, Handler<AsyncResult<JsonObject>> handler)
    {
        vertx.<JsonObject>executeBlocking(future ->
                {
                    var result = getPolicyCounts(context);

                    if (!result.isEmpty())
                    {
                        handler.handle(Future.succeededFuture(result));
                    }
                    else
                    {
                        handler.handle(Future.failedFuture(ErrorMessageConstants.INVALID_DATA_SOURCE));
                    }

                }, false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

        return this;

    }

    /**
     * {@inheritDoc}
     * <p>
     * This implementation executes a blocking operation to build a policy heatmap based on the
     * context. It retrieves policy data from the MetricPolicyCacheStore and organizes it by
     * object, severity, and other criteria to create a heatmap visualization.
     */
    @Override
    public PolicyCacheService getPolicyHeatmap(JsonObject context, Handler<AsyncResult<JsonObject>> handler)
    {
        vertx.<JsonObject>executeBlocking(future ->
                {
                    var result = buildAlertHeatmap(context);

                    if (!result.isEmpty())
                    {
                        handler.handle(Future.succeededFuture(result));
                    }
                    else
                    {
                        handler.handle(Future.failedFuture(ErrorMessageConstants.INVALID_DATA_SOURCE));
                    }

                }, false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

        return this;
    }

    /**
     * {@inheritDoc}
     * <p>
     * This implementation executes a blocking operation to retrieve active policies based on the
     * context. It determines the appropriate policy type (event, metric, or network route) based
     * on the context parameters and retrieves the corresponding policy data from the appropriate
     * cache store. The results are sorted by timestamp in descending order.
     */
    @Override
    public PolicyCacheService getActivePolicies(JsonObject context, Handler<AsyncResult<JsonObject>> handler)
    {
        if (context.containsKey(VisualizationConstants.CATEGORY) && (context.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.LOG.getName()) || context.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.FLOW.getName()) || context.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.TRAP.getName())))
        {
            vertx.<JsonObject>executeBlocking(future -> getEventPolicies(context).onComplete(result ->
                            handler.handle(Future.succeededFuture(sort(result.result())))), false,
                    result ->
                    {
                        if (result.succeeded())
                        {
                            handler.handle(Future.succeededFuture(result.result()));
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            handler.handle(Future.failedFuture(result.cause()));
                        }
                    });
        }
        else if (context.containsKey(VisualizationConstants.GROUP_BY) && context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(APIConstants.Entity.OBJECT.getName().toLowerCase()))
        {
            vertx.<JsonObject>executeBlocking(future -> getObjectSeverities(context).onComplete(result ->
                            handler.handle(Future.succeededFuture(sort(result.result())))), false,
                    result ->
                    {
                        if (result.succeeded())
                        {
                            handler.handle(Future.succeededFuture(result.result()));
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            handler.handle(Future.failedFuture(result.cause()));
                        }
                    });
        }
        else if (context.containsKey(VisualizationConstants.CATEGORY) && (context.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.NETROUTE_METRIC.getName())
                || context.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.NETROUTE_EVENT.getName())))
        {
            vertx.<JsonObject>executeBlocking(future -> getNetRoutePolicies(context).onComplete(result ->
                            handler.handle(Future.succeededFuture(sort(result.result())))), false,
                    result ->
                    {
                        if (result.succeeded())
                        {
                            handler.handle(Future.succeededFuture(result.result()));
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            handler.handle(Future.failedFuture(result.cause()));
                        }
                    });
        }
        else
        {
            vertx.<JsonObject>executeBlocking(future -> getMetricPolicies(context).onComplete(result ->
                            handler.handle(Future.succeededFuture(sort(result.result())))), false,
                    result ->
                    {
                        if (result.succeeded())
                        {
                            handler.handle(Future.succeededFuture(result.result()));
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            handler.handle(Future.failedFuture(result.cause()));
                        }
                    });
        }

        return this;
    }

    /**
     * {@inheritDoc}
     * <p>
     * This implementation executes a blocking operation to retrieve the last triggered ticks for
     * policies based on the context. It queries the event bus for policy trigger tick data and
     * formats the results according to the data points specified in the context.
     */
    @Override
    public PolicyCacheService getPolicyLastTriggeredTicks(JsonObject context, Handler<AsyncResult<JsonObject>> handler)
    {
        vertx.<JsonObject>executeBlocking(future -> getMetricPolicyLastTriggeredTicks(context).onComplete(result ->
                        handler.handle(Future.succeededFuture(result.result()))), false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

        return this;
    }

    /**
     * Gets policy counts grouped by the criteria specified in the context.
     * <p>
     * This method retrieves policy data from the MetricPolicyCacheStore and groups the policies
     * based on the specified grouping field or by severity. It counts the number of policies in
     * each group and returns the counts in a JsonObject.
     *
     * @param context The context containing the parameters for the query
     * @return A JsonObject containing the policy counts by group
     */
    private JsonObject getPolicyCounts(JsonObject context)
    {
        var result = new JsonObject();

        if (context.containsKey(VisualizationConstants.GROUP_BY) && context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase("monitor"))
        {
            for (var entity : context.getJsonArray(ENTITIES))
            {
                var severity = MetricPolicyCacheStore.getStore().getSeverity(CommonUtil.getLong(entity));

                if (severity != null)
                {
                    setSeverityCounts(result, severity);
                }
            }
        }
        else
        {
            var items = MetricPolicyCacheStore.getStore().getItems(context.getJsonArray(ENTITIES));

            for (var item : items.entrySet())
            {
                try
                {
                    if (valid(item.getValue(), context))
                    {
                        var group = item.getValue().getString(context.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).getString(0));

                        setSeverityCounts(result, group);
                    }
                }

                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }

        }

        return result;
    }

    /**
     * Sets the severity counts in the result object.
     * <p>
     * This method increments the count for the specified group in the result object.
     * If the group doesn't exist in the result, it initializes the count to 1.
     *
     * @param result The result object to which to add the severity counts
     * @param group  The group for which to increment the count
     */
    private void setSeverityCounts(JsonObject result, String group)
    {
        var count = result.getInteger(group);

        if (count != null)
        {
            result.put(group, count + 1);
        }
        else
        {
            result.put(group, 1);
        }
    }

    /**
     * Retrieves metric policies based on the context.
     * <p>
     * This method retrieves metric policy data from the MetricPolicyCacheStore and formats it
     * according to the parameters specified in the context. It can group policies by various
     * criteria and calculate counts or retrieve detailed policy information.
     *
     * @param context The context containing the parameters for the query
     * @return A future that will be completed with the result
     */
    private Future<JsonObject> getMetricPolicies(JsonObject context)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            var items = MetricPolicyCacheStore.getStore().getItems(context.getJsonArray(ENTITIES));

            var result = new JsonObject();

            var severityResult = new AtomicBoolean();

            if (context.containsKey("severity.result"))
            {
                severityResult.set(true);
            }

            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_POLICY_TRIGGER_DURATION_QUERY, new JsonObject(), reply ->
            {
                try
                {
                    var keys = new ArrayList<String>();

                    var correlatedKeys = new ArrayList<String>();

                    var policies = new HashMap<String, Integer>();

                    var durations = reply.result().body();

                    var groupingFields = context.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY, new JsonArray());

                    if (context.containsKey(AIOpsConstants.CORRELATION_ROOT_OBJECT))
                    {
                        var key = EMPTY_VALUE;

                        if (CommonUtil.isNotNullOrEmpty(context.getString(METRIC)) && CommonUtil.isNotNullOrEmpty(context.getString(INSTANCE)))
                        {
                            key = context.getLong(AIOpsConstants.CORRELATION_ROOT_OBJECT) + SEPARATOR + PolicyType.AVAILABILITY.getName() + SEPARATOR + context.getString(METRIC) + SEPARATOR + context.getString(INSTANCE);
                        }
                        else
                        {
                            key = context.getLong(AIOpsConstants.CORRELATION_ROOT_OBJECT) + SEPARATOR + PolicyType.AVAILABILITY.getName() + SEPARATOR + STATUS;
                        }

                        var policy = items.get(key);

                        var dataPoints = context.getJsonArray(VisualizationConstants.DATA_POINTS);

                        if (policy != null)
                        {
                            var objects = policy.getJsonArray(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS);

                            for (var index = 0; index < objects.size(); index++)
                            {
                                var value = MetricPolicyCacheStore.getStore().getItem(objects.getString(index).hashCode());

                                if (value != null)
                                {
                                    buildWorklog(dataPoints, result, value, MetricPolicyConfigStore.getStore().getItem(value.getLong(ID)), durations);
                                }
                            }
                        }
                    }
                    else
                    {
                        for (var entry : items.entrySet())
                        {
                            try
                            {
                                keys.clear();

                                correlatedKeys.clear();

                                if (valid(entry.getValue(), context))
                                {
                                    var item = MetricPolicyConfigStore.getStore().getItem(entry.getValue().getLong(ID));

                                    if (!groupingFields.isEmpty())
                                    {
                                        groupingFields.forEach(groupingField ->
                                        {
                                            var field = CommonUtil.getString(groupingField);

                                            if (field.equalsIgnoreCase(POLICY_TYPE))
                                            {
                                                keys.add(item.getString(field));
                                            }
                                            else if (field.equalsIgnoreCase(AIOpsObject.OBJECT_ID))
                                            {
                                                keys.add(CommonUtil.getString(ObjectConfigStore.getStore().getObjectId(entry.getValue().getLong(ENTITY_ID))));
                                            }
                                            else if (field.equalsIgnoreCase(AIOpsObject.OBJECT_TYPE))
                                            {
                                                keys.add(ObjectConfigStore.getStore().getItem(entry.getValue().getLong(ENTITY_ID)).getString(AIOpsObject.OBJECT_TYPE));
                                            }
                                            else if (field.equalsIgnoreCase(AIOpsObject.OBJECT_CATEGORY))
                                            {
                                                keys.add(entry.getValue().getString(field));

                                                if (entry.getValue().containsKey(AIOpsConstants.CORRELATION_PROBE))
                                                {
                                                    correlatedKeys.add(entry.getValue().getString(SEVERITY));

                                                    correlatedKeys.add("Correlated Policies");
                                                }
                                            }
                                            else
                                            {
                                                keys.add(entry.getValue().getString(field));
                                            }
                                        });

                                        var group = StringUtils.join(keys, CARET_SEPARATOR);

                                        policies.merge(group, 1, Integer::sum);

                                        if (!correlatedKeys.isEmpty())
                                        {
                                            group = StringUtils.join(correlatedKeys, CARET_SEPARATOR);

                                            policies.merge(group, 1, Integer::sum);
                                        }
                                    }
                                    else
                                    {
                                        buildWorklog(context.getJsonArray(VisualizationConstants.DATA_POINTS), result, entry.getValue(), item, durations);
                                    }
                                }
                            }

                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                        }
                    }

                    if (!groupingFields.isEmpty())
                    {
                        if (!severityResult.get())
                        {
                            groupingFields.forEach(group -> result.put(CommonUtil.getString(group), new JsonArray()));

                            result.put(DatastoreConstants.AggregationType.COUNT.getName(), new JsonArray());

                            policies.forEach((key, value) ->
                            {
                                try
                                {
                                    var tokens = key.split(CARET_SEPARATOR_WITH_ESCAPE);

                                    for (var index = 0; index < groupingFields.size(); index++)
                                    {
                                        if (tokens[index] != null)
                                        {
                                            result.getJsonArray(groupingFields.getString(index)).add(tokens[index]);
                                        }
                                        else
                                        {
                                            result.getJsonArray(groupingFields.getString(index)).add(EMPTY_VALUE);
                                        }
                                    }

                                    result.getJsonArray(DatastoreConstants.AggregationType.COUNT.getName()).add(value);
                                }

                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            });
                        }
                        else
                        {
                            var entries = new HashMap<String, JsonObject>();

                            policies.forEach((key, value) ->
                            {
                                try
                                {
                                    var tokens = key.split(CARET_SEPARATOR_WITH_ESCAPE);

                                    entries.computeIfAbsent(tokens[0], val -> new JsonObject()).put(tokens[1] + "." + DatastoreConstants.AggregationType.COUNT.getName(), value);
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            });

                            for (var entry : entries.entrySet())
                            {
                                ((JsonArray) result.getMap().computeIfAbsent(APIConstants.Entity.OBJECT.getName(), value -> new JsonArray())).add(entry.getKey());

                                var value = entry.getValue();

                                for (var severity : context.getJsonArray(SEVERITY))
                                {
                                    ((JsonArray) result.getMap().computeIfAbsent(severity + "." + DatastoreConstants.AggregationType.COUNT.getName(), val -> new JsonArray())).add(value.containsKey(severity + "." + DatastoreConstants.AggregationType.COUNT.getName()) ? value.getValue(severity + "." + DatastoreConstants.AggregationType.COUNT.getName()) : 0);
                                }
                            }
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

                promise.complete(result);
            });

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return promise.future();
    }

    /**
     * Retrieves network route policies based on the context.
     * <p>
     * This method retrieves network route policy data from the NetRoutePolicyCacheStore or
     * queries the event bus for network route policy trigger data. It formats the data
     * according to the parameters specified in the context.
     *
     * @param context The context containing the parameters for the query
     * @return A future that will be completed with the result
     */
    private Future<JsonObject> getNetRoutePolicies(JsonObject context)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            if (context.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.NETROUTE_METRIC.getName()))
            {
                var items = NetRoutePolicyCacheStore.getStore().getItems(context.getJsonArray(ENTITIES));

                var result = new JsonObject();

                Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_NETROUTE_POLICY_TRIGGER_DURATION_QUERY, new JsonObject(), reply ->
                {
                    try
                    {
                        var keys = new ArrayList<String>();

                        var policies = new HashMap<String, Integer>();

                        var groupingFields = context.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY, new JsonArray());

                        var durations = reply.result().body();

                        for (var entry : items.entrySet())
                        {
                            try
                            {
                                if (valid(entry.getValue(), context))
                                {
                                    keys.clear();

                                    var item = NetRoutePolicyConfigStore.getStore().getItem(entry.getValue().getLong(ID));

                                    if (!groupingFields.isEmpty())
                                    {
                                        groupingFields.forEach(groupingField ->
                                                keys.add(entry.getValue().getString(CommonUtil.getString(groupingField))));

                                        policies.merge(StringUtils.join(keys, CARET_SEPARATOR), 1, Integer::sum);
                                    }
                                    else
                                    {
                                        buildWorklog(context.getJsonArray(VisualizationConstants.DATA_POINTS), result, entry.getValue(), item, durations);
                                    }
                                }
                            }

                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                        }

                        if (!groupingFields.isEmpty())
                        {
                            groupingFields.forEach(groupingField -> result.put(CommonUtil.getString(groupingField), new JsonArray()));

                            result.put(DatastoreConstants.AggregationType.COUNT.getName(), new JsonArray());

                            policies.forEach((key, value) ->
                            {
                                try
                                {
                                    var tokens = key.split(CARET_SEPARATOR_WITH_ESCAPE);

                                    for (var index = 0; index < groupingFields.size(); index++)
                                    {
                                        if (tokens[index] != null)
                                        {
                                            result.getJsonArray(groupingFields.getString(index)).add(tokens[index]);
                                        }
                                        else
                                        {
                                            result.getJsonArray(groupingFields.getString(index)).add(EMPTY_VALUE);
                                        }
                                    }

                                    result.getJsonArray(DatastoreConstants.AggregationType.COUNT.getName()).add(value);
                                }

                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            });
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }

                    promise.complete(result);
                });
            }
            else
            {
                Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_NETROUTE_POLICY_TRIGGER_TICK_QUERY, new JsonObject().put(PolicyEngineConstants.POLICY_EVALUATION_TYPE, NetRouteConstants.NetRouteType.HOP_BY_HOP.getName()), reply ->
                {
                    var result = new JsonObject();

                    try
                    {
                        var ticks = reply.result().body();

                        var dataPoints = context.getJsonArray(VisualizationConstants.DATA_POINTS);

                        ticks.getMap().forEach((key, tick) ->
                        {
                            var tokens = key.split(SEPARATOR_WITH_ESCAPE);

                            var policy = NetRoutePolicyConfigStore.getStore().getItem(CommonUtil.getLong(tokens[0]), false);

                            var item = NetRouteConfigStore.getStore().getItem(CommonUtil.getLong(tokens[1]), false);

                            for (var index = 0; index < dataPoints.size(); index++)
                            {
                                var dataPoint = dataPoints.getJsonObject(index).getString(VisualizationConstants.DATA_POINT);

                                result.getMap().computeIfAbsent(dataPoint, value -> new JsonArray());

                                if (dataPoint.equalsIgnoreCase(POLICY_ID))
                                {
                                    result.getJsonArray(dataPoint).add(CommonUtil.getLong(tokens[0]));
                                }
                                else if (dataPoint.equalsIgnoreCase(NetRoute.NETROUTE_ID))
                                {
                                    result.getJsonArray(dataPoint).add(CommonUtil.getLong(tokens[1]));
                                }
                                else if (dataPoint.equalsIgnoreCase(PolicyEngineConstants.POLICY_TAGS))
                                {
                                    result.getJsonArray(dataPoint).add(policy != null && policy.getJsonArray(PolicyEngineConstants.POLICY_TAGS) != null ? StringUtils.join(policy.getJsonArray(PolicyEngineConstants.POLICY_TAGS).getList(), COMMA_SEPARATOR) : EMPTY_VALUE);
                                }
                                else if (dataPoint.equalsIgnoreCase(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK))
                                {
                                    result.getJsonArray(dataPoint).add(tick != null ? ((JsonObject) tick).getLong(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK) : 0L);
                                }
                                else if (dataPoint.equalsIgnoreCase(PolicyEngineConstants.POLICY_LAST_TRIGGER_TICK))
                                {
                                    result.getJsonArray(dataPoint).add(tick != null ? ((JsonObject) tick).getLong(PolicyEngineConstants.POLICY_LAST_TRIGGER_TICK) : 0L);
                                }
                                else if (dataPoint.equalsIgnoreCase(SEVERITY))
                                {
                                    result.getJsonArray(dataPoint).add(policy != null ? policy.getString(POLICY_SEVERITY) : EMPTY_VALUE);
                                }
                                else if (dataPoint.equalsIgnoreCase(POLICY_THRESHOLD))
                                {
                                    result.getJsonArray(dataPoint).add(policy != null ? policy.getJsonObject(POLICY_CONTEXT).getJsonObject(POLICY_SEVERITY).getJsonObject(policy.getString(POLICY_SEVERITY)).getValue(POLICY_THRESHOLD) : EMPTY_VALUE);
                                }
                                else if (dataPoint.equalsIgnoreCase(NetRoute.NETROUTE_TAGS))
                                {
                                    result.getJsonArray(dataPoint).add(item != null && item.getJsonArray(NetRoute.NETROUTE_TAGS) != null ? StringUtils.join(TagConfigStore.getStore().getItems(item.getJsonArray(NetRoute.NETROUTE_TAGS)), COMMA_SEPARATOR) : EMPTY_VALUE);
                                }
                                else
                                {
                                    result.getJsonArray(dataPoint).add(policy != null && policy.containsKey(dataPoint) && policy.getValue(dataPoint) != null ? CommonUtil.getString(policy.getValue(dataPoint)) : EMPTY_VALUE);
                                }
                            }
                        });

                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }

                    promise.complete(result);
                });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return promise.future();
    }

    /**
     * Retrieves object severities based on the context.
     * <p>
     * This method retrieves severity information for objects from the MetricPolicyCacheStore
     * and groups the data by object type and severity. It counts the number of objects in each
     * group and returns the counts in a structured format.
     *
     * @param context The context containing the parameters for the query
     * @return A future that will be completed with the result
     */
    private Future<JsonObject> getObjectSeverities(JsonObject context)
    {
        var promise = Promise.<JsonObject>promise();

        var result = new JsonObject();

        var severities = new JsonObject();

        result.put(AIOpsObject.OBJECT_TYPE, new JsonArray()).put(SEVERITY, new JsonArray()).put("count", new JsonArray());

        for (var entity : context.getJsonArray(ENTITIES))
        {
            var severity = MetricPolicyCacheStore.getStore().getSeverity(CommonUtil.getLong(entity));

            if (severity != null)
            {
                setSeverityCounts(severities, ObjectConfigStore.getStore().getItem(CommonUtil.getLong(entity), false).getString(AIOpsObject.OBJECT_TYPE) + COLON_SEPARATOR + severity);
            }
        }

        if (!severities.isEmpty())
        {
            for (var entry : severities)
            {
                var tokens = entry.getKey().split(COLON_SEPARATOR);

                result.getJsonArray(AIOpsObject.OBJECT_TYPE).add(tokens[0]);

                result.getJsonArray(SEVERITY).add(tokens[1]);

                result.getJsonArray("count").add(entry.getValue());

            }

        }

        promise.complete(result);

        return promise.future();
    }

    /**
     * Retrieves event policies based on the context.
     * <p>
     * This method retrieves event policy data from the EventPolicyCacheStore and formats it
     * according to the data points specified in the context. It filters the policies based on
     * the category specified in the context.
     *
     * @param context The context containing the parameters for the query
     * @return A future that will be completed with the result
     */
    private Future<JsonObject> getEventPolicies(JsonObject context)
    {
        var promise = Promise.<JsonObject>promise();

        var result = new JsonObject();

        try
        {
            var dataPoints = context.getJsonArray(VisualizationConstants.DATA_POINTS);

            var ticks = EventPolicyCacheStore.getStore().getTriggerTicks();

            ticks.forEach((key, tick) ->
            {
                var item = EventPolicyConfigStore.getStore().getItem(CommonUtil.getLong(key));

                var valid = !context.containsKey(VisualizationConstants.CATEGORY) || context.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(item.getString(POLICY_TYPE));

                if (valid)
                {
                    for (var index = 0; index < dataPoints.size(); index++)
                    {
                        var dataPoint = dataPoints.getJsonObject(index).getString(VisualizationConstants.DATA_POINT);

                        result.getMap().computeIfAbsent(dataPoint, dataPointValue -> new JsonArray());

                        if (dataPoint.equalsIgnoreCase(POLICY_ID))
                        {
                            result.getJsonArray(dataPoint).add(CommonUtil.getLong(key));
                        }

                        else if (dataPoint.equalsIgnoreCase(PolicyEngineConstants.POLICY_TAGS))
                        {
                            result.getJsonArray(dataPoint).add(item != null && item.getJsonArray(PolicyEngineConstants.POLICY_TAGS) != null ? StringUtils.join(item.getJsonArray(PolicyEngineConstants.POLICY_TAGS).getList(), COMMA_SEPARATOR) : EMPTY_VALUE);
                        }

                        else if (dataPoint.equalsIgnoreCase(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK))
                        {
                            result.getJsonArray(dataPoint).add(tick != null ? tick.getLong(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK) : 0L);
                        }

                        else if (dataPoint.equalsIgnoreCase(PolicyEngineConstants.POLICY_LAST_TRIGGER_TICK))
                        {
                            result.getJsonArray(dataPoint).add(tick != null ? tick.getLong(PolicyEngineConstants.POLICY_LAST_TRIGGER_TICK) : 0L);
                        }

                        else
                        {
                            result.getJsonArray(dataPoint).add(item != null && item.containsKey(dataPoint) && item.getValue(dataPoint) != null ? CommonUtil.getString(item.getValue(dataPoint)) : EMPTY_VALUE);
                        }
                    }
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        promise.complete(result);

        return promise.future();
    }

    /**
     * Builds an alert heatmap based on the context.
     * <p>
     * This method builds a heatmap visualization of policy alerts based on the parameters
     * specified in the context. It can build heatmaps by applications or by objects,
     * depending on the context parameters.
     *
     * @param context The context containing the parameters for the query
     * @return A JsonObject containing the alert heatmap data
     */
    private JsonObject buildAlertHeatmap(JsonObject context)
    {
        var result = new JsonObject();

        if (context.containsKey(VisualizationConstants.GROUP_BY) && context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(VisualizationConstants.GROUP_APP))
        {
            buildHeatmapByApplications(context, result);
        }
        else
        {
            buildHeatmapByObjects(context, result);
        }

        return result;
    }

    /**
     * Builds a heatmap by objects based on the context.
     * <p>
     * This method builds a heatmap visualization of policy alerts grouped by objects.
     * It retrieves policy data from the MetricPolicyCacheStore and organizes it by
     * object, severity, and object type.
     *
     * @param context The context containing the parameters for the query
     * @param result  The result object to which to add the heatmap data
     */
    private void buildHeatmapByObjects(JsonObject context, JsonObject result)
    {
        var severities = new HashMap<Long, TreeSet<Severity>>();

        result.put(APIConstants.Entity.OBJECT.getName().toLowerCase(), new JsonArray()).put(SEVERITY, new JsonArray()).put(AIOpsObject.OBJECT_TYPE, new JsonArray());

        if (context.containsKey(POLICIES) && !context.getJsonArray(POLICIES).isEmpty())
        {
            var items = MetricPolicyCacheStore.getStore().getItems(context.getJsonArray(ENTITIES));

            items.forEach((key, value) ->
            {
                try
                {
                    if (valid(value, context))
                    {
                        var severity = value.getString(SEVERITY);

                        if (value.getString(INSTANCE) != null && value.getString(SEVERITY).equalsIgnoreCase(Severity.DOWN.name()))
                        {
                            severity = Severity.CRITICAL.name();
                        }

                        severities.computeIfAbsent(value.getLong(ENTITY_ID), val -> new TreeSet<>()).add(Severity.valueOf(severity));

                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });
        }
        else
        {
            var entities = context.getJsonArray(ENTITIES);

            for (var index = 0; index < entities.size(); index++)
            {
                var severity = MetricPolicyCacheStore.getStore().getSeverity(entities.getLong(index));

                if (severity != null)
                {
                    if (context.containsKey(SEVERITIES) && !context.getJsonArray(SEVERITIES).isEmpty())
                    {
                        if (context.getJsonArray(SEVERITIES).contains(severity))
                        {
                            severities.computeIfAbsent(entities.getLong(index), val -> new TreeSet<>()).add(Severity.valueOf(severity));
                        }
                    }
                    else
                    {
                        severities.computeIfAbsent(entities.getLong(index), val -> new TreeSet<>()).add(Severity.valueOf(severity));
                    }
                }
            }
        }

        severities.forEach((key, value) ->
        {
            try
            {
                result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).add(ObjectConfigStore.getStore().getObjectId(key));

                result.getJsonArray(AIOpsObject.OBJECT_TYPE).add(ObjectConfigStore.getStore().getItem(key).getString(AIOpsObject.OBJECT_TYPE));

                result.getJsonArray(SEVERITY).add(value.last().name());
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });
    }

    /**
     * Builds a heatmap by applications based on the context.
     * <p>
     * This method builds a heatmap visualization of policy alerts grouped by applications.
     * It retrieves policy data from the MetricPolicyCacheStore and organizes it by
     * object, application type, and severity.
     *
     * @param context The context containing the parameters for the query
     * @param result  The result object to which to add the heatmap data
     */
    private void buildHeatmapByApplications(JsonObject context, JsonObject result)
    {
        var severities = new HashMap<Long, Map<String, TreeSet<Severity>>>();

        result.put(APIConstants.Entity.OBJECT.getName().toLowerCase(), new JsonArray()).put(SEVERITY, new JsonArray()).put(Metric.METRIC_TYPE, new JsonArray());

        var items = MetricPolicyCacheStore.getStore().getItems(context.getJsonArray(ENTITIES));

        if (items != null && !items.isEmpty())
        {
            items.forEach((key, value) ->
            {
                try
                {
                    if (valid(value, context) && !value.getString(Metric.METRIC_TYPE).equalsIgnoreCase(EMPTY_VALUE) && NMSConstants.APPLICATION_TYPES.contains(value.getString(Metric.METRIC_TYPE).split(SEPARATOR_WITH_ESCAPE)[1].trim()))
                    {
                        var severity = value.getString(SEVERITY);

                        if (value.getString(INSTANCE) != null && value.getString(SEVERITY).equalsIgnoreCase(Severity.DOWN.name()))
                        {
                            severity = Severity.CRITICAL.name();
                        }

                        severities.computeIfAbsent(value.getLong(ENTITY_ID), val -> new HashMap<>()).computeIfAbsent(value.getString(Metric.METRIC_TYPE).split(SEPARATOR_WITH_ESCAPE)[1].trim(), val -> new TreeSet<>()).add(Severity.valueOf(severity));
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });
        }

        severities.forEach((key, value) ->
        {
            try
            {
                for (var entry : value.entrySet())
                {
                    result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).add(ObjectConfigStore.getStore().getObjectId(key));

                    result.getJsonArray(Metric.METRIC_TYPE).add(entry.getKey());

                    result.getJsonArray(SEVERITY).add(entry.getValue().last().name());
                }

            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });
    }

    /**
     * Sorts the active alert grid result based on timestamp in descending order.
     * <p>
     * This method sorts the rows in the result object by the timestamp field in descending order.
     * It creates a list of JsonObjects representing each row, sorts the list, and then
     * reconstructs the result object with the sorted rows.
     *
     * @param results The result object to sort
     * @return The sorted result object
     */
    private JsonObject sort(JsonObject results)
    {
        try
        {
            if (results != null && !results.isEmpty() && results.containsKey(GlobalConstants.TIME_STAMP))
            {
                var sortedRows = new ArrayList<JsonObject>();

                var size = results.getJsonArray(GlobalConstants.TIME_STAMP).size();

                for (var index = 0; index < size; index++)
                {
                    var row = new JsonObject();

                    for (var entry : results.getMap().entrySet())
                    {
                        var rows = entry.getValue() instanceof ArrayList arrayList ? new JsonArray(arrayList) : (JsonArray) entry.getValue();

                        row.put(entry.getKey(), rows.getValue(index));
                    }

                    sortedRows.add(row);
                }

                sortedRows.sort(Comparator.comparing(item -> JsonObject.mapFrom(item).getLong(GlobalConstants.TIME_STAMP)).reversed());

                results.clear();

                for (var row : sortedRows)
                {
                    row.getMap().forEach((key, value) ->
                            ((JsonArray) results.getMap().computeIfAbsent(key, val -> new JsonArray())).add(value));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return results;
    }

    /**
     * Retrieves the last triggered ticks for metric policies based on the context.
     * <p>
     * This method queries the event bus for metric policy trigger tick data and formats the
     * results according to the data points specified in the context. It filters the data
     * based on the entities specified in the context.
     *
     * @param context The context containing the parameters for the query
     * @return A future that will be completed with the result
     */
    private Future<JsonObject> getMetricPolicyLastTriggeredTicks(JsonObject context)
    {
        var promise = Promise.<JsonObject>promise();

        var result = new JsonObject();

        var entities = context.getJsonArray(ENTITIES, new JsonArray());

        Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_POLICY_TRIGGER_TICK_QUERY, new JsonObject(), reply ->
        {
            var items = reply.result().body();

            if (!entities.isEmpty())
            {
                items.getMap().forEach((key, value) ->
                {
                    try
                    {
                        var tokens = key.split(SEPARATOR_WITH_ESCAPE);

                        var entity = CommonUtil.getLong(tokens[0]);//monitor

                        if (entities.contains(entity))
                        {
                            var dataPoints = context.getJsonArray(VisualizationConstants.DATA_POINTS);

                            for (var index = 0; index < dataPoints.size(); index++)
                            {
                                var dataPoint = dataPoints.getJsonObject(index).getString(VisualizationConstants.DATA_POINT);

                                result.getMap().computeIfAbsent(dataPoint, dataPointValue -> new JsonArray());

                                if (dataPoint.equalsIgnoreCase(AIOpsObject.OBJECT_ID))
                                {
                                    result.getJsonArray(dataPoint).add(ObjectConfigStore.getStore().getObjectId(CommonUtil.getLong(tokens[0])));
                                }

                                else if (dataPoint.equalsIgnoreCase(PolicyEngineConstants.POLICY_ID))
                                {
                                    result.getJsonArray(dataPoint).add(CommonUtil.getLong(tokens[1]));
                                }

                                else if (dataPoint.equalsIgnoreCase(METRIC))
                                {
                                    result.getJsonArray(dataPoint).add(CommonUtil.getString(tokens[2]));
                                }

                                else if (dataPoint.equalsIgnoreCase(INSTANCE))
                                {
                                    result.getJsonArray(dataPoint).add(tokens.length > 3 ? CommonUtil.getString(tokens[3]) : EMPTY_VALUE);
                                }

                                else if (dataPoint.equalsIgnoreCase(POLICY_FIRST_TRIGGER_TICK))
                                {
                                    result.getJsonArray(dataPoint).add(value);
                                }
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                });
            }

            promise.complete(result);
        });

        return promise.future();
    }

    /**
     * Checks if a policy item is valid based on the context.
     * <p>
     * This method checks if a policy item matches the criteria specified in the context,
     * including severity, policy ID, metric type, and instance. It returns true if the
     * item is valid according to all criteria, false otherwise.
     *
     * @param item    The policy item to check
     * @param context The context containing the criteria
     * @return True if the item is valid, false otherwise
     */
    private boolean valid(JsonObject item, JsonObject context)
    {
        var valid = true;

        if (context.containsKey(SEVERITIES) && !context.getJsonArray(SEVERITIES).isEmpty())
        {
            valid = context.getJsonArray(SEVERITIES).contains(item.getString(SEVERITY));
        }

        if (valid && context.containsKey(POLICIES) && !context.getJsonArray(POLICIES).isEmpty())
        {
            valid = context.getJsonArray(POLICIES).contains(item.getLong(ID));
        }

        if (valid && context.containsKey(Metric.METRIC_TYPE) && !context.getString(Metric.METRIC_TYPE).isEmpty())
        {
            valid = context.getString(Metric.METRIC_TYPE).equalsIgnoreCase(item.getString(Metric.METRIC_TYPE).contains(SEPARATOR) ? item.getString(Metric.METRIC_TYPE).split(SEPARATOR_WITH_ESCAPE)[1].trim() : item.getString(Metric.METRIC_TYPE));
        }

        if (valid && context.containsKey(VisualizationConstants.FILTER_KEYS) && !context.getJsonArray(VisualizationConstants.FILTER_KEYS).isEmpty())
        {
            valid = item.getValue(INSTANCE) != null && context.getJsonArray(VisualizationConstants.FILTER_KEYS).contains(item.getValue(INSTANCE));
        }

        else if (valid && context.containsKey(VisualizationConstants.INSTANCE_TYPE) && !context.getString(VisualizationConstants.INSTANCE_TYPE).isEmpty())
        {
            valid = item.getValue(INSTANCE) != null && item.getString(METRIC).contains(INSTANCE_SEPARATOR) && context.getString(VisualizationConstants.INSTANCE_TYPE).equalsIgnoreCase(item.getString(METRIC).split(INSTANCE_SEPARATOR)[0].trim());
        }

        else if (valid && item.containsKey(INSTANCE) && context.containsKey(INSTANCE) && !context.getString(INSTANCE).isEmpty())
        {
            valid = context.getString(INSTANCE).equalsIgnoreCase(item.getString(INSTANCE));
        }

        return valid;
    }

    /**
     * Builds a worklog for a policy based on the data points.
     * <p>
     * This method processes a policy item and adds its data to the result object based on
     * the data points specified. It handles various types of data points, including object ID,
     * policy ID, policy tags, policy threshold, and policy acknowledgment status.
     *
     * @param dataPoints The array of data points to include in the result
     * @param result     The result object to which to add the worklog data
     * @param value      The policy value object
     * @param item       The policy configuration item
     * @param durations  The durations object containing trigger and acknowledgment information
     */
    void buildWorklog(JsonArray dataPoints, JsonObject result, JsonObject value, JsonObject item, JsonObject durations)
    {
        try
        {
            for (var index = 0; index < dataPoints.size(); index++)
            {
                var dataPoint = dataPoints.getJsonObject(index).getString(VisualizationConstants.DATA_POINT);

                if (dataPoint.equalsIgnoreCase(AIOpsObject.OBJECT_ID))
                {
                    dataPoint = APIConstants.Entity.OBJECT.getName().toLowerCase();

                    result.getMap().computeIfAbsent(dataPoint, val -> new JsonArray());
                }
                else if (dataPoint.equalsIgnoreCase(NMSConstants.PREVIOUS_FLAP_TIMESTAMP))
                {
                    result.getMap().computeIfAbsent(TIME_STAMP, val -> new JsonArray());
                }
                else
                {
                    result.getMap().computeIfAbsent(dataPoint, val -> new JsonArray());
                }

                if (dataPoint.equalsIgnoreCase(APIConstants.Entity.OBJECT.getName()))
                {
                    result.getJsonArray(dataPoint).add(ObjectConfigStore.getStore().getObjectId(value.getLong(ENTITY_ID)));
                }
                else if (dataPoint.equalsIgnoreCase(NetRoute.NETROUTE_ID))
                {
                    result.getJsonArray(dataPoint).add(value.getLong(ENTITY_ID));
                }
                else if (dataPoint.equalsIgnoreCase(PolicyEngineConstants.POLICY_ID))
                {
                    result.getJsonArray(dataPoint).add(value.getValue(ID));
                }

                else if (dataPoint.equalsIgnoreCase(PolicyEngineConstants.POLICY_TAGS))
                {
                    result.getJsonArray(dataPoint).add(item != null && item.getJsonArray(PolicyEngineConstants.POLICY_TAGS) != null ? StringUtils.join(item.getJsonArray(PolicyEngineConstants.POLICY_TAGS).getList(), COMMA_SEPARATOR) : EMPTY_VALUE);
                }

                else if (dataPoint.equalsIgnoreCase(POLICY_TYPE))
                {
                    result.getJsonArray(dataPoint).add(item != null ? item.getString(POLICY_TYPE) : EMPTY_VALUE);
                }

                else if (dataPoint.equalsIgnoreCase(POLICY_THRESHOLD))
                {
                    result.getJsonArray(dataPoint).add(value.getString(POLICY_THRESHOLD, EMPTY_VALUE));
                }
                else if (dataPoint.equalsIgnoreCase(PolicyEngineConstants.POLICY_NAME))
                {
                    result.getJsonArray(dataPoint).add(item != null ? item.getString(PolicyEngineConstants.POLICY_NAME) : EMPTY_VALUE);
                }

                else if (dataPoint.equalsIgnoreCase(AIOpsConstants.CORRELATION_PROBE))
                {
                    result.getJsonArray(dataPoint).add(value.containsKey(AIOpsConstants.CORRELATION_PROBE) ? value.getString(AIOpsConstants.CORRELATION_PROBE) : NO);
                }

                else if (dataPoint.equalsIgnoreCase(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS))
                {
                    result.getJsonArray(dataPoint).add(value.containsKey(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS) ? value.getString(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS) : EMPTY_VALUE);
                }
                else if (dataPoint.equalsIgnoreCase(IntegrationEngine.ACK_ID))
                {
                    if (item != null)
                    {
                        var response = IntegrationCacheStore.getStore().getItem(IntegrationCacheStore.getStore().getIntegrationType(item.getString(ID)), item.getString(ID) + SEPARATOR + value.getLong(ENTITY_ID) + SEPARATOR + item.getString(POLICY_TYPE) + SEPARATOR + (value.getValue(INSTANCE, null) != null ? value.getString(GlobalConstants.METRIC) + SEPARATOR + value.getValue(INSTANCE) : value.getString(GlobalConstants.METRIC)));

                        if (response != null && !CommonUtil.getString(response).isEmpty() && !response.equals(NOT_AVAILABLE))
                        {
                            result.getJsonArray(dataPoint).add(CommonUtil.getString(response));
                        }
                        else
                        {
                            result.getJsonArray(dataPoint).add(EMPTY_VALUE);
                        }
                    }
                }
                else if (dataPoint.equalsIgnoreCase(DURATION) ||
                        dataPoint.equalsIgnoreCase(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK) ||
                        dataPoint.equalsIgnoreCase(NMSConstants.PREVIOUS_FLAP_TIMESTAMP) ||
                        dataPoint.equalsIgnoreCase(POLICY_ACKNOWLEDGE) ||
                        dataPoint.equalsIgnoreCase(POLICY_NOTE) ||
                        dataPoint.equalsIgnoreCase(ACKNOWLEDGED) ||
                        dataPoint.equalsIgnoreCase(POLICY_ACKNOWLEDGE_BY) ||
                        dataPoint.equalsIgnoreCase(POLICY_ACKNOWLEDGE_TIME) ||
                        dataPoint.equalsIgnoreCase(EVENT_TIMESTAMP))
                {
                    var duration = durations.getJsonObject(value.getLong(ENTITY_ID) + SEPARATOR + value.getLong(ID) + SEPARATOR + (value.getValue(INSTANCE, null) != null ? value.getString(GlobalConstants.METRIC) + SEPARATOR + value.getValue(INSTANCE) : value.getString(GlobalConstants.METRIC)));

                    if (duration != null)
                    {
                        var acknowledgmentStatus = duration.containsKey(POLICY_ACKNOWLEDGE) ? new JsonObject(duration.getString(POLICY_ACKNOWLEDGE)) : null;

                        if (dataPoint.equalsIgnoreCase(DURATION))
                        {
                            result.getJsonArray(dataPoint).add(duration.getValue(DURATION, 0));
                        }
                        else if (dataPoint.equalsIgnoreCase(POLICY_FIRST_TRIGGER_TICK))
                        {
                            result.getJsonArray(dataPoint).add(duration.getLong(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK));
                        }
                        else if (dataPoint.equalsIgnoreCase(EVENT_TIMESTAMP))
                        {
                            result.getMap().computeIfAbsent(dataPoint, val -> new JsonArray());

                            result.getJsonArray(dataPoint).add(duration.getLong(EVENT_TIMESTAMP));
                        }
                        else if (dataPoint.equalsIgnoreCase(NMSConstants.PREVIOUS_FLAP_TIMESTAMP))
                        {
                            dataPoint = TIME_STAMP;

                            result.getMap().computeIfAbsent(dataPoint, val -> new JsonArray());

                            result.getJsonArray(dataPoint).add(duration.getLong(NMSConstants.PREVIOUS_FLAP_TIMESTAMP) * 1000);
                        }
                        else if (dataPoint.equalsIgnoreCase(ACKNOWLEDGED))
                        {
                            result.getJsonArray(dataPoint).add(acknowledgmentStatus != null ? acknowledgmentStatus.containsKey(ACKNOWLEDGED) ? acknowledgmentStatus.getString(ACKNOWLEDGED) : NO : NO);
                        }
                        else if (dataPoint.equalsIgnoreCase(POLICY_ACKNOWLEDGE_BY))
                        {
                            result.getJsonArray(dataPoint).add(acknowledgmentStatus != null ? UserConfigStore.getStore().existItem(acknowledgmentStatus.getLong(User.USER_ID, DUMMY_ID)) ? UserConfigStore.getStore().getItem(acknowledgmentStatus.getLong(User.USER_ID, DUMMY_ID)).getString(User.USER_NAME) : EMPTY_VALUE : EMPTY_VALUE);
                        }
                        else if (dataPoint.equalsIgnoreCase(POLICY_ACKNOWLEDGE_TIME))
                        {
                            result.getJsonArray(dataPoint).add(acknowledgmentStatus != null ? acknowledgmentStatus.getLong(POLICY_ACKNOWLEDGE_TIME, DateTimeUtil.currentMilliSeconds()) : EMPTY_VALUE);
                        }
                        else
                        {
                            result.getJsonArray(dataPoint).add(duration.getValue(dataPoint, EMPTY_VALUE));
                        }
                    }
                    else
                    {
                        if (dataPoint.equalsIgnoreCase(DURATION) || dataPoint.equalsIgnoreCase(POLICY_FIRST_TRIGGER_TICK))
                        {
                            result.getJsonArray(dataPoint).add(0);
                        }
                        else if (dataPoint.equalsIgnoreCase(NMSConstants.PREVIOUS_FLAP_TIMESTAMP))
                        {
                            dataPoint = TIME_STAMP;

                            result.getMap().computeIfAbsent(dataPoint, val -> new JsonArray());

                            result.getJsonArray(dataPoint).add(0);
                        }
                        else
                        {
                            result.getJsonArray(dataPoint).add(EMPTY_VALUE);
                        }
                    }

                }

                else
                {
                    result.getJsonArray(dataPoint).add(value.containsKey(dataPoint) && value.getValue(dataPoint) != null ? CommonUtil.getString(value.getValue(dataPoint)) : EMPTY_VALUE);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
