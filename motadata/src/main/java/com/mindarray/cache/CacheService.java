/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.cache;

import com.mindarray.util.MotadataConfigUtil;
import io.vertx.codegen.annotations.Fluent;
import io.vertx.codegen.annotations.GenIgnore;
import io.vertx.codegen.annotations.ProxyGen;
import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import io.vertx.core.Vertx;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonObject;

/**
 * The CacheService interface provides methods for retrieving cached data related to metrics,
 * correlation worklogs, and trap acknowledgments.
 * <p>
 * This service is implemented as a Vert.x service proxy, allowing for asynchronous communication
 * across the event bus. It is registered by the {@link CacheServiceProvider} verticle.
 * <p>
 * The service improves performance by retrieving pre-computed or previously stored data instead
 * of performing expensive operations each time the data is requested.
 *
 * @see CacheServiceImpl
 * @see CacheServiceProvider
 */
@ProxyGen
public interface CacheService
{

    /**
     * Creates a new instance of the CacheService.
     *
     * @param vertx   The Vert.x instance
     * @param handler The handler to be called when the service is created
     */
    @GenIgnore
    static void create(Vertx vertx, Handler<AsyncResult
            <CacheService>> handler)
    {
        new CacheServiceImpl(vertx, handler);
    }

    /**
     * Creates a proxy to the CacheService.
     *
     * @param vertx   The Vert.x instance
     * @param address The event bus address where the service is registered
     * @return A proxy to the CacheService
     */
    @GenIgnore
    static CacheService createProxy(Vertx vertx, String address)
    {
        return new CacheServiceVertxEBProxy(vertx, address, new DeliveryOptions().setSendTimeout(MotadataConfigUtil.getCacheServiceTimeoutMillis()));
    }

    /**
     * Retrieves correlated metrics based on the provided context.
     * <p>
     * This method reads cached metric data from files in the cache directory and returns
     * the correlated metrics for the specified entities and data points.
     *
     * @param context The context containing the entities and data points for which to retrieve metrics
     * @param handler The handler to be called with the result
     * @return This instance for fluent API usage
     */
    @Fluent
    CacheService getCorrelatedMetrics(JsonObject context, Handler<AsyncResult<JsonObject>> handler);

    /**
     * Retrieves correlation worklogs based on the provided context.
     * <p>
     * This method reads cached correlation worklog data from files in the cache directory
     * and returns the worklogs for the specified entity, policy, and instance.
     *
     * @param context The context containing the entity ID, policy ID, and instance for which to retrieve worklogs
     * @param handler The handler to be called with the result
     * @return This instance for fluent API usage
     */
    @Fluent
    CacheService getCorrelationWorklogs(JsonObject context, Handler<AsyncResult<JsonObject>> handler);

    /**
     * Retrieves trap acknowledgments from the cache.
     * <p>
     * This method reads cached trap acknowledgment data from the TrapCacheStore
     * and returns the acknowledgment status for each trap.
     *
     * @param handler The handler to be called with the result
     * @return This instance for fluent API usage
     */
    @Fluent
    CacheService getTrapAcks(Handler<AsyncResult<JsonObject>> handler);
}
