/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*Change Logs
 *   Date          Author              Notes
 *  2025-02-06    <PERSON><PERSON>      Merged ObjectConfigStore item into event.
 *  2025-02-28    <PERSON><PERSON>      Added Info for Compliance Dump
 *   2025-02-28   <PERSON><PERSON> Sharma      CompliancePolicyCacheStore Items for the Scheduler statuses
 */

package com.mindarray.compliance;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.ComplianceBenchmark;
import com.mindarray.api.CompliancePolicy;
import com.mindarray.api.User;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.nms.NMSConstants;
import com.mindarray.notification.Notification;
import com.mindarray.store.ComplianceBenchmarkConfigStore;
import com.mindarray.store.CompliancePolicyCacheStore;
import com.mindarray.store.CompliancePolicyConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.text.StringSubstitutor;

import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.COMPLIANCE_POLICY_NOTIFICATION_SUBJECT_SUCCESSFUL;
import static com.mindarray.InfoMessageConstants.COMPLIANCE_POLICY_NOTIFICATION_SUCCESSFUL_MESSAGE;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.compliance.ComplianceConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;

public class ComplianceManager extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(ComplianceManager.class, GlobalConstants.MOTADATA_COMPLIANCE, "Compliance Manager");

    private final Map<Long, JsonObject> policies = new HashMap<>();//policies context

    private final Map<Long, JsonArray> rules = new HashMap<>(); // Benchmark Contexts
    private final StringBuilder builder = new StringBuilder(0);
    private Set<String> mappers;
    private EventEngine eventEngine;

    private static void setRecipients(Set<String> emailRecipients, Set<String> smsRecipients, JsonObject context)
    {

        if (context.containsKey(COMPLIANCE_POLICY_EMAIL_NOTIFICATION_RECIPIENT) && !context.getJsonArray(COMPLIANCE_POLICY_EMAIL_NOTIFICATION_RECIPIENT).isEmpty())
        {
            emailRecipients.addAll(context.getJsonArray(COMPLIANCE_POLICY_EMAIL_NOTIFICATION_RECIPIENT).getList());
        }
        if (context.containsKey(COMPLIANCE_POLICY_USER_NOTIFICATION_RECIPIENT) && !context.getJsonArray(COMPLIANCE_POLICY_USER_NOTIFICATION_RECIPIENT).isEmpty())
        {
            context.getJsonArray(COMPLIANCE_POLICY_USER_NOTIFICATION_RECIPIENT).forEach(userName ->
            {
                var item = UserConfigStore.getStore().getItemByValue(User.USER_NAME, CommonUtil.getString(userName));

                if (item != null && !item.isEmpty())
                {
                    if (item.containsKey(User.USER_EMAIL) && !item.getString(User.USER_EMAIL).isEmpty())
                    {
                        emailRecipients.add(item.getString(User.USER_EMAIL));
                    }

                    if (item.containsKey(User.USER_MOBILE) && !item.getString(User.USER_MOBILE).isEmpty() && !item.getString(User.USER_MOBILE).equals("0"))
                    {
                        smsRecipients.add(item.getString(User.USER_MOBILE));
                    }
                }
            });
        }

    }

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            mappers = new HashSet<>();

            var items = CompliancePolicyConfigStore.getStore().getItems();

            for (var i = 0; i < items.size(); i++)
            {
                var item = items.getJsonObject(i);

                policies.put(item.getLong(ID), item);

                rules.put(item.getLong(ID), ComplianceBenchmarkConfigStore.getStore().getItem(item.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).getLong(CompliancePolicy.COMPLIANCE_POLICY_BENCHMARK)).getJsonArray(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULE_IDS));
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_COMPLIANCE_POLICY_INSPECTION_COMPLETE, message ->
        {
            try
            {
                var event = message.body();

                // We will publish event to UI indicating audit policy execution is completed.
                // Later We will also send event to dump data in MotaStore.
                EventBusConstants.publish(EventBusConstants.EVENT_COMPLIANCE_POLICY_STATE_CHANGE, new JsonObject().put(ID, event.getLong(GlobalConstants.ID)).put(CompliancePolicy.COMPLIANCE_POLICY_SCHEDULER, YES).put(NMSConstants.STATE, NMSConstants.STATE_NOT_RUNNING));

                CompliancePolicyCacheStore.getStore().complete(event.getLong(ID));

                if (event.getLong(EVENT_SCHEDULER) != null)
                {
                    vertx.eventBus().send(EventBusConstants.EVENT_SCHEDULER_COMPLETE, event);
                }
                else
                {
                    var policy = policies.get(event.getLong(GlobalConstants.ID));

                    if (policy.containsKey(COMPLIANCE_POLICY_NOTIFICATION_CONTEXT) && !policy.getJsonObject(COMPLIANCE_POLICY_NOTIFICATION_CONTEXT).isEmpty())
                    {
                        var emailRecipients = new HashSet<String>();

                        var smsRecipients = new HashSet<String>();

                        setRecipients(emailRecipients, smsRecipients, policy.getJsonObject(COMPLIANCE_POLICY_NOTIFICATION_CONTEXT));

                        if (!emailRecipients.isEmpty())
                        {
                            Notification.sendEmail(new JsonObject()
                                    .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                    .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add("information.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                    .put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format(COMPLIANCE_POLICY_NOTIFICATION_SUBJECT_SUCCESSFUL, policy.getString(CompliancePolicy.COMPLIANCE_POLICY_NAME)))
                                    .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, new JsonArray(new ArrayList(emailRecipients))).put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject().put(CompliancePolicy.COMPLIANCE_POLICY_NAME, policy.getString(CompliancePolicy.COMPLIANCE_POLICY_NAME)).put(ComplianceBenchmark.COMPLIANCE_BENCHMARK_NAME, ComplianceBenchmarkConfigStore.getStore().getItem(policy.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).getLong(CompliancePolicy.COMPLIANCE_POLICY_BENCHMARK)).getString(ComplianceBenchmark.COMPLIANCE_BENCHMARK_NAME)).put(CompliancePolicy.COMPLIANCE_POLICY_CONFIG_FILE_TYPE, policy.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).getString(CompliancePolicy.COMPLIANCE_POLICY_CONFIG_FILE_TYPE)).put(ComplianceState.VULNERABLE.name().toLowerCase(), event.getInteger(ComplianceState.VULNERABLE.name().toLowerCase())).put(ComplianceState.POOR.name().toLowerCase(), event.getInteger(ComplianceState.POOR.name().toLowerCase())).put(ComplianceState.MODERATE.name().toLowerCase(), event.getInteger(ComplianceState.MODERATE.name().toLowerCase())).put(ComplianceState.SECURE.name().toLowerCase(), event.getInteger(ComplianceState.SECURE.name().toLowerCase())).put(ComplianceConstants.COMPLIANCE_PERCENTAGE, event.getInteger(ComplianceConstants.COMPLIANCE_PERCENTAGE)).put(USER_NAME, policy.getString(USER_NAME, DEFAULT_USER)).put(TIME_STAMP, DateTimeUtil.timestamp()).put(STATUS, "Success").getMap()).replace(Notification.EMAIL_NOTIFICATION_COMPLIANCE_POLICY_SUCCESSFUL_HTML_TEMPLATE).getBytes(StandardCharsets.UTF_8)));
                        }

                        if (!smsRecipients.isEmpty())
                        {
                            Notification.sendSMS(String.format(COMPLIANCE_POLICY_NOTIFICATION_SUCCESSFUL_MESSAGE, policy.getString(CompliancePolicy.COMPLIANCE_POLICY_NAME), DateTimeUtil.timestamp(), policy.getString(USER_NAME, DEFAULT_USER), event.getInteger(ComplianceConstants.COMPLIANCE_PERCENTAGE), event.getInteger(ComplianceState.VULNERABLE.name().toLowerCase()), event.getInteger(ComplianceState.POOR.name().toLowerCase()), event.getInteger(ComplianceState.MODERATE.name().toLowerCase()), event.getInteger(ComplianceState.SECURE.name().toLowerCase())), new JsonArray(new ArrayList(smsRecipients)));
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
        {
            try
            {
                var event = message.body();

                switch (EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)))
                {
                    case ADD_COMPLIANCE_POLICY, UPDATE_COMPLIANCE_POLICY ->
                    {
                        var item = CompliancePolicyConfigStore.getStore().getItem(event.getLong(GlobalConstants.ID));

                        policies.put(item.getLong(ID), item);

                        rules.put(item.getLong(ID), ComplianceBenchmarkConfigStore.getStore().getItem(item.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).getLong(CompliancePolicy.COMPLIANCE_POLICY_BENCHMARK)).getJsonArray(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULE_IDS));
                    }

                    case DELETE_COMPLIANCE_POLICY ->
                    {
                        policies.remove(event.getLong(ID));

                        rules.remove(event.getLong(ID));

                        delete(event.getLong(ID));
                    }

                    default ->
                    {
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

        }).exceptionHandler(LOGGER::error);


        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_COMPLIANCE_POLICY_DATASTORE_DUMP, message ->
                Bootstrap.complianceDBService().get(TABLE_COMPLIANCE_STATS_POLICY, new JsonObject().put(TRANSFORM, false).put(QUERY, QUERY_COMPLIANCE_STATS_POLICY_TABLE), result ->
                {
                    if (result.succeeded() && result.result() != null)
                    {
                        var timestamp = DateTimeUtil.currentSeconds();

                        var rows = result.result().getJsonArray(RESULT);

                        for (var i = 0; i < rows.size(); i++)
                        {
                            DatastoreConstants.write(rows.getJsonObject(i)
                                            .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.COMPLIANCE.ordinal()).put(EVENT_SOURCE, EMPTY_VALUE)
                                            .put(PLUGIN_ID, DatastoreConstants.PluginId.COMPLIANCE.getName()).put(EVENT_TIMESTAMP, timestamp)
                                    , VisualizationConstants.VisualizationDataSource.COMPLIANCE.getName(), mappers, builder);
                        }

                        LOGGER.info("Compliance Data Dumped Successfully");
                    }
                    else
                    {
                        LOGGER.error(result.cause().getCause());
                    }

                }));

        eventEngine = new EventEngine()
                .setEventType(EventBusConstants.EVENT_COMPLIANCE_MANAGER)
                .setLogger(LOGGER)
                .setPersistEventOffset(true)
                .setEventHandler(this::process).start(vertx, promise);
    }

    /**
     * This method will receive event for audit policy execution.
     * Per rule, it will prepare and send event to Rule Engine.
     */
    private void process(JsonObject event)
    {
        try
        {
            var ids = event.getJsonArray(CompliancePolicy.COMPLIANCE_POLICY_ENTITIES);

            if (ids != null && !ids.isEmpty())
            {
                for (var index = 0; index < ids.size(); index++)
                {
                    var policy = policies.get(ids.getLong(index));

                    if (CompliancePolicyConfigStore.getStore().existItem(ids.getLong(index)))
                    {
                        EventBusConstants.publish(EventBusConstants.EVENT_COMPLIANCE_POLICY_STATE_CHANGE, new JsonObject().put(ID, ids.getLong(index)).put(NMSConstants.STATE, NMSConstants.STATE_RUNNING));

                        CompliancePolicyCacheStore.getStore().updateItem(policy.getLong(ID), 1);

                        if (!event.containsKey("manual.run"))
                        {
                            policy.put(EVENT_SCHEDULER, event.getLong(ID));
                        }

                        send(policy.put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER)
                                .put(REMOTE_ADDRESS, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS));
                    }
                    else
                    {
                        LOGGER.warn(String.format("failed to send event to rule engine , reason : Compliance policy not found , id : %s ", ids.getLong(index)));
                    }
                }
            }
            else
            {
                LOGGER.warn("Empty compliance policies received");
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * This method will prepare event to be sent to Rule Engine.
     * Before sending for rule engine, we would clear data for the policy, to avoid data duplication as we have same ids to reuse and will insert rather than upsert.
     */
    private void send(JsonObject policy)
    {
        var context = policy.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT);

        var benchmark = rules.getOrDefault(policy.getLong(ID), null);

        if (benchmark != null && !benchmark.isEmpty())
        {
            var event = new JsonObject().mergeIn(context).put(USER_NAME, policy.getString(USER_NAME)).put(ID, policy.getLong(ID)).put(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULE_IDS, benchmark);

            if (policy.containsKey(EVENT_SCHEDULER))
            {
                event.put(EVENT_SCHEDULER, policy.getLong(EVENT_SCHEDULER));
            }

            // remove entries from three tables
            delete(policy.getLong(ID))
                    .onComplete(result ->
                    {
                        if (result.succeeded())
                        {
                            vertx.eventBus().send(EventBusConstants.EVENT_RULE_ENGINE, event);
                        }
                        else if (result.failed())
                        {
                            LOGGER.error(result.cause().getCause());

                            vertx.eventBus().send(EVENT_COMPLIANCE_POLICY_INSPECTION_COMPLETE, new JsonObject().put(ID, event.getLong(ID)).put(MESSAGE, result.cause().getCause()).put(ComplianceConstants.COMPLIANCE_POLICY_ID, event.getLong(ComplianceConstants.COMPLIANCE_POLICY_ID)));

                            vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION,
                                    new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_COMPLIANCE_POLICY)
                                            .put(MESSAGE, result.cause().getMessage())
                                            .put(STATUS, STATUS_FAIL));
                        }
                    });
        }
        else
        {
            LOGGER.warn(String.format("Benchmark does not exists for compliance policy %s", policy.getString(CompliancePolicy.COMPLIANCE_POLICY_NAME)));
        }
    }

    private Future<Void> delete(long id)
    {
        var promise = Promise.<Void>promise();

        try
        {
            var futures = new ArrayList<Future<Void>>();

            var trailPromise = Promise.<Void>promise();

            var entityStatsPromise = Promise.<Void>promise();

            var policyStatsPromise = Promise.<Void>promise();

            futures.add(trailPromise.future());

            futures.add(entityStatsPromise.future());

            futures.add(policyStatsPromise.future());

            // For Raw Table
            Bootstrap.complianceDBService().delete(TABLE_COMPLIANCE_TRAIL, new JsonObject().put(ComplianceConstants.COMPLIANCE_POLICY_ID.replace(".", "_"), id), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
            {
                if (result.succeeded())
                {
                    LOGGER.debug("Deletion Successfully for " + TABLE_COMPLIANCE_TRAIL);

                    trailPromise.complete();
                }
                else
                {
                    LOGGER.debug("Deletion UnSuccessfully for " + TABLE_COMPLIANCE_TRAIL);

                    LOGGER.debug(result.cause());

                    trailPromise.fail(result.cause());
                }
            });

            // For Object Table
            Bootstrap.complianceDBService().delete(TABLE_COMPLIANCE_STATS_ENTITY, new JsonObject().put(ComplianceConstants.COMPLIANCE_POLICY_ID.replace(".", "_"), id), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
            {
                if (result.succeeded())
                {
                    LOGGER.debug("Deletion Successfully for " + TABLE_COMPLIANCE_STATS_ENTITY);

                    entityStatsPromise.complete();
                }
                else
                {
                    LOGGER.debug("Deletion UnSuccessfully for " + TABLE_COMPLIANCE_STATS_ENTITY);

                    LOGGER.debug(result.cause());

                    entityStatsPromise.fail(result.cause());
                }
            });

            // For Audit Table
            Bootstrap.complianceDBService().delete(TABLE_COMPLIANCE_STATS_POLICY, new JsonObject().put(ComplianceConstants.COMPLIANCE_POLICY_ID.replace(".", "_"), id), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
            {
                if (result.succeeded())
                {
                    LOGGER.debug("Deletion Successfully for " + TABLE_COMPLIANCE_STATS_POLICY);

                    policyStatsPromise.complete();
                }
                else
                {
                    LOGGER.debug("Deletion UnSuccessfully for " + TABLE_COMPLIANCE_STATS_POLICY);

                    LOGGER.debug(result.cause());

                    policyStatsPromise.fail(result.cause());
                }
            });

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    promise.complete();
                }
                else
                {
                    promise.fail(result.cause().getMessage());
                }
            });
        }
        catch (Exception exception)
        {
            promise.fail(exception.getMessage());

            LOGGER.error(exception);
        }

        return promise.future();
    }

    @Override
    public void stop(Promise<Void> promise)
    {
        eventEngine.stop(vertx, promise);
    }
}
