/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/* Change Logs:
 *  Date          Author               Notes
 *  06-Feb-2025    <PERSON><PERSON>        Added CLI as Category for Compliance.
 *  11-Feb-2025    <PERSON><PERSON>        Added ADD, DELETE and UPDATE for credential Profile
 *  28-Feb-2025    <PERSON><PERSON>        Added failsafe code and logger
 *  16-Apr-2025    Bharat              MOTADATA-5798: Enhance compliance in the plugin engine by implementing batching for CLI sessions.
 */

package com.mindarray.compliance;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.config.ConfigConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.nms.NMSConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.MatchResult;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.ComplianceRule.COMPLIANCE_RULE_COMMAND;
import static com.mindarray.api.ComplianceRule.COMPLIANCE_RULE_SEVERITY;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;

public class ComplianceRuleEngine extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(ComplianceRuleEngine.class, GlobalConstants.MOTADATA_COMPLIANCE, "Compliance Rule Engine");
    private static final int SSH_CLIENT_TIMEOUT = MotadataConfigUtil.getSSHClientTimeoutSeconds();
    private final Map<String, Pattern> patterns = new HashMap<>(); //Map for Pattern, so we don't need to compile everytime..
    private final Map<Long, JsonObject> rules = new HashMap<>();
    private final Map<Long, JsonArray> records = new HashMap<>(); //Rule based data from both CLI and Config...
    private final Map<Long, Set<String>> activeRequests = new HashMap<>(); //track record for CLI as per compliance.policy.
    private final Map<Long, JsonObject> credentialProfiles = new HashMap<>(); //Collect Cred profiles
    private EventEngine eventEngine;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            var items = ComplianceRuleConfigStore.getStore().getItems();

            for (var i = 0; i < items.size(); i++)
            {
                var item = items.getJsonObject(i);

                rules.put(item.getLong(ID), item);
            }

            items = CredentialProfileConfigStore.getStore().getItems();

            for (var i = 0; i < items.size(); i++)
            {
                var item = items.getJsonObject(i);

                credentialProfiles.put(item.getLong(ID), item);
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
        {
            try
            {
                var event = message.body();

                switch (EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)))
                {
                    case ADD_COMPLIANCE_RULE, UPDATE_COMPLIANCE_RULE ->
                    {
                        var item = ComplianceRuleConfigStore.getStore().getItem(event.getLong(GlobalConstants.ID));

                        rules.put(item.getLong(ID), item);
                    }

                    case DELETE_COMPLIANCE_RULE -> rules.remove(event.getLong(ID));

                    case ADD_CREDENTIAL_PROFILE, UPDATE_CREDENTIAL_PROFILE ->
                    {
                        var item = CredentialProfileConfigStore.getStore().getItem(event.getLong(GlobalConstants.ID));

                        credentialProfiles.put(item.getLong(ID), item);
                    }

                    case DELETE_CREDENTIAL_PROFILE -> credentialProfiles.remove(event.getLong(ID));

                    default ->
                    {
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

        }).exceptionHandler(LOGGER::error);

        /*
         * Event received from Plugin Engine Response Processor for compliance policy.
         * Event also received from config File that will directly dump data in Postgres.
         * This event will be used to update the compliance trail record for CLI.
         */
        vertx.eventBus().<JsonObject>localConsumer(config().getString(EVENT_TYPE) + DOT_SEPARATOR + RESULT, message ->
        {
            try
            {
                var event = message.body();

                if (records.containsKey(event.getLong(ID)))
                {
                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("Event received for %s for compliance policy", event.getLong(ID)));
                    }

                    EventBusConstants.complete(event);

                    // if queryTracker contains the CLI event, then remove it from the tracker and update the compliance trail record and put it in complianceResult Map.
                    if (activeRequests.containsKey(event.getLong(ID)) && activeRequests.get(event.getLong(ID)).remove(event.getLong(ID) + SEPARATOR + event.getInteger(AIOpsObject.OBJECT_ID) + event.getLong(ComplianceConstants.COMPLIANCE_CLI_IDENTIFIER)))
                    {
                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("Event received from Plugin Engine Response Processor for compliance policy %s, object.id %s and compliance.policy.id %s.", event.getLong(ID), event.getInteger(AIOpsObject.OBJECT_ID), event.getLong(ComplianceConstants.COMPLIANCE_POLICY_ID)));
                        }

                        var ruleIds = event.getJsonArray(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULE_IDS);

                        for (var i = 0; i < ruleIds.size(); i++)
                        {
                            var rule = rules.get(ruleIds.getLong(i));

                            var trailRecord = new JsonObject();

                            trailRecord.put(ComplianceConstants.CURRENT_SCAN_TIMESTAMP, DateTimeUtil.currentMilliSeconds())
                                    .put(ComplianceConstants.LAST_SCAN_TIMESTAMP, Long.MIN_VALUE)
                                    .put(ComplianceConstants.COMPLIANCE_RULE_ID, rule.getLong(ID))
                                    .put(AIOpsObject.OBJECT_ID, event.containsKey(AIOpsObject.OBJECT_ID) ? event.getInteger(AIOpsObject.OBJECT_ID) : event.getInteger(Configuration.CONFIG_OBJECT))
                                    .put(ComplianceConstants.COMPLIANCE_POLICY_ID, event.getLong(ID))
                                    .put(COMPLIANCE_RULE_SEVERITY, rule.getString(COMPLIANCE_RULE_SEVERITY))
                                    .put(ComplianceConstants.COMPLIANCE_BENCHMARK_ID, event.getLong(CompliancePolicy.COMPLIANCE_POLICY_BENCHMARK))// need to fetch from rule?
                                    .put(ComplianceConstants.CURRENT_SCAN_STATUS, ComplianceConstants.RuleStatus.UNKNOWN.getType())
                                    .put(ComplianceConstants.LAST_SCAN_STATUS, ComplianceConstants.RuleStatus.UNKNOWN.getType());

                            if (!event.containsKey(ERROR) && event.containsKey(RESULT) && event.getJsonObject(RESULT).containsKey("output"))
                            {
                                //output from plugin engine
                                // split with separator and output will be in order with rule ids so we can manage the same.
                                var output = event.getJsonObject(RESULT).getString("output").split(Pattern.quote(VALUE_SEPARATOR))[i];

                                if (output != null && !output.isEmpty())
                                {
                                    trailRecord.put(ComplianceConstants.CONTENT, output);

                                    trailRecord.put(ComplianceConstants.CURRENT_SCAN_STATUS, evaluateConditions(rule.getJsonObject(ComplianceRule.COMPLIANCE_RULE_CONTEXT).getJsonArray(ComplianceRule.COMPLIANCE_RULE_CONDITIONS), trailRecord).getInteger(STATUS));

                                    trailRecord.remove(ComplianceConstants.CONTENT);

                                    updateLastScanRecord(event, trailRecord, ruleIds.getLong(i));
                                }
                                else
                                {
                                    // Fails the record as the output is empty or null.
                                    trailRecord.put(ComplianceConstants.CURRENT_SCAN_STATUS, ComplianceConstants.RuleStatus.FAILED.getType());

                                    updateLastScanRecord(event, trailRecord, ruleIds.getLong(i));
                                }
                            }
                            else
                            {
                                // Fails the record as there is error from the plugin engine result.
                                trailRecord.put(ComplianceConstants.CURRENT_SCAN_STATUS, ComplianceConstants.RuleStatus.FAILED.getType());

                                updateLastScanRecord(event, trailRecord, ruleIds.getLong(i));
                            }

                            records.get(event.getLong(ID)).add(trailRecord);
                        }
                    }

                    if (!activeRequests.containsKey(event.getLong(ID)) || activeRequests.get(event.getLong(ID)).isEmpty())
                    {
                        activeRequests.remove(event.getLong(ID));

                        var statuses = new HashMap<Integer, JsonObject>(); // key -> object.id value -> succeed -> count, fail -> count

                        var trailRecords = new JsonArray();  //Collect Data for raw table.

                        var entityStatsRecords = new JsonArray(); //Collect Data for object table.

                        var policyStatsRecords = new JsonArray(); //Collect Data for audit table.

                        var policyStatsRecord = new JsonObject(); // Collect Micro data for audit table

                        policyStatsRecord.put(ComplianceConstants.COMPLIANCE_POLICY_ID, event.getLong(ID));

                        policyStatsRecord.put(ComplianceConstants.ComplianceState.VULNERABLE.name().toLowerCase(), 0).put(ComplianceConstants.ComplianceState.MODERATE.name().toLowerCase(), 0).put(ComplianceConstants.ComplianceState.SECURE.name().toLowerCase(), 0).put(ComplianceConstants.ComplianceState.POOR.name().toLowerCase(), 0);

                        policyStatsRecord.put(ComplianceConstants.LAST_SCAN_TIMESTAMP, DateTimeUtil.currentMilliSeconds());

                        var entities = records.get(event.getLong(ID));

                        trailRecords.addAll(entities);

                        for (var i = 0; i < entities.size(); i++)
                        {
                            var entity = entities.getJsonObject(i);

                            statuses.computeIfAbsent(entity.getInteger(AIOpsObject.OBJECT_ID), value -> new JsonObject().put(STATUS_SUCCEED, 0).put(STATUS_FAIL, 0));

                            var status = statuses.get(entity.getInteger(AIOpsObject.OBJECT_ID));

                            if (entity.getInteger(ComplianceConstants.CURRENT_SCAN_STATUS).equals(ComplianceConstants.RuleStatus.SUCCEEDED.getType()))
                            {
                                //will need it in future using weightage severity calculation
//                            passedRuleCount += ComplianceConstants.getScoreByRuleWeightage(ComplianceConstants.RuleSeverity.valueOf(rawResult.getString(SEVERITY)));

                                status.put(STATUS_SUCCEED, status.getInteger(STATUS_SUCCEED) + 1);
                            }
                            else
                            {
                                //will need it in future using weightage severity calculation
//                           failedRuleCount += ComplianceConstants.getScoreByRuleWeightage(ComplianceConstants.RuleSeverity.valueOf(rawResult.getString(SEVERITY)));

                                status.put(STATUS_FAIL, status.getInteger(STATUS_FAIL) + 1);
                            }
                        }

                        for (var entity : statuses.entrySet())
                        {
                            var value = entity.getValue();

                            var entityStatsRecord = new JsonObject();// Collect Micro data for object table

                            entityStatsRecord.put(ComplianceConstants.SCANNED_RULE, value.getInteger(STATUS_SUCCEED) + value.getInteger(STATUS_FAIL));

                            entityStatsRecord.put(ComplianceConstants.COMPLIANCE_POLICY_ID, event.getLong(ID));

                            entityStatsRecord.put(AIOpsObject.OBJECT_ID, entity.getKey());

                            entityStatsRecord.put(ComplianceConstants.LAST_SCAN_STATUS, ComplianceConstants.RuleStatus.SUCCEEDED.getType());

                            entityStatsRecord.put(MESSAGE, "Scan Completed Successfully");

                            calculate(policyStatsRecord, entityStatsRecord, value.getInteger(STATUS_SUCCEED) + value.getInteger(STATUS_FAIL), value.getInteger(STATUS_SUCCEED));

                            entityStatsRecord.put(ComplianceConstants.LAST_SCAN_TIMESTAMP, DateTimeUtil.currentMilliSeconds());

                            entityStatsRecords.add(entityStatsRecord);

                            policyStatsRecord.put(ComplianceConstants.COMPLIANCE_PERCENTAGE, (policyStatsRecord.getInteger(ComplianceConstants.COMPLIANCE_PERCENTAGE, 0) + entityStatsRecord.getInteger(ComplianceConstants.COMPLIANCE_PERCENTAGE)));

                        }

                        policyStatsRecord.put(ComplianceConstants.COMPLIANCE_PERCENTAGE, (policyStatsRecord.getInteger(ComplianceConstants.COMPLIANCE_PERCENTAGE) / statuses.size()));

                        policyStatsRecords.add(policyStatsRecord);

                        Bootstrap.complianceDBService().saveAll(ComplianceConstants.TABLE_COMPLIANCE_TRAIL, trailRecords, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                        {
                            if (result.succeeded())
                            {
                                if (CommonUtil.debugEnabled())
                                {
                                    LOGGER.debug("Inserted Successfully for " + ComplianceConstants.TABLE_COMPLIANCE_TRAIL);
                                }
                            }
                            else
                            {
                                LOGGER.error(result.cause().getCause());
                            }

                        });

                        Bootstrap.complianceDBService().saveAll(ComplianceConstants.TABLE_COMPLIANCE_STATS_ENTITY, entityStatsRecords, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                        {
                            if (result.succeeded())
                            {
                                if (CommonUtil.debugEnabled())
                                {
                                    LOGGER.debug("Inserted Successfully for " + ComplianceConstants.TABLE_COMPLIANCE_STATS_ENTITY);
                                }
                            }
                            else
                            {
                                LOGGER.error(result.cause().getCause());
                            }

                        });

                        Bootstrap.complianceDBService().saveAll(ComplianceConstants.TABLE_COMPLIANCE_STATS_POLICY, policyStatsRecords, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                        {
                            if (result.succeeded())
                            {
                                if (CommonUtil.debugEnabled())
                                {
                                    LOGGER.debug("Inserted Successfully for " + ComplianceConstants.TABLE_COMPLIANCE_STATS_POLICY);
                                }
                            }
                            else
                            {
                                LOGGER.error(result.cause().getCause());
                            }

                        });

                        if (MotadataConfigUtil.devMode())
                        {
                            vertx.eventBus().send("test.compliance.manager", new JsonObject().put(ID, event.getLong(ID)).put(MESSAGE, "Scan Completed").put(EVENT_SCHEDULER, event.getLong(EVENT_SCHEDULER, null)).put(ComplianceConstants.ComplianceState.VULNERABLE.name().toLowerCase(), policyStatsRecords.getJsonObject(0).getInteger(ComplianceConstants.ComplianceState.VULNERABLE.name().toLowerCase())).put(ComplianceConstants.ComplianceState.POOR.name().toLowerCase(), policyStatsRecords.getJsonObject(0).getInteger(ComplianceConstants.ComplianceState.POOR.name().toLowerCase())).put(ComplianceConstants.ComplianceState.MODERATE.name().toLowerCase(), policyStatsRecords.getJsonObject(0).getInteger(ComplianceConstants.ComplianceState.MODERATE.name().toLowerCase())).put(ComplianceConstants.ComplianceState.SECURE.name().toLowerCase(), policyStatsRecords.getJsonObject(0).getInteger(ComplianceConstants.ComplianceState.SECURE.name().toLowerCase())).put(ComplianceConstants.COMPLIANCE_PERCENTAGE, policyStatsRecords.getJsonObject(0).getInteger(ComplianceConstants.COMPLIANCE_PERCENTAGE)));
                        }

                        LOGGER.info(event.getLong(ID) + " Processing for Result " + DateTimeUtil.currentSeconds());

                        vertx.eventBus().send(EVENT_COMPLIANCE_POLICY_INSPECTION_COMPLETE, new JsonObject().put(ID, event.getLong(ID)).put(MESSAGE, "Scan Completed").put(EVENT_SCHEDULER, event.getLong(EVENT_SCHEDULER, null)).put(ComplianceConstants.ComplianceState.VULNERABLE.name().toLowerCase(), policyStatsRecords.getJsonObject(0).getInteger(ComplianceConstants.ComplianceState.VULNERABLE.name().toLowerCase())).put(ComplianceConstants.ComplianceState.POOR.name().toLowerCase(), policyStatsRecords.getJsonObject(0).getInteger(ComplianceConstants.ComplianceState.POOR.name().toLowerCase())).put(ComplianceConstants.ComplianceState.MODERATE.name().toLowerCase(), policyStatsRecords.getJsonObject(0).getInteger(ComplianceConstants.ComplianceState.MODERATE.name().toLowerCase())).put(ComplianceConstants.ComplianceState.SECURE.name().toLowerCase(), policyStatsRecords.getJsonObject(0).getInteger(ComplianceConstants.ComplianceState.SECURE.name().toLowerCase())).put(ComplianceConstants.COMPLIANCE_PERCENTAGE, policyStatsRecords.getJsonObject(0).getInteger(ComplianceConstants.COMPLIANCE_PERCENTAGE)));

                        vertx.eventBus().send(EVENT_USER_NOTIFICATION,
                                new JsonObject().put(EVENT_TYPE, EVENT_COMPLIANCE_POLICY)
                                        .put(MESSAGE, "Scan Completed Successfully")
                                        .put(STATUS, STATUS_SUCCEED));

                        clear(event.getLong(ID));
                    }

                }
                else
                {
                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("Event received for %s for compliance policy with no record availability", event.getLong(ID)));
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        eventEngine = new EventEngine().setEventType(config().getString(EventBusConstants.EVENT_TYPE))
                .setPersistEventOffset(true)
                .setLogger(LOGGER).setEventHandler(this::inspect).start(vertx, promise);
    }

    /**
     * This method will fetch qualified entities for rule and once rule is executed on all the devices it will send event to Compliance Manager indicating completion of rule.
     * So what would inspect do is, it will create 3 JsonArrays, in each Arrays there would be multiple JsonObjects, why? Because we would be performing Bulk insertion into postgres.
     * Now there are 3 JsonObjects which will roll over each objects and rules to calculate percentage and data according to config file from NCM and put that JsonObject into above 3 jsonArrays and than will send to perform Bulk Operation.
     */
    private void inspect(JsonObject event)
    {
        try
        {
            LOGGER.info(event.getLong(ID) + " Processing for " + DateTimeUtil.currentSeconds());

            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("Event received for rule %s for compliance policy", event.getLong(ID)));
            }

            var objects = ComplianceConstants.qualifyObjects(event, NMSConstants.Category.valueOfName(event.getString(CompliancePolicy.COMPLIANCE_POLICY_CATEGORY)));

            if (objects != null && !objects.isEmpty())
            {
                if (event.getString(CompliancePolicy.COMPLIANCE_POLICY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.NETWORK.getName()))
                {
                    var ruleIds = event.getJsonArray(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULE_IDS);

                    var configRules = new JsonArray();  // JsonArray for Config File

                    var commandRules = new JsonArray(); // JsonArray for CLI

                    for (var index = 0; index < ruleIds.size(); index++)
                    {
                        var rule = rules.get(ruleIds.getLong(index));

                        if (rule.getJsonObject(ComplianceRule.COMPLIANCE_RULE_CONTEXT).getString(ComplianceRule.COMPLIANCE_RULE_CHECK_TYPE).equalsIgnoreCase(ComplianceConstants.RuleCheckIn.CONFIG_FILE.getName()))
                        {
                            configRules.add(rule);
                        }
                        else
                        {
                            commandRules.add(rule);
                        }
                    }

                    for (var i = 0; i < objects.size(); i++)
                    {
                        var item = objects.getJsonObject(i);

                        var monitorId = ObjectConfigStore.getStore().getIdByObjectId(item.containsKey(AIOpsObject.OBJECT_ID) ? item.getInteger(AIOpsObject.OBJECT_ID) : item.getInteger(Configuration.CONFIG_OBJECT));

                        if (event.getString(ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.OBJECT.getName()) || event.getString(ENTITY_TYPE).equalsIgnoreCase("Tag"))
                        {
                            //ConfigurationConfigStore as we require config management status yes or no so will require it from config store
                            item = ConfigurationConfigStore.getStore().getItem(ConfigurationConfigStore.getStore().getIdByObjectId(item.getInteger(AIOpsObject.OBJECT_ID))).mergeIn(item);
                        }
                        else if (event.getString(ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.GROUP.getName()))
                        {
                            item.mergeIn(ObjectConfigStore.getStore().getItem(monitorId)).put(ConfigConstants.CONFIG_OBJECT_ID, item.getLong(ID));
                        }

                        if (item != null && !item.isEmpty())
                        {
                            event.put(AIOpsObject.OBJECT_ID, item.getInteger(Configuration.CONFIG_OBJECT)).put(ComplianceConstants.COMPLIANCE_POLICY_ID, event.getLong(ID));

                            var objectStatus = ObjectStatusCacheStore.getStore().getItem(monitorId);

                            var valid = false;

                            if (!commandRules.isEmpty())
                            {
                                event.put(AIOpsObject.OBJECT_DISCOVERY_METHOD, item.getString(AIOpsObject.OBJECT_DISCOVERY_METHOD));

                                if (item.containsKey(AIOpsObject.OBJECT_EVENT_PROCESSORS))
                                {
                                    event.put(AIOpsObject.OBJECT_EVENT_PROCESSORS, item.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS));
                                }

                                if ((objectStatus != null && objectStatus.equalsIgnoreCase(STATUS_UP)) || PingUtil.isReachable(item.getString(AIOpsObject.OBJECT_IP), PORT_TIMEOUT_SECONDS, 3, CommonUtil.newEventId()))
                                {
                                    var configContext = item.getJsonObject(Configuration.CONFIG_CONTEXT);

                                    //if port is connected then valid is true else false.
                                    if (PortUtil.isConnected(item.getString(AIOpsObject.OBJECT_IP), configContext.getInteger(PORT), PORT_TIMEOUT_SECONDS))
                                    {
                                        event.put(PORT, configContext.getInteger(PORT));

                                        valid = true;
                                    }
                                    else
                                    {
                                        LOGGER.trace(String.format(ErrorMessageConstants.PORT_CONNECTION_FAILED, configContext.getInteger(PORT)));
                                    }
                                }
                                else
                                {
                                    LOGGER.trace(String.format(ErrorMessageConstants.PING_FAILED, item.getString(AIOpsObject.OBJECT_IP)));
                                }

                                if (valid && item.getString(Configuration.CONFIG_MANAGEMENT_STATUS).equalsIgnoreCase(GlobalConstants.YES))
                                {

                                    var credentialProfile = credentialProfiles.get(item.getLong(Configuration.CONFIG_CREDENTIAL_PROFILE));

                                    // added a batch facility which is monitor wise
                                    // per monitor collect all cli rule Build a command sting with separator which will later process by plugin engine
                                    // we will maintain order or output accordingly just like rulesByCLI
                                    var commands = new StringBuilder();

                                    var ids = new JsonArray();

                                    for (var j = 0; j < commandRules.size(); j++)
                                    {
                                        var rule = commandRules.getJsonObject(j);

                                        ids.add(rule.getLong(ID));

                                        commands.append(rule.getJsonObject(ComplianceRule.COMPLIANCE_RULE_CONTEXT).getString(ComplianceRule.COMPLIANCE_RULE_COMMAND));

                                        if (j != commandRules.size() - 1)
                                        {
                                            commands.append(VALUE_SEPARATOR);
                                        }

                                    }

                                    activeRequests.computeIfAbsent(event.getLong(ID), k -> new HashSet<>())
                                            .add(event.getLong(ID) + SEPARATOR + item.getInteger(Configuration.CONFIG_OBJECT) + ids.getLong(0));

                                    var eventId = CommonUtil.newEventId();

                                    event.put(EVENT_ID, eventId);

                                    vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_ID, event.getLong(EVENT_ID))
                                            .put(PluginEngineConstants.PLUGIN_ENGINE_REQUEST, PluginEngineConstants.PluginEngineRequest.COMPLIANCE.getName())
                                            .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_PLUGIN_ENGINE).put(TIMEOUT, SSH_CLIENT_TIMEOUT)
                                            .put(USER_NAME, event.containsKey(User.USER_NAME) ? event.getString(User.USER_NAME) : SYSTEM_USER)
                                            .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                                            .put(EventBusConstants.EVENT_CONTEXT, event));

                                    records.computeIfAbsent(event.getLong(ID), value -> new JsonArray());

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace(String.format("Executing for CLI rule on device %s ", item.getString(AIOpsObject.OBJECT_NAME)));
                                    }

                                    vertx.eventBus().send(EventBusConstants.EVENT_ROUTER, new JsonObject().mergeIn(event).put(EVENT_ID, eventId)
                                            .put(PluginEngineConstants.PLUGIN_ENGINE_REQUEST, PluginEngineConstants.PluginEngineRequest.COMPLIANCE.getName())
                                            .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_PLUGIN_ENGINE)
                                            .put(EVENT_ADDRESS, config().getString(EVENT_TYPE) + DOT_SEPARATOR + RESULT)
                                            .put(ComplianceBenchmark.COMPLIANCE_BENCHMARK_RULE_IDS, ids)
                                            .put(ComplianceConstants.COMPLIANCE_CLI_IDENTIFIER, ids.getLong(0))
                                            .put(EventBusConstants.EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                                            .put(TIMEOUT, SSH_CLIENT_TIMEOUT).put(COMPLIANCE_RULE_COMMAND, commands.toString())
                                            .put(AIOpsObject.OBJECT_IP, item.getString(AIOpsObject.OBJECT_IP))
                                            .put(AIOpsObject.OBJECT_CONTEXT, item.getJsonObject(AIOpsObject.OBJECT_CONTEXT))
                                            .put(AIOpsObject.OBJECT_TARGET, item.getString(AIOpsObject.OBJECT_TARGET))
                                            .put(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL, credentialProfile.getString(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL))
                                            .mergeIn(credentialProfile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT)));
                                }
                                else
                                {
                                    for (var j = 0; j < commandRules.size(); j++)
                                    {
                                        var rule = commandRules.getJsonObject(j);

                                        event.put(ComplianceConstants.COMPLIANCE_RULE_ID, rule.getLong(ID));

                                        var trailRecord = new JsonObject(); // Collect Micro data for raw table

                                        trailRecord.put(ComplianceConstants.CURRENT_SCAN_TIMESTAMP, DateTimeUtil.currentMilliSeconds())
                                                .put(ComplianceConstants.LAST_SCAN_TIMESTAMP, Long.MIN_VALUE)
                                                .put(ComplianceConstants.COMPLIANCE_RULE_ID, rule.getLong(ID))
                                                .put(AIOpsObject.OBJECT_ID, item.getInteger(Configuration.CONFIG_OBJECT))
                                                .put(ComplianceConstants.COMPLIANCE_POLICY_ID, event.getLong(GlobalConstants.ID))
                                                .put(COMPLIANCE_RULE_SEVERITY, rule.getString(ComplianceRule.COMPLIANCE_RULE_SEVERITY))
                                                .put(ComplianceConstants.COMPLIANCE_BENCHMARK_ID, event.getLong(CompliancePolicy.COMPLIANCE_POLICY_BENCHMARK))
                                                .put(ComplianceConstants.CURRENT_SCAN_STATUS, ComplianceConstants.RuleStatus.UNKNOWN.getType())
                                                .put(ComplianceConstants.LAST_SCAN_STATUS, ComplianceConstants.RuleStatus.UNKNOWN.getType());

                                        trailRecord.put(ComplianceConstants.CURRENT_SCAN_STATUS, ComplianceConstants.RuleStatus.FAILED.getType());

                                        updateLastScanRecord(event, trailRecord, ruleIds.getLong(j));

                                        records.computeIfAbsent(event.getLong(ID), value -> new JsonArray()).add(trailRecord);

                                        if (CommonUtil.traceEnabled())
                                        {
                                            LOGGER.trace(String.format("Executing for CLI rule %s on device %s %s which are failed due to connection or config management status is off", rule.getString(ComplianceRule.COMPLIANCE_RULE_NAME), item.getString(AIOpsObject.OBJECT_NAME), rule.getString(ID)));
                                        }

                                        if (MotadataConfigUtil.devMode())
                                        {
                                            vertx.eventBus().send("test.compliance.manager", new JsonObject().put(ID, event.getLong(ID)).put(MESSAGE, "Scan stopped as config status is off"));
                                        }

                                    }
                                }

                            }

                            if (!configRules.isEmpty())
                            {
                                for (var j = 0; j < configRules.size(); j++)
                                {
                                    var trailRecord = new JsonObject(); // Collect Micro data for raw table

                                    var rule = configRules.getJsonObject(j);

                                    event.put(ComplianceConstants.COMPLIANCE_RULE_ID, rule.getLong(ID));

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace(String.format("Executing rule %s on device %s", rule.getString(ComplianceRule.COMPLIANCE_RULE_NAME), item.getString(AIOpsObject.OBJECT_NAME)));
                                    }

                                    evaluate(event, item.put(AIOpsObject.OBJECT_NAME, item.getString(AIOpsObject.OBJECT_NAME)).put(ComplianceRule.COMPLIANCE_RULE_NAME, rule.getString(ComplianceRule.COMPLIANCE_RULE_NAME)), rule, trailRecord);

                                    records.computeIfAbsent(event.getLong(ID), value -> new JsonArray()).add(trailRecord);
                                }
                            }

                            if (records.containsKey(event.getLong(ID)) && !records.get(event.getLong(ID)).isEmpty())
                            {
                                vertx.eventBus().send(config().getString(EVENT_TYPE) + DOT_SEPARATOR + RESULT, event);
                            }
                        }
                        else
                        {
                            LOGGER.debug("Item is null or empty that being used for Compliance Policy");
                        }
                    }
                }
                else
                {
                    LOGGER.warn(String.format("Unsupported category %s", event.getString(CompliancePolicy.COMPLIANCE_POLICY_CATEGORY)));
                }
            }
            else
            {
                LOGGER.info("No object Found for Compliance Policy to run for " + event.getString(CompliancePolicy.COMPLIANCE_POLICY_NAME));

                EventBusConstants.publish(EventBusConstants.EVENT_COMPLIANCE_POLICY_STATE_CHANGE, new JsonObject().put(ID, event.getLong(GlobalConstants.ID)).put(CompliancePolicy.COMPLIANCE_POLICY_SCHEDULER, YES).put(NMSConstants.STATE, NMSConstants.STATE_NOT_RUNNING));

                vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION,
                        new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_COMPLIANCE_POLICY)
                                .put(MESSAGE, "Scan Failed, No Object Found for Compliance Policy.")
                                .put(STATUS, STATUS_FAIL));

                if (MotadataConfigUtil.devMode())
                {
                    vertx.eventBus().send("test.compliance.manager", new JsonObject().put(ID, event.getLong(ID)).put(MESSAGE, "No object Found for Compliance Policy"));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * This method will prepare query for calculating Compliance percentage as per rules passed or failed count
     * if passed count is 15 and failed count is 5 so total compliance count as percentage would be 75%
     * Function will also set Severity as per Compliance Percentage.
     * Eg :- compliance percentage is 75% then severity would be secure.
     */
    private void calculate(JsonObject policyStatsRecord, JsonObject entityStatsRecord, int totalScore, int passedRules)
    {
        var compliancePercentage = CommonUtil.getInteger((passedRules * 100) / totalScore);

        entityStatsRecord.put(ComplianceConstants.COMPLIANCE_PERCENTAGE, compliancePercentage);

        var state = ComplianceConstants.getState(compliancePercentage).toLowerCase();

        entityStatsRecord.put(SEVERITY, state);

        policyStatsRecord.put(state, policyStatsRecord.getInteger(state) + 1);
    }

    /**
     * This method will prepare context and will evaluate all the conditions of rule on given device.
     * Once whole rule is executed, it will dump data into CacheStore.
     * If manage status is off for Network Category, then all the rule's status will be failed.
     * As of now we are only supporting rule.check.type == Config. In future we will have CLI.
     */
    private void evaluate(JsonObject event, JsonObject item, JsonObject rule, JsonObject trailRecord)
    {
        try
        {
            trailRecord.put(ComplianceConstants.CURRENT_SCAN_TIMESTAMP, DateTimeUtil.currentMilliSeconds())
                    .put(ComplianceConstants.LAST_SCAN_TIMESTAMP, Long.MIN_VALUE)
                    .put(ComplianceConstants.COMPLIANCE_RULE_ID, rule.getLong(ID))
                    .put(AIOpsObject.OBJECT_ID, item.getInteger(Configuration.CONFIG_OBJECT))
                    .put(ComplianceConstants.COMPLIANCE_POLICY_ID, event.getLong(GlobalConstants.ID))
                    .put(COMPLIANCE_RULE_SEVERITY, rule.getString(ComplianceRule.COMPLIANCE_RULE_SEVERITY))
                    .put(ComplianceConstants.COMPLIANCE_BENCHMARK_ID, event.getLong(CompliancePolicy.COMPLIANCE_POLICY_BENCHMARK))
                    .put(ComplianceConstants.CURRENT_SCAN_STATUS, ComplianceConstants.RuleStatus.UNKNOWN.getType())
                    .put(ComplianceConstants.LAST_SCAN_STATUS, ComplianceConstants.RuleStatus.UNKNOWN.getType());

            if (item.getString(Configuration.CONFIG_MANAGEMENT_STATUS).equalsIgnoreCase(GlobalConstants.YES))
            {
                var ruleContext = rule.getJsonObject(ComplianceRule.COMPLIANCE_RULE_CONTEXT);

                if (ruleContext.getString(ComplianceRule.COMPLIANCE_RULE_CHECK_TYPE).equalsIgnoreCase(ComplianceConstants.RuleCheckIn.CONFIG_FILE.getName()))
                {
                    if (event.getString(CompliancePolicy.COMPLIANCE_POLICY_CONFIG_FILE_TYPE).equalsIgnoreCase(ConfigConstants.ConfigBackupType.STARTUP.getName()))
                    {
                        trailRecord.put(ComplianceConstants.CONTENT, CodecUtil.toString(ConfigurationCacheStore.getStore().getRecentStartUpBackupRecords(item.getInteger(Configuration.CONFIG_OBJECT))));
                    }
                    else
                    {
                        trailRecord.put(ComplianceConstants.CONTENT, CodecUtil.toString(ConfigurationCacheStore.getStore().getRecentRunningBackupRecords(item.getInteger(Configuration.CONFIG_OBJECT))));
                    }

                    if (trailRecord.getString(ComplianceConstants.CONTENT) != null && !trailRecord.getString(ComplianceConstants.CONTENT).isEmpty())
                    {
                        if (ruleContext.getString(ComplianceRule.COMPLIANCE_RULE_CHECK_CATEGORY).equalsIgnoreCase(ComplianceConstants.RuleCheckConfiguration.ADVANCED.name()))
                        {
                            var blockCriteria = ruleContext.getJsonObject(ComplianceRule.COMPLIANCE_RULE_BLOCK_CRITERIA);

                            String pattern;

                            if (blockCriteria.containsKey(ComplianceRule.COMPLIANCE_RULE_BLOCK_END) && !blockCriteria.getString(ComplianceRule.COMPLIANCE_RULE_BLOCK_END).isEmpty())
                            {
                                pattern = String.format(ComplianceConstants.BLOCK_CRITERIA_REGEX_PATTERN, blockCriteria.getString(ComplianceRule.COMPLIANCE_RULE_BLOCK_START), blockCriteria.getString(ComplianceRule.COMPLIANCE_RULE_BLOCK_END));
                            }
                            else
                            {
                                pattern = String.format(ComplianceConstants.BLOCK_CRITERIA_REGEX_PATTERN_WITH_START, blockCriteria.getString(ComplianceRule.COMPLIANCE_RULE_BLOCK_START));
                            }

                            patterns.computeIfAbsent(pattern, value -> Pattern.compile(value, Pattern.DOTALL));

                            var matcher = patterns.get(pattern).matcher(trailRecord.getString(ComplianceConstants.CONTENT));

                            /*`
                                Using Stream Instead of while loop because we did benchmarking of 1000 elements on both while and stream, Stream taking less time than while loop every time we take new matcher .
                             */
                            var result = matcher.results()
                                    .map(MatchResult::group)
                                    .collect(Collectors.joining("\n\n"));

                            if (!result.isEmpty())
                            {
                                trailRecord.put(ComplianceConstants.CONTENT, result);

                                if (blockCriteria.containsKey(ComplianceRule.COMPLIANCE_RULE_CONDITIONS) && !blockCriteria.getJsonArray(ComplianceRule.COMPLIANCE_RULE_CONDITIONS).isEmpty())
                                {
                                    trailRecord.put(ComplianceConstants.CURRENT_SCAN_STATUS, evaluateConditions(blockCriteria.getJsonArray(ComplianceRule.COMPLIANCE_RULE_CONDITIONS), trailRecord).getInteger(STATUS).equals(ComplianceConstants.RuleStatus.SUCCEEDED.getType()) ? evaluateConditions(ruleContext.getJsonArray(ComplianceRule.COMPLIANCE_RULE_CONDITIONS), trailRecord).getInteger(STATUS) : ComplianceConstants.RuleStatus.FAILED.getType());
                                }
                                else
                                {
                                    trailRecord.put(ComplianceConstants.CURRENT_SCAN_STATUS, evaluateConditions(ruleContext.getJsonArray(ComplianceRule.COMPLIANCE_RULE_CONDITIONS), trailRecord).getInteger(STATUS));
                                }
                            }
                            else
                            {
                                trailRecord.put(ComplianceConstants.CURRENT_SCAN_STATUS, ComplianceConstants.RuleStatus.FAILED.getType());
                            }
                        }
                        else
                        {
                            trailRecord.put(ComplianceConstants.CURRENT_SCAN_STATUS, evaluateConditions(ruleContext.getJsonArray(ComplianceRule.COMPLIANCE_RULE_CONDITIONS), trailRecord).getInteger(STATUS)); // BASIC CATEGORY..
                        }
                    }
                    else
                    {
                        trailRecord.put(ComplianceConstants.CURRENT_SCAN_STATUS, ComplianceConstants.RuleStatus.FAILED.getType());
                    }

                    trailRecord.remove(ComplianceConstants.CONTENT);

                    updateLastScanRecord(event, trailRecord, rule.getLong(ID));

                    LOGGER.debug(String.format("Output for rule %s on device %s is %s", rule.getString(ComplianceRule.COMPLIANCE_RULE_NAME), item.getString(AIOpsObject.OBJECT_NAME), trailRecord));
                }
            }
            else
            {
                LOGGER.warn(String.format("Manage status for device %s is off", item.getString(AIOpsObject.OBJECT_NAME)));

                trailRecord.put(ComplianceConstants.CURRENT_SCAN_STATUS, ComplianceConstants.RuleStatus.FAILED.getType());

                updateLastScanRecord(event, trailRecord, rule.getLong(ID));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    // added rule id as we are fetching it from the event first because we are updating rule wise status.
    // now we are doing batch update we have list of rule ids in event by passing rule id we are updating that record only
    private void updateLastScanRecord(JsonObject event, JsonObject trailRecord, long ruleId)
    {
        if (CompliancePolicyCacheStore.getStore().getLastScanStatus(event.getLong(GlobalConstants.ID), event.getInteger(AIOpsObject.OBJECT_ID), ruleId) != null)
        {
            trailRecord.put(ComplianceConstants.LAST_SCAN_STATUS, CompliancePolicyCacheStore.getStore().getLastScanStatus(event.getLong(GlobalConstants.ID), event.getInteger(AIOpsObject.OBJECT_ID), ruleId));
        }

        //update LastScanStatus as per Current Status scan...
        CompliancePolicyCacheStore.getStore().updateLastScanStatus(event.getLong(GlobalConstants.ID), event.getInteger(AIOpsObject.OBJECT_ID), ruleId, trailRecord.getInteger(ComplianceConstants.CURRENT_SCAN_STATUS));

        if (CompliancePolicyCacheStore.getStore().getLastScanTimestamp(event.getLong(GlobalConstants.ID), event.getInteger(AIOpsObject.OBJECT_ID), ruleId) > 0)
        {
            trailRecord.put(ComplianceConstants.LAST_SCAN_TIMESTAMP, CompliancePolicyCacheStore.getStore().getLastScanTimestamp(event.getLong(GlobalConstants.ID), event.getInteger(AIOpsObject.OBJECT_ID), ruleId));
        }

        //update LastScanTimestamp as per CurrentScanTimestamp..
        CompliancePolicyCacheStore.getStore().updateLastScanTimestamp(event.getLong(GlobalConstants.ID), event.getInteger(AIOpsObject.OBJECT_ID), ruleId, trailRecord.getLong(ComplianceConstants.CURRENT_SCAN_TIMESTAMP));
    }

    /**
     * This method will evaluate all conditions.
     *
     * @param conditions all conditions
     * @param context    contains all the required fields necessary to evaluate conditions.
     * @return JsonObject contains status, message if rule is failed.
     */
    private JsonObject evaluateConditions(JsonArray conditions, JsonObject context)
    {
        var result = new JsonObject().put(STATUS, ComplianceConstants.RuleStatus.SUCCEEDED.getType());

        try
        {
            var output = false;

            var previousStatus = true;

            var errors = new JsonArray();

            for (var index = 0; index < conditions.size(); index++)
            {
                var condition = conditions.getJsonObject(index);

                var occurrence = condition.containsKey(ComplianceConstants.RULE_OCCURRENCE) ? condition.getInteger(ComplianceConstants.RULE_OCCURRENCE) : NOT_AVAILABLE; // means "any"

                patterns.computeIfAbsent(condition.getString(ComplianceConstants.RESULT_PATTERN), value -> Pattern.compile(condition.getString(ComplianceConstants.RESULT_PATTERN)));

                output = evaluateCondition(condition.getString(ComplianceConstants.RULE_CONDITION), condition.getString(ComplianceConstants.RESULT_PATTERN), context.getValue(ComplianceConstants.CONTENT), occurrence);

                if (condition.getString(OPERATOR).equalsIgnoreCase(DatastoreConstants.ConditionGroup.AND.getName()))
                {
                    previousStatus = previousStatus && output;
                }
                else if (condition.getString(OPERATOR).equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName()))
                {
                    previousStatus = previousStatus || output;
                }
                else
                {
                    previousStatus = output;
                }

                if (!output)
                {
                    errors.add(new JsonObject().put(ComplianceConstants.RULE_CONDITION, condition.getString(ComplianceConstants.RULE_CONDITION))
                            .put(ComplianceConstants.RESULT_PATTERN, condition.getString(ComplianceConstants.RESULT_PATTERN))
                            .put(MESSAGE, "Regex match/occurrence not found"));
                }
            }

            if (!previousStatus)
            {
                result.put(STATUS, ComplianceConstants.RuleStatus.FAILED.getType())
                        .put(ERRORS, errors)
                        .put(MESSAGE, errors.getJsonObject(errors.size() - 1).getString(MESSAGE));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        eventEngine.stop(vertx, promise);
    }

    private boolean evaluateCondition(String condition, Object conditionValue, Object operandValue, Object occurrence)
    {
        var result = false;

        if (condition != null && conditionValue != null)
        {
            switch (PolicyEngineConstants.Operator.valueOfName(condition))
            {
                case CONTAINS ->
                {
                    var count = 0L;

                    var pattern = patterns.get(CommonUtil.getString(conditionValue));

                    if (pattern != null)
                    {
                        count = pattern.matcher(CommonUtil.getString(operandValue)).results().count();
                    }

                    if (CommonUtil.getInteger(occurrence) == NOT_AVAILABLE) // means value of occurrence is any
                    {
                        result = count > 0;
                    }
                    else
                    {
                        result = count == CommonUtil.getInteger(occurrence);
                    }
                }

                case NOT_CONTAINS ->
                        result = patterns.get(CommonUtil.getString(conditionValue)).matcher(CommonUtil.getString(operandValue)).results().findAny().isEmpty();

                default ->
                {
                }
            }
        }

        return result;
    }

    /*

     */
    private void clear(long id)
    {
        records.remove(id);

        activeRequests.remove(id);
    }
}
