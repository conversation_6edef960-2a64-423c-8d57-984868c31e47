/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */


/*   Change Logs:
 *   Date          Author              Notes
 *   11-Feb-2025    Chandresh           MOTADATA-446   Event Policy Template related enhancements
 *   28-Feb-2025    Bharat              MOTADATA-5233: Added template for different type of share.
 *   4-Mar-2025     Bharat              MOTADATA-4740: Two factor authentication 2FA
 *   5-Mar-2025     Pruthviraj          MOTADATA-5331 : Notification templates added for netroute policy
 *   9-Apr-2025     Bharat              MOTADATA-5141: Alert Drill-down from email and Teams Notification
 *   7-Apr-2025     Vismit              MOTADATA-5613: Changed config email notification html template.
 */


package com.mindarray.notification;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.api.SNMPTrapForwarder;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.EMPTY_VALUE;

/**
 * Abstract base class for handling various types of notifications in the Motadata system.
 * <p>
 * This class provides:
 * 1. Constants for notification-related fields and templates
 * 2. Static utility methods for sending different types of notifications (email, SMS, SNMP trap, etc.)
 * 3. Methods for retrieving appropriate notification templates based on notification type and context
 * 4. Enums for notification types, content types, and share types
 * <p>
 * The class uses Vert.x EventBus to publish notification events that are handled by
 * appropriate notification handlers elsewhere in the system.
 * <p>
 * Concrete notification implementations should extend this class and implement
 * the abstract notify method.
 */
public abstract class Notification
{

    public static final String NOTIFICATION_TYPE = "notification.type";
    public static final String SMS_NOTIFICATION_RECIPIENTS = "sms.notification.recipients";
    public static final String EMAIL_NOTIFICATION_RECIPIENTS = "email.notification.recipients";
    public static final String EMAIL_NOTIFICATION_SENDER = "email.notification.sender";
    public static final String EMAIL_NOTIFICATION_SUBJECT = "email.notification.subject";
    public static final String EMAIL_NOTIFICATION_MESSAGE = "email.notification.message";
    public static final String EMAIL_NOTIFICATION_CONTENT = "email.notification.content";
    public static final String EMAIL_NOTIFICATION_ATTACHMENTS = "email.notification.attachments";
    public static final String EMAIL_NOTIFICATION_ATTACHMENT_TYPE = "email.notification.attachment.type";
    public static final String EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE = "email.notification.attachment.disposition.type";
    public static final String SMS_NOTIFICATION_MESSAGE = "sms.notification.message";
    public static final String CHANNEL_NOTIFICATION_CONTENT = "channel.notification.content";

    /**
     * Default disclaimer text for Motadata branding
     */
    public static final String EMAIL_DISCLAIMER_DEFAULT = "This is an automated message generated by Motadata. Please do not reply to this email. For any queries, reach out to your System Admin";

    /**
     * Rebranded disclaimer text for custom branding
     */
    public static final String EMAIL_DISCLAIMER_REBRANDED = "This is an auto-generated message. Do not respond to this email. If you have any questions, please contact your System Administrator.";

    public static final String EMAIL_USER_OTP_VERIFICATION_HTML_TEMPLATE = "<!doctype html> <html> <head> <meta charset=\"utf-8\"> <title>Motadata AIOps - Authentication Code</title> </head> <body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"> <table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\" border-collapse: collapse; font-family: Arial, Helvetica, sans-serif; background:#F3F6F8;\"> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"40\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" width=\"650\" valign=\"top\" align=\"center\"> <a href=\"https://www.motadata.com/\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px; height:25px;\" src=\"cid:logo.png\" alt=\"motadata\" /></a> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"24\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" valign=\"top\" align=\"center\"> <table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\" padding:0 24px; border-top: 4px solid #009DDC; background:#fff;\"> <tbody> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"24\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0; text-align: center;\"><img width=\"37\" height=\"37\" style=\"width:37px; height:37px;\" src=\"cid:security-lock.png\" alt=\"Motadata AIOps - Authentication Code\" /></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"10\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0; text-align: center;\"> <h1 style=\"font-size: 16px; color:#364658; font-family: Arial, Helvetica, sans-serif; margin:0; padding:0 0 5px;\"> Motadata AIOps - Authentication Code</h1> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"32\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"16\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <p style=\"font-size:#364658; font-size:11px; line-height:16px; padding:0; margin:0;\">Hi ${user.name}, </p> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"12\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <p style=\"font-size:#364658; font-size:11px; line-height:16px; padding:0 0 12px 0; margin:0;\"> The One Time Password (OTP) to access Motadata AIOps is ${otp}.<br /> The OTP is valid for 3 minutes.</p> <p style=\"font-size:#364658; font-size:11px; line-height:16px; padding:0; margin:0;\">For your security, please do not share this OTP to anyone.</p> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"12\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <p style=\"font-size:#364658; font-size:11px; line-height:16px; padding:0; margin:0;\"> Best regards,<br />Motadata team </p> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"16\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\"> <p style=\"color:#364658; font-size:10px; line-height:14px; text-align:center; margin:0; padding:0 0 5px;\"> <strong>Disclaimer:</strong> ${disclaimer}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"24\"></td> </tr> <tr align=\"center\" ; style=\"margin:0; padding:0; text-align: center;\"> <td style=\"margin:0; padding:0;\"> <p style=\"font-size:10px; margin:0; padding:0; color:#7B8FA5;\">For more details, visit <a style=\"color:#009DDC; text-decoration: none;\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"16\"></td> </tr> <tr align=\"center\" style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\" border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:fb.png\" alt=\"Facebook\" /></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:twitter.png\" alt=\"Twitter\" /></a> </td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\" /></a> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"32\"></td> </tr> </table> </body> </html>";
    public static final String EMAIL_NOTIFICATION_DATABASE_BACKUP_SUCCESSFUL_HTML_TEMPLATE = "<!doctype html> <html> <head> <meta charset=\"utf-8\"> <title>Backup Successful</title> </head> <body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"> <table width=\"100%\" bgcolor=\"#F3F6F8\"  border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\" border-collapse: collapse; font-family: Arial, Helvetica, sans-serif; background:#F3F6F8;\"> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"40\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" width=\"650\" valign=\"top\" align=\"center\"> <a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px; height:25px;\" src=\"cid:${logo}\" alt=\"motadata\"/></a> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" valign=\"top\" align=\"center\"> <table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\"  bgcolor=\"#ffffff\" style=\" padding:0 24px; border-top: 4px solid #89C540; background:#fff;\"> <tbody> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0; text-align: center;\"><img width=\"36\" height=\"36\" style=\"width:36px; height:36px;\" src=\"cid:successful.png\" alt=\"successful\"/></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"10\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0; text-align: center;\"> <h1 style=\"font-size: 16px; color:#364658;  font-family: Arial, Helvetica, sans-serif; margin:0; padding:0 0 5px;\">Motadata ${backup.profile.type} Backup</h1> <p style=\"color:#9BA3AB; font-size:12px; text-align:center; margin:0; padding:3px 0 0 0; font-weight:bold;\">${timestamp}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr style=\"margin:0; padding:0;\" valign=\"top\"> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Database Type</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${backup.profile.type}</p> </td> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Triggered at</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${backup.start.time}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0\" valign=\"top\"> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Duration </p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${duration}</p> </td> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Backup Destination</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${storage.profile.protocol}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"2\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"2\" style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"2\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"2\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Message</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">The ${backup.profile.type} backup is completed successfully.</p> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\"> <p style=\"color:#364658; font-size:10px; line-height:14px; text-align:center; margin:0; padding:0 0 5px;\"><strong>Disclaimer:</strong> ${disclaimer}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr align=\"center\"; style=\"margin:0; padding:0; text-align: center;\"><td style=\"margin:0; padding:0;\"><p style=\"font-size:10px; margin:0; padding:0; color:#7B8FA5;\">For more details, visit  <a style=\"color:#009DDC; text-decoration: none;\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr align=\"center\" style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\" border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:fb.png\" alt=\"Facebook\"/></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:twitter.png\" alt=\"Twitter\"/></a></td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"/></a></td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr> </table> </body> </html>";
    public static final String EMAIL_NOTIFICATION_DATABASE_BACKUP_FAILED_HTML_TEMPLATE = "<!doctype html> <html> <head> <meta charset=\"utf-8\"> <title>Backup Failed</title> </head> <body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"> <table width=\"100%\" bgcolor=\"#F3F6F8\"  border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\" border-collapse: collapse; font-family: Arial, Helvetica, sans-serif; background:#F3F6F8;\"> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"40\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" width=\"650\" valign=\"top\" align=\"center\"> <a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px; height:25px;\" src=\"cid:${logo}\" alt=\"motadata\"/></a> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" valign=\"top\" align=\"center\"> <table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\"  bgcolor=\"#ffffff\" style=\" padding:0 24px; border-top: 4px solid #F04E3E; background:#fff;\"> <tbody> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0; text-align: center;\"><img width=\"36\" height=\"36\" style=\"width:36px; height:36px;\" src=\"cid:critical.png\" alt=\"failed\"/></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"10\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0; text-align: center;\"> <h1 style=\"font-size: 16px; color:#364658;  font-family: Arial, Helvetica, sans-serif; margin:0; padding:0 0 5px;\">Motadata ${backup.profile.type} Backup</h1> <p style=\"color:#9BA3AB; font-size:12px; text-align:center; margin:0; padding:3px 0 0 0; font-weight:bold;\">${timestamp}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr style=\"margin:0; padding:0;\" valign=\"top\"> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Database Type</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${backup.profile.type}</p> </td> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Triggered at</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${backup.start.time}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0\" valign=\"top\"> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Duration </p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${duration}</p> </td> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Backup Destination</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${storage.profile.protocol}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"2\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"2\" style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"2\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"2\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Message</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">The ${backup.profile.type} backup failed.</p> </td> </tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Reason</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${message}</p> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\"> <p style=\"color:#364658; font-size:10px; line-height:14px; text-align:center; margin:0; padding:0 0 5px;\"><strong>Disclaimer:</strong> ${disclaimer}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr align=\"center\"; style=\"margin:0; padding:0; text-align: center;\"><td style=\"margin:0; padding:0;\"><p style=\"font-size:10px; margin:0; padding:0; color:#7B8FA5;\">For more details, visit  <a style=\"color:#009DDC; text-decoration: none;\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr align=\"center\" style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\" border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:fb.png\" alt=\"Facebook\"/></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:twitter.png\" alt=\"Twitter\"/></a></td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"/></a></td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr> </table> </body> </html>";
    public static final String EMAIL_NOTIFICATION_DISCOVERY_HTML_TEMPLATE = "<!doctype html><html><head><meta charset=\"utf-8\"><title>Executed – (Discovery Profile)</title><style type=\"text/css\">.success{ color:#89C540;}.Failure{color: #F04E3E;}</style></head><body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"><table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\" border-collapse: collapse; font-family: Arial, Helvetica, sans-serif; background:#F3F6F8;\"><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"40\"></td></tr><tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" width=\"650\" valign=\"top\" align=\"center\"> <a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px; height:25px;\" src=\"cid:${logo}\" alt=\"motadata\"/></a> </td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" valign=\"top\" align=\"center\"> <table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\" padding:0 24px; border-top: 4px solid #009DDC; background:#fff;\"> <tbody> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0; text-align: center;\"><img width=\"36\" height=\"36\" style=\"width:36px; height:36px;\" src=\"cid:information.png\" alt=\"clear\"/></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"10\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0; text-align: center;\"><h1 style=\"font-size: 16px; color:#364658; font-family: Arial, Helvetica, sans-serif; margin:0; padding:0 0 5px;\">Executed – (Discovery Profile)</h1><p style=\"color:#9BA3AB; font-size:12px; text-align:center; margin:0; padding:3px 0 0 0; font-weight:bold;\">${timestamp}</p></td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody><tr style=\"margin:0; padding:0;\" valign=\"top\"> <td width=\"33.333%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Discovery Profile(s)</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${discovery.name}</p> </td> <td width=\"33.333%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Object Type</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${discovery.object.type}</p> </td> <td width=\"33.333%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Group</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${discovery.groups}</p> </td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr><tr style=\"margin:0; padding:0\" valign=\"top\"> <td width=\"33.333%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Discovered Object(s) Count</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\"><span style=\"color:#89C540\">${discovery.discovered.objects}</span>/<span style=\"color:#F04E3E\">${discovery.failed.objects}</span></p> </td> <td width=\"33.333%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Execution Type</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">(Manual/Scheduled)</p> </td> <td width=\"33.333%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Status</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\"><span class=\"${discovery.status}\">${discovery.status}</span></p> </td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0 5px;\"><p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Message</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${discovery.name} has been successfully executed.</p></td></tr> </tbody></table></td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\"><p style=\"color:#364658; font-size:10px; line-height:14px; text-align:center; margin:0; padding:0 0 5px;\"><strong>Disclaimer:</strong> ${disclaimer}</p></td> </tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr> </tbody></table> </td> </tr> </tbody></table> </td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr align=\"center\"; style=\"margin:0; padding:0; text-align: center;\"><td style=\"margin:0; padding:0;\"><p style=\"font-size:10px; margin:0; padding:0; color:#7B8FA5;\">For more details, visit <a style=\"color:#009DDC; text-decoration: none;\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p></td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr><tr align=\"center\" style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\" border-collapse: collapse; margin:0; padding:0;\"> <tbody><tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:fb.png\" alt=\"Facebook\"/></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:twitter.png\" alt=\"Twitter\"/></a></td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"/></a></td></tr> </tbody></table></td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr></table></body></html>";
    public static final String EMAIL_NOTIFICATION_WIDGET_HTML_TEMPLATE = "<!doctype html> <html> <head> <meta charset=\"utf-8\"> <title>Executed – (Runbook)</title> </head> <body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"> <table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\" border-collapse: collapse; font-family: Arial, Helvetica, sans-serif; background:#F3F6F8;\"> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"40\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" width=\"650\" valign=\"top\" align=\"center\"> <a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px; height:25px;\" src=\"cid:${logo}\" alt=\"motadata\"/></a> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" valign=\"top\" align=\"center\"> <table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\" padding:0 24px; border-top: 4px solid #009DDC; background:#fff;\"> <tbody> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><p style=\"font-size:13px; font-weight:normal; line-height:normal; margin:0; padding:0;\"><strong>${user.name}</strong> has shared the below ${type} with you.</p></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><img src=\"cid:${filename}\" alt=\"\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Message</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${message}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\"> <p style=\"color:#364658; font-size:10px; line-height:14px; text-align:center; margin:0; padding:0 0 5px;\"><strong>Disclaimer:</strong> ${disclaimer}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr align=\"center\"; style=\"margin:0; padding:0; text-align: center;\"><td style=\"margin:0; padding:0;\"><p style=\"font-size:10px; margin:0; padding:0; color:#7B8FA5;\">For more details, visit <a style=\"color:#009DDC; text-decoration: none;\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr align=\"center\" style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\" border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:fb.png\" alt=\"Facebook\"/></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:twitter.png\" alt=\"Twitter\"/></a></td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"/></a></td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr> </table> </body> </html>";
    public static final String EMAIL_NOTIFICATION_METRIC_EXPLORER_HTML_TEMPLATE = "<!doctype html> <html> <head> <meta charset=\"utf-8\"> <title>Executed – (Runbook)</title> </head> <body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"> <table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\" border-collapse: collapse; font-family: Arial, Helvetica, sans-serif; background:#F3F6F8;\"> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"40\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" width=\"650\" valign=\"top\" align=\"center\"> <a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px; height:25px;\" src=\"cid:${logo}\" alt=\"motadata\"/></a> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" valign=\"top\" align=\"center\"> <table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\" padding:0 24px; border-top: 4px solid #009DDC; background:#fff;\"> <tbody> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><p style=\"font-size:13px; font-weight:normal; line-height:normal; margin:0; padding:0;\"><strong>${user.name}</strong> has shared the below ${type} with you.</p></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><img src=\"cid:${filename}\" alt=\"\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Message</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${message}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\"> <p style=\"color:#364658; font-size:10px; line-height:14px; text-align:center; margin:0; padding:0 0 5px;\"><strong>Disclaimer:</strong> ${disclaimer}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr align=\"center\"; style=\"margin:0; padding:0; text-align: center;\"><td style=\"margin:0; padding:0;\"><p style=\"font-size:10px; margin:0; padding:0; color:#7B8FA5;\">For more details, visit <a style=\"color:#009DDC; text-decoration: none;\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr align=\"center\" style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\" border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:fb.png\" alt=\"Facebook\"/></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:twitter.png\" alt=\"Twitter\"/></a></td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"/></a></td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr> </table> </body> </html>";
    public static final String EMAIL_NOTIFICATION_SEARCH_QUERY_HTML_TEMPLATE = "<!doctype html> <html> <head> <meta charset=\"utf-8\"> <title>Executed – (Runbook)</title> </head> <body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"> <table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\" border-collapse: collapse; font-family: Arial, Helvetica, sans-serif; background:#F3F6F8;\"> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"40\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" width=\"650\" valign=\"top\" align=\"center\"> <a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px; height:25px;\" src=\"cid:${logo}\" alt=\"motadata\" /></a> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"24\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" valign=\"top\" align=\"center\"> <table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\" padding:0 24px; border-top: 4px solid #009DDC; background:#fff;\"> <tbody> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"24\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <p style=\"font-size:13px; font-weight:normal; line-height:normal; margin:0; padding:0;\"> <strong>${user.name}</strong> has shared the below ${type} with you.</p> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"24\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"24\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"><img src=\"cid:${filename}\" alt=\"\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"24\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\"> <hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\"> Query</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\"> ${search.query}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"> </td> </tr> <tr style=\"margin:0; padding:0;\"></tr> <td style=\"margin:0; padding:0;\" height=\"24\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\"> Message</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\"> ${message}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\"> <p style=\"color:#364658; font-size:10px; line-height:14px; text-align:center; margin:0; padding:0 0 5px;\"> <strong>Disclaimer:</strong> ${disclaimer}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"24\"></td> </tr> <tr align=\"center\" ; style=\"margin:0; padding:0; text-align: center;\"> <td style=\"margin:0; padding:0;\"> <p style=\"font-size:10px; margin:0; padding:0; color:#7B8FA5;\">For more details, visit <a style=\"color:#009DDC; text-decoration: none;\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"16\"></td> </tr> <tr align=\"center\" style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\" border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:fb.png\" alt=\"Facebook\" /></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:twitter.png\" alt=\"Twitter\" /></a></td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\" /></a></td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"32\"></td> </tr> </table> </body> </html>";
    public static final String EMAIL_INTEGRATION_FAILED_HTML_TEMPLATE = "<!doctype html><html><head><meta charset=\"utf-8\"><title>Integration Failed</title></head><body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"><table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\" border-collapse: collapse; font-family: Arial, Helvetica, sans-serif; background:#F3F6F8;\"><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"40\"></td></tr><tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" width=\"650\" valign=\"top\" align=\"center\"> <a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px; height:25px;\" src=\"cid:${logo}\" alt=\"motadata\"/></a> </td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" valign=\"top\" align=\"center\"> <table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\" padding:0 24px; border-top: 4px solid #F04E3E; background:#fff;\"> <tbody> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0; text-align: center;\"><img width=\"36\" height=\"36\" style=\"width:36px; height:36px;\" src=\"cid:critical.png\" alt=\"successful\"/></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"10\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0; text-align: center;\"><h1 style=\"font-size: 16px; color:#364658; font-family: Arial, Helvetica, sans-serif; margin:0; padding:0 0 5px;\">${integration.type} Integration</h1><p style=\"color:#9BA3AB; font-size:12px; text-align:center; margin:0; padding:3px 0 0 0; font-weight:bold;\">${timestamp}</p></td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody><tr style=\"margin:0; padding:0;\" valign=\"top\"> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">IP/URL</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${target}</p> </td> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Triggered at</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${timestamp}</p> </td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"2\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"2\" style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"2\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"2\" style=\"margin:0; padding:0 5px;\"><p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Message</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">An integration with ${integration.type} is failed!</p></td></tr></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0 5px;\"><p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Reason</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${message}</p></td></tr> </tbody></table></td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\"><p style=\"color:#364658; font-size:10px; line-height:14px; text-align:center; margin:0; padding:0 0 5px;\"><strong>Disclaimer:</strong> ${disclaimer}</p></td> </tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr> </tbody></table> </td> </tr> </tbody></table> </td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr align=\"center\"; style=\"margin:0; padding:0; text-align: center;\"><td style=\"margin:0; padding:0;\"><p style=\"font-size:10px; margin:0; padding:0; color:#7B8FA5;\">For more details, visit <a style=\"color:#009DDC; text-decoration: none;\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p></td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr><tr align=\"center\" style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\" border-collapse: collapse; margin:0; padding:0;\"> <tbody><tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:fb.png\" alt=\"Facebook\"/></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:twitter.png\" alt=\"Twitter\"/></a></td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"/></a></td></tr> </tbody></table></td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr></table></body></html>";
    public static final String EMAIL_NOTIFICATION_REDISCOVERY_HTML_TEMPLATE = "<!doctype html><meta charset=utf-8><title>Executed – (Rediscovery Profile)</title><style>.success{color:#89c540}.Failure{color:#f04e3e}</style><body bgcolor=#F3F6F8 leftmargin=0 marginheight=0 marginwidth=0 topmargin=0><table border=0 cellpadding=0 cellspacing=0 style=border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8 width=100% align=center bgcolor=#F3F6F8><tr style=margin:0;padding:0><td style=margin:0;padding:0 height=40><tr style=margin:0;padding:0><td style=margin:0;padding:0 width=650 align=center valign=top><a href=${link} target=_blank><img alt=motadata src=cid:${logo} style=width:85px;height:25px height=25 width=85></a><tr style=margin:0;padding:0><td style=margin:0;padding:0 height=24><tr style=margin:0;padding:0><td style=margin:0;padding:0 align=center valign=top><table border=0 cellpadding=0 cellspacing=0 style=\"padding:0 24px;border-top:4px solid #009ddc;background:#fff\"width=600 bgcolor=#ffffff><tr style=margin:0;padding:0><td style=margin:0;padding:0 height=24><tr style=margin:0;padding:0><td style=margin:0;padding:0;text-align:center><img alt=clear src=cid:information.png style=width:36px;height:36px height=36 width=36><tr style=margin:0;padding:0><td style=margin:0;padding:0 height=10><tr style=margin:0;padding:0><td style=margin:0;padding:0;text-align:center><h1 style=\"font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px\">Executed – (Rediscovery Profile)</h1><p style=\"color:#9ba3ab;font-size:12px;text-align:center;margin:0;padding:3px 0 0 0;font-weight:700\">${timestamp}<tr style=margin:0;padding:0><td style=margin:0;padding:0 height=32><tr style=margin:0;padding:0><td style=margin:0;padding:0><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"><tr style=margin:0;padding:0><td style=margin:0;padding:0 height=16><tr style=margin:0;padding:0><td style=margin:0;padding:0><table border=0 cellpadding=0 cellspacing=0 style=border-collapse:collapse;margin:0;padding:0 width=100%><tr style=margin:0;padding:0 valign=top><td style=\"margin:0;padding:0 5px\"width=33.333%><p style=\"color:#7b8fa5;font-size:10px;line-height:14px;margin:0;padding:0 0 5px\">Discovered Object(s) Count<p style=color:#364658;font-size:10px;font-weight:700;line-height:14px;margin:0;padding:0><span style=color:#89c540>${discovered.objects}</span><td style=\"margin:0;padding:0 5px\"width=33.333%><p style=\"color:#7b8fa5;font-size:10px;line-height:14px;margin:0;padding:0 0 5px\">Type<p style=color:#364658;font-size:10px;font-weight:700;line-height:14px;margin:0;padding:0>${rediscover.job}<tr style=margin:0;padding:0><td style=margin:0;padding:0 height=16 colspan=3><tr style=margin:0;padding:0 valign=top><td style=\"margin:0;padding:0 5px\"width=33.333%><p style=\"color:#7b8fa5;font-size:10px;line-height:14px;margin:0;padding:0 0 5px\">Execution Type<p style=color:#364658;font-size:10px;font-weight:700;line-height:14px;margin:0;padding:0>(Manual/Scheduled)<td style=\"margin:0;padding:0 5px\"width=33.333%><p style=\"color:#7b8fa5;font-size:10px;line-height:14px;margin:0;padding:0 0 5px\">Status<p style=color:#364658;font-size:10px;font-weight:700;line-height:14px;margin:0;padding:0><span class=${discovery.status}>${status}</span><tr style=margin:0;padding:0><td style=margin:0;padding:0 height=16 colspan=3><tr style=margin:0;padding:0><td style=margin:0;padding:0 colspan=3><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"><tr style=margin:0;padding:0><td style=margin:0;padding:0 height=16 colspan=3><tr style=margin:0;padding:0><td style=\"margin:0;padding:0 5px\"colspan=3><p style=\"color:#7b8fa5;font-size:10px;line-height:14px;margin:0;padding:0 0 5px\">Message<p style=color:#364658;font-size:10px;font-weight:700;line-height:14px;margin:0;padding:0>Rediscovery for ${rediscover.job} has been successfully executed.</table><tr style=margin:0;padding:0><td style=margin:0;padding:0 height=16><tr style=margin:0;padding:0><td style=margin:0;padding:0><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"><tr style=margin:0;padding:0><td style=margin:0;padding:0><table border=0 cellpadding=0 cellspacing=0 style=border-collapse:collapse;margin:0;padding:0 width=100%><tr style=margin:0;padding:0><td style=margin:0;padding:0 height=24 colspan=3><tr style=margin:0;padding:0><td style=margin:0;padding:0 colspan=3><tr style=margin:0;padding:0><td style=margin:0;padding:0 height=16 colspan=3><tr style=margin:0;padding:0><td style=margin:0;padding:0 colspan=3><p style=\"color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px\"><strong>Disclaimer:</strong> ${disclaimer}<tr style=margin:0;padding:0><td style=margin:0;padding:0 height=24 colspan=3></table></table><tr style=margin:0;padding:0><td style=margin:0;padding:0 height=24><tr style=margin:0;padding:0;text-align:center align=center ;><td style=margin:0;padding:0><p style=font-size:10px;margin:0;padding:0;color:#7b8fa5>For more details, visit <a href=https://www.motadata.com/ target=_blank style=color:#009ddc;text-decoration:none>www.motadata.com</a><tr style=margin:0;padding:0><td style=margin:0;padding:0 height=16><tr style=margin:0;padding:0 align=center><td style=margin:0;padding:0><table border=0 cellpadding=0 cellspacing=0 style=border-collapse:collapse;margin:0;padding:0 width=102 align=center><tr><td><a href=https://www.facebook.com/motadata target=_blank><img alt=Facebook src=cid:fb.png style=vertical-align:middle></a><td><a href=https://twitter.com/MotadataSystems target=_blank><img alt=Twitter src=cid:twitter.png style=vertical-align:middle></a><td><a href=https://www.linkedin.com/company/motadata/ target=_blank><img alt=Linkedin src=cid:linkedin.png style=vertical-align:middle></a></table><tr style=margin:0;padding:0><td style=margin:0;padding:0 height=32></table>";

    public static final String EMAIL_NOTIFICATION_RUNBOOK_PLUGIN_TEMPLATE = "<!doctype html> <html> <head> <meta charset=\"utf-8\"> <title>Executed – Runbook Scheduler</title> </head> <body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"> <table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\" border-collapse: collapse; font-family: Arial, Helvetica, sans-serif; background:#F3F6F8;\"> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"40\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" width=\"650\" valign=\"top\" align=\"center\"> <a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px; height:25px;\" src=\"cid:${logo}\" alt=\"motadata\" /></a> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"24\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" valign=\"top\" align=\"center\"> <table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\" padding:0 24px; border-top: 4px solid #009DDC; background:#fff;\"> <tbody> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"24\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0; text-align: center;\"><img width=\"36\" height=\"36\" style=\"width:36px; height:36px;\" src=\"cid:information.png\" alt=\"clear\" /></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"10\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0; text-align: center;\"> <h1 style=\"font-size: 16px; color:#364658;  font-family: Arial, Helvetica, sans-serif; margin:0; padding:0 0 5px;\"> Executed – Runbook Scheduler</h1> <p style=\"color:#9BA3AB; font-size:12px; text-align:center; margin:0; padding:3px 0 0 0; font-weight:bold;\"> ${timestamp}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"32\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"16\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr style=\"margin:0; padding:0;\" valign=\"top\"> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\"> Runbook</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\"> ${runbook.plugin.name}</p> </td> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\"> Scheduler Type</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\"> ${scheduler.timeline}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td> </tr> <tr style=\"margin:0; padding:0;\" valign=\"top\"> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\"> Assigned Monitor(s)</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\"> ${runbook.plugin.objects}</p> </td> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\"> Executed At</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\"> ${timestamp}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\"> <hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\"> Message</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\"> The runbook scheduler has been successfully executed.</p> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"16\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td> </tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\"> <p style=\"color:#364658; font-size:10px; line-height:14px; text-align:center; margin:0; padding:0 0 5px;\"> <strong>Disclaimer:</strong> ${disclaimer}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"24\"></td> </tr> <tr align=\"center\" ; style=\"margin:0; padding:0; text-align: center;\"> <td style=\"margin:0; padding:0;\"> <p style=\"font-size:10px; margin:0; padding:0; color:#7B8FA5;\">For more details, visit <a style=\"color:#009DDC; text-decoration: none; text-align:center;\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"16\"></td> </tr> <tr align=\"center\" style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\" border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr style=\"margin:0; padding:0;\" align=\"center\"> <td style=\"margin:0; padding:0;\"><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:fb.png\" alt=\"Facebook\" /></a></td> <td style=\"margin:0; padding:0;\"><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:twitter.png\" alt=\"Twitter\" /></a></td> <td style=\"margin:0; padding:0;\"><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\" /></a></td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" height=\"32\"></td> </tr> </table> </body> </html>";
    public static final String EMAIL_NOTIFICATION_TOPOLOGY_HTML_TEMPLATE = "<!doctype html><html><head><meta charset=\"utf-8\"><title>Executed – (Topology Scanner)</title><style type=\"text/css\">.success{ color:#89C540;}.Failure{color: #F04E3E;}</style></head><body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"><table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\" border-collapse: collapse; font-family: Arial, Helvetica, sans-serif; background:#F3F6F8;\"><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"40\"></td></tr><tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" width=\"650\" valign=\"top\" align=\"center\"> <a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px; height:25px;\" src=\"cid:${logo}\" alt=\"motadata\"/></a> </td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" valign=\"top\" align=\"center\"> <table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\" padding:0 24px; border-top: 4px solid #009DDC; background:#fff;\"> <tbody> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0; text-align: center;\"><img width=\"36\" height=\"36\" style=\"width:36px; height:36px;\" src=\"cid:information.png\" alt=\"clear\"/></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"10\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0; text-align: center;\"><h1 style=\"font-size: 16px; color:#364658; font-family: Arial, Helvetica, sans-serif; margin:0; padding:0 0 5px;\">Executed – (Topology Scanner)</h1><p style=\"color:#9BA3AB; font-size:12px; text-align:center; margin:0; padding:3px 0 0 0; font-weight:bold;\">${timestamp}</p></td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody><tr style=\"margin:0; padding:0;\" valign=\"top\"> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Entry Point(s) </p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${topology.plugin.entry.points}</p> </td> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Execution Type</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">Daily</p> </td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"2\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr><tr style=\"margin:0; padding:0\" valign=\"top\"> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Link Layer</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${topology.plugin.protocol}</p> </td> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Status</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\"><span style=\"color:#89C540\">Success</span></p> </td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"2\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"2\" style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"2\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0 5px;\"><p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Message</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">Topology Scanner has been Successfully executed.</p></td></tr> </tbody></table></td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\"><p style=\"color:#364658; font-size:10px; line-height:14px; text-align:center; margin:0; padding:0 0 5px;\"><strong>Disclaimer:</strong> ${disclaimer}</p></td> </tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr> </tbody></table> </td> </tr> </tbody></table> </td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr align=\"center\"; style=\"margin:0; padding:0; text-align: center;\"><td style=\"margin:0; padding:0;\"><p style=\"font-size:10px; margin:0; padding:0; color:#7B8FA5;\">For more details, visit <a style=\"color:#009DDC; text-decoration: none;\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p></td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr><tr align=\"center\" style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\" border-collapse: collapse; margin:0; padding:0;\"> <tbody><tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:fb.png\" alt=\"Facebook\"/></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:twitter.png\" alt=\"Twitter\"/></a></td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"icid:linkedin.png\" alt=\"Linkedin\"/></a></td></tr> </tbody></table></td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr></table></body></html>";
    public static final String EMAIL_NOTIFICATION_METRIC_POLICY_HTML_TEMPLATE = "<!doctype html><html><head><meta charset=\"utf-8\"><meta name=\"viewport\" content=\"width=device-width;initial-scale=1;maximum-scale=1;user-scalable=no;\"><meta name=\"x-apple-disable-message-reformatting\"><title>Major</title><style type=\"text/css\">@media screen and (max-width:599px){.mobile-block{display:block}.mobile-100p{width:100%;height:auto}.mobile-hide{display:none}.mobile-space{padding-top:8px!important;padding-bottom:8px!important}.space16{height:16px}.side-spacing{padding-left:16px!important;padding-right:16px!important}.message-width{width:65%}}.major{color:#f58518}.critical{color:#f04e3e}.clear{color:#89c540}.down{color:#ed7c70}.warning{color:#f5bc18}.major.border{border-color:#f58518!important}.critical.border{border-color:#f04e3e!important}.clear.border{border-color:#89c540!important}.down.border{border-color:#ed7c70!important}.warning.border{border-color:#f5bc18!important}</style></head><body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"><table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\"border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8\"><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"40\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" class=\"mobile-100p\" width=\"650\" valign=\"top\" align=\"center\"><a href=\"${link}/\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px;height:25px\" src=\"cid:${logo}\" alt=\"motadata\"></a></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" valign=\"top\" align=\"center\"><table class=\"${severity} border mobile-100p side-spacing\" border=\"0\" width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"padding:0 24px;border-top:4px solid;background:#fff\"><tbody><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0;text-align:center\"><img width=\"36\" height=\"36\" style=\"width:36px;height:36px\" src=\"cid:${severity}.png\" alt=\"clear\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"10\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0;text-align:center\"><h1 style=\"font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px\">${object.name} - ${metric}</h1><p style=\"color:#9ba3ab;font-size:14px;text-align:center;margin:0;padding:3px 0 0 0;font-weight:700\">${timestamp}</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"32\" class=\"space16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr style=\"margin:0;padding:0\" valign=\"top\"><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Object Name</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${object.name}</p></td><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">IP / Host</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${object.target}</p></td><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Object Type</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${object.type}</p></td></tr><tr class=\"mobile-hide\" style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\" valign=\"top\"><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Metric</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${metric}</p></td><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Metric Value</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${value}</p></td><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Severity</p><p class=\"${severity}\" style=\"font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${severity}</p></td></tr><tr class=\"mobile-hide\" style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\" valign=\"top\"><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Policy Name</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${policy.name}</p></td><td class=\"mobile-block mobile-100p mobile-space\" width=\"66.666%\" colspan=\"2\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Policy Type</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${policy.type}</p></td></tr><tr class=\"mobile-hide\" style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Message</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${policy.message}</p></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\"><p style=\"color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px\"><strong>Disclaimer:</strong>${disclaimer}</p></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td></tr></tbody></table></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr align=\"center\" ; style=\"margin:0;padding:0;text-align:center\"><td style=\"margin:0;padding:0\"><p style=\"font-size:10px;margin:0;padding:0;color:#7b8fa5\">For more details, <a style=\"color:#009ddc;text-decoration:none\" href=\"${policy.url}\" target=\"_blank\">Click here</a>.</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr align=\"center\" style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr><td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:fb.png\" alt=\"Facebook\"></a></td><td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:twitter.png\" alt=\"Twitter\"></a></td><td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"></a></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"32\"></td></tr></table></body></html>";
    public static final String EMAIL_RENOTIFICATION_METRIC_POLICY_HTML_TEMPLATE = "<!doctype html><html><head> <meta charset=\"utf-8\"> <meta name=\"viewport\" content=\"width=device-width;initial-scale=1;maximum-scale=1;user-scalable=no;\"> <meta name=\"x-apple-disable-message-reformatting\"> <title>Major</title> <style type=\"text/css\"> @media screen and (max-width:599px) { .mobile-block { display: block } .mobile-100p { width: 100%; height: auto } .mobile-hide { display: none } .mobile-space { padding-top: 8px !important; padding-bottom: 8px !important } .space16 { height: 16px } .side-spacing { padding-left: 16px !important; padding-right: 16px !important } .message-width { width: 65% } } .major { color: #f58518 } .critical { color: #f04e3e } .clear { color: #89c540 } .down { color: #ed7c70 } .warning { color: #f5bc18 } .major.border { border-color: #f58518 !important } .critical.border { border-color: #f04e3e !important } .clear.border { border-color: #89c540 !important } .down.border { border-color: #ed7c70 !important } .warning.border { border-color: #f5bc18 !important } </style></head><body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"> <table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\"border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8\"> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"40\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" class=\"mobile-100p\" width=\"650\" valign=\"top\" align=\"center\"><a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px;height:25px\" src=\"cid:${logo}\" alt=\"motadata\"></a></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" valign=\"top\" align=\"center\"> <table class=\"${severity} border mobile-100p side-spacing\" border=\"0\" width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"padding:0 24px;border-top:4px solid;background:#fff\"> <tbody> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0;text-align:center\"><img width=\"36\" height=\"36\" style=\"width:36px;height:36px\" src=\"cid:${severity}.png\" alt=\"clear\"></td> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"10\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0;text-align:center\"> <h1 style=\"font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px\">${object.name} - ${metric}</h1> <p style=\"color:#9ba3ab;font-size:14px;text-align:center;margin:0;padding:3px 0 0 0;font-weight:700\">${timestamp}</p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"32\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Object Name</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${object.name}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">IP / Host</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${object.target}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Object Type</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${object.type}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Metric</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${metric}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Metric Value</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${value}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Severity</p> <p class=\"${severity}\" style=\"font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${severity}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Policy Name</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${policy.name}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.33%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Policy Type</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${policy.type}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Active Since</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${active.since}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\"> <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"> </td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Message</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${policy.message}</p> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\"> <p style=\"color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px\"><strong>Disclaimer:</strong>${disclaimer}</p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\"></td> </tr> <tr align=\"center\" ; style=\"margin:0;padding:0;text-align:center\"> <td style=\"margin:0;padding:0\"> <p style=\"font-size:10px;margin:0;padding:0;color:#7b8fa5\">For more details, <a style=\"color:#009ddc;text-decoration:none\" href=\"${policy.url}\" target=\"_blank\">Click here</a>.</p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr align=\"center\" style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:fb.png\" alt=\"Facebook\"></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:twitter.png\" alt=\"Twitter\"></a></td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"></a></td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"32\"></td> </tr> </table></body></html>";
    public static final String EMAIL_RUNBOOK_NOTIFICATION_HTML_TEMPLATE = "<!doctype html> <html> <head> <meta charset=\"utf-8\"> <title>Executed – Runbook</title> <style type=\"text/css\">@media screen and (max-width:599px){.mobile-block{display:block}.mobile-100p{width:100%;height:auto}.mobile-hide{display:none}.mobile-space{padding-top:8px!important;padding-bottom:8px!important}.space16{height:16px}.side-spacing{padding-left:16px!important;padding-right:16px!important}.message-width{width:65%}}.major{color:#f58518}.critical{color:#f04e3e}.clear{color:#89c540}.down{color:#ed7c70}.warning{color:#f5bc18}.major.border{border-color:#f58518!important}.critical.border{border-color:#f04e3e!important}.clear.border{border-color:#89c540!important}.down.border{border-color:#ed7c70!important}.warning.border{border-color:#f5bc18!important}</style> </head> <body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"> <table width=\"100%\" bgcolor=\"#F3F6F8\"  border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\" border-collapse: collapse; font-family: Arial, Helvetica, sans-serif; background:#F3F6F8;\"> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"40\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" width=\"650\" valign=\"top\" align=\"center\"> <a href=\"https://www.motadata.com/\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px; height:25px;\" src=\"cid:${logo}\" alt=\"motadata\"/></a> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" valign=\"top\" align=\"center\"> <table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\"  bgcolor=\"#ffffff\" style=\" padding:0 24px; border-top: 4px solid #009DDC; background:#fff;\"> <tbody> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0; text-align: center;\"><img width=\"36\" height=\"36\" style=\"width:36px; height:36px;\" src=\"cid:information.png\" alt=\"clear\"/></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"10\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0; text-align: center;\"> <h1 style=\"font-size: 16px; color:#364658;  font-family: Arial, Helvetica, sans-serif; margin:0; padding:0 0 5px;\">Executed – Runbook</h1> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr style=\"margin:0; padding:0;\" valign=\"top\"> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Runbook</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${runbook.plugin.name}</p> </td> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Triggred Policy</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${policy.name}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\" valign=\"top\"> <td width=\"33.333%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Severity</p> <p class=\"${severity}\" style=\"font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${severity}</p> </td> <td width=\"50%\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Executed At</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${timestamp}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0 5px;\"> <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Message</p> <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">Kindly find the output of the executed runbook attached with this email.</p> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td colspan=\"3\" style=\"margin:0; padding:0;\"> <p style=\"color:#364658; font-size:10px; line-height:14px; text-align:center; margin:0; padding:0 0 5px;\"><strong>Disclaimer:</strong> ${disclaimer}</p> </td> </tr> <tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr> <tr align=\"center\"; style=\"margin:0; padding:0; text-align: center;\"><td style=\"margin:0; padding:0;\"><p style=\"font-size:10px; margin:0; padding:0; color:#7B8FA5;\">For more details, visit  <a style=\"color:#009DDC; text-decoration: none; text-align:center;\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr align=\"center\" style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\" border-collapse: collapse; margin:0; padding:0;\"> <tbody> <tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:fb.png\" alt=\"Facebook\"></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:twitter.png\" alt=\"Twitter\"></a></td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"></a></td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr> </table> </body> </html> ";
    public static final String EMAIL_NOTIFICATION_DISK_UTILIZATION_HTML_TEMPLATE = "<!doctype html>\n<html>\n\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width;initial-scale=1;maximum-scale=1;user-scalable=no;\">\n    <meta name=\"x-apple-disable-message-reformatting\">\n    <title>Major</title>\n    <style type=\"text/css\">\n        @media screen and (max-width:599px) {\n            .mobile-block {\n                display: block\n            }\n\n            .mobile-100p {\n                width: 100%;\n                height: auto\n            }\n\n            .mobile-hide {\n                display: none\n            }\n\n            .mobile-space {\n                padding-top: 8px !important;\n                padding-bottom: 8px !important\n            }\n\n            .space16 {\n                height: 16px\n            }\n\n            .side-spacing {\n                padding-left: 16px !important;\n                padding-right: 16px !important\n            }\n\n            .message-width {\n                width: 65%\n            }\n        }\n\n        .major {\n            color: #f58518\n        }\n\n        .critical {\n            color: #f04e3e\n        }\n\n        .clear {\n            color: #89c540\n        }\n\n        .down {\n            color: #ed7c70\n        }\n\n        .warning {\n            color: #f5bc18\n        }\n\n        .major.border {\n            border-color: #f58518 !important\n        }\n\n        .critical.border {\n            border-color: #f04e3e !important\n        }\n\n        .clear.border {\n            border-color: #89c540 !important\n        }\n\n        .down.border {\n            border-color: #ed7c70 !important\n        }\n\n        .warning.border {\n            border-color: #f5bc18 !important\n        }\n    </style>\n</head>\n\n<body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\">\n    <table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\"\n        style=\"border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8\">\n        <tr style=\"margin:0;padding:0\">\n            <td style=\"margin:0;padding:0\" height=\"40\"></td>\n        </tr>\n        <tr style=\"margin:0;padding:0\">\n            <td style=\"margin:0;padding:0\" class=\"mobile-100p\" width=\"650\" valign=\"top\" align=\"center\"><a\n                    href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\"\n                        style=\"width:85px;height:25px\" src=\"cid:${logo}\" alt=\"motadata\"></a></td>\n        </tr>\n        <tr style=\"margin:0;padding:0\">\n            <td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td>\n        </tr>\n        <tr style=\"margin:0;padding:0\">\n            <td style=\"margin:0;padding:0\" valign=\"top\" align=\"center\">\n                <table class=\"${severity} border mobile-100p side-spacing\" border=\"0\" width=\"600\" border=\"0\"\n                    cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\"\n                    style=\"padding:0 24px;border-top:4px solid;background:#fff\">\n                    <tbody>\n                        <tr style=\"margin:0;padding:0\">\n                            <td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td>\n                        </tr>\n                        <tr style=\"margin:0;padding:0\">\n                            <td style=\"margin:0;padding:0;text-align:center\"><img width=\"36\" height=\"36\"\n                                    style=\"width:36px;height:36px\" src=\"cid:${severity}.png\" alt=\"clear\"></td>\n                        </tr>\n                        <tr style=\"margin:0;padding:0\">\n                            <td style=\"margin:0;padding:0\" height=\"10\"></td>\n                        </tr>\n                        <tr style=\"margin:0;padding:0\">\n                            <td style=\"margin:0;padding:0;text-align:center\">\n                                <h1\n                                    style=\"font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px\">\n                                    ${object.name} - ${metric}</h1>\n                                <p\n                                    style=\"color:#9ba3ab;font-size:14px;text-align:center;margin:0;padding:3px 0 0 0;font-weight:700\">\n                                    ${timestamp}</p>\n                            </td>\n                        </tr>\n                        <tr style=\"margin:0;padding:0\">\n                            <td style=\"margin:0;padding:0\" height=\"32\" class=\"space16\"></td>\n                        </tr>\n                        <tr style=\"margin:0;padding:0\">\n                            <td style=\"margin:0;padding:0\">\n                                <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\">\n                            </td>\n                        </tr>\n                        <tr style=\"margin:0;padding:0\">\n                            <td style=\"margin:0;padding:0\" height=\"16\"></td>\n                        </tr>\n                        <tr style=\"margin:0;padding:0\">\n                            <td style=\"margin:0;padding:0\">\n                                <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\"\n                                    style=\"border-collapse:collapse;margin:0;padding:0\">\n                                    <tbody>\n                                        <tr style=\"margin:0;padding:0\" valign=\"top\">\n                                            <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\"\n                                                style=\"margin:0;padding:0 5px\">\n                                                <p\n                                                    style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">\n                                                    Object Name</p>\n                                                <p\n                                                    style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">\n                                                    ${object.name}</p>\n                                            </td>\n                                            <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\"\n                                                style=\"margin:0;padding:0 5px\">\n                                                <p\n                                                    style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">\n                                                    IP / Host</p>\n                                                <p\n                                                    style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">\n                                                    ${object.target}</p>\n                                            </td>\n                                            <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\"\n                                                style=\"margin:0;padding:0 5px\">\n                                                <p\n                                                    style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">\n                                                    Object Type</p>\n                                                <p\n                                                    style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">\n                                                    ${object.type}</p>\n                                            </td>\n                                        </tr>\n                                        <tr class=\"mobile-hide\" style=\"margin:0;padding:0\">\n                                            <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td>\n                                        </tr>\n                                        <tr style=\"margin:0;padding:0\" valign=\"top\">\n                                            <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\"\n                                                style=\"margin:0;padding:0 5px\">\n                                                <p\n                                                    style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">\n                                                    Metric</p>\n                                                <p\n                                                    style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">\n                                                    ${metric}</p>\n                                            </td>\n                                            <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\"\n                                                style=\"margin:0;padding:0 5px\">\n                                                <p\n                                                    style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">\n                                                    Metric Value</p>\n                                                <p\n                                                    style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">\n                                                    ${value}</p>\n                                            </td>\n                                            <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\"\n                                                style=\"margin:0;padding:0 5px\">\n                                                <p\n                                                    style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">\n                                                    Severity</p>\n                                                <p class=\"${severity}\"\n                                                    style=\"font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">\n                                                    ${severity}</p>\n                                            </td>\n                                        </tr>\n                                        <tr class=\"mobile-hide\" style=\"margin:0;padding:0\">\n                                            <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td>\n                                        </tr>\n                                        <tr class=\"mobile-hide\" style=\"margin:0;padding:0\">\n                                            <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td>\n                                        </tr>\n                                        <tr style=\"margin:0;padding:0\">\n                                            <td colspan=\"3\" style=\"margin:0;padding:0\">\n                                                <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\">\n                                            </td>\n                                        </tr>\n                                        <tr style=\"margin:0;padding:0\">\n                                            <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td>\n                                        </tr>\n                                        <tr style=\"margin:0;padding:0\">\n                                            <td colspan=\"3\" style=\"margin:0;padding:0 5px\">\n                                                <p\n                                                    style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">\n                                                    Message</p>\n                                                <p\n                                                    style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">\n                                                    ${policy.message}</p>\n                                            </td>\n                                        </tr>\n                                    </tbody>\n                                </table>\n                            </td>\n                        </tr>\n                        <tr style=\"margin:0;padding:0\">\n                            <td style=\"margin:0;padding:0\">\n                                <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\"\n                                    style=\"border-collapse:collapse;margin:0;padding:0\">\n                                    <tbody>\n                                        <tr style=\"margin:0;padding:0\">\n                                            <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td>\n                                        </tr>\n                                        <tr style=\"margin:0;padding:0\">\n                                            <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td>\n                                        </tr>\n                                        <tr style=\"margin:0;padding:0\">\n                                            <td colspan=\"3\" style=\"margin:0;padding:0\">\n                                                <p\n                                                    style=\"color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px\">\n                                                    <strong>Disclaimer:</strong>This is an automated message generated\n                                                    by Motadata.Please do not reply to this email.<br>For any queries,\n                                                    reach out to your System Admin</p>\n                                            </td>\n                                        </tr>\n                                        <tr style=\"margin:0;padding:0\">\n                                            <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td>\n                                        </tr>\n                                    </tbody>\n                                </table>\n                            </td>\n                        </tr>\n                    </tbody>\n                </table>\n            </td>\n        </tr>\n        <tr style=\"margin:0;padding:0\">\n            <td style=\"margin:0;padding:0\" height=\"24\"></td>\n        </tr>\n        <tr align=\"center\" ; style=\"margin:0;padding:0;text-align:center\">\n            <td style=\"margin:0;padding:0\">\n                <p style=\"font-size:10px;margin:0;padding:0;color:#7b8fa5\">For more details, visit<a\n                        style=\"color:#009ddc;text-decoration:none\" href=\"https://www.motadata.com/\"\n                        target=\"_blank\">www.motadata.com</a></p>\n            </td>\n        </tr>\n        <tr style=\"margin:0;padding:0\">\n            <td style=\"margin:0;padding:0\" height=\"16\"></td>\n        </tr>\n        <tr align=\"center\" style=\"margin:0;padding:0\">\n            <td style=\"margin:0;padding:0\">\n                <table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\"\n                    style=\"border-collapse:collapse;margin:0;padding:0\">\n                    <tbody>\n                        <tr>\n                            <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img\n                                        style=\"vertical-align:middle\" src=\"cid:fb.png\" alt=\"Facebook\"></a></td>\n                            <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img\n                                        style=\"vertical-align:middle\" src=\"cid:twitter.png\" alt=\"Twitter\"></a></td>\n                            <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img\n                                        style=\"vertical-align:middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"></a></td>\n                        </tr>\n                    </tbody>\n                </table>\n            </td>\n        </tr>\n        <tr style=\"margin:0;padding:0\">\n            <td style=\"margin:0;padding:0\" height=\"32\"></td>\n        </tr>\n    </table>\n</body>\n\n</html>";
    public static final String EMAIL_NOTIFICATION_EVENT_POLICY_HTML_TEMPLATE = "<!doctype html><html><head><meta charset=\"utf-8\"><meta name=\"viewport\" content=\"width=device-width;initial-scale=1;maximum-scale=1;user-scalable=no;\"><meta name=\"x-apple-disable-message-reformatting\"><style type=\"text/css\">@media screen and (max-width:599px){.mobile-block{display:block}.mobile-100p{width:100%;height:auto}.mobile-hide{display:none}.mobile-space{padding-top:8px!important;padding-bottom:8px!important}.space16{height:16px}.side-spacing{padding-left:16px!important;padding-right:16px!important}.message-width{width:65%}}.major{color:#f58518}.critical{color:#f04e3e}.clear{color:#89c540}.down{color:#ed7c70}.warning{color:#f5bc18}.major.border{border-color:#f58518!important}.critical.border{border-color:#f04e3e!important}.clear.border{border-color:#89c540!important}.down.border{border-color:#ed7c70!important}.warning.border{border-color:#f5bc18!important}</style><title>Critical</title></head><body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"><table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\"border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8\"><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"40\" class=\"space16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" class=\"mobile-100p\" width=\"650\" valign=\"top\" align=\"center\"><a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px;height:25px\" src=\"cid:${logo}\" alt=\"motadata\"></a></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" valign=\"top\" align=\"center\"><table class=\"mobile-100p side-spacing ${severity} border\" width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"padding:0 24px;border-top:4px solid;background:#fff\"><tbody><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0;text-align:center\"><img width=\"36\" height=\"36\" style=\"width:36px;height:36px\" src=\"cid:${severity}.png\" alt=\"clear\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"10\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0;text-align:center\"><h1 style=\"font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px\">${policy.type} Alert</h1><p style=\"color:#9ba3ab;font-size:14px;text-align:center;margin:0;padding:3px 0 0 0;font-weight:700\">${timestamp}</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"32\" class=\"space16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr style=\"margin:0;padding:0\" valign=\"top\"><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Policy Name</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${policy.name}</p></td><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Policy Type</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${policy.type}</p></td><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.3333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Evaluation Window</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${evaluation.window}</p></td></tr><tr class=\"mobile-hide\" style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\" valign=\"top\"><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Alert Type</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${trigger.mode}</p></td><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Severity</p><p class=\"${severity}\" style=\"font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${severity}</p></td></tr><tr class=\"mobile-hide\" style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\" valign=\"top\"><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Dimension</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${event.field}</p></td><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Trigger Value</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${value}</p></td></tr><tr class=\"mobile-hide\" style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Trigger Condition</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${policy.message}</p></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\"><p style=\"color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px\"><strong>Disclaimer:</strong>${disclaimer}</p></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td></tr></tbody></table></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr align=\"center\" ; style=\"margin:0;padding:0;text-align:center\"><td style=\"margin:0;padding:0\"><p style=\"font-size:10px;margin:0;padding:0;color:#7b8fa5\">For more details, <a style=\"color:#009ddc;text-decoration:none\" href=\"${policy.url}\" target=\"_blank\">Click here</a>.</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr align=\"center\" style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr><td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:fb.png\" alt=\"Facebook\"></a></td><td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:twitter.png\" alt=\"Twitter\"></a></td><td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"></a></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"32\"></td></tr></table></body></html>";
    public static final String EMAIL_NOTIFICATION_TRAP_POLICY_HTML_TEMPLATE = "<!doctype html><html><head><meta charset=\"utf-8\"><meta name=\"viewport\" content=\"width=device-width;initial-scale=1;maximum-scale=1;user-scalable=no;\"><meta name=\"x-apple-disable-message-reformatting\"><style type=\"text/css\">@media screen and (max-width:599px) {.mobile-block {display: block}.mobile-100p {width: 100%;height: auto}.mobile-hide {display: none}.mobile-space {padding-top: 8px !important;padding-bottom: 8px !important}.space16 {height: 16px}.side-spacing {padding-left: 16px !important;padding-right: 16px !important}.message-width {width: 65%}}.major {color: #f58518}.critical {color: #f04e3e}.clear {color: #89c540}.down {color: #ed7c70}.warning {color: #f5bc18}.major.border {border-color: #f58518 !important}.critical.border {border-color: #f04e3e !important}.clear.border {border-color: #89c540 !important}.down.border {border-color: #ed7c70 !important}.warning.border {border-color: #f5bc18 !important}</style><title>Critical</title></head><body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"><table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\"border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8\"><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"40\" class=\"space16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" class=\"mobile-100p\" width=\"650\" valign=\"top\" align=\"center\"><a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px;height:25px\" src=\"cid:${logo}\" alt=\"motadata\"></a></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" valign=\"top\" align=\"center\"><table class=\"mobile-100p side-spacing ${severity} border\" width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"padding:0 24px;border-top:4px solid;background:#fff\"><tbody><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0;text-align:center\"><img width=\"36\" height=\"36\" style=\"width:36px;height:36px\" src=\"cid:${severity}.png\" alt=\"clear\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"10\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0;text-align:center\"><h1 style=\"font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px\">${policy.type} Alert</h1><p style=\"color:#9ba3ab;font-size:14px;text-align:center;margin:0;padding:3px 0 0 0;font-weight:700\">${timestamp}</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"32\" class=\"space16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr style=\"margin:0;padding:0\" valign=\"top\"><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Policy Name</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${policy.name}</p></td><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Policy Type</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${policy.type}</p></td></tr><tr class=\"mobile-hide\" style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\" valign=\"top\"><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Alert Type</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${trigger.mode}</p></td><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Severity</p><p class=\"${severity}\" style=\"font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${severity}</p></td></tr><tr class=\"mobile-hide\" style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\" valign=\"top\"><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Dimension</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${event.field}</p></td><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Trigger Value</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${triggered.value}</p></td></tr><tr class=\"mobile-hide\" style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Trigger Condition</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${policy.message}</p></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\"><p style=\"color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px\"><strong>Disclaimer:</strong>${disclaimer}</p></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td></tr></tbody></table></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr align=\"center\" ; style=\"margin:0;padding:0;text-align:center\"><td style=\"margin:0;padding:0\"><p style=\"font-size:10px;margin:0;padding:0;color:#7b8fa5\">For more details, <a style=\"color:#009ddc;text-decoration:none\" href=\"${policy.url}\" target=\"_blank\">Click here</a>.</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr align=\"center\" style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr><td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:fb.png\" alt=\"Facebook\"></a></td><td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:twitter.png\" alt=\"Twitter\"></a></td><td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"></a></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"32\"></td></tr></table></body></html>";
    public static final String EMAIL_NOTIFICATION_POLLING_ERROR_HTML_TEMPLATE = "<!doctype html><html><head><meta charset=\"utf-8\"><title>Executed – (Monitor Polling Failed Notification)</title></head><body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"><table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\"border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8\"><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"40\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" width=\"650\" valign=\"top\" align=\"center\"><a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px;height:25px\" src=\"cid:${logo}\" alt=\"motadata\"></a></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" valign=\"top\" align=\"center\"><table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"padding:0 24px;border-top:4px solid #f04e3e;background:#fff\"><tbody><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0;text-align:center\"><img width=\"36\" height=\"36\" style=\"width:36px;height:36px\" src=\"cid:${severity}.png\" alt=\"clear\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"10\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0;text-align:center\"><h1 style=\"font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px\">${object.name} – (Polling Failed)</h1><p style=\"color:#9ba3ab;font-size:12px;text-align:center;margin:0;padding:3px 0 0 0;font-weight:700\">${timestamp}</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"32\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr style=\"margin:0;padding:0\" valign=\"top\"><td width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:10px;line-height:14px;margin:0;padding:0 0 5px\">Object Name</p><p style=\"color:#364658;font-size:10px;font-weight:700;line-height:14px;margin:0;padding:0\">${object.name}</p></td><td width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:10px;line-height:14px;margin:0;padding:0 0 5px\">IP / Host</p><p style=\"color:#364658;font-size:10px;font-weight:700;line-height:14px;margin:0;padding:0\">${object.target}</p></td><td width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:10px;line-height:14px;margin:0;padding:0 0 5px\">Group</p><p style=\"color:#364658;font-size:10px;font-weight:700;line-height:14px;margin:0;padding:0\">${object.groups}</p></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:10px;line-height:14px;margin:0;padding:0 0 5px\">Message</p><p style=\"color:#364658;font-size:10px;font-weight:700;line-height:14px;margin:0;padding:0\">${message}</p></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td></tr></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\"><p style=\"color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px\"><strong>Disclaimer:</strong>${disclaimer}</p></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td></tr></tbody></table></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr align=\"center\" ; style=\"margin:0;padding:0;text-align:center\"><td style=\"margin:0;padding:0\"><p style=\"font-size:10px;margin:0;padding:0;color:#7b8fa5\">For more details, visit<a style=\"color:#009ddc;text-decoration:none\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr align=\"center\" style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr><td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:fb.png\" alt=\"Facebook\"></a></td><td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:twitter.png\" alt=\"Twitter\"></a></td><td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"></a></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"32\"></td></tr></table></body></html>";
    public static final String EMAIL_NOTIFICATION_NETROUTE_POLICY_SOURCE_TO_DESTINATION_HTML_TEMPLATE = "<!doctype html> <html> <head> <meta charset=\"utf-8\"> <meta name=\"viewport\" content=\"width=device-width;initial-scale=1;maximum-scale=1;user-scalable=no;\"> <meta name=\"x-apple-disable-message-reformatting\"> <title>Major</title> <style type=\"text/css\"> @media screen and (max-width:599px) { .mobile-block { display: block } .mobile-100p { width: 100%; height: auto } .mobile-hide { display: none } .mobile-space { padding-top: 8px !important; padding-bottom: 8px !important } .space16 { height: 16px } .side-spacing { padding-left: 16px !important; padding-right: 16px !important } .message-width { width: 65% } } .major { color: #f58518 } .critical { color: #f04e3e } .clear { color: #89c540 } .down { color: #ed7c70 } .warning { color: #f5bc18 } .major.border { border-color: #f58518 !important } .critical.border { border-color: #f04e3e !important } .clear.border { border-color: #89c540 !important } .down.border { border-color: #ed7c70 !important } .warning.border { border-color: #f5bc18 !important } </style> </head> <body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"> <table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\"border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8\"> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"40\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" class=\"mobile-100p\" width=\"650\" valign=\"top\" align=\"center\"><a href=\"${link}/\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px;height:25px\" src=\"cid:${logo}\" alt=\"motadata\"></a></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" valign=\"top\" align=\"center\"> <table class=\"${severity} border mobile-100p side-spacing\" border=\"0\" width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"padding:0 24px;border-top:4px solid;background:#fff\"> <tbody> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0;text-align:center\"><img width=\"36\" height=\"36\" style=\"width:36px;height:36px\" src=\"cid:${severity}.png\" alt=\"clear\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"10\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0;text-align:center\"> <h1 style=\"font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px\"> ${netroute.name} - ${metric}</h1> <p style=\"color:#9ba3ab;font-size:14px;text-align:center;margin:0;padding:3px 0 0 0;font-weight:700\"> ${timestamp}</p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"32\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Route Name</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${netroute.name}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Source</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${netroute.source}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Destination</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${netroute.destination}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Metric</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${metric}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Metric Value</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${value}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Severity</p> <p class=\"${severity}\" style=\"font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${severity}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Policy Name</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${policy.name}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"66.666%\" colspan=\"2\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Route Evaluation Type</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${policy.evaluation.type}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\"> <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"> </td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Message</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${policy.message}</p> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\"> <p style=\"color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px\"> <strong>Disclaimer:</strong>${disclaimer} </p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\"></td> </tr> <tr align=\"center\" ; style=\"margin:0;padding:0;text-align:center\"> <td style=\"margin:0;padding:0\"> <p style=\"font-size:10px;margin:0;padding:0;color:#7b8fa5\">For more details, visit<a style=\"color:#009ddc;text-decoration:none\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr align=\"center\" style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:fb.png\" alt=\"Facebook\"></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:twitter.png\" alt=\"Twitter\"></a></td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"></a></td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"32\"></td> </tr> </table> </body> </html>";
    public static final String EMAIL_NOTIFICATION_NETROUTE_POLICY_HOP_BY_HOP_HTML_TEMPLATE = "<!doctype html> <html> <head> <meta charset=\"utf-8\"> <meta name=\"viewport\" content=\"width=device-width;initial-scale=1;maximum-scale=1;user-scalable=no;\"> <meta name=\"x-apple-disable-message-reformatting\"> <title>Major</title> <style type=\"text/css\"> @media screen and (max-width:599px) { .mobile-block { display: block } .mobile-100p { width: 100%; height: auto } .mobile-hide { display: none } .mobile-space { padding-top: 8px !important; padding-bottom: 8px !important } .space16 { height: 16px } .side-spacing { padding-left: 16px !important; padding-right: 16px !important } .message-width { width: 65% } } .major { color: #f58518 } .critical { color: #f04e3e } .clear { color: #89c540 } .down { color: #ed7c70 } .warning { color: #f5bc18 } .major.border { border-color: #f58518 !important } .critical.border { border-color: #f04e3e !important } .clear.border { border-color: #89c540 !important } .down.border { border-color: #ed7c70 !important } .warning.border { border-color: #f5bc18 !important } </style> </head> <body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"> <table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\"border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8\"> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"40\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" class=\"mobile-100p\" width=\"650\" valign=\"top\" align=\"center\"><a href=\"${link}/\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px;height:25px\" src=\"cid:${logo}\" alt=\"motadata\"></a></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" valign=\"top\" align=\"center\"> <table class=\"${severity} border mobile-100p side-spacing\" border=\"0\" width=\"600\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"padding:0 24px;border-top:4px solid;background:#fff\"> <tbody> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0;text-align:center\"><img width=\"36\" height=\"36\" style=\"width:36px;height:36px\" src=\"cid:${severity}.png\" alt=\"clear\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"10\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0;text-align:center\"> <h1 style=\"font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px\"> ${netroute.name} - ${metric}</h1> <p style=\"color:#9ba3ab;font-size:14px;text-align:center;margin:0;padding:3px 0 0 0;font-weight:700\"> ${timestamp}</p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"32\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Route Name</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${netroute.name}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Source</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${netroute.source}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Destination</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${netroute.destination}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Metric</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${metric}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Metric Value</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ---</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Severity</p> <p class=\"${severity}\" style=\"font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${severity}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Policy Name</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${policy.name}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"66.666%\" colspan=\"2\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Route Evaluation Type</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${policy.evaluation.type}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\"> <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"> </td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Message</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${policy.message}</p> </td> </tr> <tr> <td> &nbsp; </td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"margin: 0; padding: 0\"> <thead> <tr style=\"margin: 0; padding: 0\"> <td style=\"padding: 5px; font-size:12px; font-weight:bold; color:#364658; background:#f9f9f9;\" width=\"50%\">Route</td> <td style=\"padding: 5px; font-size:12px; font-weight:bold; color:#364658; background:#f9f9f9;\" width=\"50%\">Value</td> </tr> </thead> <tbody> ##ROWS## </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\"> <p style=\"color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px\"> <strong>Disclaimer:</strong>${disclaimer} </p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\"></td> </tr> <tr align=\"center\" ; style=\"margin:0;padding:0;text-align:center\"> <td style=\"margin:0;padding:0\"> <p style=\"font-size:10px;margin:0;padding:0;color:#7b8fa5\">For more details, visit<a style=\"color:#009ddc;text-decoration:none\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr align=\"center\" style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table class=\"${severity} border mobile-100p side-spacing\" width=\"102\" align=\"center\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:fb.png\" alt=\"Facebook\"></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:twitter.png\" alt=\"Twitter\"></a></td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"></a></td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"32\"></td> </tr> </table> </body> </html>";
    public static final String EMAIL_NOTIFICATION_NETROUTE_HOP_RESULT = "<tr> <td style=\"padding:10px 5px; font-size:12px; font-weight:bold; border-bottom:1px solid #eef2f6;\">${netroute.source} ==> ${netroute.destination.ip}</td> <td style=\"padding:10px 5px; font-size:12px; font-weight:bold; border-bottom:1px solid #eef2f6;\">${value}</td> </tr>";
    public static final String EMAIL_RENOTIFICATION_NETROUTE_POLICY_HTML_TEMPLATE = "<!doctype html> <html> <head> <meta charset=\"utf-8\"> <meta name=\"viewport\" content=\"width=device-width;initial-scale=1;maximum-scale=1;user-scalable=no;\"> <meta name=\"x-apple-disable-message-reformatting\"> <title>Major</title> <style type=\"text/css\"> @media screen and (max-width:599px) { .mobile-block { display: block } .mobile-100p { width: 100%; height: auto } .mobile-hide { display: none } .mobile-space { padding-top: 8px !important; padding-bottom: 8px !important } .space16 { height: 16px } .side-spacing { padding-left: 16px !important; padding-right: 16px !important } .message-width { width: 65% } } .major { color: #f58518 } .critical { color: #f04e3e } .clear { color: #89c540 } .down { color: #ed7c70 } .warning { color: #f5bc18 } .major.border { border-color: #f58518 !important } .critical.border { border-color: #f04e3e !important } .clear.border { border-color: #89c540 !important } .down.border { border-color: #ed7c70 !important } .warning.border { border-color: #f5bc18 !important } </style> </head> <body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"> <table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\"border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8\"> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"40\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" class=\"mobile-100p\" width=\"650\" valign=\"top\" align=\"center\"><a href=\"${link}/\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px;height:25px\" src=\"cid:${logo}\" alt=\"motadata\"></a></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" valign=\"top\" align=\"center\"> <table class=\"${severity} border mobile-100p side-spacing\" border=\"0\" width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"padding:0 24px;border-top:4px solid;background:#fff\"> <tbody> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0;text-align:center\"><img width=\"36\" height=\"36\" style=\"width:36px;height:36px\" src=\"cid:${severity}.png\" alt=\"clear\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"10\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0;text-align:center\"> <h1 style=\"font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px\"> ${netroute.name} - ${metric}</h1> <p style=\"color:#9ba3ab;font-size:14px;text-align:center;margin:0;padding:3px 0 0 0;font-weight:700\"> ${timestamp}</p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"32\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Route Name</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${netroute.name}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Source</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${netroute.source}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Destination</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${netroute.destination}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Metric</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${metric}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Metric Value</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${value}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Severity</p> <p class=\"${severity}\" style=\"font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${severity}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Policy Name</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${policy.name}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"40.444%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Route Evaluation Type</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${policy.evaluation.type}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Active Since</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${active.since}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\"> <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"> </td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Message</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${policy.message}</p> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\"> <p style=\"color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px\"> <strong>Disclaimer:</strong>${disclaimer} </p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\"></td> </tr> <tr align=\"center\" ; style=\"margin:0;padding:0;text-align:center\"> <td style=\"margin:0;padding:0\"> <p style=\"font-size:10px;margin:0;padding:0;color:#7b8fa5\">For more details, visit<a style=\"color:#009ddc;text-decoration:none\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr align=\"center\" style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:fb.png\" alt=\"Facebook\"></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:twitter.png\" alt=\"Twitter\"></a></td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"></a></td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"32\"></td> </tr> </table> </body> </html>";
    public static final String EMAIL_SHARE_METRIC_POLICY_HTML_TEMPLATE = "<!doctype html> <html> <head> <meta charset=\"utf-8\"> <meta name=\"viewport\" content=\"width=device-width;initial-scale=1;maximum-scale=1;user-scalable=no;\"> <meta name=\"x-apple-disable-message-reformatting\"> <title>Major</title> <style type=\"text/css\"> @media screen and (max-width:599px) { .mobile-block { display: block } .mobile-100p { width: 100%; height: auto } .mobile-hide { display: none } .mobile-space { padding-top: 8px !important; padding-bottom: 8px !important } .space16 { height: 16px } .side-spacing { padding-left: 16px !important; padding-right: 16px !important } .message-width { width: 65% } } .major { color: #f58518 } .unreachable { color: #8d3abc; } .critical { color: #f04e3e } .clear { color: #89c540 } .down { color: #ed7c70 } .warning { color: #f5bc18 } .major.border { border-color: #f58518 !important } .critical.border { border-color: #f04e3e !important } .clear.border { border-color: #89c540 !important } .down.border { border-color: #ed7c70 !important } .warning.border { border-color: #f5bc18 !important } </style> </head> <body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"> <table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\"border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8\"> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"40\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" class=\"mobile-100p\" width=\"650\" valign=\"top\" align=\"center\"><a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px;height:25px\" src=\"cid:${logo}\" alt=\"motadata\"></a></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" valign=\"top\" align=\"center\"> <table class=\"${severity} border mobile-100p side-spacing\" border=\"0\" width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"padding:0 24px;border-top:4px solid;background:#fff\"> <tbody> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0;text-align:center\"><img width=\"36\" height=\"36\" style=\"width:36px;height:36px\" src=\"cid:${severity}.png\" alt=\"clear\"></td> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"10\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0;text-align:center\"> <h1 style=\"font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px\"> ${object.name} - ${metric}</h1> <p style=\"color:#9ba3ab;font-size:14px;text-align:center;margin:0;padding:3px 0 0 0;font-weight:700\"> ${Timestamp}</p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"32\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Object Name</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${object.name}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> IP / Host</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${object.target}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Object Type</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${object.type}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Metric</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${metric}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Metric Value</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${value}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Severity</p> <p class=\"${severity}\" style=\"font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${severity}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Policy Name</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${policy.name}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.33%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Policy Type</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${policy.type}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Active Since</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${active.since}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\"> <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"> </td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Alert Message</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${policy.message}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\"> <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"> </td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Message</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${message}</p> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\"> <p style=\"color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px\"> <strong>Disclaimer:</strong>${disclaimer}</p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\"></td> </tr> <tr align=\"center\" ; style=\"margin:0;padding:0;text-align:center\"> <td style=\"margin:0;padding:0\"> <p style=\"font-size:10px;margin:0;padding:0;color:#7b8fa5\">For more details, <a style=\"color:#009ddc;text-decoration:none\" href=\"{policy.url}\" target=\"_blank\">Click here</a>.</p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr align=\"center\" style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:fb.png\" alt=\"Facebook\"></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:twitter.png\" alt=\"Twitter\"></a></td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"></a></td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"32\"></td> </tr> </table> </body> </html>";
    public static final String EMAIL_SHARE_EVENT_POLICY_HTML_TEMPLATE = "<!doctype html> <html> <head> <meta charset=\"utf-8\"> <meta name=\"viewport\" content=\"width=device-width;initial-scale=1;maximum-scale=1;user-scalable=no;\"> <meta name=\"x-apple-disable-message-reformatting\"> <style type=\"text/css\"> @media screen and (max-width:599px) { .mobile-block { display: block } .mobile-100p { width: 100%; height: auto } .mobile-hide { display: none } .mobile-space { padding-top: 8px !important; padding-bottom: 8px !important } .space16 { height: 16px } .side-spacing { padding-left: 16px !important; padding-right: 16px !important } .message-width { width: 65% } } .major { color: #f58518 } .critical { color: #f04e3e } .clear { color: #89c540 } .down { color: #ed7c70 } .warning { color: #f5bc18 } .major.border { border-color: #f58518 !important } .critical.border { border-color: #f04e3e !important } .clear.border { border-color: #89c540 !important } .down.border { border-color: #ed7c70 !important } .warning.border { border-color: #f5bc18 !important } </style> <title>Critical</title> </head> <body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"> <table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\"border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8\"> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"40\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" class=\"mobile-100p\" width=\"650\" valign=\"top\" align=\"center\"><a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px;height:25px\" src=\"cid:${logo}\" alt=\"motadata\"></a></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" valign=\"top\" align=\"center\"> <table class=\"mobile-100p side-spacing ${severity} border\" width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"padding:0 24px;border-top:4px solid;background:#fff\"> <tbody> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0;text-align:center\"><img width=\"36\" height=\"36\" style=\"width:36px;height:36px\" src=\"cid:${severity}.png\" alt=\"clear\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"10\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0;text-align:center\"> <h1 style=\"font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px\"> ${policy.type} Alert</h1> <p style=\"color:#9ba3ab;font-size:14px;text-align:center;margin:0;padding:3px 0 0 0;font-weight:700\"> ${Timestamp}</p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"32\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Policy Name</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${policy.name}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Policy Type</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${policy.type}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.3333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Evaluation Window</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${evaluation.window}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Alert Type</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${trigger.mode}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Severity</p> <p class=\"${severity}\" style=\"font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${severity}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Last Seen</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${active.since}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Dimension</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${event.field}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Trigger Value</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${value}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\"> <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"> </td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Trigger Condition</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${triggerCondition}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"></tr> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\"> <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"> </td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Message</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${message}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"></tr> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\"> <p style=\"color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px\"> <strong>Disclaimer:</strong>${disclaimer} </p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\"></td> </tr> <tr align=\"center\" ; style=\"margin:0;padding:0;text-align:center\"> <td style=\"margin:0;padding:0\"> <p style=\"font-size:10px;margin:0;padding:0;color:#7b8fa5\">For more details, visit<a style=\"color:#009ddc;text-decoration:none\" href=\"${policy.url}\" target=\"_blank\">Click here</a>.</p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr align=\"center\" style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:fb.png\" alt=\"Facebook\"></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:twitter.png\" alt=\"Twitter\"></a></td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"></a></td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"32\"></td> </tr> </table> </body> </html>";
    public static final String EMAIL_SHARE_TRAP_POLICY_HTML_TEMPLATE = "<!doctype html> <html> <head> <meta charset=\"utf-8\"> <meta name=\"viewport\" content=\"width=device-width;initial-scale=1;maximum-scale=1;user-scalable=no;\"> <meta name=\"x-apple-disable-message-reformatting\"> <style type=\"text/css\"> @media screen and (max-width:599px) { .mobile-block { display: block } .mobile-100p { width: 100%; height: auto } .mobile-hide { display: none } .mobile-space { padding-top: 8px !important; padding-bottom: 8px !important } .space16 { height: 16px } .side-spacing { padding-left: 16px !important; padding-right: 16px !important } .message-width { width: 65% } } .major { color: #f58518 } .critical { color: #f04e3e } .clear { color: #89c540 } .down { color: #ed7c70 } .warning { color: #f5bc18 } .major.border { border-color: #f58518 !important } .critical.border { border-color: #f04e3e !important } .clear.border { border-color: #89c540 !important } .down.border { border-color: #ed7c70 !important } .warning.border { border-color: #f5bc18 !important } </style> <title>Critical</title> </head> <body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"> <table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\"border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8\"> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"40\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" class=\"mobile-100p\" width=\"650\" valign=\"top\" align=\"center\"><a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px;height:25px\" src=\"cid:${logo}\" alt=\"motadata\"></a></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" valign=\"top\" align=\"center\"> <table class=\"mobile-100p side-spacing ${severity} border\" width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"padding:0 24px;border-top:4px solid;background:#fff\"> <tbody> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0;text-align:center\"><img width=\"36\" height=\"36\" style=\"width:36px;height:36px\" src=\"cid:${severity}.png\" alt=\"clear\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"10\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0;text-align:center\"> <h1 style=\"font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px\"> ${policy.type} Alert</h1> <p style=\"color:#9ba3ab;font-size:14px;text-align:center;margin:0;padding:3px 0 0 0;font-weight:700\"> ${Timestamp}</p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"32\" class=\"space16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Policy Name</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${policy.name}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Policy Type</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${policy.type}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Alert Type</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${trigger.mode}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Severity</p> <p class=\"${severity}\" style=\"font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${severity}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Dimension</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${event.field}</p> </td> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Trigger Value</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${value}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\" valign=\"top\"> <td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Last Seen</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${active.since}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\"> <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"> </td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Trigger Condition</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${triggerCondition}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\"> <hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"> </td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0 5px\"> <p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\"> Message</p> <p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\"> ${message}</p> </td> </tr> <tr class=\"mobile-hide\" style=\"margin:0;padding:0\"></tr> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\"> <p style=\"color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px\"> <strong>Disclaimer:</strong>${disclaimer} </p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"24\"></td> </tr> <tr align=\"center\" ; style=\"margin:0;padding:0;text-align:center\"> <td style=\"margin:0;padding:0\"> <p style=\"font-size:10px;margin:0;padding:0;color:#7b8fa5\">For more details, <a style=\"color:#009ddc;text-decoration:none\" href=\"${policy.url}\" target=\"_blank\">Click here</a>.</p> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"16\"></td> </tr> <tr align=\"center\" style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\"> <table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"> <tbody> <tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:fb.png\" alt=\"Facebook\"></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:twitter.png\" alt=\"Twitter\"></a></td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"></a></td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin:0;padding:0\"> <td style=\"margin:0;padding:0\" height=\"32\"></td> </tr> </table> </body> </html>";
    public static final JsonArray EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS = new JsonArray().add("fb.png").add("logo.png").add("linkedin.png").add("twitter.png");
    public static final String EMAIL_NOTIFICATION_REPORT_HTML_TEMPLATE = "<!doctype html><html><head><meta charset=\"utf-8\"><title>Info Rediscovery</title></head><body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"><table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\"border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8\"><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"40\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" width=\"650\" valign=\"top\" align=\"center\"><a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px;height:25px\" src=\"cid:${logo}\" alt=\"motadata\"></a></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" valign=\"top\" align=\"center\"><table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"padding:0 24px;border-top:4px solid #009ddc;background:#fff\"><tbody><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0;text-align:center\"><img width=\"36\" height=\"36\" style=\"width:36px;height:36px\" src=\"cid:information.png\" alt=\"clear\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"10\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0;text-align:center\"><h1 style=\"font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px\">Executed – ${report.name}</h1><p style=\"color:#9ba3ab;font-size:12px;text-align:center;margin:0;padding:3px 0 0 0;font-weight:700\">${timestamp}</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"32\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0 5px\"><p style=\"color:#364658;font-size:10px;font-weight:700;line-height:14px;margin:0;padding:0 0 10px 0\">${message}</p></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\"><p style=\"color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px\"><strong>Disclaimer:</strong>${disclaimer}</p></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td></tr></tbody></table></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr align=\"center\" ; style=\"margin:0;padding:0;text-align:center\"><td style=\"margin:0;padding:0\"><p style=\"font-size:10px;margin:0;padding:0;color:#7b8fa5\">For more details, visit<a style=\"color:#009ddc;text-decoration:none\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr align=\"center\" style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr><td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:fb.png\" alt=\"Facebook\"></a></td><td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:twitter.png\" alt=\"Twitter\"></a></td><td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"></a></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"32\"></td></tr></table></body></html>";
    public static final String EMAIL_NOTIFICATION_CONFIG_HTML_TEMPLATE = "\t<!doctype html><html><head><meta charset=\"utf-8\"><title>Executed – Network Config ${config.operation}</title></head>\t<body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\">\t\t<table width=\"100%\" bgcolor=\"#F3F6F8\"  border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\" border-collapse: collapse; font-family: Arial, Helvetica, sans-serif; background:#F3F6F8;\">\t\t\t<tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"40\"></td></tr>\t\t\t<tr style=\"margin:0; padding:0;\">\t\t\t  <td style=\"margin:0; padding:0;\" width=\"650\" valign=\"top\" align=\"center\">\t\t\t\t <a href=\"https://www.motadata.com/\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px; height:25px;\" src=\"cid:${logo}\" alt=\"motadata\"/></a>\t\t\t  </td>\t\t\t</tr>\t\t\t<tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr>\t\t\t<tr style=\"margin:0; padding:0;\">\t\t\t\t  <td style=\"margin:0; padding:0;\" valign=\"top\" align=\"center\">\t\t\t\t\t  <table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\"  bgcolor=\"#ffffff\" style=\" padding:0 24px; border-top: 4px solid #009DDC; background:#fff;\">\t\t\t\t\t\t  <tbody>\t\t\t\t\t\t\t  \t<tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr>\t\t\t\t\t\t\t\t<tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0; text-align: center;\"><img width=\"36\" height=\"36\" style=\"width:36px; height:36px;\" src=\"cid:information.png\" alt=\"clear\"/></td></tr>\t\t\t\t\t\t\t    <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"10\"></td></tr>\t\t\t\t\t\t\t    <tr style=\"margin:0; padding:0;\">\t\t\t\t\t\t\t\t\t<td style=\"margin:0; padding:0; text-align: center;\">\t\t\t\t\t\t\t\t\t\t<h1 style=\"font-size: 16px; color:#364658;  font-family: Arial, Helvetica, sans-serif; margin:0; padding:0 0 5px;\">Executed – Network Config ${config.operation}</h1>\t\t\t\t\t\t\t\t\t\t<p style=\"color:#9BA3AB; font-size:12px; text-align:center; margin:0; padding:3px 0 0 0; font-weight:bold;\">${timestamp}</p>\t\t\t\t\t\t\t\t\t</td>\t\t\t\t\t\t\t   </tr>\t\t\t\t\t\t\t  <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr>\t\t\t\t\t\t\t  <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr>\t\t\t\t\t\t\t  <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr>\t\t\t\t\t\t\t  <tr style=\"margin:0; padding:0;\">\t\t\t\t\t\t\t  \t<td style=\"margin:0; padding:0;\">\t\t\t\t\t\t\t\t\t<table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\">\t\t\t\t\t\t\t\t\t  <tbody>\t\t\t\t\t\t\t\t\t\t<tr style=\"margin:0; padding:0;\" valign=\"top\">\t\t\t\t\t\t\t\t\t\t  <td width=\"50%\" style=\"margin:0; padding:0 5px;\">\t\t\t\t\t\t\t\t\t\t\t  <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">${config.operation} Status</p>\t\t\t\t\t\t\t\t\t\t\t  <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\"><span style=\"color:#89C540\">${succeeded}</span>/<span style=\"color:#F04E3E\">${failed}</span></p>\t\t\t\t\t\t\t\t\t\t  </td>\t\t\t\t\t\t\t\t\t\t <td width=\"50%\" style=\"margin:0; padding:0 5px;\">\t\t\t\t\t\t\t\t\t\t\t  <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Executed At</p>\t\t\t\t\t\t\t\t\t\t\t  <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${event.received.time}</p>\t\t\t\t\t\t\t\t\t\t </td>\t\t\t\t\t\t\t\t\t\t</tr>\t\t\t\t\t\t\t\t\t\t<tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr>\t\t\t\t\t\t\t\t\t\t<tr style=\"margin:0; padding:0\" valign=\"top\">\t\t\t\t\t\t\t\t\t\t  <td width=\"50%\" style=\"margin:0; padding:0 5px;\">\t\t\t\t\t\t\t\t\t\t\t  <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Execution Mode</p>\t\t\t\t\t\t\t\t\t\t\t  <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${execution.mode}</p>\t\t\t\t\t\t\t\t\t\t  </td>\t\t\t\t\t\t\t\t\t\t  <td width=\"50%\" style=\"margin:0; padding:0 5px;\">\t\t\t\t\t\t\t\t\t\t\t  <p style=\"color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;\">Executed By</p>\t\t\t\t\t\t\t\t\t\t\t  <p style=\"color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;\">${user.name}</p>\t\t\t\t\t\t\t\t\t\t  </td>\t\t\t\t\t\t\t\t\t\t</tr>\t\t\t\t\t\t\t\t\t  </tbody>\t\t\t\t\t\t\t\t\t</table>\t\t\t\t\t\t\t\t</td>\t\t\t\t\t\t\t  </tr>\t\t\t\t\t\t\t  <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr>\t\t\t\t\t\t\t  <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr>\t\t\t\t\t\t\t  <tr style=\"margin:0; padding:0;\">\t\t\t\t\t\t\t  \t  <td style=\"margin:0; padding:0;\">\t\t\t\t\t\t\t\t  \t<table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\">\t\t\t\t\t\t\t\t\t  <tbody>\t\t\t\t\t\t\t\t\t\t<tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr>\t\t\t\t\t\t\t\t\t\t<tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\"><p style=\"margin:0; padding:0; font-size:11px; color:#727E8A; text-align:center;\">For more information, <a href=\"https://${host}/alerts/dashboard\" target=\"_blank\" style=\"color:#009DDC; text-decoration: underline;\">Click here.</a></p></td></tr>\t\t\t\t\t\t\t\t\t\t<tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"16\"></td></tr>\t\t\t\t\t\t\t\t\t\t<tr style=\"margin:0; padding:0;\">\t\t\t\t\t\t\t\t\t\t\t<td colspan=\"3\" style=\"margin:0; padding:0;\">\t\t\t\t\t\t\t\t\t\t\t\t<p style=\"color:#364658; font-size:10px; line-height:14px; text-align:center; margin:0; padding:0 0 5px;\"><strong>Disclaimer:</strong> ${disclaimer}</p>\t\t\t\t\t\t\t\t\t\t\t</td>\t\t\t\t\t\t\t\t\t\t </tr>\t\t\t\t\t\t\t\t\t\t<tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr>\t\t\t\t\t\t\t\t\t  </tbody>\t\t\t\t\t\t\t\t\t</table>\t\t\t\t\t\t\t\t  </td>\t\t\t\t\t\t\t  </tr>\t\t\t\t\t\t\t  \t\t\t\t\t\t  </tbody>\t\t\t\t\t</table>\t\t\t\t  </td>\t\t\t</tr>\t\t\t<tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr>\t\t\t<tr align=\"center\"; style=\"margin:0; padding:0; text-align: center;\"><td style=\"margin:0; padding:0;\"><p style=\"font-size:10px; margin:0; padding:0; color:#7B8FA5;\">For more details, visit  <a style=\"color:#009DDC; text-decoration: none;\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p></td></tr>\t\t\t<tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr>\t\t\t<tr align=\"center\" style=\"margin:0; padding:0;\">\t\t\t\t<td style=\"margin:0; padding:0;\">\t\t\t\t\t<table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\" border-collapse: collapse; margin:0; padding:0;\">\t\t\t\t\t  <tbody>\t\t\t\t\t\t<tr>\t\t\t\t\t\t  <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:fb.png\" alt=\"Facebook\"/></a></td>\t\t\t\t\t\t  <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:twitter.png\" alt=\"Twitter\"/></a></td>\t\t\t\t\t\t  <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"/></a></td>\t\t\t\t\t\t</tr>\t\t\t\t\t  </tbody>\t\t\t\t\t</table>\t\t\t\t</td>\t\t\t</tr>\t\t\t<tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr>\t\t\t\t\t</table>\t</body></html>";
    public static final String EMAIL_NOTIFICATION_DAILY_LIMIT_NEARLY_REACHED_TEMPLATE = "<!doctype html><html><head><meta charset=\"utf-8\"><title>License Daily Quota</title></head><body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"><table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\" border-collapse: collapse; font-family: Arial, Helvetica, sans-serif; background:#F3F6F8;\"><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"40\"></td></tr><tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" width=\"650\" valign=\"top\" align=\"center\"> <a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px; height:25px;\" src=\"cid:${logo}\" alt=\"motadata\"/></a> </td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" valign=\"top\" align=\"center\"> <table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\" padding:0 24px; border-top: 4px solid #F5BC18; background:#fff;\"> <tbody> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0; text-align: center;\"><img width=\"36\" height=\"36\" style=\"width:36px; height:36px;\" src=\"cid:warning.png\" alt=\"License Daily Quota\"/></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"10\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0; text-align: center;\"><h1 style=\"font-size: 16px; color:#364658; font-family: Arial, Helvetica, sans-serif; margin:0; padding:0 0 5px;\">Attention – Approaching ${event.type} Volume Daily Limit</h1></td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"><p style=\"font-size:#364658; font-size:11px; line-height:16px; padding:0; margin:0;\">Hi User, </p></td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"12\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"><p style=\"font-size:#364658; font-size:11px; line-height:16px; padding:0 0 16px 0; margin:0;\">Today's <strong>${event.type}</strong> volume has reached <strong>${event.user.limit}GB</strong>, which is over <strong>90%</strong> of your <strong>${event.license.limit}GB</strong> Daily Quota. To prevent service disruption:</p><ul style=\"margin:0; padding:0 0 0 24px\"><li style=\"font-size:#364658; font-size:11px; line-height:16px; padding:0 0 4px 0; margin:0;\">Review and optimize your ${event.type} configurations.</li><li style=\"font-size:#364658; font-size:11px; line-height:16px; padding:0 0 4px 0; margin:0;\">Consider upgrading your license for more capacity.</li></ul></td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"12\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"><p style=\"font-size:#364658; font-size:11px; line-height:16px; padding:0; margin:0;\">Best regards,<br/>Motadata team </p></td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\"><p style=\"color:#364658; font-size:10px; line-height:14px; text-align:center; margin:0; padding:0 0 5px;\"><strong>Disclaimer:</strong> ${disclaimer}</p></td> </tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr> </tbody></table> </td> </tr> </tbody></table> </td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr align=\"center\"; style=\"margin:0; padding:0; text-align: center;\"><td style=\"margin:0; padding:0;\"><p style=\"font-size:10px; margin:0; padding:0; color:#7B8FA5;\">For more details, visit <a style=\"color:#009DDC; text-decoration: none;\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p></td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr><tr align=\"center\" style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\" border-collapse: collapse; margin:0; padding:0;\"> <tbody><tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:fb.png\" alt=\"Facebook\"/></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:twitter.png\" alt=\"Twitter\"/></a></td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"/></a></td></tr> </tbody></table></td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr></table></body></html>";
    public static final String EMAIL_NOTIFICATION_DAILY_LIMIT_REACHED_TEMPLATE = "<!doctype html><html><head><meta charset=\"utf-8\"><title>License Fully Consumed</title></head><body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"><table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\" border-collapse: collapse; font-family: Arial, Helvetica, sans-serif; background:#F3F6F8;\"><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"40\"></td></tr><tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" width=\"650\" valign=\"top\" align=\"center\"> <a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px; height:25px;\" src=\"cid:${logo}\" alt=\"motadata\"/></a> </td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\" valign=\"top\" align=\"center\"> <table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\" padding:0 24px; border-top: 4px solid #F04E3E; background:#fff;\"> <tbody> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0; text-align: center;\"><img width=\"36\" height=\"36\" style=\"width:36px; height:36px;\" src=\"cid:critical.png\" alt=\"License Fully Consumed\"/></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"10\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0; text-align: center;\"><h1 style=\"font-size: 16px; color:#364658; font-family: Arial, Helvetica, sans-serif; margin:0; padding:0 0 5px;\">Attention – ${event.type} Volume Limit Exceeded</h1></td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"><p style=\"font-size:#364658; font-size:11px; line-height:16px; padding:0; margin:0;\">Hi User, </p></td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"12\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"><p style=\"font-size:#364658; font-size:11px; line-height:16px; padding:0 0 16px 0; margin:0;\">Your daily <strong>${event.type}</strong> volume has exceeded the allocated daily limit of <strong>${event.license.limit}GB</strong>, reaching <strong>100%</strong>. To prevent service disruption:</p><ul style=\"margin:0; padding:0 0 0 24px\"><li style=\"font-size:#364658; font-size:11px; line-height:16px; padding:0 0 4px 0; margin:0;\">Review and optimize your ${event.type} configurations.</li><li style=\"font-size:#364658; font-size:11px; line-height:16px; padding:0 0 4px 0; margin:0;\">Consider upgrading your license for more capacity.</li></ul></td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"12\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"><p style=\"font-size:#364658; font-size:11px; line-height:16px; padding:0; margin:0;\">Best regards,<br/>Motadata team </p></td> </tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr> <tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><hr style=\"border: 1px solid #EEF2F6; margin:0; padding:0;\"></td></tr> <tr style=\"margin:0; padding:0;\"> <td style=\"margin:0; padding:0;\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin:0; padding:0;\"> <tbody><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\"><p style=\"color:#364658; font-size:10px; line-height:14px; text-align:center; margin:0; padding:0 0 5px;\"><strong>Disclaimer:</strong> ${disclaimer}</p></td> </tr><tr style=\"margin:0; padding:0;\"><td colspan=\"3\" style=\"margin:0; padding:0;\" height=\"24\"></td></tr> </tbody></table> </td> </tr> </tbody></table> </td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"24\"></td></tr><tr align=\"center\"; style=\"margin:0; padding:0; text-align: center;\"><td style=\"margin:0; padding:0;\"><p style=\"font-size:10px; margin:0; padding:0; color:#7B8FA5;\">For more details, visit <a style=\"color:#009DDC; text-decoration: none;\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p></td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"16\"></td></tr><tr align=\"center\" style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\"><table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\" border-collapse: collapse; margin:0; padding:0;\"> <tbody><tr> <td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:fb.png\" alt=\"Facebook\"/></a></td> <td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:twitter.png\" alt=\"Twitter\"/></a></td> <td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align: middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"/></a></td></tr> </tbody></table></td></tr><tr style=\"margin:0; padding:0;\"><td style=\"margin:0; padding:0;\" height=\"32\"></td></tr></table></body></html>";
    public static final String EMAIL_NOTIFICATION_DISK_UTILIZATION_TEMPLATE = "<!doctype html><html><head><meta charset=\"utf-8\"><meta name=\"viewport\" content=\"width=device-width;initial-scale=1;maximum-scale=1;user-scalable=no;\"><meta name=\"x-apple-disable-message-reformatting\"><title>Major</title><style type=\"text/css\">@media screen and (max-width:599px){.mobile-block{display:block}.mobile-100p{width:100%;height:auto}.mobile-hide{display:none}.mobile-space{padding-top:8px!important;padding-bottom:8px!important}.space16{height:16px}.side-spacing{padding-left:16px!important;padding-right:16px!important}.message-width{width:65%}}.major{color:#f58518}.critical{color:#f04e3e}.clear{color:#89c540}.down{color:#ed7c70}.warning{color:#f5bc18}.major.border{border-color:#f58518!important}.critical.border{border-color:#f04e3e!important}.clear.border{border-color:#89c540!important}.down.border{border-color:#ed7c70!important}.warning.border{border-color:#f5bc18!important}</style></head><body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"><table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\"border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8\"><tbody><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"40\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" class=\"mobile-100p\" width=\"650\" valign=\"top\" align=\"center\"><a href=\"${link}\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px;height:25px\" src=\"cid:${logo}\" alt=\"motadata\"></a></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" valign=\"top\" align=\"center\"><table class=\"${severity} border mobile-100p side-spacing\" border=\"0\" width=\"600\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"padding:0 24px;border-top:4px solid;background:#fff\"><tbody><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\" class=\"space16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0;text-align:center\"><img width=\"36\" height=\"36\" style=\"width:36px;height:36px\" src=\"cid:${severity}.png\" alt=\"clear\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"10\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0;text-align:center\"><h1 style=\"font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px\">${object.name} - ${metric}</h1><p style=\"color:#9ba3ab;font-size:14px;text-align:center;margin:0;padding:3px 0 0 0;font-weight:700\">${timestamp}</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"32\" class=\"space16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr style=\"margin:0;padding:0\" valign=\"top\"><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Object Name</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${object.name}</p></td><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">IP / Host</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${object.target}</p></td><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Object Type</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${object.type}</p></td></tr><tr class=\"mobile-hide\" style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\" valign=\"top\"><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Metric</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${metric}</p></td><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Metric Value</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${value}</p></td><td class=\"mobile-block mobile-100p mobile-space\" width=\"33.333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Severity</p><p class=\"${severity}\" style=\"font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${severity}</p></td></tr><tr class=\"mobile-hide\" style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px\">Message</p><p style=\"color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0\">${message}</p></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\"><p style=\"color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px\"><strong>Disclaimer:</strong>${disclaimer}</p></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td></tr></tbody></table></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr align=\"center\" ;=\"\" style=\"margin:0;padding:0;text-align:center\"><td style=\"margin:0;padding:0\"><p style=\"font-size:10px;margin:0;padding:0;color:#7b8fa5\">For more details, visit<a style=\"color:#009ddc;text-decoration:none\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr align=\"center\" style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr><td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:fb.png\" alt=\"Facebook\"></a></td><td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:twitter.png\" alt=\"Twitter\"></a></td><td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"></a></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"32\"></td></tr></tbody></table></body></html>";
    public static final String EMAIL_NOTIFICATION_COMPLIANCE_POLICY_SUCCESSFUL_HTML_TEMPLATE = "<!doctype html><html><head><meta charset=\"utf-8\"><title>Successful - Compliance Policy Scan</title></head><body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"><table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\"border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8\"><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"40\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" width=\"650\" valign=\"top\" align=\"center\"><a href=\"https://www.motadata.com/\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px;height:25px\" src=\"cid:${logo}\" alt=\"motadata\"></a></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" valign=\"top\" align=\"center\"><table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"padding:0 24px;border-top:4px solid #89c540;background:#fff\"><tbody><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0;text-align:center\"><img width=\"36\" height=\"36\" style=\"width:36px;height:36px\" src=\"cid:information.png\" alt=\"Successful\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"10\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0;text-align:center\"><h1 style=\"font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px\">Successful - Compliance Policy Scan</h1></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"32\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr style=\"margin:0;padding:0\" valign=\"top\"><td width=\"33.3333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:10px;line-height:14px;margin:0;padding:0 0 5px\">Compliance Policy</p><p style=\"color:#364658;font-size:10px;font-weight:700;line-height:14px;margin:0;padding:0\">${compliance.policy.name}</p></td><td width=\"33.3333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:10px;line-height:14px;margin:0;padding:0 0 5px\">Benchmark</p><p style=\"color:#364658;font-size:10px;font-weight:700;line-height:14px;margin:0;padding:0\">${compliance.benchmark.name}</p></td><td width=\"33.3333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:10px;line-height:14px;margin:0;padding:0 0 5px\">Config File Type</p><p style=\"color:#364658;font-size:10px;font-weight:700;line-height:14px;margin:0;padding:0\">${compliance.policy.config.file.type}</p></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\" valign=\"top\"><td width=\"33.3333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:10px;line-height:14px;margin:0;padding:0 0 5px\">Policy Execution Status</p><p style=\"color:#364658;font-size:10px;font-weight:700;line-height:14px;margin:0;padding:0\">(<span style=\"color:#f04e3e\">${vulnerable}</span>|<span style=\"color:#f58518\">${poor}</span>|<span style=\"color:#f5bc18\">${moderate}</span>|<span style=\"color:#89c540\">${secure}</span>)</p></td><td width=\"33.3333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:10px;line-height:14px;margin:0;padding:0 0 5px\">Compliance Score</p><p style=\"color:#364658;font-size:10px;font-weight:700;line-height:14px;margin:0;padding:0\">${compliance.percentage}%</p></td><td width=\"33.3333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:10px;line-height:14px;margin:0;padding:0 0 5px\">Scan At</p><p style=\"color:#364658;font-size:10px;font-weight:700;line-height:14px;margin:0;padding:0\">${timestamp}</p></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\" valign=\"top\"><td width=\"33.3333%\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:10px;line-height:14px;margin:0;padding:0 0 5px\">Executed By</p><p style=\"color:#364658;font-size:10px;font-weight:700;line-height:14px;margin:0;padding:0\">${user.name}</p></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"4\" style=\"margin:0;padding:0 5px\"><p style=\"color:#7b8fa5;font-size:10px;line-height:14px;margin:0;padding:0 0 5px\">Message</p><p style=\"color:#364658;font-size:10px;font-weight:700;line-height:14px;margin:0;padding:0\">Compliance policy ${compliance.policy.name} was scanned on ${timestamp} by ${user.name}, achieving a ${compliance.percentage}% compliance score. Scan results: ${vulnerable} vulnerable, ${poor} poor, ${moderate} moderate, and ${secure} secure.</p></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\"><p style=\"color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px\"><strong>Disclaimer:</strong>${disclaimer}</p></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td></tr></tbody></table></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr align=\"center\" ; style=\"margin:0;padding:0;text-align:center\"><td style=\"margin:0;padding:0\"><p style=\"font-size:10px;margin:0;padding:0;color:#7b8fa5\">For more details, visit<a style=\"color:#009ddc;text-decoration:none\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr align=\"center\" style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr><td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:fb.png\" alt=\"Facebook\"></a></td><td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:twitter.png\" alt=\"Twitter\"></a></td><td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"></a></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"32\"></td></tr></table></body></html>";
    public static final String EMAIL_NOTIFICATION_PASSWORD_ABOUT_TO_EXPIRE_HTML_TEMPLATE = "<!DOCTYPE html> <html> <head> <meta charset=\"utf-8\" /> <title>Password About to Expire</title> </head> <body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\" > <table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\" border-collapse: collapse; font-family: Arial, Helvetica, sans-serif; background: #f3f6f8; \" > <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"40\"></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" width=\"650\" valign=\"top\" align=\"center\" > <a href=\"https://www.motadata.com/\" target=\"_blank\" ><img width=\"85\" height=\"25\" style=\"width: 85px; height: 25px\" src=\"cid:${logo}\" alt=\"motadata\" /></a> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"24\"></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" valign=\"top\" align=\"center\"> <table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\" padding: 0 24px; border-top: 4px solid #f5bc18; background: #fff; \" > <tbody> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"24\"></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0; text-align: center\"> <img width=\"36\" height=\"36\" style=\"width: 36px; height: 36px\" src=\"cid:expiring.png\" alt=\"License Fully Consumed\" /> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"10\"></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0; text-align: center\"> <h1 style=\" font-size: 16px; color: #364658; font-family: Arial, Helvetica, sans-serif; margin: 0; padding: 0 0 5px; \" > Attention - Your Password is about to Expire </h1> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"32\"></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\"> <hr style=\"border: 1px solid #eef2f6; margin: 0; padding: 0\" /> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"16\"></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\"> <p style=\" font-size: #364658; font-size: 11px; line-height: 16px; padding: 0; margin: 0; \" > Hi ${user.name}, </p> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"12\"></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\"> <p style=\" font-size: #364658; font-size: 11px; line-height: 16px; padding: 0 0 12px 0; margin: 0; \" > This is a reminder that the password for <strong>${user.name}</strong> will expire in <strong>${remaining.days} days</strong>. To avoid any disruption in accessing the AIOps platform, we recommend updating your password before it expires. To update password <a href=\"${link}/settings/my-account/my-profile\" target=\"_blank\" style=\"color: #009ddc\" >Click here</a >. </p> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"12\"></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\"> <p style=\" font-size: #364658; font-size: 11px; line-height: 16px; padding: 0; margin: 0; \" > Best regards,<br />Motadata team </p> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"16\"></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\"> <hr style=\"border: 1px solid #eef2f6; margin: 0; padding: 0\" /> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin: 0; padding: 0\" > <tbody> <tr style=\"margin: 0; padding: 0\"> <td colspan=\"3\" style=\"margin: 0; padding: 0\" height=\"24\" ></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td colspan=\"3\" style=\"margin: 0; padding: 0\"> <p style=\" color: #364658; font-size: 10px; line-height: 14px; text-align: center; margin: 0; padding: 0 0 5px; \" > <strong>Disclaimer:</strong> ${disclaimer} </p> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td colspan=\"3\" style=\"margin: 0; padding: 0\" height=\"24\" ></td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"24\"></td> </tr> <tr align=\"center\" ; style=\"margin: 0; padding: 0; text-align: center\"> <td style=\"margin: 0; padding: 0\"> <p style=\"font-size: 10px; margin: 0; padding: 0; color: #7b8fa5\"> For more details, visit <a style=\"color: #009ddc; text-decoration: none\" href=\"https://www.motadata.com/\" target=\"_blank\" >www.motadata.com</a > </p> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"16\"></td> </tr> <tr align=\"center\" style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\"> <table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin: 0; padding: 0\" > <tbody> <tr> <td> <a href=\"https://www.facebook.com/motadata\" target=\"_blank\" ><img style=\"vertical-align: middle\" src=\"cid:fb.png\" alt=\"Facebook\" /></a> </td> <td> <a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\" ><img style=\"vertical-align: middle\" src=\"cid:twitter.png\" alt=\"Twitter\" /></a> </td> <td> <a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\" ><img style=\"vertical-align: middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\" /></a> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"32\"></td> </tr> </table> </body> </html>";
    public static final String EMAIL_NOTIFICATION_PASSWORD_EXPIRED_HTML_TEMPLATE = "<!DOCTYPE html> <html> <head> <meta charset=\"utf-8\" /> <title>Password Expired</title> </head> <body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\" > <table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\" border-collapse: collapse; font-family: Arial, Helvetica, sans-serif; background: #f3f6f8; \" > <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"40\"></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" width=\"650\" valign=\"top\" align=\"center\" > <a href=\"https://www.motadata.com/\" target=\"_blank\" ><img width=\"85\" height=\"25\" style=\"width: 85px; height: 25px\" src=\"cid:${logo}\" alt=\"motadata\" /></a> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"24\"></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" valign=\"top\" align=\"center\"> <table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\" padding: 0 24px; border-top: 4px solid #f25c4e; background: #fff; \" > <tbody> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"24\"></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0; text-align: center\"> <img width=\"36\" height=\"36\" style=\"width: 36px; height: 36px\" src=\"cid:expired.png\" alt=\"License Fully Consumed\" /> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"10\"></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0; text-align: center\"> <h1 style=\" font-size: 16px; color: #364658; font-family: Arial, Helvetica, sans-serif; margin: 0; padding: 0 0 5px; \" > Attention - Your Password has Expired </h1> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"32\"></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\"> <hr style=\"border: 1px solid #eef2f6; margin: 0; padding: 0\" /> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"16\"></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\"> <p style=\" font-size: #364658; font-size: 11px; line-height: 16px; padding: 0; margin: 0; \" > Hi ${user.name}, </p> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"12\"></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\"> <p style=\" font-size: #364658; font-size: 11px; line-height: 16px; padding: 0 0 12px 0; margin: 0; \" > The password for <strong>${user.name}</strong> has expired. To restore access, please reset your password as soon as possible.<br /> To reset password <a href=\"${link}\" target=\"_blank\" style=\"color: #009ddc\" >Click here</a >. </p> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"12\"></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\"> <p style=\" font-size: #364658; font-size: 11px; line-height: 16px; padding: 0; margin: 0; \" > Best regards,<br />Motadata team </p> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"16\"></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\"> <hr style=\"border: 1px solid #eef2f6; margin: 0; padding: 0\" /> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\"> <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin: 0; padding: 0\" > <tbody> <tr style=\"margin: 0; padding: 0\"> <td colspan=\"3\" style=\"margin: 0; padding: 0\" height=\"24\" ></td> </tr> <tr style=\"margin: 0; padding: 0\"> <td colspan=\"3\" style=\"margin: 0; padding: 0\"> <p style=\" color: #364658; font-size: 10px; line-height: 14px; text-align: center; margin: 0; padding: 0 0 5px; \" > <strong>Disclaimer:</strong> ${disclaimer} </p> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td colspan=\"3\" style=\"margin: 0; padding: 0\" height=\"24\" ></td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"24\"></td> </tr> <tr align=\"center\" ; style=\"margin: 0; padding: 0; text-align: center\"> <td style=\"margin: 0; padding: 0\"> <p style=\"font-size: 10px; margin: 0; padding: 0; color: #7b8fa5\"> For more details, visit <a style=\"color: #009ddc; text-decoration: none\" href=\"https://www.motadata.com/\" target=\"_blank\" >www.motadata.com</a > </p> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"16\"></td> </tr> <tr align=\"center\" style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\"> <table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse: collapse; margin: 0; padding: 0\" > <tbody> <tr> <td> <a href=\"https://www.facebook.com/motadata\" target=\"_blank\" ><img style=\"vertical-align: middle\" src=\"cid:fb.png\" alt=\"Facebook\" /></a> </td> <td> <a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\" ><img style=\"vertical-align: middle\" src=\"cid:twitter.png\" alt=\"Twitter\" /></a> </td> <td> <a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\" ><img style=\"vertical-align: middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\" /></a> </td> </tr> </tbody> </table> </td> </tr> <tr style=\"margin: 0; padding: 0\"> <td style=\"margin: 0; padding: 0\" height=\"32\"></td> </tr> </table> </body> </html>";

    public static final String TEAMS_NOTIFICATION_METRIC_POLICY_JSON_TEMPLATE = "{\"subject\":null,\"body\":{\"contentType\":\"html\",\"content\":\"<attachment id=\\\"motadata\\\"><\\/attachment>\"},\"attachments\":[{\"id\":\"motadata\",\"contentType\":\"application\\/vnd.microsoft.card.adaptive\",\"contentUrl\":null,\"content\":\"{\\\"type\\\":\\\"AdaptiveCard\\\",\\\"speak\\\":\\\"Version 2.2 performance optimization\\\",\\\"body\\\":[{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"Table\\\",\\\"columns\\\":[{\\\"width\\\":\\\"3px\\\"},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"|\\\",\\\"wrap\\\":true,\\\"size\\\":\\\"Large\\\",\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity} - ${object.name} | ${object.target}\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\",\\\"size\\\":\\\"Large\\\"}],\\\"showBorder\\\":false}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"style\\\":\\\"default\\\"}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false}]},{\\\"type\\\":\\\"Table\\\",\\\"targetWidth\\\":\\\"AtLeast:Narrow\\\",\\\"columns\\\":[{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Object Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${object.type}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"roundedCorners\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Severity\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric Counter:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${metric}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric Value:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${value}\\\",\\\"wrap\\\":true}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\"}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Name:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.name}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.type}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Triggered Time:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${timestamp}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"rtl\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Action:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.action}\\\",\\\"wrap\\\":true}]}]}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false},{\\\"type\\\":\\\"Container\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"auto\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Message:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.message}\\\",\\\"wrap\\\":true}]}]}]},{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"ActionSet\\\",\\\"actions\\\":[{\\\"type\\\":\\\"Action.OpenUrl\\\",\\\"iconUrl\\\":\\\"icon:Alert\\\",\\\"title\\\":\\\"View Alert in AIOps\\\",\\\"style\\\":\\\"positive\\\",\\\"url\\\":\\\"${policy.url}\\\"}],\\\"horizontalAlignment\\\":\\\"Left\\\"}]}]}],\\\"horizontalAlignment\\\":\\\"Left\\\"}],\\\"version\\\":\\\"1.5\\\",\\\"msTeams\\\":{\\\"width\\\":\\\"full\\\"},\\\"$schema\\\":\\\"https://adaptivecards.io/schemas/adaptive-card.json\\\"}\",\"name\":null,\"thumbnailUrl\":null}]}";
    public static final String TEAMS_NOTIFICATION_TRAP_POLICY_JSON_TEMPLATE = "{\"subject\":null,\"body\":{\"contentType\":\"html\",\"content\":\"<attachment id=\\\"motadata\\\"><\\/attachment>\"},\"attachments\":[{\"id\":\"motadata\",\"contentType\":\"application\\/vnd.microsoft.card.adaptive\",\"contentUrl\":null,\"content\":\"{\\\"type\\\":\\\"AdaptiveCard\\\",\\\"speak\\\":\\\"Version 2.2 performance optimization\\\",\\\"body\\\":[{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"Table\\\",\\\"columns\\\":[{\\\"width\\\":\\\"3px\\\"},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"|\\\",\\\"wrap\\\":true,\\\"size\\\":\\\"Large\\\",\\\"weight\\\":\\\"Bolder\\\",\\\"color\\\":\\\"${severity.color}\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity} - ${policy.name}\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\",\\\"size\\\":\\\"Large\\\"}],\\\"showBorder\\\":false}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"style\\\":\\\"default\\\"}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false}]},{\\\"type\\\":\\\"Table\\\",\\\"targetWidth\\\":\\\"AtLeast:Narrow\\\",\\\"columns\\\":[{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Name:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.name}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"roundedCorners\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Severity:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity}\\\",\\\"wrap\\\":true,\\\"color\\\":\\\"${severity.color}\\\"}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Source:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${event.source}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.type}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Trigger Condition:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${trigger.condition}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"rtl\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true}]}]}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false},{\\\"type\\\":\\\"Container\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image\\\\\\/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"auto\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Message:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.message}\\\",\\\"wrap\\\":true}]}]}]},{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image\\\\\\/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"ActionSet\\\",\\\"actions\\\":[{\\\"type\\\":\\\"Action.OpenUrl\\\",\\\"iconUrl\\\":\\\"icon:Alert\\\",\\\"title\\\":\\\"View Alert in AIOps\\\",\\\"style\\\":\\\"positive\\\",\\\"url\\\":\\\"${policy.url}\\\"}],\\\"horizontalAlignment\\\":\\\"Left\\\"}]}]}],\\\"horizontalAlignment\\\":\\\"Left\\\"}],\\\"version\\\":\\\"1.5\\\",\\\"msTeams\\\":{\\\"width\\\":\\\"full\\\"},\\\"$schema\\\":\\\"https://adaptivecards.io/schemas/adaptive-card.json\\\"}\",\"name\":null,\"thumbnailUrl\":null}]}";
    public static final String TEAMS_NOTIFICATION_EVENT_POLICY_JSON_TEMPLATE = "{\"subject\":null,\"body\":{\"contentType\":\"html\",\"content\":\"<attachment id=\\\"motadata\\\"><\\/attachment>\"},\"attachments\":[{\"id\":\"motadata\",\"contentType\":\"application\\/vnd.microsoft.card.adaptive\",\"contentUrl\":null,\"content\":\"{\\\"type\\\":\\\"AdaptiveCard\\\",\\\"speak\\\":\\\"Version 2.2 performance optimization\\\",\\\"body\\\":[{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"Table\\\",\\\"columns\\\":[{\\\"width\\\":\\\"3px\\\"},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"|\\\",\\\"wrap\\\":true,\\\"size\\\":\\\"Large\\\",\\\"weight\\\":\\\"Bolder\\\",\\\"color\\\":\\\"${severity.color}\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity} - ${policy.name}\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\",\\\"size\\\":\\\"Large\\\"}],\\\"showBorder\\\":false}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"style\\\":\\\"default\\\"}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false}]},{\\\"type\\\":\\\"Table\\\",\\\"targetWidth\\\":\\\"AtLeast:Narrow\\\",\\\"columns\\\":[{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Event Policy Name:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.name}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"roundedCorners\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Severity:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity}\\\",\\\"wrap\\\":true,\\\"color\\\":\\\"${severity.color}\\\"}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Evaluation Window:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true,\\\"text\\\":\\\"${evaluation.window}\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.type}\\\",\\\"wrap\\\":true}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\"}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${event.field}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Trigger Condition:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${trigger.condition}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Alert Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${trigger.mode}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"rtl\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Trigger Value:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${value}\\\",\\\"wrap\\\":true}]}]}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false},{\\\"type\\\":\\\"Container\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image\\\\\\/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"auto\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Message:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.message}\\\",\\\"wrap\\\":true}]}]}]},{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image\\\\\\/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"ActionSet\\\",\\\"actions\\\":[{\\\"type\\\":\\\"Action.OpenUrl\\\",\\\"iconUrl\\\":\\\"icon:Alert\\\",\\\"title\\\":\\\"View Alert in AIOps\\\",\\\"style\\\":\\\"positive\\\",\\\"url\\\":\\\"${policy.url}\\\"}],\\\"horizontalAlignment\\\":\\\"Left\\\"}]}]}],\\\"horizontalAlignment\\\":\\\"Left\\\"}],\\\"version\\\":\\\"1.5\\\",\\\"msTeams\\\":{\\\"width\\\":\\\"full\\\"},\\\"$schema\\\":\\\"https://adaptivecards.io/schemas/adaptive-card.json\\\"}\",\"name\":null,\"thumbnailUrl\":null}]}";
    public static final String TEAMS_RENOTIFICATION_METRIC_POLICY_JSON_TEMPLATE = "{\"subject\":null,\"body\":{\"contentType\":\"html\",\"content\":\"<attachment id=\\\"motadata\\\"><\\/attachment>\"},\"attachments\":[{\"id\":\"motadata\",\"contentType\":\"application\\/vnd.microsoft.card.adaptive\",\"contentUrl\":null,\"content\":\"{\\\"type\\\":\\\"AdaptiveCard\\\",\\\"speak\\\":\\\"Version 2.2 performance optimization\\\",\\\"body\\\":[{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"Table\\\",\\\"columns\\\":[{\\\"width\\\":\\\"3px\\\"},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"|\\\",\\\"wrap\\\":true,\\\"size\\\":\\\"Large\\\",\\\"weight\\\":\\\"Bolder\\\",\\\"color\\\":\\\"${severity.color}\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity} - ${object.name} | ${object.target}\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\",\\\"size\\\":\\\"Large\\\"}],\\\"showBorder\\\":false}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"style\\\":\\\"default\\\"}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false}]},{\\\"type\\\":\\\"Table\\\",\\\"targetWidth\\\":\\\"AtLeast:Narrow\\\",\\\"columns\\\":[{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Object Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${object.type}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"roundedCorners\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Severity\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity}\\\",\\\"wrap\\\":true,\\\"color\\\":\\\"${severity.color}\\\"}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric Counter:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${metric}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric Value:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${value}\\\",\\\"wrap\\\":true}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\"}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Name:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.name}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.type}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Triggered Time:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${timestamp}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"rtl\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Action:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.action}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Active Since:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${active.since}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true}]}]}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false},{\\\"type\\\":\\\"Container\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"auto\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Message:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.message}\\\",\\\"wrap\\\":true}]}]}]},{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"ActionSet\\\",\\\"actions\\\":[{\\\"type\\\":\\\"Action.OpenUrl\\\",\\\"iconUrl\\\":\\\"icon:Alert\\\",\\\"title\\\":\\\"View Alert in AIOps\\\",\\\"style\\\":\\\"positive\\\",\\\"url\\\":\\\"${policy.url}\\\"}],\\\"horizontalAlignment\\\":\\\"Left\\\"}]}]}],\\\"horizontalAlignment\\\":\\\"Left\\\"}],\\\"version\\\":\\\"1.5\\\",\\\"msTeams\\\":{\\\"width\\\":\\\"full\\\"},\\\"$schema\\\":\\\"https://adaptivecards.io/schemas/adaptive-card.json\\\"}\",\"name\":null,\"thumbnailUrl\":null}]}";
    public static final String TEAMS_NOTIFICATION_SEARCH_QUERY_SHARE_JSON_TEMPLATE = "{\n    \"subject\": null,\n    \"body\": {\n        \"contentType\": \"html\",\n        \"content\": \"<div class=\\\"motadata-msg\\\"><attachment id=\\\"motadata\\\"></attachment><img  src=\\\"../hostedContents/1/$value\\\" style=\\\"width: auto; height: auto;\\\"></div>\"\n    },\n    \"attachments\": [\n        {\n            \"id\": \"motadata\",\n            \"contentType\": \"application/vnd.microsoft.card.adaptive\",\n            \"contentUrl\": null,\n            \"content\": \"{\\n    \\\"type\\\": \\\"AdaptiveCard\\\",\\n    \\\"$schema\\\": \\\"https://adaptivecards.io/schemas/adaptive-card.json\\\",\\n    \\\"version\\\": \\\"1.5\\\",\\n    \\\"msteams\\\": {\\n        \\\"widget\\\": \\\"full\\\"\\n    },\\n    \\\"body\\\": [\\n        {\\n            \\\"type\\\": \\\"Container\\\",\\n            \\\"items\\\": [\\n                {\\n                    \\\"type\\\": \\\"TextBlock\\\",\\n                    \\\"text\\\": \\\"Motadata Search\\\",\\n                    \\\"wrap\\\": true,\\n                    \\\"weight\\\": \\\"Bolder\\\",\\n                    \\\"color\\\": \\\"Accent\\\"\\n                },\\n                {\\n                    \\\"type\\\": \\\"Container\\\",\\n                    \\\"items\\\": [\\n                        {\\n                            \\\"type\\\": \\\"ColumnSet\\\",\\n                            \\\"columns\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"Column\\\",\\n                                    \\\"width\\\": \\\"59px\\\",\\n                                    \\\"items\\\": [\\n                                        {\\n                                            \\\"type\\\": \\\"TextBlock\\\",\\n                                            \\\"text\\\": \\\"Query:\\\",\\n                                            \\\"wrap\\\": true,\\n                                            \\\"weight\\\": \\\"Bolder\\\"\\n                                        }\\n                                    ]\\n                                },\\n                                {\\n                                    \\\"type\\\": \\\"Column\\\",\\n                                    \\\"width\\\": \\\"stretch\\\",\\n                                    \\\"items\\\": [\\n                                        {\\n                                            \\\"type\\\": \\\"TextBlock\\\",\\n                                            \\\"text\\\": \\\"${search.query}\\\",\\n                                            \\\"wrap\\\": true\\n                                        }\\n                                    ]\\n                                }\\n                            ]\\n                        }\\n                    ]\\n                }\\n            ]\\n        },\\n        {\\n            \\\"type\\\": \\\"Container\\\",\\n            \\\"items\\\": [\\n                {\\n                    \\\"type\\\": \\\"Container\\\",\\n                    \\\"items\\\": [\\n                        {\\n                            \\\"type\\\": \\\"ColumnSet\\\",\\n                            \\\"columns\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"Column\\\",\\n                                    \\\"width\\\": \\\"auto\\\",\\n                                    \\\"items\\\": [\\n                                        {\\n                                            \\\"type\\\": \\\"TextBlock\\\",\\n                                            \\\"text\\\": \\\"Message:\\\",\\n                                            \\\"wrap\\\": true,\\n                                            \\\"weight\\\": \\\"Bolder\\\"\\n                                        }\\n                                    ]\\n                                },\\n                                {\\n                                    \\\"type\\\": \\\"Column\\\",\\n                                    \\\"width\\\": \\\"stretch\\\",\\n                                    \\\"items\\\": [\\n                                        {\\n                                            \\\"type\\\": \\\"TextBlock\\\",\\n                                            \\\"text\\\": \\\"${message}\\\",\\n                                            \\\"wrap\\\": true\\n                                        }\\n                                    ]\\n                                }\\n                            ]\\n                        }\\n                    ]\\n                },\\n                {\\n                    \\\"type\\\": \\\"ColumnSet\\\",\\n                    \\\"columns\\\": [\\n                        {\\n                            \\\"type\\\": \\\"Column\\\",\\n                            \\\"width\\\": \\\"59px\\\",\\n                            \\\"items\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"TextBlock\\\",\\n                                    \\\"text\\\": \\\"Sent By:\\\",\\n                                    \\\"wrap\\\": true,\\n                                    \\\"weight\\\": \\\"Bolder\\\"\\n                                }\\n                            ]\\n                        },\\n                        {\\n                            \\\"type\\\": \\\"Column\\\",\\n                            \\\"width\\\": \\\"stretch\\\",\\n                            \\\"items\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"TextBlock\\\",\\n                                    \\\"text\\\": \\\" ${user.name}\\\",\\n                                    \\\"wrap\\\": true\\n                                }\\n                            ]\\n                        }\\n                    ]\\n                }\\n            ]\\n        }\\n    ]\\n}\",\n            \"name\": null,\n            \"thumbnailUrl\": null\n        }\n    ],\n    \"hostedContents\": [\n        {\n            \"@microsoft.graph.temporaryId\": \"1\",\n            \"contentBytes\": \"${content}\",\n            \"contentType\": \"image/png\"\n        }\n    ]\n}";
    public static final String TEAMS_NOTIFICATION_WIDGET_SHARE_JSON_TEMPLATE = "{\n    \"subject\": null,\n    \"body\": {\n        \"contentType\": \"html\",\n        \"content\": \"<div class=\\\"motadata-msg\\\"><attachment id=\\\"motadata\\\"></attachment><img  src=\\\"../hostedContents/1/$value\\\" style=\\\"width: auto; height: auto;\\\"></div>\"\n    },\n    \"attachments\": [\n        {\n            \"id\": \"motadata\",\n            \"contentType\": \"application/vnd.microsoft.card.adaptive\",\n            \"contentUrl\": null,\n            \"content\": \"{\\n    \\\"type\\\": \\\"AdaptiveCard\\\",\\n    \\\"$schema\\\": \\\"https://adaptivecards.io/schemas/adaptive-card.json\\\",\\n    \\\"version\\\": \\\"1.5\\\",\\n    \\\"msteams\\\": {\\n        \\\"widget\\\": \\\"full\\\"\\n    },\\n    \\\"body\\\": [\\n        {\\n            \\\"type\\\": \\\"Container\\\",\\n            \\\"items\\\": [\\n                {\\n                    \\\"type\\\": \\\"TextBlock\\\",\\n                    \\\"text\\\": \\\"${visualization.name}\\\",\\n                    \\\"wrap\\\": true,\\n                    \\\"weight\\\": \\\"Bolder\\\",\\n                    \\\"color\\\": \\\"Accent\\\"\\n                },\\n                {\\n                    \\\"type\\\": \\\"Container\\\",\\n                    \\\"items\\\": [\\n                        {\\n                            \\\"type\\\": \\\"ColumnSet\\\",\\n                            \\\"columns\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"Column\\\",\\n                                    \\\"width\\\": \\\"62px\\\",\\n                                    \\\"items\\\": [\\n                                        {\\n                                            \\\"type\\\": \\\"TextBlock\\\",\\n                                            \\\"text\\\": \\\"Message:\\\",\\n                                            \\\"wrap\\\": true,\\n                                            \\\"weight\\\": \\\"Bolder\\\"\\n                                        }\\n                                    ]\\n                                },\\n                                {\\n                                    \\\"type\\\": \\\"Column\\\",\\n                                    \\\"width\\\": \\\"stretch\\\",\\n                                    \\\"items\\\": [\\n                                        {\\n                                            \\\"type\\\": \\\"TextBlock\\\",\\n                                            \\\"text\\\": \\\"${message}\\\",\\n                                            \\\"wrap\\\": true\\n                                        }\\n                                    ]\\n                                }\\n                            ]\\n                        }\\n                    ]\\n                }\\n            ]\\n        },\\n        {\\n            \\\"type\\\": \\\"Container\\\",\\n            \\\"items\\\": [\\n                {\\n                    \\\"type\\\": \\\"ColumnSet\\\",\\n                    \\\"columns\\\": [\\n                        {\\n                            \\\"type\\\": \\\"Column\\\",\\n                            \\\"width\\\": \\\"62px\\\",\\n                            \\\"items\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"TextBlock\\\",\\n                                    \\\"text\\\": \\\"Sent By:\\\",\\n                                    \\\"wrap\\\": true,\\n                                    \\\"weight\\\": \\\"Bolder\\\"\\n                                }\\n                            ]\\n                        },\\n                        {\\n                            \\\"type\\\": \\\"Column\\\",\\n                            \\\"width\\\": \\\"stretch\\\",\\n                            \\\"items\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"TextBlock\\\",\\n                                    \\\"text\\\": \\\" ${user.name}\\\",\\n                                    \\\"wrap\\\": true\\n                                }\\n                            ]\\n                        }\\n                    ]\\n                }\\n            ]\\n        }\\n    ]\\n}\",\n            \"name\": null,\n            \"thumbnailUrl\": null\n        }\n    ],\n    \"hostedContents\": [\n        {\n            \"@microsoft.graph.temporaryId\": \"1\",\n            \"contentBytes\": \"${content}\",\n            \"contentType\": \"image/png\"\n        }\n    ]\n}";
    public static final String TEAMS_NOTIFICATION_METRIC_EXPLORER_SHARE_JSON_TEMPLATE = "{\n    \"subject\": null,\n    \"body\": {\n        \"contentType\": \"html\",\n        \"content\": \"<div class=\\\"motadata-msg\\\"><attachment id=\\\"motadata\\\"></attachment><img  src=\\\"../hostedContents/1/$value\\\" style=\\\"width: auto; height: auto;\\\"></div>\"\n    },\n    \"attachments\": [\n        {\n            \"id\": \"motadata\",\n            \"contentType\": \"application/vnd.microsoft.card.adaptive\",\n            \"contentUrl\": null,\n            \"content\": \"{\\n    \\\"type\\\": \\\"AdaptiveCard\\\",\\n    \\\"$schema\\\": \\\"https://adaptivecards.io/schemas/adaptive-card.json\\\",\\n    \\\"version\\\": \\\"1.5\\\",\\n    \\\"msTeams\\\": {\\n        \\\"width\\\": \\\"full\\\"\\n    },\\n    \\\"body\\\": [\\n        {\\n            \\\"type\\\": \\\"Container\\\",\\n            \\\"spacing\\\": \\\"None\\\",\\n            \\\"items\\\": [\\n                {\\n                    \\\"type\\\": \\\"TextBlock\\\",\\n                    \\\"text\\\": \\\"Metric Explorer\\\",\\n                    \\\"wrap\\\": true,\\n                    \\\"weight\\\": \\\"Bolder\\\",\\n                    \\\"color\\\": \\\"Accent\\\",\\n                    \\\"spacing\\\": \\\"None\\\"\\n                },\\n                {\\n                    \\\"type\\\": \\\"ColumnSet\\\",\\n                    \\\"spacing\\\": \\\"Small\\\",\\n                    \\\"columns\\\": [\\n                        {\\n                            \\\"type\\\": \\\"Column\\\",\\n                            \\\"width\\\": \\\"62px\\\",\\n                            \\\"items\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"TextBlock\\\",\\n                                    \\\"text\\\": \\\"Message:\\\",\\n                                    \\\"wrap\\\": true,\\n                                    \\\"weight\\\": \\\"Bolder\\\",\\n                                    \\\"spacing\\\": \\\"None\\\"\\n                                }\\n                            ]\\n                        },\\n                        {\\n                            \\\"type\\\": \\\"Column\\\",\\n                            \\\"width\\\": \\\"stretch\\\",\\n                            \\\"items\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"TextBlock\\\",\\n                                    \\\"text\\\": \\\"${message}\\\",\\n                                    \\\"wrap\\\": true,\\n                                    \\\"spacing\\\": \\\"None\\\"\\n                                }\\n                            ]\\n                        }\\n                    ]\\n                },\\n                {\\n                    \\\"type\\\": \\\"ColumnSet\\\",\\n                    \\\"spacing\\\": \\\"Small\\\",\\n                    \\\"columns\\\": [\\n                        {\\n                            \\\"type\\\": \\\"Column\\\",\\n                            \\\"width\\\": \\\"62px\\\",\\n                            \\\"items\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"TextBlock\\\",\\n                                    \\\"text\\\": \\\"Sent By:\\\",\\n                                    \\\"wrap\\\": true,\\n                                    \\\"weight\\\": \\\"Bolder\\\",\\n                                    \\\"spacing\\\": \\\"None\\\"\\n                                }\\n                            ]\\n                        },\\n                        {\\n                            \\\"type\\\": \\\"Column\\\",\\n                            \\\"width\\\": \\\"stretch\\\",\\n                            \\\"items\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"TextBlock\\\",\\n                                    \\\"text\\\": \\\"${user.name}\\\",\\n                                    \\\"wrap\\\": true,\\n                                    \\\"spacing\\\": \\\"None\\\",\\n                                    \\\"spacing\\\": \\\"None\\\"\\n                                }\\n                            ]\\n                        }\\n                    ]\\n                }\\n            ]\\n        }\\n    ]\\n}\",\n            \"name\": null,\n            \"thumbnailUrl\": null\n        }\n    ],\n    \"hostedContents\": [\n        {\n            \"@microsoft.graph.temporaryId\": \"1\",\n            \"contentBytes\": \"${content}\",\n            \"contentType\": \"image/png\"\n        }\n    ]\n}";
    public static final String TEAMS_SHARE_METRIC_POLICY_JSON_TEMPLATE = "{\"subject\":null,\"body\":{\"contentType\":\"html\",\"content\":\"<attachment id=\\\"motadata\\\"><\\/attachment>\"},\"attachments\":[{\"id\":\"motadata\",\"contentType\":\"application\\/vnd.microsoft.card.adaptive\",\"contentUrl\":null,\"content\":\"{\\\"type\\\":\\\"AdaptiveCard\\\",\\\"speak\\\":\\\"Version 2.2 performance optimization\\\",\\\"body\\\":[{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${message}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"Table\\\",\\\"columns\\\":[{\\\"width\\\":\\\"3px\\\"},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"|\\\",\\\"wrap\\\":true,\\\"size\\\":\\\"Large\\\",\\\"weight\\\":\\\"Bolder\\\",\\\"color\\\":\\\"${severity.color}\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity} - ${object.name} | ${object.target}\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\",\\\"size\\\":\\\"Large\\\"}],\\\"showBorder\\\":false}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"style\\\":\\\"default\\\"}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false}]},{\\\"type\\\":\\\"Table\\\",\\\"targetWidth\\\":\\\"AtLeast:Narrow\\\",\\\"columns\\\":[{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Object Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${object.type}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"roundedCorners\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Severity\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity}\\\",\\\"wrap\\\":true,\\\"color\\\":\\\"${severity.color}\\\"}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric Counter:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${metric}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric Value:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${value}\\\",\\\"wrap\\\":true}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\"}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Name:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.name}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.type}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Triggered Time:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${Timestamp}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"rtl\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Action:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.action}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Active Since:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${active.since}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true}]}]}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false},{\\\"type\\\":\\\"Container\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"auto\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Alert Message:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.message}\\\",\\\"wrap\\\":true}]}]}]},{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT\\\\\\/bpqBf9skNwAAAAAA1B75\\\\\\/gGXfzlCzgAAAABJRU5ErkJggg==\\\"},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"ActionSet\\\",\\\"actions\\\":[{\\\"type\\\":\\\"Action.OpenUrl\\\",\\\"iconUrl\\\":\\\"icon:Alert\\\",\\\"title\\\":\\\"View Alert in AIOps\\\",\\\"style\\\":\\\"positive\\\",\\\"url\\\":\\\"${policy.url}\\\"}],\\\"horizontalAlignment\\\":\\\"Left\\\"}]}]}],\\\"horizontalAlignment\\\":\\\"Left\\\"}],\\\"version\\\":\\\"1.5\\\",\\\"msTeams\\\":{\\\"width\\\":\\\"full\\\"},\\\"$schema\\\":\\\"https://adaptivecards.io/schemas/adaptive-card.json\\\"}\",\"name\":null,\"thumbnailUrl\":null}]}";
    public static final String TEAMS_SHARE_EVENT_POLICY_JSON_TEMPLATE = "{\"subject\":null,\"body\":{\"contentType\":\"html\",\"content\":\"<attachment id=\\\"motadata\\\"><\\/attachment>\"},\"attachments\":[{\"id\":\"motadata\",\"contentType\":\"application\\/vnd.microsoft.card.adaptive\",\"contentUrl\":null,\"content\":\"{\\\"type\\\":\\\"AdaptiveCard\\\",\\\"speak\\\":\\\"Version 2.2 performance optimization\\\",\\\"body\\\":[{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${message}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"Table\\\",\\\"columns\\\":[{\\\"width\\\":\\\"3px\\\"},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"|\\\",\\\"wrap\\\":true,\\\"size\\\":\\\"Large\\\",\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity} - ${policy.name}\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\",\\\"size\\\":\\\"Large\\\"}],\\\"showBorder\\\":false}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"style\\\":\\\"default\\\"}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false}]},{\\\"type\\\":\\\"Table\\\",\\\"targetWidth\\\":\\\"AtLeast:Narrow\\\",\\\"columns\\\":[{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Event Policy Name:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.name}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"roundedCorners\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Severity:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Evaluation Window:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true,\\\"text\\\":\\\"${evaluation.window}\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.type}\\\",\\\"wrap\\\":true}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\"}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${event.field}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Trigger Condition:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${triggerCondition}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Alert Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${trigger.mode}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"rtl\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Trigger Value:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${value}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Last Seen:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${active.since}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true}]}]}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false},{\\\"type\\\":\\\"Container\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"auto\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Alert Message:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${alert.message}\\\",\\\"wrap\\\":true}]}]}]},{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"ActionSet\\\",\\\"actions\\\":[{\\\"type\\\":\\\"Action.OpenUrl\\\",\\\"iconUrl\\\":\\\"icon:Alert\\\",\\\"title\\\":\\\"View Alert in AIOps\\\",\\\"style\\\":\\\"positive\\\",\\\"url\\\":\\\"${policy.url}\\\"}],\\\"horizontalAlignment\\\":\\\"Left\\\"}]}]}],\\\"horizontalAlignment\\\":\\\"Left\\\"}],\\\"version\\\":\\\"1.5\\\",\\\"msTeams\\\":{\\\"width\\\":\\\"full\\\"},\\\"$schema\\\":\\\"https://adaptivecards.io/schemas/adaptive-card.json\\\"}\",\"name\":null,\"thumbnailUrl\":null}]}";
    public static final String TEAMS_SHARE_TRAP_POLICY_JSON_TEMPLATE = "{\"subject\":null,\"body\":{\"contentType\":\"html\",\"content\":\"<attachment id=\\\"motadata\\\"><\\/attachment>\"},\"attachments\":[{\"id\":\"motadata\",\"contentType\":\"application\\/vnd.microsoft.card.adaptive\",\"contentUrl\":null,\"content\":\"{\\\"type\\\":\\\"AdaptiveCard\\\",\\\"speak\\\":\\\"Version 2.2 performance optimization\\\",\\\"body\\\":[{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${message}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"Table\\\",\\\"columns\\\":[{\\\"width\\\":\\\"3px\\\"},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"|\\\",\\\"wrap\\\":true,\\\"size\\\":\\\"Large\\\",\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity} - ${policy.name}\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\",\\\"size\\\":\\\"Large\\\"}],\\\"showBorder\\\":false}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"style\\\":\\\"default\\\"}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false}]},{\\\"type\\\":\\\"Table\\\",\\\"targetWidth\\\":\\\"AtLeast:Narrow\\\",\\\"columns\\\":[{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Name:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.name}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"roundedCorners\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Severity:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Source:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${eventSource}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.type}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Trigger Condition:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${triggerCondition}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"rtl\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\",\\\"text\\\":\\\"Last Seen:\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true,\\\"text\\\":\\\"${active.since}\\\"}]}]}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false},{\\\"type\\\":\\\"Container\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"auto\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Alert Message:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${alert.message}\\\",\\\"wrap\\\":true}]}]}]},{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"ActionSet\\\",\\\"actions\\\":[{\\\"type\\\":\\\"Action.OpenUrl\\\",\\\"iconUrl\\\":\\\"icon:Alert\\\",\\\"title\\\":\\\"View Alert in AIOps\\\",\\\"style\\\":\\\"positive\\\",\\\"url\\\":\\\"${policy.url}\\\"}],\\\"horizontalAlignment\\\":\\\"Left\\\"}]}]}],\\\"horizontalAlignment\\\":\\\"Left\\\"}],\\\"version\\\":\\\"1.5\\\",\\\"msTeams\\\":{\\\"width\\\":\\\"full\\\"},\\\"$schema\\\":\\\"https://adaptivecards.io/schemas/adaptive-card.json\\\"}\",\"name\":null,\"thumbnailUrl\":null}]}";
    public static final Set<String> SMS_GATEWAY_ERROR_CODES = new HashSet<>(MotadataConfigUtil.getSMSGatewayErrors());

    /**
     * Sends an email notification with the specified subject, message, and recipients.
     * <p>
     * This method creates a notification context with the required fields and publishes
     * it to the EVENT_NOTIFICATION address on the Vert.x EventBus.
     *
     * @param subject    The subject line of the email
     * @param message    The body content of the email
     * @param recipients A JsonArray containing the email addresses of the recipients
     */
    public static void sendEmail(String subject, String message, JsonArray recipients)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_NOTIFICATION, new JsonObject().put(NOTIFICATION_TYPE, Notification.NotificationType.EMAIL.getName()).put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, recipients)
                .put(Notification.EMAIL_NOTIFICATION_MESSAGE, message).put(Notification.EMAIL_NOTIFICATION_SUBJECT, subject));
    }

    /**
     * Sends an email notification using a pre-configured context object.
     * <p>
     * This method adds the EMAIL notification type to the context and publishes
     * it to the EVENT_NOTIFICATION address on the Vert.x EventBus.
     *
     * @param context A JsonObject containing all the necessary email notification parameters
     *                (recipients, subject, message, attachments, etc.)
     */
    public static void sendEmail(JsonObject context)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_NOTIFICATION, context.put(NOTIFICATION_TYPE, NotificationType.EMAIL.getName()));
    }


    /**
     * Sends an SMS notification with the specified message to the given recipients.
     * <p>
     * This method creates a notification context with the required fields and publishes
     * it to the EVENT_NOTIFICATION address on the Vert.x EventBus.
     *
     * @param message    The text message content to be sent
     * @param recipients A JsonArray containing the phone numbers of the recipients
     */
    public static void sendSMS(String message, JsonArray recipients)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_NOTIFICATION, new JsonObject().put(NOTIFICATION_TYPE, NotificationType.SMS.getName()).put(Notification.SMS_NOTIFICATION_RECIPIENTS, recipients)
                .put(Notification.SMS_NOTIFICATION_MESSAGE, message));
    }

    /**
     * Sends an SNMP trap notification using the provided event data and forwarder configuration.
     * <p>
     * This method adds the SNMP_TRAP notification type and target information to the event
     * and publishes it to the EVENT_NOTIFICATION address on the Vert.x EventBus.
     *
     * @param event     A JsonObject containing the event data to be sent as an SNMP trap
     * @param forwarder A JsonObject containing the SNMP trap forwarder configuration
     *                  (destination host, port, etc.)
     */
    public static void sendSNMPTrap(JsonObject event, JsonObject forwarder)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_NOTIFICATION, event.put(Notification.NOTIFICATION_TYPE, Notification.NotificationType.SNMP_TRAP.getName())
                .put(GlobalConstants.TARGET, forwarder.getString(SNMPTrapForwarder.SNMP_TRAP_FORWARDER_DESTINATION_HOST) + "/" + forwarder.getInteger(SNMPTrapForwarder.SNMP_TRAP_FORWARDER_DESTINATION_PORT)));
    }

    /**
     * Sends an SNMP trap notification using the provided event data map and forwarder configuration.
     * <p>
     * This method adds the SNMP_TRAP notification type and target information to the event map
     * and publishes it to the EVENT_NOTIFICATION address on the Vert.x EventBus.
     *
     * @param event     A Map containing the event data to be sent as an SNMP trap
     * @param forwarder A JsonObject containing the SNMP trap forwarder configuration
     *                  (destination host, port, etc.)
     */
    public static void sendSNMPTrap(Map<String, Object> event, JsonObject forwarder)
    {
        event.put(Notification.NOTIFICATION_TYPE, Notification.NotificationType.SNMP_TRAP.getName());
        event.put(GlobalConstants.TARGET, forwarder.getString(SNMPTrapForwarder.SNMP_TRAP_FORWARDER_DESTINATION_HOST) + "/" + forwarder.getInteger(SNMPTrapForwarder.SNMP_TRAP_FORWARDER_DESTINATION_PORT));
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_NOTIFICATION, event);
    }

    /**
     * Sends a sound notification using the provided context.
     * <p>
     * This method adds the SOUND notification type to the context and publishes
     * it to the EVENT_NOTIFICATION address on the Vert.x EventBus.
     *
     * @param context A JsonObject containing the sound notification parameters
     */
    public static void playSound(JsonObject context)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_NOTIFICATION, context.put(NOTIFICATION_TYPE, NotificationType.SOUND.getName()));
    }

    /**
     * Sends a generic notification using the provided context.
     * <p>
     * This method publishes the context directly to the EVENT_NOTIFICATION address
     * on the Vert.x EventBus without modifying it. The context should already
     * contain all necessary notification parameters, including the notification type.
     *
     * @param context A JsonObject containing all the necessary notification parameters,
     *                including the notification type
     */
    public static void send(JsonObject context)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_NOTIFICATION, context);
    }

    /**
     * Retrieves the appropriate notification template for a channel based on event type and channel type.
     * <p>
     * This method selects the correct template based on:
     * 1. The channel type (currently only Microsoft Teams is supported)
     * 2. The event type (TRAP, LOG, FLOW, or METRIC)
     * 3. Whether this is a renotification (for metric policies)
     *
     * @param eventType   The type of event (TRAP, LOG, FLOW, or METRIC)
     * @param channelType The notification channel type (e.g., Microsoft Teams)
     * @param renotify    Flag indicating whether this is a renotification
     * @return The appropriate template string for the notification, or EMPTY_VALUE if no matching template is found
     */
    public static String getChannelNotificationTemplate(String eventType, String channelType, boolean renotify)
    {
        var template = EMPTY_VALUE;

        if (channelType.equalsIgnoreCase(NotificationType.MICROSOFT_TEAMS.getName()))
        {
            if (PolicyEngineConstants.PolicyType.TRAP.getName().equalsIgnoreCase(eventType))
            {
                template = TEAMS_NOTIFICATION_TRAP_POLICY_JSON_TEMPLATE;
            }
            else if (PolicyEngineConstants.PolicyType.LOG.getName().equalsIgnoreCase(eventType) || PolicyEngineConstants.PolicyType.FLOW.getName().equalsIgnoreCase(eventType))
            {
                template = TEAMS_NOTIFICATION_EVENT_POLICY_JSON_TEMPLATE;
            }
            else
            {
                template = renotify ? TEAMS_RENOTIFICATION_METRIC_POLICY_JSON_TEMPLATE : TEAMS_NOTIFICATION_METRIC_POLICY_JSON_TEMPLATE;
            }
        }

        return template;
    }

    /**
     * Retrieves the appropriate notification subject based on notification type and share type.
     * <p>
     * This method selects the correct subject string based on:
     * 1. The notification type (currently only EMAIL is supported)
     * 2. The share type (ALERT, METRIC_EXPLORER, SEARCH_QUERY, or WIDGET)
     *
     * @param notificationType The type of notification (e.g., EMAIL)
     * @param type             The type of share (ALERT, METRIC_EXPLORER, SEARCH_QUERY, or WIDGET)
     * @return The appropriate subject string for the notification, or EMPTY_VALUE if no matching subject is found
     */
    public static String getNotificationSubject(String notificationType, String type)
    {
        var subject = EMPTY_VALUE;

        if (notificationType.equalsIgnoreCase(NotificationType.EMAIL.getName()))
        {
            subject = switch (Notification.ShareType.valueOfName(type))
            {
                case ALERT -> InfoMessageConstants.ALERT_SHARE_NOTIFICATION_SUBJECT;

                case METRIC_EXPLORER -> InfoMessageConstants.METRIC_EXPLORER_SHARE_NOTIFICATION_SUBJECT;

                case SEARCH_QUERY -> InfoMessageConstants.SEARCH_QUERY_SHARE_NOTIFICATION_SUBJECT;

                case WIDGET -> InfoMessageConstants.WIDGET_SHARE_NOTIFICATION_SUBJECT;
            };
        }

        return subject;
    }

    /**
     * Retrieves the appropriate notification template based on notification type, share type, and alert type.
     * <p>
     * This method selects the correct template based on:
     * 1. The notification type (EMAIL or MICROSOFT_TEAMS)
     * 2. The share type (ALERT, METRIC_EXPLORER, SEARCH_QUERY, or WIDGET)
     * 3. For ALERT share type, the alert type (TRAP, LOG, FLOW, or METRIC)
     *
     * @param notificationType The type of notification (EMAIL or MICROSOFT_TEAMS)
     * @param type             The type of share (ALERT, METRIC_EXPLORER, SEARCH_QUERY, or WIDGET)
     * @param alertType        The type of alert (TRAP, LOG, FLOW, or METRIC), used only when type is ALERT
     * @return The appropriate template string for the notification, or EMPTY_VALUE if no matching template is found
     */
    public static String getNotificationTemplate(String notificationType, String type, String alertType)
    {
        var template = EMPTY_VALUE;

        if (notificationType.equalsIgnoreCase(NotificationType.MICROSOFT_TEAMS.getName()))
        {
            template = switch (Notification.ShareType.valueOfName(type))
            {
                case ALERT ->
                {
                    if (PolicyEngineConstants.PolicyType.TRAP.getName().equalsIgnoreCase(alertType))
                    {
                        yield TEAMS_SHARE_TRAP_POLICY_JSON_TEMPLATE;
                    }
                    else if (PolicyEngineConstants.PolicyType.LOG.getName().equalsIgnoreCase(alertType) || PolicyEngineConstants.PolicyType.FLOW.getName().equalsIgnoreCase(alertType))
                    {
                        yield TEAMS_SHARE_EVENT_POLICY_JSON_TEMPLATE;
                    }
                    else
                    {
                        yield TEAMS_SHARE_METRIC_POLICY_JSON_TEMPLATE;
                    }
                }

                case METRIC_EXPLORER -> TEAMS_NOTIFICATION_METRIC_EXPLORER_SHARE_JSON_TEMPLATE;

                case SEARCH_QUERY -> TEAMS_NOTIFICATION_SEARCH_QUERY_SHARE_JSON_TEMPLATE;

                case WIDGET -> TEAMS_NOTIFICATION_WIDGET_SHARE_JSON_TEMPLATE;

            };

        }
        else if (notificationType.equalsIgnoreCase(NotificationType.EMAIL.getName()))
        {
            template = switch (Notification.ShareType.valueOfName(type))
            {
                case ALERT ->
                {
                    if (PolicyEngineConstants.PolicyType.TRAP.getName().equalsIgnoreCase(alertType))
                    {
                        yield EMAIL_SHARE_TRAP_POLICY_HTML_TEMPLATE;
                    }
                    else if (PolicyEngineConstants.PolicyType.LOG.getName().equalsIgnoreCase(alertType) || PolicyEngineConstants.PolicyType.FLOW.getName().equalsIgnoreCase(alertType))
                    {
                        yield EMAIL_SHARE_EVENT_POLICY_HTML_TEMPLATE;
                    }
                    else
                    {
                        yield EMAIL_SHARE_METRIC_POLICY_HTML_TEMPLATE;
                    }
                }

                case METRIC_EXPLORER -> EMAIL_NOTIFICATION_METRIC_EXPLORER_HTML_TEMPLATE;

                case SEARCH_QUERY -> EMAIL_NOTIFICATION_SEARCH_QUERY_HTML_TEMPLATE;

                case WIDGET -> EMAIL_NOTIFICATION_WIDGET_HTML_TEMPLATE;

            };
        }

        return template;
    }

    /**
     * Abstract method that must be implemented by concrete notification classes.
     * <p>
     * This method is responsible for processing and sending a notification based on
     * the provided event data. The implementation should handle all aspects of
     * notification delivery, including formatting, addressing, and transmission.
     *
     * @param event A JsonObject containing the event data and notification parameters
     * @return A Promise that resolves to a JsonObject containing the result of the notification attempt
     */
    abstract Promise<JsonObject> notify(JsonObject event);

    /**
     * Enumeration of content types used for notification attachments and content.
     * <p>
     * These content types follow the MIME type standards as defined by IANA.
     *
     * @see <a href="https://www.iana.org/assignments/media-types/media-types.xhtml">IANA Media Types</a>
     */
    public enum ContentType
    {
        PDF("application/pdf"),
        CSV("text/csv"),
        HTML("text/html"),
        PNG("image/png"),
        JSON("application/json"),
        EXCEL("application/vnd.ms-excel"),
        JPG("image/jpg"),
        TEXT("text/plain");


        private final String type;

        ContentType(String type)
        {
            this.type = type;
        }

        public static String getType(String extension)
        {
            return switch (extension.toLowerCase(Locale.ROOT))
            {
                case "pdf" -> PDF.type;

                case "csv" -> CSV.type;

                case "png" -> PNG.type;

                case "json" -> JSON.type;

                case "xlsx" -> EXCEL.type;

                case "jpg" -> JPG.type;

                case "txt" -> TEXT.type;

                default -> HTML.type;
            };
        }
    }

    /**
     * Enumeration of supported notification types in the system.
     * <p>
     * This enum defines all the notification channels that can be used to deliver
     * notifications to users or external systems. Each notification type has a
     * display name that is used in the user interface and API.
     */
    public enum NotificationType
    {
        /**
         * Email notifications sent via SMTP
         */
        EMAIL("Email"),

        /**
         * SMS text message notifications
         */
        SMS("SMS"),

        /**
         * HTTP webhook notifications to external systems
         */
        WEBHOOK("Webhook"),

        /**
         * Push notifications to mobile devices or browsers
         */
        PUSH("Push"),

        /**
         * SNMP trap notifications to network management systems
         */
        SNMP_TRAP("SNMP Trap"),

        /**
         * Syslog notifications to logging systems
         */
        SYSLOG("Syslog"),

        /**
         * Sound alerts played on the local system
         */
        SOUND("Sound"),

        /**
         * Microsoft Teams channel notifications
         */
        MICROSOFT_TEAMS("Microsoft Teams");

        private static final Map<String, NotificationType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(NotificationType::getName, e -> e)));
        private final String name;

        NotificationType(String name)
        {
            this.name = name;
        }

        public static NotificationType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }


    /**
     * Enumeration of supported share types for notifications.
     * <p>
     * This enum defines the different types of content that can be shared via
     * notifications. Each share type has specific templates and handling logic
     * associated with it.
     */
    public enum ShareType
    {
        /**
         * Metric Explorer dashboard or view
         */
        METRIC_EXPLORER("Metric Explorer"),

        /**
         * Search query results
         */
        SEARCH_QUERY("Search Query"),

        /**
         * Alert information from policy violations
         */
        ALERT("Alert"),

        /**
         * Dashboard widget
         */
        WIDGET("Widget");

        private static final Map<String, ShareType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(ShareType::getName, e -> e)));
        private final String name;

        ShareType(String name)
        {
            this.name = name;
        }

        public static ShareType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }
}
