/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	25-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
*/
package com.mindarray.nms;

import com.mindarray.*;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.*;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.notification.Notification;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.WorkerExecutor;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;

import java.net.InetAddress;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mindarray.ErrorMessageConstants.REDISCOVER_FAILED;
import static com.mindarray.ErrorMessageConstants.TOPOLOGY_ENTRY_POINT_MISSING;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.TOPOLOGY_NOTIFICATION_SUBJECT;
import static com.mindarray.api.AIOpsObject.OBJECT_IP;
import static com.mindarray.api.AIOpsObject.OBJECT_NAME;
import static com.mindarray.api.Scheduler.SCHEDULER_EMAIL_RECIPIENTS;
import static com.mindarray.api.Scheduler.SCHEDULER_SMS_RECIPIENTS;
import static com.mindarray.api.TopologyPlugin.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.CHANGE_NOTIFICATION_TYPE;
import static com.mindarray.eventbus.EventBusConstants.EVENT_TOPOLOGY;
import static com.mindarray.nms.NMSConstants.*;

/**
 * TopologyEngine is responsible for discovering and managing network topology relationships.
 * <p>
 * This class handles:
 * <ul>
 *   <li>Network topology discovery using various protocols (SNMP, CDP, LLDP, etc.)</li>
 *   <li>Building dependency maps between network devices</li>
 *   <li>Tracking connections between network elements</li>
 *   <li>Managing worker allocation for topology discovery jobs</li>
 *   <li>Processing topology discovery events and responses</li>
 *   <li>Notifying users about topology discovery results</li>
 * </ul>
 * <p>
 * The engine supports both Layer 2 (L2) and Layer 3 (L3) topology discovery and can
 * handle various network protocols. It implements a worker pool model to efficiently
 * process topology discovery requests in parallel while managing system resources.
 */
public class TopologyEngine extends AbstractVerticle
{
    /**
     * Logger instance for this class
     */
    private static final Logger LOGGER = new Logger(TopologyEngine.class, GlobalConstants.MOTADATA_NMS, "Topology Engine");

    /**
     * Delivery options for event bus messages with a 10-minute timeout
     */
    private static final DeliveryOptions DELIVERY_OPTIONS = new DeliveryOptions().setSendTimeout(600000L);

    /**
     * Worker executor for processing topology discovery jobs with a 60-minute timeout
     */
    private final WorkerExecutor workerExecutor = Bootstrap.vertx().createSharedWorkerExecutor("Topology Engine", MotadataConfigUtil.getTopologyWorkers(), 60L, TimeUnit.MINUTES);

    /**
     * Map to store topology discovery events by event ID
     */
    private final Map<Long, JsonObject> events = new HashMap<>();

    /**
     * Map to organize topology discovery events by plugin type
     */
    private final Map<NMSConstants.TopologyPluginType, List<Long>> eventsByPluginType = new EnumMap<>(NMSConstants.TopologyPluginType.class);

    /**
     * Map to track the number of idle workers for each topology plugin type
     */
    private final Map<NMSConstants.TopologyPluginType, Integer> idleWorkersByPluginType = new EnumMap<>(NMSConstants.TopologyPluginType.class);

    /**
     * Counter for tracking the total number of idle workers across all plugin types
     */
    private final AtomicInteger idleWorkers = new AtomicInteger(MotadataConfigUtil.getTopologyWorkers());

    /**
     * Map to organize network protocols by link layer (L2 or L3)
     */
    private final Map<String, JsonArray> protocolsByLinkLayer = new HashMap<>();

    /**
     * Map to track connections between network devices
     */
    private final Map<String, String> connections = new HashMap<>();

    /**
     * Counter for tracking consecutive empty event checks to determine when to stop the timer
     */
    private int eventProbes = 0;

    /**
     * Flag indicating whether the periodic timer for processing events is active
     */
    private boolean timeHandlerActive = false;

    /**
     * Initializes the TopologyEngine verticle.
     * <p>
     * This method:
     * 1. Sets up data structures to organize protocols by link layer (L2/L3)
     * 2. Initializes event tracking and worker allocation for different plugin types
     * 3. Registers event bus consumers for topology events and statistics
     * 4. Sets up a periodic timer to process pending topology discovery events
     * 5. Configures worker allocation for different topology plugin types
     * <p>
     * The method handles different types of topology discovery events based on their plugin type
     * and manages worker allocation to ensure efficient resource usage.
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        for (var entry : LEVELS_BY_NETWORK_PROTOCOLS.entrySet())
        {
            var linkLayer = entry.getValue().equals(AIOpsConstants.DependencyLevel.SIX.getName()) ? TopologyLinkLayer.L2.getName() : TopologyLinkLayer.L3.getName();

            protocolsByLinkLayer.computeIfAbsent(linkLayer, value -> new JsonArray()).add(entry.getKey());

        }

        for (var topologyType : NMSConstants.TopologyPluginType.values())
        {
            eventsByPluginType.put(topologyType, new ArrayList<>());

            idleWorkersByPluginType.put(topologyType, NMSConstants.getTopologyWorkersByPluginType(topologyType));
        }

        vertx.eventBus().<Void>localConsumer(EventBusConstants.EVENT_ENGINE_STATS, message ->
        {
            if (MotadataConfigUtil.devMode())
            {
                vertx.eventBus().publish(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_ENGINE_STATS_RESPONSE,
                        new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ENGINE_STATS)
                                .put(EventBusConstants.ENGINE_TYPE, EVENT_TOPOLOGY)
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType().name()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                .put(HealthUtil.HEALTH_STATS, new JsonObject().put(HealthUtil.PENDING_EVENTS, events.size())
                                        .put(HealthUtil.IDLE_WORKERS, idleWorkers.get())));
            }
            else
            {
                vertx.eventBus().send(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_ENGINE_STATS_RESPONSE,
                        new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ENGINE_STATS)
                                .put(EventBusConstants.ENGINE_TYPE, EVENT_TOPOLOGY)
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType().name()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                .put(HealthUtil.HEALTH_STATS, new JsonObject().put(HealthUtil.PENDING_EVENTS, events.size())
                                        .put(HealthUtil.IDLE_WORKERS, idleWorkers.get())));
            }
        });

        if (Bootstrap.bootstrapType() == BootstrapType.APP && (Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.STANDALONE.name())) || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.PRIMARY.name()))
        {
            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_TOPOLOGY_RUN, message ->
            {
                try
                {
                    var event = message.body();

                    if (event != null && !event.isEmpty())
                    {
                        var schedulerId = event.getLong(EventBusConstants.EVENT_SCHEDULER);

                        LOGGER.info(String.format("request received to schedule topology for scheduler id %s", schedulerId));

                        if (!event.containsKey(TopologyPlugin.TOPOLOGY_PLUGIN_DISCOVERY))
                        {
                            vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                                    .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.RUN_TOPOLOGY.name())
                                    .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                                    .put(ID, schedulerId)
                                    .put(EventBusConstants.EVENT_SCHEDULER, schedulerId));
                        }

                        vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_ID, TopologyCacheStore.getStore().getEventId(schedulerId))
                                .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_TOPOLOGY_RUN)
                                .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                                .put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER)
                                .put(EventBusConstants.EVENT_CONTEXT, event).mergeIn(event));

                        var objects = ObjectConfigStore.getStore().getItems(event.getJsonArray(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS));

                        if (objects != null && !objects.isEmpty())
                        {
                            EventBusConstants.updateEvent(TopologyCacheStore.getStore().getEventId(schedulerId),
                                    String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_STARTED, DateTimeUtil.timestamp()), EventBusConstants.EVENT_STATE_RUNNING);

                            var probes = 0;

                            var runbook = getNextHopRunbook();

                            event.put(TOPOLOGY_PLUGIN_FILTER_TARGETS, new JsonArray(new ArrayList<>(setFilterTargets(event))));

                            var qualifiedPlugins = qualify();

                            var credentials = new HashMap<Long, Long>();

                            MetricConfigStore.getStore().flatMap().values().stream().filter(item -> item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_DEVICE.getName()))
                                    .collect(Collectors.groupingBy(item -> item.getLong(Metric.METRIC_OBJECT),
                                            Collectors.mapping(item -> item.getLong(Metric.METRIC_CREDENTIAL_PROFILE), Collectors.toList())))
                                    .forEach((key, value) -> credentials.put(key, value.getFirst()));

                            var qualifiedObjects = new HashSet<String>();

                            var qualifiedProtocols = event.getJsonArray(TOPOLOGY_PROTOCOLS);

                            for (var index = 0; index < objects.size(); index++)
                            {
                                var object = objects.getJsonObject(index);

                                LOGGER.info(String.format("seed ip is : %s ", object.getString(OBJECT_IP)));

                                var status = ObjectStatusCacheStore.getStore().getItem(object.getLong(ID));

                                if (object.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()) && (status == null || status.equalsIgnoreCase(STATUS_UP)))
                                {
                                    probes++;

                                    probe(object, qualifiedObjects, runbook, credentials, qualifiedPlugins, event, object.getString(OBJECT_IP), qualifiedProtocols);
                                }
                            }

                            if (probes == 0)
                            {
                                LOGGER.info(String.format("topology scheduler aborted, reason: %s", ErrorMessageConstants.OBJECT_ERROR));

                                vertx.eventBus().send(EventBusConstants.EVENT_TOPOLOGY_STOP, event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(MESSAGE,
                                        String.format(ErrorMessageConstants.TOPOLOGY_RUN_ERROR, ErrorMessageConstants.OBJECT_ERROR)));
                            }
                        }
                        else
                        {
                            LOGGER.info(String.format("topology scheduler aborted, reason: %s", TOPOLOGY_ENTRY_POINT_MISSING));

                            vertx.eventBus().send(EventBusConstants.EVENT_TOPOLOGY_STOP, event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(MESSAGE,
                                    String.format(ErrorMessageConstants.TOPOLOGY_RUN_ERROR, TOPOLOGY_ENTRY_POINT_MISSING)));
                        }
                    }
                }

                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }).exceptionHandler(LOGGER::error);
        }

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_TOPOLOGY_COMPLETE, message ->
        {
            try
            {
                var event = message.body();

                var dependencies = TopologyCacheStore.getStore().getDependencies(event.getLong(EventBusConstants.EVENT_SCHEDULER));

                // processing all dependencies one by one where destination port is not available
                if (dependencies != null && !dependencies.isEmpty())
                {
                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("dependencies cache after running topology on scheduler id %s is %s", event.getLong(EventBusConstants.EVENT_SCHEDULER), dependencies.encode()));
                    }

                    /* dependencies will be like & from this we will process the dependencies whose destination port value is empty.
                    {
                      "6``||``172.16.8.2``||``172.16.8.2``||``28``||``172.16.10.47": "24",
                      "6``||``172.16.10.47``||``172.16.10.47``||``19``||``172.16.10.43": "10119",
                      "6``||``172.16.10.47``||``172.16.10.47``||``20``||``172.16.10.43": ""
                    }
                   */

                    for (var entry : dependencies.getMap().entrySet())
                    {
                        if (entry.getValue().toString().equalsIgnoreCase(EMPTY_VALUE) && entry.getKey().split(SEPARATOR_WITH_ESCAPE).length == 5)
                        {
                            var tokens = entry.getKey().split(SEPARATOR_WITH_ESCAPE);

                            vertx.eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(),
                                    new JsonObject().put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                                            .put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.ADD.getName())
                                            .put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.valueOfName(CommonUtil.getByteValue(tokens[0])).getName())
                                            .put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, tokens[3])
                                            .put(AIOpsConstants.DEPENDENCY_SOURCE, tokens[2])
                                            .put(AIOpsConstants.DEPENDENCY_SOURCE_PORT, tokens[3])
                                            .put(AIOpsConstants.DEPENDENCY_DESTINATION, tokens[4])
                                            .put(AIOpsConstants.DEPENDENCY_DESTINATION_PORT, EMPTY_VALUE)
                                            .put(AIOpsConstants.DEPENDENCY_PARENT, tokens[1]));
                        }
                    }
                }

                connections.clear();

                LOGGER.info(String.format("topology discovery %s completed successfully....", event.getLong(EventBusConstants.EVENT_SCHEDULER)));

                if (!event.containsKey(TopologyPlugin.TOPOLOGY_PLUGIN_DISCOVERY) && Bootstrap.bootstrapType() == BootstrapType.APP && (Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.STANDALONE.name())) || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.PRIMARY.name()))
                {
                    vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                            .put(CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.COMPLETE_TOPOLOGY.name())
                            .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                            .put(ID, event.getLong(EventBusConstants.EVENT_SCHEDULER)));
                }

                //# 27964
                if (TopologyCacheStore.getStore().topologyRunning(event.getLong(EventBusConstants.EVENT_SCHEDULER)))
                {
                    vertx.eventBus().send(EventBusConstants.EVENT_SUCCEED, new JsonObject().put(EventBusConstants.EVENT_ID, TopologyCacheStore.getStore().getEventId(event.getLong(EventBusConstants.EVENT_SCHEDULER))));

                    complete(event, event.getLong(EventBusConstants.EVENT_SCHEDULER));

                }

            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_TOPOLOGY_STOP, message ->
        {
            try
            {
                var event = message.body();

                if (CommonUtil.getLong(event.getValue(EventBusConstants.EVENT_SCHEDULER)) > 0)
                {
                    abort(event);

                    if (Bootstrap.bootstrapType() == BootstrapType.APP && (Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.STANDALONE.name())) || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.PRIMARY.name()))
                    {
                        vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.STOP_TOPOLOGY.name())
                                .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                                .put(ID, event.getLong(EventBusConstants.EVENT_SCHEDULER)));
                    }

                    //# 27964
                    if (TopologyCacheStore.getStore().topologyRunning(event.getLong(EventBusConstants.EVENT_SCHEDULER)))
                    {
                        vertx.eventBus().send(EventBusConstants.EVENT_ABORT, event.put(EventBusConstants.EVENT_ID, TopologyCacheStore.getStore().getEventId(event.getLong(EventBusConstants.EVENT_SCHEDULER))));

                        complete(event, event.getLong(EventBusConstants.EVENT_SCHEDULER));
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_TOPOLOGY, message ->
        {
            var event = message.body();

            try
            {
                eventsByPluginType.get(NMSConstants.TopologyPluginType.valueOfName(event.getString(TopologyPlugin.TOPOLOGY_PLUGIN_TYPE))).add(event.getLong(EventBusConstants.EVENT_ID));

                events.put(event.getLong(EventBusConstants.EVENT_ID), event);

                if (!timeHandlerActive)
                {
                    eventProbes = 0;

                    timeHandlerActive = true;

                    vertx.setPeriodic(500, timer ->
                    {
                        if (!events.isEmpty())
                        {
                            eventProbes = 0;

                            if (idleWorkers.get() > 0)
                            {
                                var topologyPluginTypes = new ArrayList<NMSConstants.TopologyPluginType>();

                                var pendingEventsByTopologyPluginType = new EnumMap<NMSConstants.TopologyPluginType, Integer>(NMSConstants.TopologyPluginType.class);

                                var pendingEvents = 0;

                                var batchEvents = new ArrayList<List<Long>>();

                                var pluginEngines = new ArrayList<PluginEngineConstants.PluginEngine>();  // for plugin engine selection (python/go)

                                for (var pluginType : NMSConstants.TopologyPluginType.values())
                                {
                                    pendingEvents += dequeue(pluginType, topologyPluginTypes, pendingEventsByTopologyPluginType, batchEvents, pluginEngines, pluginType == NMSConstants.TopologyPluginType.SNMP
                                            ? PluginEngineConstants.PluginEngine.GO : PluginEngineConstants.PluginEngine.PYTHON);
                                }

                                if (idleWorkers.get() > 0 && pendingEvents > 0)
                                {
                                    dequeue(pendingEventsByTopologyPluginType, topologyPluginTypes, batchEvents, pluginEngines);
                                }

                                for (var index = 0; index < batchEvents.size(); index++)
                                {
                                    var eventIds = new ArrayList<Long>();

                                    var contexts = new HashMap<Long, JsonObject>();

                                    var topologyPluginType = topologyPluginTypes.get(index);

                                    var pluginEngine = pluginEngines.get(index);

                                    var timeout = 60;

                                    for (var id : batchEvents.get(index))
                                    {
                                        var context = events.remove(id);

                                        if (context != null)
                                        {
                                            var eventId = context.getLong(EventBusConstants.EVENT_ID);

                                            if (EventCacheStore.getStore().validItem(eventId))
                                            {
                                                eventIds.add(eventId);

                                                EventBusConstants.updateEvent(eventId, String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_QUALIFIED, DateTimeUtil.timestamp()));

                                                contexts.put(eventId, context);

                                                if (context.containsKey(TIMEOUT) && context.getInteger(TIMEOUT) > timeout)
                                                {
                                                    timeout = context.getInteger(TIMEOUT);
                                                }
                                            }
                                        }
                                    }

                                    if (!contexts.isEmpty())
                                    {
                                        var context = contexts.values().stream().findFirst().get();

                                        context.put(TIMEOUT, timeout); // timeout will be contexts max timeout....as of now we don't think we need batch based on timeout value same as metric poller...in future if we found any scalability issues we will apply it.

                                        workerExecutor.<Void>executeBlocking(future ->
                                        {
                                            try
                                            {
                                                for (var eventId : eventIds)
                                                {
                                                    EventBusConstants.startEvent(eventId, Thread.currentThread().getName());
                                                }

                                                WorkerUtil.spawnWorker(contexts, context, eventIds, context.getInteger(TIMEOUT), false, pluginEngine, System.currentTimeMillis(), true);
                                            }
                                            catch (Exception exception)
                                            {
                                                LOGGER.error(exception);
                                            }
                                            finally
                                            {
                                                future.complete();
                                            }
                                        }, false, result -> complete(topologyPluginType));
                                    }
                                    else
                                    {
                                        complete(topologyPluginType);
                                    }
                                }
                            }
                        }
                        else
                        {
                            eventProbes++;
                        }

                        if (eventProbes > 10) //max 10 probes...
                        {
                            //disable timer to save cpu cycle...

                            vertx.cancelTimer(timer);

                            timeHandlerActive = false;
                        }
                    });
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        promise.complete();
    }

    private JsonObject getNextHopRunbook()
    {
        var runbook = RunbookPluginConfigStore.getStore().getItem(Runbook.RunbookPluginId.SNMP_NEXT_HOP.getName());

        if (runbook != null && runbook.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT))
        {
            runbook.mergeIn(runbook.getJsonObject(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT));

            runbook.remove(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT);
        }

        return runbook;
    }

    private void probe(JsonObject object, Set<String> qualifiedObjects, JsonObject runbook, Map<Long, Long> credentials, Map<Long, List<JsonObject>> qualifiedPlugins, JsonObject event, String parent, JsonArray qualifiedProtocols)
    {
        try
        {
            if (!qualifiedObjects.contains(object.getString(OBJECT_IP)))
            {
                qualifiedObjects.add(object.getString(OBJECT_IP));

                var schedulerId = event.getLong(EventBusConstants.EVENT_SCHEDULER);

                // for auto discovery after monitor provision we don't have event.scheduler so in that case scheduler id is null
                // for manual topology discovery ran from topology scanner we need to check that it's still running or not as if someone abort the scheduler don't need to do next probe
                if (TopologyCacheStore.getStore().topologyRunning(schedulerId))
                {
                    updateProbe(event, 1);

                    var promise = Promise.<JsonObject>promise();

                    // ping runbook context is not null
                    // for auto discovery need to check next hop probe as at that time we don't rely routing protocol metric as it's totally background process and we don't know exact time to complete it
                    // for manual discovery need to check that routing protocol metric is present
                    if (runbook != null && (event.containsKey(TopologyPlugin.TOPOLOGY_PLUGIN_DISCOVERY) || MetricConfigStore.getStore().getItemsByObject(object.getLong(ID)).stream().anyMatch(item -> item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.ROUTING.getName()))))
                    {
                        var context = new JsonObject().mergeIn(event).mergeIn(object).mergeIn(runbook).put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray(new ArrayList<Long>(1)).add(object.getLong(ID)))
                                .put(EventBusConstants.EVENT_REPLY, YES)
                                .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()).put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_RUNBOOK);

                        context.remove(EventBusConstants.EVENT_SCHEDULER);

                        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_RUNBOOK, context, DELIVERY_OPTIONS, reply ->
                        {
                            JsonObject items = null;

                            if (reply.succeeded())
                            {
                                try
                                {
                                    var results = reply.result().body();

                                    if (results != null && results.containsKey(EventBusConstants.EVENT_REPLY_CONTEXTS) && !results.getJsonArray(EventBusConstants.EVENT_REPLY_CONTEXTS).isEmpty())
                                    {
                                        var result = results.getJsonArray(EventBusConstants.EVENT_REPLY_CONTEXTS).getJsonObject(0);

                                        if (result != null && CommonUtil.isNotNullOrEmpty(result.getString(STATUS)) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                                        {
                                            // result is only ip's ["***********", "***********"]
                                            items = result.getJsonObject(RESULT);

                                            if (CommonUtil.debugEnabled())
                                            {
                                                LOGGER.debug(String.format("monitor %s next-hops :- %s", object.getString(OBJECT_IP), items));
                                            }
                                        }

                                        results.remove(EventBusConstants.EVENT_REPLY_CONTEXTS);
                                    }
                                }

                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            }

                            promise.complete(new JsonObject().mergeIn(event).mergeIn(object).put(NMSConstants.TOPOLOGY_NEXT_HOPS, items));
                        });
                    }
                    else
                    {
                        promise.complete(new JsonObject().mergeIn(event).mergeIn(object));
                    }

                    promise.future().onComplete(response ->
                    {
                        try
                        {
                            var context = response.result();

                            // return assigned topology plugin for that monitor if present else null
                            var topologyPlugins = qualifiedPlugins.getOrDefault(context.getLong(ID), null);

                            var protocols = new JsonArray();

                            for (var entry : protocolsByLinkLayer.entrySet())
                            {
                                if (event.getJsonArray(TOPOLOGY_LINK_LAYER).contains(entry.getKey()) && CommonUtil.isNotNullOrEmpty(qualifiedProtocols))
                                {
                                    for (var value : entry.getValue())
                                    {
                                        var protocol = CommonUtil.getString(value);

                                        if (protocol != null && qualifiedProtocols.contains(protocol) && object.containsKey(AIOpsObject.OBJECT_VENDOR) && object.getString(AIOpsObject.OBJECT_VENDOR) != null)          // not getting object vendor for wireless monitor
                                        {
                                            var valid = !protocol.equalsIgnoreCase(TopologyProtocol.CDP.getName()) || object.getString(AIOpsObject.OBJECT_VENDOR).equalsIgnoreCase(CISCO_VENDOR);

                                            if (protocolsByLinkLayer.get(TopologyLinkLayer.L3.getName()).contains(protocol)
                                                    && MetricConfigStore.getStore().getItemsByObject(object.getLong(ID)).stream().noneMatch(item -> item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.ROUTING.getName())))
                                            {
                                                valid = false;
                                            }

                                            if (valid)
                                            {
                                                protocols.add(protocol);
                                            }
                                        }
                                    }
                                }
                            }

                            context.put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()).put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_TOPOLOGY);

                            var contexts = new HashMap<Byte, List<JsonObject>>();

                            var itemProbes = new HashMap<Byte, Integer>();

                            var items = new HashMap<Byte, JsonArray>();

                            var size = new AtomicInteger(0);

                            if (topologyPlugins != null && !topologyPlugins.isEmpty())
                            {
                                for (var topologyPlugin : topologyPlugins)
                                {
                                    if (protocols.contains(topologyPlugin.getString(TOPOLOGY_PLUGIN_PROTOCOL)))
                                    {
                                        if (topologyPlugin.containsKey(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT))
                                        {
                                            topologyPlugin.mergeIn(topologyPlugin.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT));

                                            topologyPlugin.remove(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);
                                        }

                                        var level = NMSConstants.LEVELS_BY_NETWORK_PROTOCOLS.get(topologyPlugin.getString(TOPOLOGY_PLUGIN_PROTOCOL));

                                        update(items, itemProbes, level, contexts);

                                        contexts.get(level).add(new JsonObject().mergeIn(topologyPlugin).mergeIn(context)
                                                .put(TIMEOUT, topologyPlugin.containsKey(TIMEOUT) ? topologyPlugin.getInteger(TIMEOUT) : context.getInteger(TIMEOUT, 60))
                                                .put(EventBusConstants.EVENT_SCHEDULER, schedulerId)
                                                .put(AIOpsConstants.DEPENDENCY_LEVEL, level)
                                                .put(NMSConstants.OBJECT, topologyPlugin.getString(TOPOLOGY_PLUGIN_PROTOCOL))
                                                .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_TOPOLOGY));

                                        protocols.remove(topologyPlugin.getString(TOPOLOGY_PLUGIN_PROTOCOL));

                                        size.getAndIncrement();
                                    }
                                }
                            }

                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug("Protocols qualified : " + protocols + " for monitor " + object.getString(OBJECT_IP));
                            }

                            // if any protocol is still pending route it from default json
                            if (!protocols.isEmpty() && CommonUtil.isNotNullOrEmpty(context.getString(AIOpsObject.OBJECT_VENDOR)))
                            {
                                var probes = TopologyCacheStore.getStore().getItems(context.getString(AIOpsObject.OBJECT_VENDOR));

                                var iterator = protocols.iterator();

                                while (iterator.hasNext())
                                {
                                    var protocol = CommonUtil.getString(iterator.next());

                                    if (probes.containsKey(protocol))
                                    {
                                        iterator.remove();

                                        var level = NMSConstants.LEVELS_BY_NETWORK_PROTOCOLS.get(protocol);

                                        update(items, itemProbes, level, contexts);

                                        var probe = probes.getJsonObject(protocol);

                                        if (probe.containsKey(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT))
                                        {
                                            probe.mergeIn(probe.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT));

                                            probes.remove(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);
                                        }

                                        contexts.get(level).add(new JsonObject().mergeIn(probe).mergeIn(context)
                                                .put(TIMEOUT, probe.getInteger(TIMEOUT, context.getInteger(TIMEOUT, 60)))
                                                .put(AIOpsConstants.DEPENDENCY_LEVEL, level)
                                                .put(NMSConstants.OBJECT, protocol).put(EventBusConstants.EVENT_SCHEDULER, schedulerId)
                                                .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_TOPOLOGY));

                                        size.getAndIncrement();
                                    }
                                }
                            }

                            if (!contexts.isEmpty())
                            {
                                updateProbe(context, size.get() - 1);

                                event.put(EventBusConstants.EVENT_SCHEDULER, schedulerId);

                                event.put(AIOpsConstants.DEPENDENCY_PARENT, parent);

                                var existingChildren = new JsonArray();

                                 /*
                                      fetch all existing parent connections to refrain from deleting by Dependency Manager.
                                      why need this logic ? Answer is that if topology ran from seed ip 8.2 and if 8.2(10) -> 10.47(19) connection is found then,
                                      reverse connection i.e. 10.47(19) -> 8.2(10) will also be added but when topology will run from child node 10.47 then,
                                      reverse connection i.e. i.e. 10.47(19) -> 8.2(10) can be deleted which can show destination link as NA in UI which is wrong.
                                      To tackle this issue, connectedLinks in TopologyCacheStore is maintained where parent -> array of connected ports will be maintained
                                      which means save these reverse connections from deleting.
                                    */
                                if (TopologyCacheStore.getStore().getConnectedLinks(schedulerId, context.getString(OBJECT_IP)) != null)
                                {
                                    existingChildren.addAll(TopologyCacheStore.getStore().getConnectedLinks(schedulerId, context.getString(OBJECT_IP)));
                                }

                                // list down existing dependencies for requested monitor
                                var mappers = DependencyMapperConfigStore.getStore().getItemsByObject(context.getString(OBJECT_IP));

                                //remove auto dependency from dependency mapper collection also...
                                var iterator = mappers.get(DependencyMapper.PARENTS).iterator();

                                var ids = new JsonArray();

                                while (iterator.hasNext())
                                {
                                    var item = JsonObject.mapFrom(iterator.next());

                                    if (item.getString(ConfigDBConstants.FIELD_TYPE).equalsIgnoreCase(ConfigDBConstants.ENTITY_TYPE_SYSTEM) &&
                                            contexts.containsKey(AIOpsConstants.getDependencyLevel(item, false)))
                                    {
                                        ids.add(item.getLong(ID));

                                        iterator.remove();

                                    }

                                    // excludeChildren for persisting manual link
                                    if (!item.getString(ConfigDBConstants.FIELD_TYPE).equalsIgnoreCase(ConfigDBConstants.ENTITY_TYPE_SYSTEM) && item.containsKey(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT))
                                    {

                                        existingChildren.add(item.getJsonObject(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT).getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE));

                                    }
                                }

                                // #24881

                                vertx.eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(), new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, context.getString(OBJECT_IP))
                                        .put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()).put(OBJECT_IP, context.getString(OBJECT_IP))
                                        .put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.MINUS_ONE.getName()).put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.REMOVE_PARENT.getName()).put(DependencyMapper.EXISTING_CHILDREN, existingChildren));

                                var future = Promise.<Void>promise();

                                if (!ids.isEmpty())
                                {
                                    Bootstrap.configDBService().deleteAll(ConfigDBConstants.COLLECTION_DEPENDENCY_MAPPER,
                                            new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, ids),
                                            DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                            result ->
                                            {
                                                DependencyMapperConfigStore.getStore().deleteItems(ids);

                                                future.complete();
                                            });
                                }

                                else
                                {
                                    future.complete();
                                }

                                future.future().onComplete(asyncResult ->
                                {

                                    var connectedObjects = new ArrayList<JsonObject>();

                                    var futures = new ArrayList<Future<Void>>();

                                    for (var entry : contexts.entrySet())
                                    {
                                        if (!entry.getValue().isEmpty())
                                        {
                                            var asyncFuture = Promise.<Void>promise();

                                            futures.add(asyncFuture.future());

                                            entry.getValue().forEach(eventContext ->
                                            {
                                                eventContext.put(EventBusConstants.EVENT, event);

                                                eventContext.mergeIn(getCredential(eventContext, credentials));

                                                route(eventContext, qualifiedObjects, items, itemProbes, mappers, connectedObjects, asyncFuture);
                                            });
                                        }
                                    }

                                    // probe connected neighbors only when requested monitor's all protocol discovery successfully completed...otherwise sometimes child override in parent
                                    Future.join(futures).onComplete(result ->
                                    {
                                        for (var connectedObject : connectedObjects)
                                        {
                                            if (!qualifiedObjects.contains(connectedObject.getString(OBJECT_IP)))
                                            {
                                                probe(connectedObject, qualifiedObjects, runbook, credentials, qualifiedPlugins, event, context.getString(OBJECT_IP), qualifiedProtocols);
                                            }
                                        }

                                        for (var index = 0; index < size.get(); index++)
                                        {
                                            updateProbe(context);
                                        }
                                    });

                                });
                            }
                            else
                            {
                                updateProbe(context);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void update(Map<Byte, JsonArray> items, Map<Byte, Integer> itemProbes, Byte level, Map<Byte, List<JsonObject>> contexts)
    {
        itemProbes.putIfAbsent(level, 0);

        itemProbes.put(level, itemProbes.get(level) + 1);

        items.computeIfAbsent(level, value -> new JsonArray(new ArrayList<JsonObject>(1)));

        contexts.computeIfAbsent(level, value -> new ArrayList<>(1));
    }

    private void updateProbe(JsonObject event, int count)
    {
        SchedulerCacheStore.getStore().updatePendingProbes(event.getLong(EventBusConstants.EVENT_SCHEDULER),
                SchedulerCacheStore.getStore().getSchedulerPendingProbes(event.getLong(EventBusConstants.EVENT_SCHEDULER)) + count);
    }

    private void updateProbe(JsonObject event)
    {
        SchedulerCacheStore.getStore().updatePendingProbes(event.getLong(EventBusConstants.EVENT_SCHEDULER));

        if (SchedulerCacheStore.getStore().getSchedulerPendingProbes(event.getLong(EventBusConstants.EVENT_SCHEDULER)) == 0)
        {
            event.remove(MESSAGE);

            vertx.eventBus().send(EventBusConstants.EVENT_TOPOLOGY_COMPLETE, event);
        }
    }

    private void route(JsonObject context, Set<String> qualifiedObjects, Map<Byte, JsonArray> items, Map<Byte, Integer> itemProbes, Map<String, List<JsonObject>> dependencyMappers, List<JsonObject> connectedObjects, Promise<Void> promise)
    {
        try
        {
            var eventId = CommonUtil.newEventId();

            var event = JsonObject.mapFrom(context.remove(EventBusConstants.EVENT));

            var parent = CommonUtil.getString(event.remove(AIOpsConstants.DEPENDENCY_PARENT));

            var objectIP = context.getString(OBJECT_IP);

            var store = TopologyCacheStore.getStore();

            var schedulerId = event.getLong(EventBusConstants.EVENT_SCHEDULER);

            context.put(EventBusConstants.EVENT_REPLY, YES)
                    .put(EventBusConstants.EVENT_ID, context).put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_TOPOLOGY);

            vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, context.put(EventBusConstants.EVENT_ID, eventId))
                    .put(EventBusConstants.EVENT_ID, eventId)
                    .put(USER_NAME, context.containsKey(USER_NAME) ? context.getString(USER_NAME) : DEFAULT_USER)
                    .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                    .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_TOPOLOGY));

            var level = CommonUtil.getByteValue(context.getValue(AIOpsConstants.DEPENDENCY_LEVEL));

            vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_ROUTER, context, DELIVERY_OPTIONS, reply ->
            {
                itemProbes.put(level, itemProbes.get(level) != null ? itemProbes.get(level) - 1 : 0);

                if (reply.succeeded())
                {
                    try
                    {
                        var response = reply.result().body();

                        if (response != null && response.getString(STATUS) != null && response.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                        {
                            var neighbors = response.getJsonArray(RESULT);

                            if (neighbors != null && !neighbors.isEmpty())
                            {
                                if (CommonUtil.debugEnabled())
                                {
                                    LOGGER.debug(String.format("topology discovery results for object %s and protocol %s :- %s", objectIP, context.getString(TOPOLOGY_PLUGIN_PROTOCOL), neighbors));
                                }

                                JsonObject transformedObjects = null;

                                JsonObject objects = null;

                                JsonObject interfaceAliases = null;

                                JsonObject interfaceNames = null;

                                var metric = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(response.getLong(ID), NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()));

                                if (metric != null)
                                {
                                    // for custom ssh topology we are getting interface name instead of interface index so need to swap it here
                                    if (response.getString(TopologyPlugin.TOPOLOGY_PLUGIN_TYPE).equalsIgnoreCase(NMSConstants.TopologyPluginType.CUSTOM.getName()) && response.getString(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL).equalsIgnoreCase(NMSConstants.Protocol.SSH.getName()))
                                    {
                                        transformedObjects = MetricConfigStore.getStore().getObjects(metric.getJsonObject(Metric.METRIC_CONTEXT), NMSConstants.INTERFACE_NAME, INTERFACE_INDEX).mergeIn(MetricConfigStore.getStore().getObjects(metric.getJsonObject(Metric.METRIC_CONTEXT), NMSConstants.INTERFACE_DESCRIPTION, INTERFACE_INDEX));
                                    }

                                    objects = MetricConfigStore.getStore().getObjects(metric.getJsonObject(Metric.METRIC_CONTEXT), INTERFACE_INDEX, INTERFACE_NAME);

                                    interfaceAliases = MetricConfigStore.getStore().getObjects(metric.getJsonObject(Metric.METRIC_CONTEXT), INTERFACE_ALIAS, INTERFACE_INDEX);

                                    interfaceNames = MetricConfigStore.getStore().getObjects(metric.getJsonObject(Metric.METRIC_CONTEXT), INTERFACE_NAME, INTERFACE_INDEX);
                                }
                                else
                                {
                                    LOGGER.warn(String.format("failed to build dependency for network object %s, reason: no active interfaces found", context.getString(AIOpsObject.OBJECT_NAME)));

                                    updateProbe(context);
                                }

                                if (objects != null)
                                {
                                    var filters = context.getJsonArray(TOPOLOGY_PLUGIN_FILTER_TARGETS);

                                    var hops = context.getJsonObject(NMSConstants.TOPOLOGY_NEXT_HOPS);

                                    var dependencies = new JsonArray();

                                    for (var index = 0; index < neighbors.size(); index++)
                                    {
                                        var neighbor = neighbors.getJsonObject(index);

                                        if (CommonUtil.isNotNullOrEmpty(neighbor.getString(NMSConstants.OBJECT)) && CommonUtil.isNotNullOrEmpty(neighbor.getString(INTERFACE)))
                                        {
                                            var destination = neighbor.getString(NMSConstants.OBJECT);

                                            if (!APIConstants.PATTERN_IP_ADDRESS.matcher(destination).find() && !APIConstants.PATTERN_IPV6_ADDRESS.matcher(destination).find() && !destination.contains(":") && ObjectConfigStore.getStore().getItemByValue(OBJECT_NAME, destination) != null)
                                            {
                                                destination = ObjectConfigStore.getStore().getItemByValue(OBJECT_NAME, destination).getString(OBJECT_IP);
                                            }

                                            // filter connected neighbor from our mac scanner store
                                            // for ex.. some protocol return mac address and some return interface ip address so at the end we need device ip address so return it if exist
                                            destination = MACScannerConfigStore.getStore().getItemByMACAddress(destination) != null ? MACScannerConfigStore.getStore().getItemByMACAddress(destination) :
                                                    MACScannerConfigStore.getStore().getItemByIPAddress(destination) != null ? MACScannerConfigStore.getStore().getItemByIPAddress(destination) : destination;

                                            // ignore mac address destination
                                            if ((APIConstants.PATTERN_IP_ADDRESS.matcher(destination).find() || APIConstants.PATTERN_IPV6_ADDRESS.matcher(destination).find()) && !objectIP.equalsIgnoreCase(destination))
                                            {
                                                var connectedLink = transformedObjects != null && response.getString(TopologyPlugin.TOPOLOGY_PLUGIN_TYPE).equalsIgnoreCase(TopologyPluginType.CUSTOM.getName()) && response.getString(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL).equalsIgnoreCase(Protocol.SSH.getName()) ?
                                                        transformedObjects.getString(neighbor.getString(INTERFACE)) : neighbor.getString(INTERFACE);

                                                // if we get port subtype as 1 means interface alias, so we need to resolve interface alias to interface index
                                                // if we get port subtype as 5 means interface name, so we need to resolve interface name to interface index
                                                if (neighbor.containsKey(INTERFACE + ".port.subtype"))
                                                {
                                                    // in case of port subtype 5 , check if interface name is present in named objects, if not match it using normalised(shorten) interface name
                                                    switch (NMSConstants.LLDPPortSubType.valueOfName(neighbor.getString(INTERFACE + ".port.subtype")))
                                                    {
                                                        case ONE ->
                                                                connectedLink = interfaceAliases.getString(neighbor.getString(INTERFACE));

                                                        case THREE ->
                                                        {
                                                            var item = MACScannerConfigStore.getStore().getItemByValue(MACScanner.MAC_SCANNER_ADDRESS, neighbor.getString(INTERFACE));

                                                            if (item != null && CommonUtil.isNotNullOrEmpty(item.getString(MACScanner.MAC_SCANNER_INTERFACE)))
                                                            {
                                                                connectedLink = interfaceNames.getString(item.getString(MACScanner.MAC_SCANNER_INTERFACE)) != null ? interfaceNames.getString(item.getString(MACScanner.MAC_SCANNER_INTERFACE)) : normalizeInterfaceName(interfaceNames, item.getString(MACScanner.MAC_SCANNER_INTERFACE));
                                                            }
                                                            else
                                                            {
                                                                connectedLink = null;
                                                            }
                                                        }

                                                        case FIVE ->
                                                                connectedLink = interfaceNames.getString(neighbor.getString(INTERFACE)) != null ? interfaceNames.getString(neighbor.getString(INTERFACE)) : normalizeInterfaceName(interfaceNames, neighbor.getString(INTERFACE));

                                                        default -> connectedLink = null;
                                                    }
                                                }

                                                // means interface must be provisioned in the system
                                                if (CommonUtil.isNotNullOrEmpty(connectedLink) && objects.containsKey(connectedLink))
                                                {
                                                    var dependency = new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE_PORT, connectedLink).put(AIOpsConstants.DEPENDENCY_SOURCE, objectIP)
                                                            .put(AIOpsConstants.DEPENDENCY_DESTINATION, destination);

                                                    // for cdp/lldp/spm topology discovery
                                                    if (level.equals(AIOpsConstants.DependencyLevel.SIX.getName()))
                                                    {
                                                        // in incremental discovery of topology we can't predict parent child and in that case parent is null so in that case only predict parent child based on next hops if available

                                                        dependency.put(AIOpsConstants.DEPENDENCY_PARENT, hops != null && hops.containsKey(objectIP) && hops.getString(objectIP).equalsIgnoreCase(connectedLink) ? destination : destination.equalsIgnoreCase(parent) || objectIP.equalsIgnoreCase(parent) ? parent : objectIP);

                                                        var connection = destination + VALUE_SEPARATOR + objectIP;

                                                        // for ring topology if we don't find parent from next hop we need to restrict duplicate parent scenario
                                                        if (connections.containsKey(connection))
                                                        {
                                                            var immediateParent = connections.get(connection);

                                                            if (!immediateParent.equalsIgnoreCase(dependency.getString(AIOpsConstants.DEPENDENCY_PARENT)))
                                                            {
                                                                dependency.put(AIOpsConstants.DEPENDENCY_PARENT, immediateParent);
                                                            }
                                                        }

                                                        else
                                                        {
                                                            connection = objectIP + VALUE_SEPARATOR + destination;

                                                            if (connections.containsKey(connection))
                                                            {
                                                                var immediateParent = connections.get(connection);

                                                                if (!immediateParent.equalsIgnoreCase(dependency.getString(AIOpsConstants.DEPENDENCY_PARENT)))
                                                                {
                                                                    dependency.put(AIOpsConstants.DEPENDENCY_PARENT, immediateParent);
                                                                }
                                                            }

                                                            else
                                                            {
                                                                connections.put(connection, dependency.getString(AIOpsConstants.DEPENDENCY_PARENT));
                                                            }
                                                        }
                                                    }

                                                    var manualLink = false;

                                                    // check that same dependency is already exist in dependency mapper or not and if exists then update child based on it and only send to dependency manager for update and do not need to send to update in dependency mapper
                                                    for (var parentDependency : dependencyMappers.get(DependencyMapper.PARENTS))
                                                    {
                                                        if (AIOpsConstants.getDependencyLevel(parentDependency, true).equals(level) && CommonUtil.isNotNullOrEmpty(parentDependency.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE))
                                                                && parentDependency.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE).equalsIgnoreCase(connectedLink))
                                                        {
                                                            manualLink = true;

                                                            destination = parentDependency.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD);

                                                            dependency.put(AIOpsConstants.DEPENDENCY_DESTINATION, parentDependency.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD));

                                                            break;
                                                        }
                                                    }

                                                    var destinationObject = ObjectConfigStore.getStore().getItemByIP(destination) != null ?
                                                            ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP(destination)) : null;

                                                    // only qualify if destination(connected device) is provisioned in system
                                                    // #28152 (excluded target should not proceed further)
                                                    if (destinationObject != null && (filters.isEmpty() || (context.containsKey(TOPOLOGY_PLUGIN_FILTER_TARGET_TYPE) && context.getString(TOPOLOGY_PLUGIN_FILTER_TARGET_TYPE).contains("include") == filters.contains(destination))))
                                                    {
                                                        var reverseLink = neighbor.getString(INTERFACE_NAME);

                                                        if (reverseLink != null && (CommonUtil.isNotNullOrEmpty(reverseLink) && NETWORK_DEVICES.contains(destinationObject.getString(AIOpsObject.OBJECT_TYPE))))
                                                        {
                                                            metric = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(destinationObject.getLong(ID), NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()));

                                                            if (metric != null)
                                                            {
                                                                var convertedObjects = MetricConfigStore.getStore().getObjects(metric.getJsonObject(Metric.METRIC_CONTEXT), INTERFACE_NAME, INTERFACE_INDEX)
                                                                        .mergeIn(MetricConfigStore.getStore().getObjects(metric.getJsonObject(Metric.METRIC_CONTEXT), INTERFACE_DESCRIPTION, INTERFACE_INDEX));


                                                                var interfaceObjects = MetricConfigStore.getStore().getObjects(metric.getJsonObject(Metric.METRIC_CONTEXT), INTERFACE_ALIAS, INTERFACE_NAME);

                                                                if (neighbor.containsKey(INTERFACE_NAME + ".port.subtype"))
                                                                {
                                                                    switch (NMSConstants.LLDPPortSubType.valueOfName(neighbor.getString(INTERFACE_NAME + ".port.subtype")))
                                                                    {
                                                                        case ONE ->
                                                                                reverseLink = interfaceObjects.getString(neighbor.getString(INTERFACE_NAME));

                                                                        case THREE ->
                                                                                reverseLink = MACScannerConfigStore.getStore().getItemByValue(MACScanner.MAC_SCANNER_ADDRESS, neighbor.getString(INTERFACE_NAME)) != null ? MACScannerConfigStore.getStore().getItemByValue(MACScanner.MAC_SCANNER_ADDRESS, neighbor.getString(INTERFACE_NAME)).getString(MACScanner.MAC_SCANNER_INTERFACE) : null;

                                                                        case FIVE ->
                                                                        {
                                                                        }
                                                                    }
                                                                }

                                                                // in case of port subtype 5 , check if interface name is present in named objects, if not match it using normalised(shorten) interface name
                                                                reverseLink = reverseLink != null && convertedObjects.containsKey(reverseLink) ? CommonUtil.getString(convertedObjects.getValue(reverseLink)) : normalizeInterfaceName(convertedObjects, reverseLink);

                                                                if (CommonUtil.isNotNullOrEmpty(reverseLink))
                                                                {
                                                                    store.addConnectedLink(schedulerId, destination, reverseLink);

                                                                    dependency.put(AIOpsConstants.DEPENDENCY_DESTINATION_PORT, reverseLink);
                                                                }
                                                            }
                                                        }

                                                        // 1. either not network type devices then valid
                                                        // 2. either reverse link is not found then valid
                                                        // 3. if network devices and reverse link found for CDP then check that interface is provisioned into system or not
                                                        if (!NETWORK_DEVICES.contains(destinationObject.getString(AIOpsObject.OBJECT_TYPE)) || reverseLink == null || CommonUtil.isNotNullOrEmpty(dependency.getString(AIOpsConstants.DEPENDENCY_DESTINATION_PORT)))
                                                        {
                                                            if (!manualLink)
                                                            {
                                                                var dependencyContext = new JsonObject().put(DependencyMapper.DEPENDENCY_MAPPER_PARENT, objectIP)
                                                                        .put(DependencyMapper.DEPENDENCY_MAPPER_CHILD, destination)
                                                                        .put(DependencyMapper.DEPENDENCY_MAPPER_ARCHIVED, NO)
                                                                        .put(ConfigDBConstants.FIELD_TYPE, ConfigDBConstants.ENTITY_TYPE_SYSTEM)
                                                                        .put(ID, CommonUtil.newId())
                                                                        .put(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT, new JsonObject()
                                                                                .put(DependencyMapper.DEPENDENCY_MAPPER_LINK_LAYER, level.equals(AIOpsConstants.DependencyLevel.SIX.getName()) ? DependencyMapper.DependencyMapperLinkLayer.L2.getName() : DependencyMapper.DependencyMapperLinkLayer.L3.getName())
                                                                                .put(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE, connectedLink));

                                                                var destinationPort = dependency.containsKey(AIOpsConstants.DEPENDENCY_DESTINATION_PORT) ? dependency.getString(AIOpsConstants.DEPENDENCY_DESTINATION_PORT) : EMPTY_VALUE;

                                                                var dependencyParent = level.equals(AIOpsConstants.DependencyLevel.SIX.getName()) ? dependency.getString(AIOpsConstants.DEPENDENCY_PARENT) : EMPTY_VALUE;

                                                                var forwardLink = level + SEPARATOR + dependencyParent + SEPARATOR + dependency.getString(AIOpsConstants.DEPENDENCY_SOURCE) + SEPARATOR + dependency.getString(AIOpsConstants.DEPENDENCY_SOURCE_PORT) + SEPARATOR + dependency.getString(AIOpsConstants.DEPENDENCY_DESTINATION);

                                                                var backwardLink = level + SEPARATOR + dependencyParent + SEPARATOR + dependency.getString(AIOpsConstants.DEPENDENCY_DESTINATION) + SEPARATOR + destinationPort + SEPARATOR + dependency.getString(AIOpsConstants.DEPENDENCY_SOURCE);

                                                                /* case 1: For an example, if the store contains 8.2(10) -> 10.47(NA) and now we got dependency 10.47(15) -> 8.2(10),
                                                                 *  In this case we will reverse the link & check if already present store link needs updation or not.
                                                                 *  If yes then update destination port of store link with source port of reverse link & then process the link. */

                                                                if (!destinationPort.equalsIgnoreCase(EMPTY_VALUE) && store.existDependency(schedulerId, backwardLink) && store.getInterfaceByDependency(schedulerId, backwardLink).equalsIgnoreCase(EMPTY_VALUE))
                                                                {
                                                                    store.updateDependency(schedulerId, backwardLink, dependency.getString(AIOpsConstants.DEPENDENCY_SOURCE_PORT));

                                                                    dependencies.add(dependency);
                                                                }

                                                                /* case 2 : For an example, if the store contains 8.2(30) -> 10.1(NA) and now we got dependency 8.2(30) -> 10.1(4),
                                                                 *  In this case we will update the destination port of store link with destination port of current link & then process it. */

                                                                else if (!destinationPort.equalsIgnoreCase(EMPTY_VALUE) && store.existDependency(schedulerId, forwardLink) && store.getInterfaceByDependency(schedulerId, forwardLink).equalsIgnoreCase(EMPTY_VALUE))
                                                                {
                                                                    store.updateDependency(schedulerId, forwardLink, destinationPort);

                                                                    dependencies.add(dependency);
                                                                }

                                                                /* case 3 : For an example, if the store contains 8.2(30) -> 10.1(4) and now we got dependency 10.1(4) -> 8.2(30),
                                                                 *  In this case we will process the link because this dependency is already there in store but for UI hierarchy purpose we will process it. */

                                                                else if (!destinationPort.equalsIgnoreCase(EMPTY_VALUE) && store.existDependency(schedulerId, backwardLink) && !store.getInterfaceByDependency(schedulerId, backwardLink).equalsIgnoreCase(dependency.getString(AIOpsConstants.DEPENDENCY_DESTINATION_PORT)))
                                                                {
                                                                    store.updateDependency(schedulerId, backwardLink, dependency.getString(AIOpsConstants.DEPENDENCY_SOURCE_PORT));

                                                                    dependencies.add(dependency);
                                                                }

                                                                /* case 4 : If dependency is not there in store then add it to store.
                                                                 Then process the dependency if dependency destination port is available. */

                                                                else if (!store.existDependency(schedulerId, forwardLink))
                                                                {
                                                                    store.updateDependency(schedulerId, forwardLink, destinationPort);

                                                                    if (!destinationPort.equalsIgnoreCase(EMPTY_VALUE))
                                                                    {
                                                                        dependencies.add(dependency);
                                                                    }
                                                                }


                                                                if (CommonUtil.isNotNullOrEmpty(dependency.getString(AIOpsConstants.DEPENDENCY_DESTINATION_PORT)))
                                                                {
                                                                    dependencyContext.getJsonObject(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT)
                                                                            .put(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE, dependency.getString(AIOpsConstants.DEPENDENCY_DESTINATION_PORT));
                                                                }

                                                                // send notification for dependency mapper add dependencies
                                                                vertx.eventBus().send(EventBusConstants.EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(OBJECT_IP, parent).put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_DEPENDENCY_MAPPER.name())
                                                                        .put(EventBusConstants.EVENT_CONTEXT, dependencyContext));
                                                            }

                                                            else
                                                            {
                                                                LOGGER.info(String.format("failed to update dependency %s into dependency mapper, reason: %s", dependency, "already exists"));
                                                            }

                                                            // rerun discovery if manual run discovery and destination is provisioned with system and also it's only network devices
                                                            if (destinationObject.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.NETWORK.getName()) && !qualifiedObjects.contains(destinationObject.getString(OBJECT_IP)))
                                                            {
                                                                var status = ObjectStatusCacheStore.getStore().getItem(destinationObject.getLong(ID));

                                                                if (destinationObject.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()) && (status == null || status.equalsIgnoreCase(STATUS_UP)))
                                                                {
                                                                    connectedObjects.add(destinationObject);
                                                                }

                                                                else
                                                                {
                                                                    LOGGER.warn(String.format("topology probe failed for monitor %s, reason: %s", destinationObject.getString(AIOpsObject.OBJECT_NAME), ErrorMessageConstants.OBJECT_ERROR));
                                                                }
                                                            }
                                                        }

                                                        else
                                                        {
                                                            LOGGER.warn(String.format(ErrorMessageConstants.TOPOLOGY_CREATE_ERROR,
                                                                    context.getString(OBJECT), context.getString(AIOpsObject.OBJECT_NAME), String.format("either connected destination monitor %s interface %s is not found or not provisioned into system", destination, reverseLink)));
                                                        }
                                                    }

                                                    else if (destinationObject == null)
                                                    {
                                                        LOGGER.warn(String.format(ErrorMessageConstants.TOPOLOGY_CREATE_ERROR,
                                                                context.getString(OBJECT), context.getString(AIOpsObject.OBJECT_NAME), String.format("connected destination monitor %s is not provisioned into system", destination)));
                                                    }
                                                }

                                                else
                                                {
                                                    LOGGER.warn(String.format(ErrorMessageConstants.TOPOLOGY_CREATE_ERROR,
                                                            context.getString(OBJECT), context.getString(AIOpsObject.OBJECT_NAME), String.format("either connected source monitor interface %s is not found or not provisioned into system", connectedLink)));
                                                }
                                            }
                                        }
                                    }

                                    if (!dependencies.isEmpty())
                                    {
                                        items.get(level).addAll(dependencies);
                                    }
                                }
                            }
                        }

                        else
                        {
                            LOGGER.warn(String.format("topology %s creation failed for monitor %s, reason: %s", context.getString(NMSConstants.OBJECT), context.getString(OBJECT_IP), response != null && CommonUtil.isNotNullOrEmpty(response.getString(MESSAGE)) ? response.getString(MESSAGE) : "either response is null or failed to discover"));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }

                else
                {
                    LOGGER.warn(String.format(ErrorMessageConstants.TOPOLOGY_CREATE_ERROR, context.getString(NMSConstants.OBJECT), context.getString(AIOpsObject.OBJECT_NAME), reply.cause()));
                }

                if (itemProbes.get(level) == 0)
                {
                    promise.complete();

                    var dependencies = items.get(level);

                    // update manual created parent relationships into dependency manager
                    for (var parentDependency : dependencyMappers.get(DependencyMapper.PARENTS))
                    {
                        if (parentDependency.getString(ConfigDBConstants.FIELD_TYPE).equalsIgnoreCase(ConfigDBConstants.ENTITY_TYPE_USER) && parentDependency.getString(DependencyMapper.DEPENDENCY_MAPPER_ARCHIVED).equalsIgnoreCase(NO)
                                && CommonUtil.isNullOrEmpty(parentDependency.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE)) && AIOpsConstants.getDependencyLevel(parentDependency, true).equals(level))
                        {
                            dependencies.add(new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, parentDependency.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT))
                                    .put(AIOpsConstants.DEPENDENCY_PARENT, parentDependency.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT))
                                    .put(AIOpsConstants.DEPENDENCY_SOURCE_PORT, parentDependency.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE))
                                    .put(AIOpsConstants.DEPENDENCY_DESTINATION_PORT, parentDependency.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE))
                                    .put(AIOpsConstants.DEPENDENCY_DESTINATION, parentDependency.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD)));
                        }
                    }

                    // update manual created child relationships into dependency manager
                    for (var childDependency : dependencyMappers.get(DependencyMapper.CHILDREN))
                    {
                        if (childDependency.getString(ConfigDBConstants.FIELD_TYPE).equalsIgnoreCase(ConfigDBConstants.ENTITY_TYPE_USER) && childDependency.getString(DependencyMapper.DEPENDENCY_MAPPER_ARCHIVED).equalsIgnoreCase(NO)
                                && AIOpsConstants.getDependencyLevel(childDependency, true).equals(level))
                        {
                            dependencies.add(new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, childDependency.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD))
                                    .put(AIOpsConstants.DEPENDENCY_DESTINATION, childDependency.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT))
                                    .put(AIOpsConstants.DEPENDENCY_PARENT, childDependency.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT))
                                    .put(AIOpsConstants.DEPENDENCY_DESTINATION_PORT, childDependency.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE))
                                    .put(AIOpsConstants.DEPENDENCY_SOURCE_PORT, childDependency.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE)));
                        }
                    }

                    if (!dependencies.isEmpty())
                    {
                        vertx.eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(),
                                context.clear().put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                                        .put(EventBusConstants.EVENT_TYPE, EVENT_TOPOLOGY)
                                        .put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.ADD_MAP.getName())
                                        .put(AIOpsConstants.DEPENDENCY_LEVEL, level)
                                        .put(AIOpsConstants.DEPENDENCY_SOURCE, objectIP).put(RESULT, dependencies));
                    }
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private Map<Long, List<JsonObject>> qualify()
    {
        var plugins = new HashMap<Long, List<JsonObject>>();

        var items = TopologyPluginConfigStore.getStore().getItems();

        var networkDevices = new JsonArray(new ArrayList<>(NMSConstants.NETWORK_DEVICES));

        for (var index = 0; index < items.size(); index++)
        {
            var item = items.getJsonObject(index);

            var ids = new JsonArray();

            if (item.getJsonArray(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES) != null && !item.getJsonArray(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES).isEmpty())
            {
                if (item.getString(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.OBJECT.getName()))
                {
                    ids.addAll(item.getJsonArray(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES));
                }

                else
                {
                    var objects = ObjectConfigStore.getStore().getItems(ObjectConfigStore.getStore().getItemsByGroups(item.getJsonArray(TopologyPlugin.TOPOLOGY_PLUGIN_ENTITIES), networkDevices));

                    for (var count = 0; count < objects.size(); count++)
                    {
                        var object = objects.getJsonObject(count);

                        if ((item.getString(TopologyPlugin.TOPOLOGY_PLUGIN_MAKE_MODEL) != null && CommonUtil.isNotNullOrEmpty(object.getString(AIOpsObject.OBJECT_MAKE_MODEL)) && object.getString(AIOpsObject.OBJECT_MAKE_MODEL).equalsIgnoreCase(item.getString(TopologyPlugin.TOPOLOGY_PLUGIN_MAKE_MODEL)))
                                || (item.getString(TopologyPlugin.TOPOLOGY_PLUGIN_VENDOR) != null && (CommonUtil.isNotNullOrEmpty(object.getString(AIOpsObject.OBJECT_VENDOR)) && object.getString(AIOpsObject.OBJECT_VENDOR).equalsIgnoreCase(item.getString(TopologyPlugin.TOPOLOGY_PLUGIN_VENDOR)) || item.getString(TopologyPlugin.TOPOLOGY_PLUGIN_VENDOR).equalsIgnoreCase(NMSConstants.GENERIC_VENDOR))))
                        {
                            ids.add(object.getLong(ID));
                        }
                    }
                }

                for (var count = 0; count < ids.size(); count++)
                {
                    plugins.computeIfAbsent(ids.getLong(count), value -> new ArrayList<>()).add(item);
                }
            }
        }

        return plugins;
    }

    private JsonObject getCredential(JsonObject context, Map<Long, Long> credentials)
    {
        var credential = new JsonObject();

        try
        {
            long credentialId = GlobalConstants.NOT_AVAILABLE;

            if (context.containsKey(TopologyPlugin.TOPOLOGY_PLUGIN_CREDENTIAL_PROFILE))
            {
                credentialId = context.getLong(TopologyPlugin.TOPOLOGY_PLUGIN_CREDENTIAL_PROFILE);
            }
            else if (context.containsKey(AIOpsObject.OBJECT_CREDENTIAL_PROFILE))
            {
                credentialId = context.getLong(AIOpsObject.OBJECT_CREDENTIAL_PROFILE);
            }
            else if (credentials != null)
            {
                credentialId = credentials.containsKey(context.getLong(ID)) ? credentials.get(context.getLong(ID)) : NOT_AVAILABLE;
            }

            if (credentialId != NOT_AVAILABLE)
            {
                credential.mergeIn(CredentialProfileConfigStore.getStore().getItem(credentialId));

                if (credential.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
                {
                    credential.mergeIn(credential.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                    credential.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                }

                credential.remove(ID); // remove credential profile
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return credential;
    }

    private Set<String> setFilterTargets(JsonObject item)
    {
        var filters = new HashSet<String>();

        try
        {
            if (item.containsKey(TopologyPlugin.TOPOLOGY_PLUGIN_FILTER_TARGET_TYPE) && item.getString(TopologyPlugin.TOPOLOGY_PLUGIN_FILTER_TARGET_TYPE, EMPTY_VALUE).split(DASH_SEPARATOR).length > 0 && !item.getJsonArray(TOPOLOGY_PLUGIN_FILTER_TARGETS).isEmpty())
            {
                var targets = item.getJsonArray(TOPOLOGY_PLUGIN_FILTER_TARGETS);

                switch (TopologyFilterType.valueOfName(item.getString(TopologyPlugin.TOPOLOGY_PLUGIN_FILTER_TARGET_TYPE).split(DASH_SEPARATOR)[1]))
                {
                    case IP_ADDRESS -> targets.forEach(target -> filters.add(CommonUtil.getString(target)));

                    case IP_ADDRESS_RANGE ->
                    {
                        for (var index = 0; index < targets.size(); index++)
                        {
                            var range = targets.getString(index);

                            var start = range.split("-")[0].trim();

                            var end = start.substring(0, start.lastIndexOf('.')) + "." + range.split("-")[1].trim();

                            if (APIUtil.validateRange(start, end))
                            {
                                CommonUtil.calculateIPRange(InetAddress.getByName(start), InetAddress.getByName(end)).forEach(ip -> filters.add(CommonUtil.getString(ip)));
                            }
                        }
                    }

                    case GROUP ->
                            ObjectConfigStore.getStore().getItemsByGroups(targets).forEach(id -> filters.add(ObjectConfigStore.getStore().getItem(CommonUtil.getLong(id)).getString(OBJECT_IP, EMPTY_VALUE)));

                    case TAG ->
                            ObjectConfigStore.getStore().getObjectIdsByTags(targets).forEach(id -> filters.add(ObjectConfigStore.getStore().getItemByObjectId(CommonUtil.getInteger(id)).getString(OBJECT_IP, EMPTY_VALUE)));

                    default ->
                    {
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return filters;
    }

    private void complete(JsonObject event, long id)
    {
        try
        {
            SchedulerCacheStore.getStore().clearSchedulerContext(id);

            var totalRuntime = DateTimeUtil.currentSeconds() - TopologyCacheStore.getStore().getTimeStamp(id);

            EventBusConstants.publish(EventBusConstants.UI_ACTION_TOPOLOGY_STOP, event.put(EventBusConstants.EVENT_TIMESTAMP, totalRuntime));

            if (event.containsKey(TopologyPlugin.TOPOLOGY_PLUGIN_DISCOVERY))
            {
                Bootstrap.configDBService().delete(ConfigDBConstants.COLLECTION_SCHEDULER, new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, id), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                {
                    if (result.succeeded())
                    {
                        SchedulerConfigStore.getStore().deleteItem(id);

                        JobScheduler.removeJob(id);
                    }
                });
            }
            else
            {
                Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_SCHEDULER,
                        new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, id),
                        new JsonObject().put(EventBusConstants.EVENT_TIMESTAMP, totalRuntime),
                        DEFAULT_USER,
                        SYSTEM_REMOTE_ADDRESS,
                        result -> SchedulerConfigStore.getStore().updateItem(id));

                var scheduler = SchedulerConfigStore.getStore().getItem(id);

                if (scheduler != null)
                {
                    if (scheduler.containsKey(Scheduler.SCHEDULER_CONTEXT))
                    {
                        scheduler.mergeIn(scheduler.getJsonObject(Scheduler.SCHEDULER_CONTEXT));

                        scheduler.remove(Scheduler.SCHEDULER_CONTEXT);
                    }

                    if (scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS) != null && !scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS).isEmpty())
                    {
                        Notification.sendEmail(new JsonObject()
                                .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add("information.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS))
                                .put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject().put(TIME_STAMP, DateTimeUtil.timestamp()).put(TOPOLOGY_PLUGIN_PROTOCOL, StringUtils.join(scheduler.getJsonArray(TOPOLOGY_LINK_LAYER), COMMA_SEPARATOR))
                                        .put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS, StringUtils.join(ObjectConfigStore.getStore().getObjectNames(scheduler.getJsonArray(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS)), COMMA_SEPARATOR)).getMap()).replace(Notification.EMAIL_NOTIFICATION_TOPOLOGY_HTML_TEMPLATE).getBytes(StandardCharsets.UTF_8))
                                .put(Notification.EMAIL_NOTIFICATION_SUBJECT, TOPOLOGY_NOTIFICATION_SUBJECT));
                    }

                    if (scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS) != null && !scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS).isEmpty())
                    {
                        Notification.sendSMS(String.format(InfoMessageConstants.TOPOLOGY_NOTIFICATION_MESSAGE, ObjectConfigStore.getStore().getObjectNames(scheduler.getJsonArray(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS)), scheduler.getJsonArray(TOPOLOGY_PLUGIN_PROTOCOL), totalRuntime), scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS));
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        TopologyCacheStore.getStore().completeTopology(id);
    }

    private void abort(JsonObject event)
    {
        if (CommonUtil.isNotNullOrEmpty(event.getString(TopologyPlugin.TOPOLOGY_PLUGIN_TYPE)))
        {
            var iterator = eventsByPluginType.get(NMSConstants.TopologyPluginType.valueOfName(event.getString(TopologyPlugin.TOPOLOGY_PLUGIN_TYPE))).iterator();

            while (iterator.hasNext())
            {
                var context = this.events.remove(iterator.next());

                if (context != null && context.getLong(EventBusConstants.EVENT_SCHEDULER).equals(event.getLong(EventBusConstants.EVENT_SCHEDULER)))
                {
                    EventCacheStore.getStore().deleteItem(context.getLong(EventBusConstants.EVENT_ID));

                    if (context.containsKey(APIConstants.SESSION_ID) && context.getString(APIConstants.SESSION_ID).equalsIgnoreCase(event.getString(APIConstants.SESSION_ID)))
                    {
                        Bootstrap.vertx().eventBus().send(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ?
                                        EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_TOPOLOGY_RESPONSE,
                                context.put(STATUS, STATUS_ABORT)
                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_MANUAL_ABORTED).put(MESSAGE, String.format(REDISCOVER_FAILED, "User aborted the event")));
                    }

                    iterator.remove();
                }
            }
        }
    }

    private void complete(NMSConstants.TopologyPluginType pluginType)
    {
        idleWorkers.getAndIncrement();

        idleWorkersByPluginType.put(pluginType, idleWorkersByPluginType.get(pluginType) + 1);
    }

    private int dequeue(NMSConstants.TopologyPluginType pluginType, List<NMSConstants.TopologyPluginType> pluginTypes, Map<NMSConstants.TopologyPluginType, Integer> pendingEventsByPlugin, List<List<Long>> batches, List<PluginEngineConstants.PluginEngine> pluginEngines, PluginEngineConstants.PluginEngine pluginEngine)
    {
        var result = 0;

        try
        {
            var qualifiedEvents = idleWorkersByPluginType.get(pluginType) > 0 && idleWorkers.get() > 0 ? this.eventsByPluginType.get(pluginType) : null;

            if (qualifiedEvents != null && !qualifiedEvents.isEmpty())
            {
                pendingEventsByPlugin.put(pluginType,
                        dequeue(qualifiedEvents, idleWorkersByPluginType.get(pluginType), pluginType, batches, pluginTypes, pluginEngines, pluginEngine));

                result += pendingEventsByPlugin.get(pluginType);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        return result;
    }

    private int dequeue(List<Long> events, int pendingEvents, NMSConstants.TopologyPluginType pluginType, List<List<Long>> batches, List<NMSConstants.TopologyPluginType> pluginTypes,
                        List<PluginEngineConstants.PluginEngine> pluginEngines, PluginEngineConstants.PluginEngine pluginEngine)
    {
        var iterator = events.listIterator();

        var batchSize = pluginEngine == PluginEngineConstants.PluginEngine.GO ? MotadataConfigUtil.getTopologyDiscoveryBatchSize() : 1;

        var workers = WorkerUtil.getWorkers(events.size(), batchSize, pendingEvents);

        var allocations = 0;

        while (allocations < workers && iterator.hasNext())
        {
            var batchEvents = new ArrayList<Long>();

            batches.add(batchEvents);

            pluginTypes.add(pluginType);

            pluginEngines.add(pluginEngine);

            for (var index = 0; index < batchSize && iterator.hasNext(); index++)
            {
                batchEvents.add(iterator.next());

                iterator.remove();
            }
            allocations++;
        }

        idleWorkers.set(idleWorkers.get() - workers);

        idleWorkersByPluginType.put(pluginType, pendingEvents - workers);

        return events.size();
    }

    private void dequeue(List<NMSConstants.TopologyPluginType> pluginTypes, Map<NMSConstants.TopologyPluginType, Integer> pendingEventsByPluginType,
                         Map.Entry<NMSConstants.TopologyPluginType, Integer> entry1, Map.Entry<NMSConstants.TopologyPluginType, Integer> entry2, List<List<Long>> batches, List<PluginEngineConstants.PluginEngine> pluginEngines, PluginEngineConstants.PluginEngine pluginEngine)
    {
        try
        {
            var qualifiedEvents = this.eventsByPluginType.get(entry1.getKey());

            if (qualifiedEvents != null && !qualifiedEvents.isEmpty())
            {
                pendingEventsByPluginType.put(entry1.getKey(), dequeue(qualifiedEvents, entry2.getValue(), entry2.getKey(), batches, pluginTypes, pluginEngines, pluginEngine));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void dequeue(Map<NMSConstants.TopologyPluginType, Integer> pendingEventsByPluginType, List<NMSConstants.TopologyPluginType> pluginTypes, List<List<Long>> batchEvents, List<PluginEngineConstants.PluginEngine> pluginEngines)
    {
        var sortedByWorkers = idleWorkersByPluginType.entrySet()
                .stream()
                .filter(entry -> entry.getValue() > 0)
                .sorted((Map.Entry.<NMSConstants.TopologyPluginType, Integer>comparingByValue().reversed()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));

        pendingEventsByPluginType.entrySet()
                .stream()
                .filter(entry -> entry.getValue() > 0)
                .sorted((Map.Entry.<NMSConstants.TopologyPluginType, Integer>comparingByValue().reversed()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new)).entrySet()
                .stream()
                .takeWhile(entry1 -> idleWorkers.get() > 0)
                .forEach(entry1 -> sortedByWorkers.entrySet()
                        .stream()
                        .takeWhile(entry2 -> idleWorkers.get() > 0)
                        .forEach(entry2 -> dequeue(pluginTypes, pendingEventsByPluginType, entry1, entry2, batchEvents, pluginEngines, entry1.getKey() == NMSConstants.TopologyPluginType.SNMP ? PluginEngineConstants.PluginEngine.GO : PluginEngineConstants.PluginEngine.PYTHON)));
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        workerExecutor.close();

        promise.complete();
    }
}
