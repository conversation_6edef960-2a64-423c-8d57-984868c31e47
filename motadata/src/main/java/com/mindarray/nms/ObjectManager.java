/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  24-Mar-2025     Chandresh           MOTADATA-5426: Docker discovery and polling support added
 *  26-May-2025     Sankalp             MOTADATA-6185 : Monitoring support for IBM AS 400
 */

package com.mindarray.nms;

import com.mindarray.*;
import com.mindarray.agent.AgentConstants;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.*;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.job.JobScheduler;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.mindarray.ErrorMessageConstants.*;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.METRIC_ACCESS_POINT_PROVISION_SUCCEEDED;
import static com.mindarray.InfoMessageConstants.METRIC_VM_PROVISION_SUCCEEDED;
import static com.mindarray.agent.AgentConstants.METRIC_AGENT_STATUS;
import static com.mindarray.api.Agent.AGENT_CONFIGS;
import static com.mindarray.api.Agent.AGENT_UUID;
import static com.mindarray.api.ApplicationMapper.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.*;

/**
 * Manages the lifecycle of monitored objects and metrics in the NMS system.
 * <p>
 * The ObjectManager is responsible for:
 * - Provisioning new objects discovered in the network
 * - Unprovisioning objects that are no longer needed
 * - Provisioning metrics for monitored objects
 * - Unprovisioning metrics that are no longer needed
 * - Enabling and disabling metrics
 * - Managing object and metric state changes
 * - Notifying other components about object and metric changes
 * <p>
 * This class acts as a central coordinator for object management operations,
 * handling events from various sources and updating the configuration database
 * and cache stores accordingly.
 */
public class ObjectManager extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(ObjectManager.class, GlobalConstants.MOTADATA_NMS, "Object Manager");

    private static final boolean ARCHIVED_ENABLED = MotadataConfigUtil.archivedObjectEnabled();

    private EventEngine eventEngine;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        //todo: license check is pending ...

        var replyTopic = EventBusConstants.EVENT_OBJECT_PROVISION + EventBusConstants.EVENT_REPLY;

        vertx.eventBus().localConsumer(EventBusConstants.EVENT_OBJECT_UNPROVISION, this::unprovisionObject).exceptionHandler(LOGGER::error);

        vertx.eventBus().localConsumer(EventBusConstants.EVENT_METRIC_PROVISION, this::provisionMetric).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_METRIC_UNPROVISION, message ->
        {
            try
            {
                var event = message.body();

                if (NMSConstants.isVirtualMachineMetric(MetricConfigStore.getStore().getItem(event.getLong(ID)).getString(Metric.METRIC_PLUGIN)))
                {
                    LicenseCacheStore.getStore().update(VMS, MetricConfigStore.getStore().getItem(event.getLong(ID)).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(OBJECTS).size(), false);
                }

                else if (NMSConstants.isAccessPointMetric(MetricConfigStore.getStore().getItem(event.getLong(ID)).getString(Metric.METRIC_PLUGIN)))
                {
                    LicenseCacheStore.getStore().update(ACCESS_POINTS, MetricConfigStore.getStore().getItem(event.getLong(ID)).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(OBJECTS).size(), false);
                }

                else if (NMSConstants.isWANLinkMetric(MetricConfigStore.getStore().getItem(event.getLong(ID)).getString(Metric.METRIC_PLUGIN)))
                {
                    LicenseCacheStore.getStore().update(WAN_LINKS, MetricConfigStore.getStore().getItem(event.getLong(ID)).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(OBJECTS).size(), false);
                }

                else if (APPLICATION_PLUGINS.contains(MetricConfigStore.getStore().getItem(event.getLong(ID)).getString(Metric.METRIC_PLUGIN)))
                {
                    LicenseCacheStore.getStore().update(APPS, false);
                }

                Bootstrap.configDBService().delete(ConfigDBConstants.COLLECTION_METRIC, new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, event.getLong(ID)), event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS, result ->
                {
                    if (result.succeeded() && !result.result().isEmpty())
                    {
                        LOGGER.info(String.format("metric %s of object %s deleted successfully...",
                                MetricConfigStore.getStore().getMetricName(event.getLong(ID)),
                                MetricConfigStore.getStore().getMetricObjectName(event.getLong(ID))));

                        vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_METRIC.name())
                                .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                                .put(ID, event.getLong(ID)));

                        vertx.eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_METRIC.name())
                                .put(ID, event.getLong(ID)));

                        MetricCacheStore.getStore().deleteMetric(event.getLong(ID)); //delete metric from scheduler

                        MetricConfigStore.getStore().deleteItemByObject(event.getLong(ID));
                    }
                    else
                    {
                        LOGGER.warn(String.format("failed to delete metric %s of object %s , reason : %s",
                                MetricConfigStore.getStore().getMetricName(event.getLong(ID)),
                                MetricConfigStore.getStore().getMetricObjectName(event.getLong(ID)), result.cause().getMessage() != null ? result.cause().getMessage() : UNKNOWN));
                    }

                    message.reply(EMPTY_VALUE);
                });
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                message.reply(EMPTY_VALUE);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_METRIC_ENABLE, message ->
        {
            var event = message.body();

            try
            {
                Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_METRIC,
                        new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, event.getLong(ID))
                        , new JsonObject().put(Metric.METRIC_STATE, NMSConstants.State.ENABLE.name()), event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS
                        , result ->
                        {
                            if (result.succeeded())
                            {
                                MetricConfigStore.getStore().updateItem(event.getLong(ID)).onComplete(asyncResult ->
                                {
                                    vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject()
                                            .put(EVENT_TYPE, EVENT_CHANGE_NOTIFICATION)
                                            .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ENABLE_METRIC.name())
                                            .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                                            .put(ID, event.getLong(ID)));

                                    vertx.eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                                            .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ENABLE_METRIC.name())
                                            .put(ID, event.getLong(ID)));

                                    LOGGER.info(String.format("metric %s of object %s enabled successfully...",
                                            MetricConfigStore.getStore().getMetricName(event.getLong(ID)),
                                            MetricConfigStore.getStore().getMetricObjectName(event.getLong(ID))));

                                    var object = ObjectConfigStore.getStore().getItem(MetricConfigStore.getStore().getItem(event.getLong(ID)).getLong(Metric.METRIC_OBJECT));

                                    if (object != null && object.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()))
                                    {
                                        var metric = MetricConfigStore.getStore().getItem(event.getLong(ID));

                                        if (!metric.getString(Metric.METRIC_DISCOVERY_METHOD).equalsIgnoreCase(DiscoveryMethod.INLINE.name()))
                                        {
                                            MetricCacheStore.getStore().addMetric(event.getLong(ID),
                                                    metric.getInteger(Metric.METRIC_POLLING_TIME)); // add metric to scheduler
                                        }
                                    }

                                    message.reply(new JsonObject().put(STATUS, STATUS_SUCCEED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(ID, event.getLong(ID))
                                            .put(MESSAGE, String.format("metric %s of object %s enabled successfully...",
                                                    MetricConfigStore.getStore().getMetricName(event.getLong(ID)),
                                                    MetricConfigStore.getStore().getMetricObjectName(event.getLong(ID)))));
                                });
                            }
                            else
                            {
                                LOGGER.warn(String.format("failed to enable metric %s of object %s , reason : %s",
                                        MetricConfigStore.getStore().getMetricName(event.getLong(ID)),
                                        MetricConfigStore.getStore().getMetricObjectName(event.getLong(ID)), result.cause().getMessage()));

                                message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ID, event.getLong(ID)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DATABASE_UPDATE).put(ERROR, result.cause().getMessage())
                                        .put(MESSAGE, String.format(METRIC_ENABLE_STATE_FAILED, MetricConfigStore.getStore().getMetricName(event.getLong(ID))
                                                , MetricConfigStore.getStore().getMetricObjectName(event.getLong(ID)))));
                            }
                        });

            }

            catch (Exception exception)
            {
                LOGGER.error(exception);

                message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ID, event.getLong(ID)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(MESSAGE, String.format(METRIC_ENABLE_STATE_FAILED, MetricConfigStore.getStore().getMetricName(event.getLong(ID))
                                , MetricConfigStore.getStore().getMetricObjectName(event.getLong(ID)))));
            }


        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_METRIC_DISABLE, message ->
        {

            var event = message.body();

            try
            {
                Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_METRIC,
                        new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, event.getLong(ID)),
                        new JsonObject().put(Metric.METRIC_STATE, NMSConstants.State.DISABLE.name()), event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS
                        , result ->
                        {
                            if (result.succeeded())
                            {
                                MetricConfigStore.getStore().updateItem(event.getLong(ID)).onComplete(asyncResult ->
                                {
                                    vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                                            .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DISABLE_METRIC.name())
                                            .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                                            .put(ID, event.getLong(ID)));

                                    vertx.eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                                            .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DISABLE_METRIC.name())
                                            .put(ID, event.getLong(ID)));

                                    MetricCacheStore.getStore().deleteMetric(event.getLong(ID)); //delete metric from scheduler

                                    LOGGER.info(String.format("metric %s of object %s disabled successfully...",
                                            MetricConfigStore.getStore().getMetricName(event.getLong(ID)),
                                            MetricConfigStore.getStore().getMetricObjectName(event.getLong(ID))));

                                    message.reply(new JsonObject().put(STATUS, STATUS_SUCCEED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(ID, event.getLong(ID))
                                            .put(MESSAGE, String.format("metric %s of object %s disabled successfully...",
                                                    MetricConfigStore.getStore().getMetricName(event.getLong(ID)),
                                                    MetricConfigStore.getStore().getMetricObjectName(event.getLong(ID)))));
                                });
                            }
                            else
                            {
                                LOGGER.warn(String.format("failed to disable metric %s of object %s , reason : %s",
                                        MetricConfigStore.getStore().getMetricName(event.getLong(ID)),
                                        MetricConfigStore.getStore().getMetricObjectName(event.getLong(ID)), result.cause().getMessage()));

                                message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ID, event.getLong(ID)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DATABASE_UPDATE).put(ERROR, result.cause().getMessage())
                                        .put(MESSAGE, String.format(METRIC_DISABLE_STATE_FAILED, MetricConfigStore.getStore().getMetricName(event.getLong(ID))
                                                , MetricConfigStore.getStore().getMetricObjectName(event.getLong(ID)))));
                            }
                        });
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ID, event.getLong(ID)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(MESSAGE, String.format(METRIC_DISABLE_STATE_FAILED, MetricConfigStore.getStore().getMetricName(event.getLong(ID))
                                , MetricConfigStore.getStore().getMetricObjectName(event.getLong(ID)))));
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EVENT_METRIC_SUSPEND, message ->
        {
            var event = message.body();

            try
            {
                Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_METRIC,
                        new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, event.getLong(ID)),
                        new JsonObject().put(Metric.METRIC_STATE, event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_METRIC_POLL) ? State.INVALID.name() : State.SUSPEND.name()),
                        event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS
                        , result ->
                        {
                            if (result.succeeded())
                            {
                                MetricConfigStore.getStore().updateItem(event.getLong(ID)).onComplete(asyncResult ->
                                {
                                    vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                                            .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.SUSPEND_METRIC.name())
                                            .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                                            .put(ID, event.getLong(ID)));

                                    vertx.eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                                            .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.SUSPEND_METRIC.name())
                                            .put(ID, event.getLong(ID)));

                                    //will be not deleting its metric from scheduler as in will require to update its status duration
//                                        MetricCacheStore.getStore().deleteMetric(event.getLong(ID)); //delete metric from scheduler

                                    LOGGER.info(String.format("metric %s of object %s suspended successfully...",
                                            MetricConfigStore.getStore().getMetricName(event.getLong(ID)),
                                            MetricConfigStore.getStore().getMetricObjectName(event.getLong(ID))));

                                    message.reply(new JsonObject().put(STATUS, STATUS_SUCCEED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(ID, event.getLong(ID))
                                            .put(MESSAGE, String.format("metric %s of object %s suspended successfully...",
                                                    MetricConfigStore.getStore().getMetricName(event.getLong(ID)),
                                                    MetricConfigStore.getStore().getMetricObjectName(event.getLong(ID)))));
                                });
                            }
                            else
                            {
                                LOGGER.warn(String.format("failed to suspend metric %s of object %s , reason : %s",
                                        MetricConfigStore.getStore().getMetricName(event.getLong(ID)),
                                        MetricConfigStore.getStore().getMetricObjectName(event.getLong(ID)), result.cause().getMessage()));

                                message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ID, event.getLong(ID)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DATABASE_UPDATE).put(ERROR, result.cause().getMessage())
                                        .put(MESSAGE, String.format(METRIC_SUSPEND_STATE_FAILED, MetricConfigStore.getStore().getMetricName(event.getLong(ID))
                                                , MetricConfigStore.getStore().getMetricObjectName(event.getLong(ID)))));
                            }
                        });
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ID, event.getLong(ID)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(MESSAGE, String.format(METRIC_SUSPEND_STATE_FAILED, MetricConfigStore.getStore().getMetricName(event.getLong(ID))
                                , MetricConfigStore.getStore().getMetricObjectName(event.getLong(ID)))));
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_OBJECT_ENABLE, message ->
        {
            var event = message.body();

            try
            {
                Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_OBJECT, new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, event.getLong(ID)),
                        new JsonObject().put(AIOpsObject.OBJECT_STATE, NMSConstants.State.ENABLE.name()).put(AIOpsObject.OBJECT_MODIFICATION_TIME, DateTimeUtil.timestamp()), event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS
                        , result ->
                        {
                            if (result.succeeded())
                            {
                                ObjectConfigStore.getStore().updateItem(event.getLong(ID));

                                ObjectStatusCacheStore.getStore().updateItem(event.getLong(ID), STATUS_UNKNOWN, DateTimeUtil.currentSeconds());

                                ObjectStatusCacheStore.getStore().deleteObject(event.getLong(ID));

                                LOGGER.info(String.format("object %s enabled successfully...",
                                        ObjectConfigStore.getStore().getObjectName(event.getLong(ID))));

                                var items = MetricConfigStore.getStore().getItemsByObjectState(event.getLong(ID), State.ENABLE.name());

                                for (JsonObject item : items)
                                {
                                    if (!item.getString(Metric.METRIC_DISCOVERY_METHOD).equalsIgnoreCase(DiscoveryMethod.INLINE.name()))
                                    {
                                        MetricCacheStore.getStore().addMetric(item.getLong(ID), item.getInteger(Metric.METRIC_POLLING_TIME));
                                    }
                                }

                                message.reply(new JsonObject().put(STATUS, STATUS_SUCCEED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(ID, event.getLong(ID))
                                        .put(MESSAGE, String.format("object %s enabled successfully...", ObjectConfigStore.getStore().getObjectName(event.getLong(ID)))));

                                updateConfigManagementStatus(event, YES);
                            }
                            else
                            {
                                LOGGER.warn(String.format(OBJECT_ENABLE_STATE_FAILED, ObjectConfigStore.getStore().getObjectName(event.getLong(ID))));

                                message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ID, event.getLong(ID)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DATABASE_UPDATE).put(ERROR, result.cause().getMessage())
                                        .put(MESSAGE, String.format(OBJECT_ENABLE_STATE_FAILED, ObjectConfigStore.getStore().getObjectName(event.getLong(ID)))));
                            }
                        });
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ID, event.getLong(ID)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(MESSAGE, String.format(OBJECT_ENABLE_STATE_FAILED, ObjectConfigStore.getStore().getObjectName(event.getLong(ID)))));
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_OBJECT_DISABLE, message ->
        {
            var event = message.body();

            try
            {
                Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_OBJECT, new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, event.getLong(ID)),
                        new JsonObject().put(AIOpsObject.OBJECT_STATE, NMSConstants.State.DISABLE.name()).put(AIOpsObject.OBJECT_MODIFICATION_TIME, DateTimeUtil.timestamp()), event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS
                        , result ->
                        {
                            if (result.succeeded())
                            {
                                ObjectConfigStore.getStore().updateItem(event.getLong(ID));

                                ObjectStatusCacheStore.getStore().updateItem(event.getLong(ID), STATUS_DISABLE, DateTimeUtil.currentSeconds());

                                ObjectStatusCacheStore.getStore().addObject(event.getLong(ID));

                                LOGGER.info(String.format("object %s disabled successfully...",
                                        ObjectConfigStore.getStore().getObjectName(event.getLong(ID))));

                                //MetricConfigStore.getStore().getItemsByObjectState(event.getLong(ID), NMSConstants.State.ENABLE.name()).forEach(metric -> MetricCacheStore.getStore().deleteMetric(metric.getLong(ID)));

                                message.reply(new JsonObject().put(STATUS, STATUS_SUCCEED).put(ID, event.getLong(ID))
                                        .put(MESSAGE, String.format("object %s disabled successfully...", ObjectConfigStore.getStore().getObjectName(event.getLong(ID)))));

                                updateConfigManagementStatus(event, NO);
                            }
                            else
                            {
                                LOGGER.warn(String.format(OBJECT_DISABLE_STATE_FAILED,
                                        ObjectConfigStore.getStore().getObjectName(event.getLong(ID))));

                                message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ID, event.getLong(ID)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DATABASE_UPDATE).put(ERROR, result.cause().getMessage())
                                        .put(MESSAGE, String.format(OBJECT_DISABLE_STATE_FAILED, ObjectConfigStore.getStore().getObjectName(event.getLong(ID)))));
                            }
                        });
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ID, event.getLong(ID)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(MESSAGE, String.format(OBJECT_DISABLE_STATE_FAILED, ObjectConfigStore.getStore().getObjectName(event.getLong(ID)))));
            }
        }).exceptionHandler(LOGGER::error);

        // only on maintenance request come here...for off maintenance ui will pass state as a enable so it will direct pass to enable object consumer
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_OBJECT_MAINTENANCE, message ->
        {
            var event = message.body();

            try
            {
                Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_OBJECT, new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, event.getLong(ID)),
                        new JsonObject().put(AIOpsObject.OBJECT_STATE, NMSConstants.State.MAINTENANCE.name()).put(AIOpsObject.OBJECT_MODIFICATION_TIME, DateTimeUtil.timestamp()), event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS
                        , result ->
                        {
                            if (result.succeeded())
                            {
                                ObjectConfigStore.getStore().updateItem(event.getLong(ID));

                                ObjectStatusCacheStore.getStore().updateItem(event.getLong(ID), STATUS_MAINTENANCE, DateTimeUtil.currentSeconds());

                                ObjectStatusCacheStore.getStore().addObject(event.getLong(ID));

                                LOGGER.info(String.format("object %s entered into maintenance state successfully...",
                                        ObjectConfigStore.getStore().getObjectName(event.getLong(ID))));

                                //MetricConfigStore.getStore().getItemsByObjectState(event.getLong(ID), NMSConstants.State.ENABLE.name()).forEach(metric -> MetricCacheStore.getStore().deleteMetric(metric.getLong(ID)));

                                message.reply(new JsonObject().put(STATUS, STATUS_SUCCEED).put(ID, event.getLong(ID))
                                        .put(MESSAGE, String.format("object %s entered into maintenance state successfully...", ObjectConfigStore.getStore().getObjectName(event.getLong(ID)))));

                                updateConfigManagementStatus(event, NO);
                            }
                            else
                            {
                                LOGGER.warn(String.format(OBJECT_MAINTENANCE_STATE_FAILED,
                                        ObjectConfigStore.getStore().getObjectName(event.getLong(ID))));

                                message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ID, event.getLong(ID)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DATABASE_UPDATE).put(ERROR, result.cause().getMessage())
                                        .put(MESSAGE, String.format(OBJECT_MAINTENANCE_STATE_FAILED, ObjectConfigStore.getStore().getObjectName(event.getLong(ID)))));
                            }
                        });
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ID, event.getLong(ID)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(MESSAGE, String.format(OBJECT_MAINTENANCE_STATE_FAILED, ObjectConfigStore.getStore().getObjectName(event.getLong(ID)))));
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EVENT_METRIC_REDISCOVER, message ->
        {
            var event = message.body();

            try
            {
                var metricPlugin = NMSConstants.MetricPlugin.valueOfName(event.getString(Metric.METRIC_PLUGIN));

                switch (metricPlugin)
                {
                    case LINUX_PROCESS, HP_UX_PROCESS, IBM_AIX_PROCESS, SOLARIS_PROCESS, WINDOWS_PROCESS ->
                    {
                        if (event.getValue(OBJECTS) != null)
                        {
                            send(SystemProcessConfigStore.getStore()
                                    .flatItemsByValues(SystemProcess.SYSTEM_PROCESS_APP_TYPE, event.getJsonArray(OBJECTS), SystemProcess.SYSTEM_PROCESS), event);
                        }
                        else
                        {
                            send(SystemProcessConfigStore.getStore().flatItems(SystemProcess.SYSTEM_PROCESS_OS, event.getString(Metric.METRIC_TYPE), SystemProcess.SYSTEM_PROCESS),
                                    event);
                        }
                    }

                    case LINUX_FILE, WINDOWS_FILE, LINUX_DIR, WINDOWS_DIR ->
                            qualifySystemFileObjects(event, metricPlugin);

                    case WINDOWS_SERVICE ->
                    {
                        if (event.getValue(OBJECTS) != null)
                        {
                            send(SystemServiceConfigStore.getStore()
                                    .flatItemsByValues(SystemService.SYSTEM_SERVICE_APP_TYPE, event.getJsonArray(OBJECTS), SystemService.SYSTEM_SERVICE), event);
                        }
                        else
                        {
                            send(SystemServiceConfigStore.getStore()
                                    .flatItems(SystemService.SYSTEM_SERVICE_OS, event.getString(Metric.METRIC_TYPE), SystemService.SYSTEM_SERVICE), event);
                        }
                    }

                    case NETWORK_SERVICE -> send(ApplicationMapperConfigStore.getStore()
                            .flatItems(APPLICATION_MAPPER_PROTOCOL, APPLICATION_MAPPER_PROTOCOL_TCP, APPLICATION_MAPPER_PORT), event);

                    default -> send(null, event);
                }

            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EVENT_METRIC_INSTANCE_UNPROVISION, message ->
        {
            var event = message.body();

            try
            {
                var item = MetricConfigStore.getStore().getItem(event.getLong(ID));

                var metricPlugin = item.getString(Metric.METRIC_PLUGIN);

                var instances = event.getJsonArray(METRIC_INSTANCES);

                if (instances != null && item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(OBJECTS) != null
                        && !item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(OBJECTS).isEmpty())
                {
                    var instanceKey = NMSConstants.MetricPlugin.SNMP_INTERFACE.getName().equalsIgnoreCase(metricPlugin) ? INTERFACE : AIOpsObject.OBJECT_NAME;

                    var availableObjects = item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(OBJECTS);

                    var iterator = availableObjects.getList().listIterator();

                    while (iterator.hasNext())
                    {
                        var object = (JsonObject) iterator.next();

                        if (object.containsKey(instanceKey) && instances.contains(object.getString(instanceKey)))
                        {
                            iterator.remove();
                        }
                    }

                    if (validateInstances(metricPlugin, item))
                    {
                        var objects = new JsonArray();

                        if (isWANLinkMetric(metricPlugin))
                        {
                            updateIPSLAMetrics(metricPlugin, item.getLong(Metric.METRIC_OBJECT), objects);
                        }

                        objects.addAll(availableObjects);

                        TagCacheStore.getStore().updateInstanceTags(item.getLong(ID), objects, INSTANCE_TYPES.getOrDefault(item.getString(Metric.METRIC_PLUGIN), EMPTY_VALUE));

                        Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_METRIC,
                                new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                item,
                                event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS,
                                result ->
                                {
                                    if (result.succeeded())
                                    {
                                        MetricConfigStore.getStore().updateItem(item.getLong(ID));

                                        Bootstrap.vertx().eventBus().publish(EVENT_CHANGE_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.DELETE_OBJECT).put(ID, item.getLong(Metric.METRIC_OBJECT)).put(METRIC_INSTANCES, instances));

                                        LOGGER.info(String.format("metric instances %s unprovisioned successfully", instances));

                                        if (isVirtualMachineMetric(metricPlugin))
                                        {
                                            LicenseCacheStore.getStore().update(VMS, instances.size(), false);
                                        }

                                        else if (isAccessPointMetric(metricPlugin))
                                        {
                                            LicenseCacheStore.getStore().update(ACCESS_POINTS, instances.size(), false);
                                        }

                                        else if (isWANLinkMetric(metricPlugin))
                                        {
                                            LicenseCacheStore.getStore().update(WAN_LINKS, instances.size(), false);
                                        }

                                        message.reply(new JsonObject().put(STATUS, STATUS_SUCCEED).put(ID, item.getLong(ID))
                                                .put(MESSAGE, String.format(InfoMessageConstants.OBJECT_METRIC_UPDATE_SUCCEEDED, ObjectConfigStore.getStore().getObjectName(CommonUtil.getLong(item.getLong(Metric.METRIC_OBJECT))))));

                                    }
                                    else
                                    {
                                        LOGGER.warn(String.format(METRIC_INSTANCES_UNPROVISION_FAILED, instances, ObjectConfigStore.getStore().getObjectName(item.getLong(Metric.METRIC_OBJECT)), result.cause().getMessage()));

                                        message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ID, item.getLong(ID)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace()))
                                                .put(MESSAGE, String.format(METRIC_INSTANCES_UNPROVISION_FAILED, instances, ObjectConfigStore.getStore().getObjectName(item.getLong(Metric.METRIC_OBJECT)), result.cause().getMessage())));
                                    }
                                });
                    }
                    else
                    {
                        LOGGER.warn(String.format(METRIC_INSTANCES_UNPROVISION_FAILED, instances, ObjectConfigStore.getStore().getObjectName(item.getLong(Metric.METRIC_OBJECT)), "no valid instances found"));

                        message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ID, item.getLong(ID)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(ERROR, String.format(METRIC_UPDATE_FAILED, "no valid instances found"))
                                .put(MESSAGE, String.format(METRIC_UPDATE_FAILED, String.format(METRIC_UPDATE_FAILED, "no valid instances found"))));
                    }
                }
                else
                {
                    LOGGER.warn(String.format(METRIC_INSTANCES_UNPROVISION_FAILED, instances, ObjectConfigStore.getStore().getObjectName(item.getLong(Metric.METRIC_OBJECT)), "no valid instances found"));

                    message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ID, item.getLong(ID)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(ERROR, String.format(METRIC_UPDATE_FAILED, "no valid instances found"))
                            .put(MESSAGE, String.format(METRIC_UPDATE_FAILED, String.format(METRIC_UPDATE_FAILED, "no valid instances found"))));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ID, event.getLong(ID)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                        .put(MESSAGE, String.format(METRIC_UPDATE_FAILED, exception.getMessage())));
            }
        }).exceptionHandler(LOGGER::error);

        eventEngine = new EventEngine().setEventType(EventBusConstants.EVENT_OBJECT_PROVISION)
                .setBlockingEvent(true).setEventQueueSize(1).setLogger(LOGGER).setEventHandler(event ->
                {
                    try
                    {
                        provisionObject(event).onComplete(asyncResult ->
                        {
                            event.put(STATUS, asyncResult.succeeded() ? STATUS_SUCCEED : STATUS_FAIL);

                            vertx.eventBus().send(replyTopic, event);
                        });
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        vertx.eventBus().send(replyTopic, event);
                    }
                }).start(vertx, promise);
    }

    private void qualifySystemFileObjects(JsonObject event, NMSConstants.MetricPlugin metricPlugin)
    {
        var items = SystemFileConfigStore.getStore()
                .getItemsByValue(SystemFile.SYSTEM_FILE_OS, event.getString(Metric.METRIC_TYPE));

        var fileType = metricPlugin == NMSConstants.MetricPlugin.LINUX_FILE || metricPlugin == NMSConstants.MetricPlugin.WINDOWS_FILE ? SystemFile.SYSTEM_FILE_TYPE_FILE : SystemFile.SYSTEM_FILE_TYPE_DIR;

        var objects = new JsonArray();

        for (var index = 0; index < items.size(); index++)
        {
            if (items.getJsonObject(index).getString(SystemFile.SYSTEM_FILE_TYPE).equalsIgnoreCase(fileType))
            {
                objects.add(items.getJsonObject(index).getString(SystemFile.SYSTEM_FILE));
            }
        }

        if (!objects.isEmpty())
        {
            send(objects, event);
        }
    }

    private Future<Void> provisionObject(JsonObject event)
    {
        var promise = Promise.<Void>promise();

        try
        {
            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug(String.format("object provision request received: %s", CommonUtil.removeSensitiveFields(event, true).encodePrettily()));

            }

            var sessionId = event.containsKey(APIConstants.SESSION_ID) ? event.getString(APIConstants.SESSION_ID) : null;

            if (event.containsKey(NMSConstants.OBJECTS) && event.containsKey(NMSConstants.REDISCOVER_JOB)) //means instance level objects ..etc (process/interface/service etc...)
            {
                var rediscoverJob = NMSConstants.RediscoverJob.valueOfName(event.getString(REDISCOVER_JOB));

                if (rediscoverJob == RediscoverJob.APP) // application provision request need to check license limit here
                {
                    if (LicenseUtil.licensedObjectConsumed())
                    {
                        EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_APP_DISCOVERY_PROGRESS, event);

                        var metrics = new ArrayList<JsonObject>();

                        vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, event.put(MESSAGE, String.format(InfoMessageConstants.DISCOVERY_APP_SUCCEEDED, event.getString(Metric.METRIC_TYPE),
                                event.getString(AIOpsObject.OBJECT_TARGET))));

                        var discoveryContext = event.getJsonObject(Discovery.DISCOVERY_CONTEXT);

                        var object = discoveryContext.getJsonObject(OBJECT);

                        discoveryContext.remove(OBJECT);

                        vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject()
                                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_APPLICATION.name()).put(ID, event.getLong(Metric.METRIC_OBJECT)).put(Metric.METRIC_TYPE, event.getString(AIOpsObject.OBJECT_TYPE)));

                        if (event.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(Type.EXCHANGE_MAILBOX_ROLE.getName()))
                        {
                            ObjectManagerCacheStore.getStore().getItemsByObjectType(Type.EXCHANGE_MAILBOX).forEach(item ->
                            {
                                var context = new JsonObject()
                                        .put(NMSConstants.REDISCOVER_JOB, NMSConstants.RediscoverJob.APP.getName()) //for mapping process->app dependency
                                        .put(Metric.METRIC_CATEGORY, NMSConstants.getCategory(NMSConstants.Type.valueOfName(event.getString(Metric.METRIC_TYPE))).getName())
                                        .put(Metric.METRIC_DISCOVERY_METHOD, event.getString(AIOpsObject.OBJECT_DISCOVERY_METHOD))
                                        .put(Metric.METRIC_CONTEXT, discoveryContext)
                                        .put(Metric.METRIC_OBJECT, event.getLong(Metric.METRIC_OBJECT))
                                        .put(Metric.METRIC_PLUGIN, item.getString(Metric.METRIC_PLUGIN))
                                        .put(Metric.METRIC_CREDENTIAL_PROFILE, event.containsKey(AIOpsObject.OBJECT_CREDENTIAL_PROFILE) ? event.getLong(AIOpsObject.OBJECT_CREDENTIAL_PROFILE) : NOT_AVAILABLE)
                                        .put(Metric.METRIC_STATE, State.ENABLE);

                                // for dependency mapping put process/service name
                                if (event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD) != null)
                                {
                                    context.put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD));
                                }

                                metrics.add(context);
                            });
                        }

                        ObjectManagerCacheStore.getStore().getItemsByObjectType(NMSConstants.Type.valueOfName(event.getString(AIOpsObject.OBJECT_TYPE))).forEach(item ->
                        {
                            var context = new JsonObject()
                                    .put(NMSConstants.REDISCOVER_JOB, NMSConstants.RediscoverJob.APP.getName()) //for mapping process->app dependency
                                    .put(Metric.METRIC_CATEGORY, NMSConstants.getCategory(NMSConstants.Type.valueOfName(event.getString(Metric.METRIC_TYPE))).getName())
                                    .put(Metric.METRIC_DISCOVERY_METHOD, event.getString(AIOpsObject.OBJECT_DISCOVERY_METHOD))
                                    .put(Metric.METRIC_CONTEXT, discoveryContext)
                                    .put(Metric.METRIC_OBJECT, event.getLong(Metric.METRIC_OBJECT))
                                    .put(Metric.METRIC_PLUGIN, item.getString(Metric.METRIC_PLUGIN))
                                    .put(Metric.METRIC_CREDENTIAL_PROFILE, event.containsKey(AIOpsObject.OBJECT_CREDENTIAL_PROFILE) ? event.getLong(AIOpsObject.OBJECT_CREDENTIAL_PROFILE) : NOT_AVAILABLE);

                            // for dependency mapping put process/service name
                            if (event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD) != null)
                            {
                                context.put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD));
                            }

                            if (APPLICATION_PLUGINS.contains(item.getString(Metric.METRIC_PLUGIN)))
                            {
                                context.put(Metric.METRIC_CONTEXT, new JsonObject().mergeIn(discoveryContext).put(OBJECT, object));
                            }

                            metrics.add(context);

                            try
                            {
                                var monitor = ObjectConfigStore.getStore().getItem(event.getLong(Metric.METRIC_OBJECT));

                                //for agent will be sending it to agent configuration
                                if (monitor != null && monitor.containsKey(AIOpsObject.OBJECT_AGENT))
                                {
                                    if (AgentConfigStore.getStore().existItem(monitor.getLong(AIOpsObject.OBJECT_AGENT)))
                                    {
                                        var agent = AgentConfigStore.getStore().getItem(monitor.getLong(AIOpsObject.OBJECT_AGENT));

                                        var configs = new JsonObject(agent.getString(Agent.AGENT_CONFIGS));

                                        monitor.mergeIn(agent);

                                        var found = false;

                                        var objects = configs.getJsonObject(Agent.AGENT_METRIC).getJsonArray(object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(SYSTEM_SERVICE) ? SERVICES : PROCESSES);

                                        for (var index = 0; index < objects.size(); index++)
                                        {
                                            if (objects.getJsonObject(index).getString(AIOpsObject.OBJECT_NAME, EMPTY_VALUE).equalsIgnoreCase(object.getString(AIOpsObject.OBJECT_NAME, EMPTY_VALUE)))
                                            {
                                                found = true;

                                                break;
                                            }
                                        }

                                        if (!found)
                                        {
                                            objects.add(object);

                                            monitor.put(Agent.AGENT_CONFIGS, configs.encodePrettily()).put(EVENT_REPLY, YES);

                                            var eventId = CommonUtil.newEventId();

                                            monitor.put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_CONFIGURATION_CHANGE)
                                                    .put(EventBusConstants.EVENT_ID, eventId);

                                            vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                                                    .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_AGENT_CONFIGURATION_CHANGE)
                                                    .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                                                    .put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER)
                                                    .put(EventBusConstants.EVENT_CONTEXT, monitor));

                                            vertx.eventBus().send(EVENT_ROUTER, monitor);

                                            if (MotadataConfigUtil.devMode())
                                            {
                                                vertx.eventBus().publish("event.agent.test", monitor);
                                            }
                                        }
                                        else
                                        {
                                            if (CommonUtil.traceEnabled())
                                            {
                                                LOGGER.trace("no new objects found!");
                                            }
                                        }
                                    }
                                    else
                                    {
                                        LOGGER.warn(String.format("either metric %s deleted or object %s disabled", event.getString(Metric.METRIC_NAME), event.getString(AIOpsObject.OBJECT_NAME)));
                                    }
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.warn("failed to upgrade agent configuration!");

                                LOGGER.error(exception);
                            }
                        });

                        NMSConstants.updateMetrics(metrics, EventBusConstants.EVENT_METRIC_PROVISION).onComplete(result ->
                        {
                            LicenseCacheStore.getStore().update(APPS, true);

                            promise.complete();

                            // means application with different port except windows rdp type application hence create/update application mapper port with that application
                            if (discoveryContext.containsKey(PORT))
                            {
                                var applicationMapper = ApplicationMapperConfigStore.getStore().getItem(ApplicationMapperConfigStore.getStore().getItemByPort(discoveryContext.getInteger(PORT)));

                                if (applicationMapper != null && applicationMapper.getString(ApplicationMapper.APPLICATION_MAPPER_PROTOCOL).equalsIgnoreCase(ApplicationMapper.APPLICATION_MAPPER_PROTOCOL_TCP))
                                {
                                    // update application mapper request
                                    if (applicationMapper.getString(ApplicationMapper.APPLICATION_MAPPER_NAME).equalsIgnoreCase(event.getString(Metric.METRIC_TYPE)))
                                    {
                                        Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_APPLICATION_MAPPER,
                                                new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, applicationMapper.getLong(ID)),
                                                new JsonObject().put(ApplicationMapper.APPLICATION_MAPPER_NAME, event.getString(Metric.METRIC_TYPE)),
                                                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                                asyncResult ->
                                                {
                                                    if (asyncResult.succeeded())
                                                    {
                                                        ApplicationMapperConfigStore.getStore().updateItem(applicationMapper.getLong(ID));
                                                    }
                                                });
                                    }
                                }
                                else
                                {
                                    Bootstrap.configDBService().save(ConfigDBConstants.COLLECTION_APPLICATION_MAPPER,
                                            new JsonObject().put(ApplicationMapper.APPLICATION_MAPPER_PROTOCOL, ApplicationMapper.APPLICATION_MAPPER_PROTOCOL_TCP)
                                                    .put(ApplicationMapper.APPLICATION_MAPPER_NAME, event.getString(Metric.METRIC_TYPE))
                                                    .put(ApplicationMapper.APPLICATION_MAPPER_PORT, discoveryContext.getInteger(PORT))
                                                    .put(ConfigDBConstants.FIELD_TYPE, ConfigDBConstants.ENTITY_TYPE_SYSTEM),
                                            DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                            asyncResult ->
                                            {
                                                if (asyncResult.succeeded())
                                                {
                                                    ApplicationMapperConfigStore.getStore().addItem(asyncResult.result());
                                                }
                                            });
                                }
                            }
                        });
                    }
                    else
                    {
                        promise.complete();

                        event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_LICENSE_LIMIT_EXCEEDED).put(MESSAGE, String.format(ErrorMessageConstants.METRIC_PROVISION_FAILED, event.getString(AIOpsObject.OBJECT_TYPE), event.getString(AIOpsObject.OBJECT_NAME), ErrorMessageConstants.LICENSE_LIMIT_EXCEEDED));

                        vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, event);

                        EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), EventBusConstants.UI_NOTIFICATION_APP_DISCOVERY_PROGRESS, event);
                    }
                }
                else if (rediscoverJob == RediscoverJob.WAN_LINK)
                {
                    var objects = event.getJsonArray(NMSConstants.OBJECTS);

                    if (objects != null)
                    {
                        var remainingObjects = LicenseUtil.getRemainingObjects(rediscoverJob.getName());

                        var iterator = objects.iterator();

                        var discoveredObjects = 0;

                        while (iterator.hasNext())
                        {
                            var object = (JsonObject) iterator.next();

                            if (discoveredObjects >= remainingObjects)
                            {
                                iterator.remove();

                                vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION,
                                        new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_REDISCOVER)
                                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_LICENSE_LIMIT_EXCEEDED)
                                                .put(MESSAGE, String.format(ErrorMessageConstants.METRIC_PROVISION_FAILED, object.getString(AIOpsObject.OBJECT_NAME), event.getString(AIOpsObject.OBJECT_NAME), ErrorMessageConstants.LICENSE_LIMIT_EXCEEDED))
                                                .put(STATUS, STATUS_FAIL));

                                publish(new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_REDISCOVER)
                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_LICENSE_LIMIT_EXCEEDED)
                                        .put(MESSAGE, String.format(ErrorMessageConstants.METRIC_PROVISION_FAILED, object.getString(AIOpsObject.OBJECT_NAME), event.getString(AIOpsObject.OBJECT_NAME), ErrorMessageConstants.LICENSE_LIMIT_EXCEEDED))
                                        .put(STATUS, STATUS_FAIL), sessionId);

                                LOGGER.warn(String.format(ErrorMessageConstants.METRIC_PROVISION_FAILED, object.getString(AIOpsObject.OBJECT_NAME), event.getString(AIOpsObject.OBJECT_NAME), ErrorMessageConstants.LICENSE_LIMIT_EXCEEDED));
                            }
                            else
                            {
                                discoveredObjects++;

                                var tags = JsonArray.of(SOURCE_IP + COLON_SEPARATOR + object.getString(SOURCE_IP),
                                        DESTINATION_IP + COLON_SEPARATOR + object.getString(DESTINATION_IP), INTERNET_SERVICE_PROVIDER + COLON_SEPARATOR + object.getString(INTERNET_SERVICE_PROVIDER));

                                if (object.containsKey(SOURCE_INTERFACE_NAME) && CommonUtil.isNotNullOrEmpty(SOURCE_INTERFACE_NAME))
                                {
                                    tags.add(SOURCE_INTERFACE_NAME + COLON_SEPARATOR + object.getString(SOURCE_INTERFACE_NAME));
                                }

                                if (object.containsKey(SOURCE_ROUTER_LOCATION) && CommonUtil.isNotNullOrEmpty(SOURCE_ROUTER_LOCATION))
                                {
                                    tags.add(SOURCE_ROUTER_LOCATION + COLON_SEPARATOR + object.getString(SOURCE_ROUTER_LOCATION));
                                }

                                if (object.containsKey(DESTINATION_ROUTER_LOCATION) && CommonUtil.isNotNullOrEmpty(DESTINATION_ROUTER_LOCATION))
                                {
                                    tags.add(DESTINATION_ROUTER_LOCATION + COLON_SEPARATOR + object.getString(DESTINATION_ROUTER_LOCATION));
                                }

                                object.put(INSTANCE_TAGS, tags);

                                object.remove(AIOpsObject.OBJECT_TYPE);
                            }
                        }

                        // if metric already exist then update objects else send to provision metric
                        if (MetricConfigStore.getStore().getItemByMetricPlugin(event.getLong(ID), event.getString(Metric.METRIC_PLUGIN)) != NOT_AVAILABLE)
                        {
                            promise.complete(); // no need to block pending events ... that's why completing earlier....

                            var metric = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(event.getLong(ID), event.getString(Metric.METRIC_PLUGIN)));

                            var availableObjects = metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(OBJECTS).addAll(objects);

                            Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_METRIC,
                                    new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, metric.getLong(ID)),
                                    metric.put(Metric.METRIC_CONTEXT, metric.getJsonObject(Metric.METRIC_CONTEXT, new JsonObject()).put(NMSConstants.OBJECTS, availableObjects)),
                                    event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS
                                    , asyncResult ->
                                    {

                                        if (asyncResult.succeeded())
                                        {
                                            // To update instance tags fields as we need all objects related to ipsla plugins
                                            updateIPSLAMetrics(metric.getString(Metric.METRIC_PLUGIN), metric.getLong(Metric.METRIC_OBJECT), availableObjects);

                                            TagCacheStore.getStore().updateInstanceTags(metric.getLong(ID), availableObjects, NMSConstants.INSTANCE_TYPES.getOrDefault(metric.getString(Metric.METRIC_PLUGIN), IPSLA));

                                            LicenseCacheStore.getStore().update(WAN_LINKS, objects.size(), true);

                                            MetricConfigStore.getStore().updateItem(metric.getLong(ID));

                                            EventBusConstants.publish(sessionId, EventBusConstants.UI_NOTIFICATION_REDISCOVER_PROVISION_PROGRESS, event.put(STATUS, STATUS_SUCCEED));
                                        }
                                        else
                                        {
                                            LOGGER.warn(String.format(OBJECT_METRIC_UPDATE_FAILED, event.getString(Metric.METRIC_NAME), event.getString(AIOpsObject.OBJECT_NAME)));

                                            event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DATABASE_UPDATE)
                                                    .put(MESSAGE, String.format(OBJECT_METRIC_UPDATE_FAILED, event.getString(Metric.METRIC_NAME), event.getString(AIOpsObject.OBJECT_NAME)));

                                            EventBusConstants.publish(sessionId, EventBusConstants.UI_NOTIFICATION_REDISCOVER_PROVISION_PROGRESS, event);
                                        }
                                    });
                        }
                        else
                        {
                            var metrics = new ArrayList<JsonObject>();

                            if (MetricConfigStore.getStore().getItemByMetricPlugin(event.getLong(ID), NMSConstants.MetricPlugin.IPSLA.getName()) == NOT_AVAILABLE)
                            {
                                metrics.add(new JsonObject().put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.IPSLA.getName())
                                        .put(Metric.METRIC_OBJECT, event.getLong(ID))
                                        .put(Metric.METRIC_DISCOVERY_METHOD, DiscoveryMethod.REMOTE.name())
                                        .put(Metric.METRIC_CREDENTIAL_PROFILE, NOT_AVAILABLE));
                            }

                            metrics.add(new JsonObject().put(Metric.METRIC_PLUGIN, event.getString(Metric.METRIC_PLUGIN))
                                    .put(NMSConstants.REDISCOVER_JOB, event.getString(REDISCOVER_JOB))
                                    .put(Metric.METRIC_CATEGORY, NMSConstants.getCategory(NMSConstants.Type.valueOfName(event.getString(Metric.METRIC_TYPE))).getName())
                                    .put(Metric.METRIC_OBJECT, event.getLong(ID))
                                    .put(OBJECTS, objects)
                                    .put(Metric.METRIC_CREDENTIAL_PROFILE, event.containsKey(AIOpsObject.OBJECT_CREDENTIAL_PROFILE) ? event.getLong(AIOpsObject.OBJECT_CREDENTIAL_PROFILE) : NOT_AVAILABLE));


                            NMSConstants.updateMetrics(metrics, EventBusConstants.EVENT_METRIC_PROVISION).onComplete(result ->
                            {
                                LicenseCacheStore.getStore().update(WAN_LINKS, objects.size(), true);

                                EventBusConstants.publish(sessionId, EventBusConstants.UI_NOTIFICATION_REDISCOVER_PROVISION_PROGRESS, event.put(STATUS, STATUS_SUCCEED));

                                promise.complete();
                            });
                        }
                    }
                    else
                    {
                        promise.complete();
                    }
                }
                else if (rediscoverJob == RediscoverJob.CONTAINER)
                {
                    promise.complete();

                    var objects = event.getJsonArray(NMSConstants.OBJECTS);

                    if (objects != null)
                    {
                        var pluginId = MetricConfigStore.getStore().getItemByMetricPlugin(event.getLong(ID), event.getString(Metric.METRIC_PLUGIN));

                        var remainingObjects = LicenseUtil.getRemainingObjects(rediscoverJob.getName());

                        if (remainingObjects < objects.size())
                        {
                            var iterator = objects.iterator();

                            var discoveredObjects = 0;

                            while (iterator.hasNext())
                            {
                                var object = (JsonObject) iterator.next();

                                if (discoveredObjects >= remainingObjects)
                                {
                                    iterator.remove();

                                    vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION,
                                            new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_REDISCOVER)
                                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_LICENSE_LIMIT_EXCEEDED)
                                                    .put(MESSAGE, String.format(ErrorMessageConstants.METRIC_PROVISION_FAILED, object.getString(AIOpsObject.OBJECT_NAME), event.getString(AIOpsObject.OBJECT_NAME), ErrorMessageConstants.LICENSE_LIMIT_EXCEEDED))
                                                    .put(STATUS, STATUS_FAIL));

                                    publish(new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_REDISCOVER)
                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_LICENSE_LIMIT_EXCEEDED)
                                            .put(MESSAGE, String.format(ErrorMessageConstants.METRIC_PROVISION_FAILED, object.getString(AIOpsObject.OBJECT_NAME), event.getString(AIOpsObject.OBJECT_NAME), ErrorMessageConstants.LICENSE_LIMIT_EXCEEDED))
                                            .put(STATUS, STATUS_FAIL), sessionId);

                                    LOGGER.warn(String.format(ErrorMessageConstants.METRIC_PROVISION_FAILED, object.getString(AIOpsObject.OBJECT_NAME), event.getString(AIOpsObject.OBJECT_NAME), ErrorMessageConstants.LICENSE_LIMIT_EXCEEDED));
                                }
                                else
                                {
                                    discoveredObjects++;
                                }
                            }
                        }

                        if (pluginId != NOT_AVAILABLE && MetricConfigStore.getStore().existItem(pluginId))
                        {
                            var item = MetricConfigStore.getStore().getItem(pluginId);

                            removeDuplicateObjects(event, objects, MetricConfigStore.getStore().getObjects(pluginId));

                            for (var index = 0; index < objects.size(); index++)
                            {
                                var object = objects.getJsonObject(index);

                                if (object.containsKey(INSTANCE_TAGS) && !object.getJsonArray(INSTANCE_TAGS).isEmpty() &&
                                        TagConfigStore.getStore().getItems(object.getJsonArray(INSTANCE_TAGS)).isEmpty())
                                {
                                    object.put(INSTANCE_TAGS, TagConfigStore.getStore().addItems(object.getJsonArray(INSTANCE_TAGS), Tag.TagType.INSTANCE.getName(), ConfigDBConstants.ENTITY_TYPE_USER));
                                }
                            }

                            Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_METRIC,
                                    new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, pluginId),
                                    item.put(Metric.METRIC_CONTEXT, item.getJsonObject(Metric.METRIC_CONTEXT, new JsonObject()).put(NMSConstants.OBJECTS, objects)),
                                    event.containsKey(USER_NAME) ? event.getString(USER_NAME) : SYSTEM_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS
                                    , asyncResult ->
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            TagCacheStore.getStore().updateInstanceTags(item.getLong(ID), objects, NMSConstants.INSTANCE_TYPES.getOrDefault(item.getString(Metric.METRIC_PLUGIN), NMSConstants.MetricPlugin.DOCKER_CONTAINER.getName()));

                                            MetricConfigStore.getStore().updateItem(pluginId);

                                            LicenseCacheStore.getStore().update(CONTAINERS, objects.size(), true);

                                            EventBusConstants.publish(sessionId, EventBusConstants.UI_NOTIFICATION_REDISCOVER_PROVISION_PROGRESS, event.put(STATUS, STATUS_SUCCEED));
                                        }
                                        else
                                        {
                                            LOGGER.warn(String.format(OBJECT_METRIC_UPDATE_FAILED, event.getString(Metric.METRIC_NAME), event.getString(AIOpsObject.OBJECT_NAME)));

                                            event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DATABASE_UPDATE)
                                                    .put(MESSAGE, String.format(OBJECT_METRIC_UPDATE_FAILED, event.getString(Metric.METRIC_NAME), event.getString(AIOpsObject.OBJECT_NAME)));

                                            EventBusConstants.publish(sessionId, EventBusConstants.UI_NOTIFICATION_REDISCOVER_PROVISION_PROGRESS, event);
                                        }
                                    });
                        }
                        else
                        {
                            var metrics = new ArrayList<JsonObject>();

                            var context = event.getJsonObject(Discovery.DISCOVERY_CONTEXT);

                            var object = ObjectManagerCacheStore.getStore().getItemByMetricPlugin(event.getString(Metric.METRIC_PLUGIN));

                            ObjectManagerCacheStore.getStore().getItemsByObjectType(NMSConstants.Type.valueOfName(object.getString(Metric.METRIC_TYPE))).forEach(item ->
                            {
                                try
                                {
                                    var metric = new JsonObject()
                                            .put(NMSConstants.REDISCOVER_JOB, RediscoverJob.CONTAINER.getName())
                                            .put(Metric.METRIC_CATEGORY, NMSConstants.getCategory(NMSConstants.Type.valueOfName(event.getString(Metric.METRIC_TYPE))).getName())
                                            .put(Metric.METRIC_OBJECT, event.getLong(ID))
                                            .put(Metric.METRIC_CONTEXT, context)
                                            .put(Metric.METRIC_TYPE, event.getString(Metric.METRIC_TYPE))
                                            .put(Metric.METRIC_PLUGIN, item.getString(Metric.METRIC_PLUGIN))
                                            .put(Metric.METRIC_CREDENTIAL_PROFILE, event.containsKey(AIOpsObject.OBJECT_CREDENTIAL_PROFILE) ? event.getLong(AIOpsObject.OBJECT_CREDENTIAL_PROFILE) : NOT_AVAILABLE);

                                    if (CONTAINER_PLUGINS.contains(item.getString(Metric.METRIC_PLUGIN)))
                                    {
                                        metric.put(Metric.METRIC_CONTEXT, new JsonObject().mergeIn(context).put(OBJECTS, objects));
                                    }

                                    metrics.add(metric);
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            });

                            NMSConstants.updateMetrics(metrics, EventBusConstants.EVENT_METRIC_PROVISION).onComplete(result ->
                            {
                                LicenseCacheStore.getStore().update(CONTAINERS, objects.size(), true);

                                EventBusConstants.publish(sessionId, EventBusConstants.UI_NOTIFICATION_REDISCOVER_PROVISION_PROGRESS, event.put(STATUS, STATUS_SUCCEED));
                            });
                        }
                    }
                }
                else
                {
                    promise.complete(); // no need to block pending events ... that's why completing earlier....

                    var metricId = event.getLong(ID);

                    var objects = event.getJsonArray(NMSConstants.OBJECTS);

                    // Check License

                    if (rediscoverJob == RediscoverJob.VIRTUAL_MACHINE || rediscoverJob == RediscoverJob.ACCESS_POINT || rediscoverJob == RediscoverJob.CLOUD || rediscoverJob == RediscoverJob.VIRTUAL_MACHINE_HCI)
                    {
                        var remainingObjects = LicenseUtil.getRemainingObjects(rediscoverJob.getName());

                        if (objects != null && remainingObjects < objects.size())
                        {
                            var iterator = objects.iterator();

                            var discoveredObjects = 0;

                            while (iterator.hasNext())
                            {
                                var object = (JsonObject) iterator.next();

                                if (discoveredObjects >= remainingObjects)   // remove extra qualified VMs
                                {
                                    iterator.remove();

                                    vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION,
                                            new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_REDISCOVER)
                                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_LICENSE_LIMIT_EXCEEDED)
                                                    .put(MESSAGE, String.format(ErrorMessageConstants.METRIC_PROVISION_FAILED, object.getString(AIOpsObject.OBJECT_NAME), event.getString(AIOpsObject.OBJECT_NAME), ErrorMessageConstants.LICENSE_LIMIT_EXCEEDED))
                                                    .put(STATUS, STATUS_FAIL));

                                    publish(new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_REDISCOVER)
                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_LICENSE_LIMIT_EXCEEDED)
                                            .put(MESSAGE, String.format(ErrorMessageConstants.METRIC_PROVISION_FAILED, object.getString(AIOpsObject.OBJECT_NAME), event.getString(AIOpsObject.OBJECT_NAME), ErrorMessageConstants.LICENSE_LIMIT_EXCEEDED))
                                            .put(STATUS, STATUS_FAIL), sessionId);

                                    LOGGER.warn(String.format(ErrorMessageConstants.METRIC_PROVISION_FAILED, object.getString(AIOpsObject.OBJECT_NAME), event.getString(AIOpsObject.OBJECT_NAME), ErrorMessageConstants.LICENSE_LIMIT_EXCEEDED));
                                }
                                else
                                {
                                    discoveredObjects++;
                                }

                            }
                        }

                        if ((RediscoverJob.VIRTUAL_MACHINE == rediscoverJob || RediscoverJob.VIRTUAL_MACHINE_HCI == rediscoverJob) && objects != null)
                        {
                            LicenseCacheStore.getStore().update(VMS, objects.size(), true);
                        }
                        else if (RediscoverJob.ACCESS_POINT == rediscoverJob && objects != null)
                        {
                            LicenseCacheStore.getStore().update(ACCESS_POINTS, objects.size(), true);
                        }
                    }

                    if (MetricConfigStore.getStore().existItem(metricId))
                    {
                        var metric = MetricConfigStore.getStore().getItem(metricId);

                        removeDuplicateObjects(event, objects, MetricConfigStore.getStore().getObjects(metricId));

                        Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_METRIC,
                                new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, metricId),
                                metric.put(Metric.METRIC_CONTEXT, metric.getJsonObject(Metric.METRIC_CONTEXT, new JsonObject()).put(NMSConstants.OBJECTS, objects)),
                                event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS
                                , asyncResult ->
                                {

                                    if (asyncResult.succeeded())
                                    {
                                        MetricConfigStore.getStore().updateItem(metricId);

                                        var object = ObjectConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_OBJECT));

                                        //for agent will be sending it to agent configuration
                                        if (object != null && object.containsKey(AIOpsObject.OBJECT_AGENT) && (rediscoverJob == RediscoverJob.PROCESS || rediscoverJob == RediscoverJob.WINDOWS_SERVICE))
                                        {
                                            if (AgentConfigStore.getStore().existItem(object.getLong(AIOpsObject.OBJECT_AGENT)))
                                            {
                                                var item = AgentConfigStore.getStore().getItem(object.getLong(AIOpsObject.OBJECT_AGENT));

                                                var configs = new JsonObject(item.getString(Agent.AGENT_CONFIGS));

                                                object.mergeIn(item);

                                                configs.getJsonObject(Agent.AGENT_METRIC).put(rediscoverJob == RediscoverJob.PROCESS ? PROCESSES : SERVICES, objects);

                                                object.put(Agent.AGENT_CONFIGS, configs.encodePrettily()).put(EVENT_REPLY, YES);

                                                var eventId = CommonUtil.newEventId();

                                                object.put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_CONFIGURATION_CHANGE)
                                                        .put(EventBusConstants.EVENT_ID, eventId);

                                                vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                                                        .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_AGENT_CONFIGURATION_CHANGE)
                                                        .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                                                        .put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER)
                                                        .put(EventBusConstants.EVENT_CONTEXT, object));

                                                vertx.eventBus().<JsonObject>request(EVENT_ROUTER, object, result ->
                                                {
                                                    if (result.succeeded())
                                                    {
                                                        EventBusConstants.publish(sessionId, EventBusConstants.UI_NOTIFICATION_REDISCOVER_PROVISION_PROGRESS, event.put(STATUS, STATUS_SUCCEED));

                                                        vertx.eventBus().send(EventBusConstants.EVENT_REDISCOVER_SCHEDULER_UPDATE, event.put(STATUS, STATUS_SUCCEED));

                                                        if (rediscoverJob == RediscoverJob.PROCESS)
                                                        {
                                                            vertx.eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(), object
                                                                    .put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                                                                    .put(AIOpsConstants.DEPENDENCY_SOURCE, object.getString(AIOpsObject.OBJECT_IP))
                                                                    .put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.FOUR.getName())
                                                                    .put(NMSConstants.OBJECTS, objects)
                                                                    .put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.ADD_MULTIPLES.getName()));
                                                        }
                                                    }
                                                    else
                                                    {
                                                        EventBusConstants.publish(sessionId, UI_NOTIFICATION_REDISCOVER_PROVISION_PROGRESS, event.put(STATUS, STATUS_FAIL)
                                                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                                .put(ERRORS, new JsonArray(new ArrayList<JsonObject>(1))
                                                                        .add(new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(ERROR, CommonUtil.formatStackTrace(asyncResult.cause().getStackTrace())).put(MESSAGE, asyncResult.cause().getMessage())))
                                                                .put(ERROR, CommonUtil.formatStackTrace(asyncResult.cause().getStackTrace()))
                                                                .put(MESSAGE, String.format(AGENT_CONFIG_UPDATE_FAILED, event.getString(AIOpsObject.OBJECT_NAME), asyncResult.cause().getMessage())));
                                                    }
                                                });
                                            }
                                            else
                                            {
                                                event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                                                        .put(MESSAGE, String.format("either metric %s deleted or object %s disabled", event.getString(Metric.METRIC_NAME), event.getString(AIOpsObject.OBJECT_NAME)));

                                                LOGGER.warn(event.getString(MESSAGE));

                                                EventBusConstants.publish(sessionId, EventBusConstants.UI_NOTIFICATION_REDISCOVER_PROVISION_PROGRESS, event);
                                            }
                                        }
                                        else
                                        {
                                            EventBusConstants.publish(sessionId, EventBusConstants.UI_NOTIFICATION_REDISCOVER_PROVISION_PROGRESS, event.put(STATUS, STATUS_SUCCEED));

                                            vertx.eventBus().send(EventBusConstants.EVENT_REDISCOVER_SCHEDULER_UPDATE, event.put(STATUS, STATUS_SUCCEED));

                                            //if metric plugin is qualified for rediscover metrics than let's go for dependency building
                                            if (AIOpsConstants.hasDependencyProp(metric.getString(Metric.METRIC_PLUGIN), true))
                                            {
                                                notifyDependencyManager(metric, objects, event);
                                            }
                                        }

                                        if (metric.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()) && object != null)
                                        {
                                            vertx.eventBus().send(EventBusConstants.EVENT_MAC_SCANNER_RESPONSE, object.put(RESULT, new JsonObject().put(NMSConstants.OBJECTS, objects)));
                                        }
                                    }
                                    else
                                    {
                                        LOGGER.warn(String.format(OBJECT_METRIC_UPDATE_FAILED, event.getString(Metric.METRIC_NAME), event.getString(AIOpsObject.OBJECT_NAME)));

                                        event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DATABASE_UPDATE)
                                                .put(MESSAGE, String.format(OBJECT_METRIC_UPDATE_FAILED, event.getString(Metric.METRIC_NAME), event.getString(AIOpsObject.OBJECT_NAME)));

                                        EventBusConstants.publish(sessionId, EventBusConstants.UI_NOTIFICATION_REDISCOVER_PROVISION_PROGRESS, event);
                                    }

                                });

                    }
                    else
                    {
                        event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                                .put(MESSAGE, String.format("either metric %s deleted or object %s disabled", event.getString(Metric.METRIC_NAME), event.getString(AIOpsObject.OBJECT_NAME)));

                        LOGGER.warn(event.getString(MESSAGE));

                        EventBusConstants.publish(sessionId, EventBusConstants.UI_NOTIFICATION_REDISCOVER_PROVISION_PROGRESS, event);
                    }
                }
            }
            else //means server/application/cloud/db/network level object
            {
                var eventId = event.getLong(EventBusConstants.EVENT_ID);

                vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                        .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_OBJECT_PROVISION)
                        .put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER)
                        .put(EventBusConstants.EVENT_ID, eventId));

                var status = new AtomicBoolean(true);

                var object = new JsonObject();

                var rediscover = false;

                if (event.containsKey(NMSConstants.REDISCOVER_JOB))
                {
                    // request from rediscover

                    var id = event.getLong(ID);

                    object.mergeIn(event.getJsonObject(NMSConstants.OBJECT));

                    if (ObjectConfigStore.getStore().existItem(id))
                    {
                        var parentObject = ObjectConfigStore.getStore().getItem(id);

                        if (CommonUtil.debugEnabled())
                        {
                            LOGGER.debug(String.format("parent object: %s", CommonUtil.removeSensitiveFields(parentObject, true).encodePrettily()));
                        }

                        object.put(AIOpsObject.OBJECT_GROUPS, parentObject.getJsonArray(AIOpsObject.OBJECT_GROUPS));

                        if (parentObject.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS) != null && !parentObject.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS).isEmpty())
                        {
                            object.put(AIOpsObject.OBJECT_EVENT_PROCESSORS, parentObject.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS));
                        }

                        if (parentObject.getJsonObject(AIOpsObject.OBJECT_CONTEXT) != null)
                        {
                            rediscover = true;

                            parentObject.getJsonObject(AIOpsObject.OBJECT_CONTEXT).mergeIn(object.getJsonObject(AIOpsObject.OBJECT_CONTEXT));

                            object.put(AIOpsObject.OBJECT_CONTEXT, parentObject.getJsonObject(AIOpsObject.OBJECT_CONTEXT));
                        }

                    }

                    else
                    {
                        LOGGER.warn(String.format("parent object %s not found in the store...", id));

                        promise.fail(String.format("parent object %s not found in the store...", id));

                        vertx.eventBus().send(EventBusConstants.EVENT_FAIL, new JsonObject().put(EventBusConstants.EVENT_ID, eventId).put(ERROR_CODE, ErrorCodes.ERROR_CODE_OBJECT_PROVISION_NO_PARENT)
                                .put(MESSAGE, String.format(PROVISION_OBJECT_FAILED_PARENT_OBJECT_NOT_FOUND, object.getString(AIOpsObject.OBJECT_NAME))));

                        publish(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_OBJECT_PROVISION_NO_PARENT).put(MESSAGE, String.format(PROVISION_OBJECT_FAILED_PARENT_OBJECT_NOT_FOUND, object.getString(AIOpsObject.OBJECT_NAME)))
                                , sessionId);

                        status.set(false);
                    }
                }

                else //request from discovery scheduler or result //
                {
                    object.mergeIn(event);
                }

                if (status.get())
                {

                    var credentialProfileId = object.containsKey(AIOpsObject.OBJECT_CREDENTIAL_PROFILE) ? CommonUtil.getLong(object.remove(AIOpsObject.OBJECT_CREDENTIAL_PROFILE)) : NOT_AVAILABLE;

                    if (credentialProfileId != NOT_AVAILABLE && !CredentialProfileConfigStore.getStore().existItem(credentialProfileId))
                    {
                        status.set(false);
                    }

                    if (status.get())
                    {
                        var type = NMSConstants.Type.valueOfName(object.getString(AIOpsObject.OBJECT_TYPE));

                        var category = CommonUtil.isNotNullOrEmpty(object.getString(AIOpsObject.OBJECT_CATEGORY))
                                ? NMSConstants.Category.valueOfName(object.getString(AIOpsObject.OBJECT_CATEGORY)) : NMSConstants.getCategory(type);

                        // check license check here for agentless monitors only...for agent monitor we put check on agent manager
                        if ((category == Category.SERVER && object.containsKey(AIOpsObject.OBJECT_AGENT)) || LicenseUtil.licensedObjectConsumed())
                        {
                            var objectId = 0;

                            var pingableObject = category == Category.NETWORK || category == Category.SERVER || category == Category.VIRTUALIZATION || category == Category.HCI || category == Category.SDN
                                    || category == Category.STORAGE || category == Category.OTHER;

                            if (category == NMSConstants.Category.SERVICE_CHECK)
                            {
                                objectId = getObjectId(ObjectConfigStore.getStore().getItemsByType(type, object.getString(AIOpsObject.OBJECT_DISCOVERY_METHOD)), object, type, false);
                            }

                            else if (type == Type.PING)
                            {
                                var id = ObjectConfigStore.getStore().getItemByTarget(object.getString(AIOpsObject.OBJECT_TARGET));

                                if (id == null)
                                {
                                    id = ObjectConfigStore.getStore().getItemByIP(object.getString(AIOpsObject.OBJECT_IP));

                                    if (id == null)
                                    {
                                        id = ObjectConfigStore.getStore().getIdByObjectName(object.getString(AIOpsObject.OBJECT_NAME));
                                    }
                                }

                                objectId = id != null ? ObjectConfigStore.getStore().getItem(id).getInteger(AIOpsObject.OBJECT_ID) : NOT_AVAILABLE;
                            }
                            else
                            {
                                objectId = ObjectConfigStore.getStore().getObjectIdByTarget(object.getString(AIOpsObject.OBJECT_TARGET), type);

                                if (objectId == NOT_AVAILABLE)
                                {
                                    objectId = category != NMSConstants.Category.CLOUD ? ObjectConfigStore.getStore().getObjectIdByIP(object.getString(AIOpsObject.OBJECT_IP), type) : NOT_AVAILABLE;

                                    if (objectId == NOT_AVAILABLE)
                                    {
                                        objectId = category != NMSConstants.Category.CLOUD ? ObjectConfigStore.getStore().getObjectIdByObjectName(object.getString(AIOpsObject.OBJECT_NAME)) : NOT_AVAILABLE;
                                    }
                                }

                                if (objectId == NOT_AVAILABLE && pingableObject)
                                {
                                    var item = ObjectConfigStore.getStore().flatItemByMultipleValues(AIOpsObject.OBJECT_IP, object.getString(AIOpsObject.OBJECT_IP),
                                            AIOpsObject.OBJECT_CATEGORY, Category.OTHER.getName(), AIOpsObject.OBJECT_TYPE, Type.PING.getName());

                                    if (item != null)
                                    {
                                        objectId = item.getInteger(AIOpsObject.OBJECT_ID);
                                    }
                                }
                            }

                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("object id: %s", objectId));
                            }

                            if (objectId != NOT_AVAILABLE)
                            {
                                if (category == NMSConstants.Category.SERVER
                                        || category == NMSConstants.Category.VIRTUALIZATION
                                        || category == NMSConstants.Category.NETWORK
                                        || category == NMSConstants.Category.OTHER
                                        || category == NMSConstants.Category.SERVICE_CHECK
                                        || category == NMSConstants.Category.HCI
                                        || category == NMSConstants.Category.SDN
                                        || category == NMSConstants.Category.STORAGE
                                        || category == NMSConstants.Category.CLOUD)
                                {
                                    status.set(false);
                                }
                            }

                            else
                            {

                                //let's check into archived store...

                                if (category == NMSConstants.Category.SERVICE_CHECK)
                                {
                                    objectId = getObjectId(ArchivedObjectConfigStore.getStore().getItemsByType(type, object.getString(AIOpsObject.OBJECT_DISCOVERY_METHOD)), object, type, true);
                                }
                                else if (type == Type.PING)
                                {
                                    var item = ArchivedObjectConfigStore.getStore().getItemByTarget(object.getString(AIOpsObject.OBJECT_TARGET), object.getString(AIOpsObject.OBJECT_CATEGORY));

                                    if (item == null)
                                    {
                                        item = ArchivedObjectConfigStore.getStore().getItemByIP(object.getString(AIOpsObject.OBJECT_IP), object.getString(AIOpsObject.OBJECT_CATEGORY));
                                    }

                                    objectId = item != null ? item.getInteger(AIOpsObject.OBJECT_ID) : NOT_AVAILABLE;
                                }
                                else if (ARCHIVED_ENABLED && category == Category.NETWORK)
                                {
                                    var item = ArchivedObjectConfigStore.getStore().getItemByTarget(object.getString(AIOpsObject.OBJECT_TARGET), type);

                                    if (item == null)
                                    {
                                        item = ArchivedObjectConfigStore.getStore().getItemByIP(object.getString(AIOpsObject.OBJECT_IP), type);
                                    }

                                    //if system oid is different then we will consider this as new object to provision
                                    if (item != null && object.containsKey(AIOpsObject.OBJECT_SYSTEM_OID) && item.containsKey(AIOpsObject.OBJECT_SYSTEM_OID))
                                    {
                                        objectId = object.getString(AIOpsObject.OBJECT_SYSTEM_OID).equalsIgnoreCase(item.getString(AIOpsObject.OBJECT_SYSTEM_OID)) ? item.getInteger(AIOpsObject.OBJECT_ID) : NOT_AVAILABLE;
                                    }
                                }
                                else
                                {
                                    objectId = ArchivedObjectConfigStore.getStore().getObjectIdByTarget(object.getString(AIOpsObject.OBJECT_TARGET), type);

                                    if (objectId == NOT_AVAILABLE)
                                    {
                                        objectId = category != NMSConstants.Category.CLOUD ? ArchivedObjectConfigStore.getStore().getObjectIdByIP(object.getString(AIOpsObject.OBJECT_IP), type) : NOT_AVAILABLE;
                                    }

                                    if (objectId == NOT_AVAILABLE && pingableObject)
                                    {
                                        var item = ArchivedObjectConfigStore.getStore().flatItemByMultipleValues(AIOpsObject.OBJECT_IP, object.getString(AIOpsObject.OBJECT_IP),
                                                AIOpsObject.OBJECT_CATEGORY, Category.OTHER.getName(), AIOpsObject.OBJECT_TYPE, Type.PING.getName());

                                        if (item != null)
                                        {
                                            objectId = item.getInteger(AIOpsObject.OBJECT_ID);
                                        }
                                    }
                                }

                                if (objectId == NOT_AVAILABLE)
                                {
                                    //not found in the archived store ... let's generate from object store...

                                    if (!ArchivedObjectConfigStore.getStore().getItems().isEmpty())
                                    {
                                        objectId = Math.max(ArchivedObjectConfigStore.getStore().getNextObjectId(), ObjectConfigStore.getStore().getNextObjectId());
                                    }
                                    else
                                    {
                                        objectId = ObjectConfigStore.getStore().getNextObjectId();
                                    }

                                    if (CommonUtil.debugEnabled())
                                    {
                                        LOGGER.debug(String.format("new generated object id: %s", objectId));
                                    }
                                }

                            }

                            if (status.get())
                            {
                                object.put(AIOpsObject.OBJECT_CREATION_TIME, DateTimeUtil.timestamp())
                                        .put(AIOpsObject.OBJECT_CREATION_TIME_SECONDS, DateTimeUtil.currentSeconds())
                                        .put(AIOpsObject.OBJECT_BUSINESS_HOUR_PROFILE, DEFAULT_ID)
                                        .put(AIOpsObject.OBJECT_CATEGORY, category.getName())
                                        .put(AIOpsObject.OBJECT_STATE, NMSConstants.State.ENABLE.name())
                                        .put(AIOpsObject.OBJECT_ID, objectId);

                                if (category == NMSConstants.Category.NETWORK && object.containsKey(AIOpsObject.OBJECT_SYSTEM_OID) && !NMSConstants.isWireless(type))
                                {
                                    // if category is network device than check into device catalog with system oid and if device catalog is available for that system oid add catalog id and vendor
                                    var deviceCatalog = SNMPDeviceCatalogConfigStore.getStore().getItemByValue(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_OID, object.getString(AIOpsObject.OBJECT_SYSTEM_OID));

                                    if (deviceCatalog != null && !deviceCatalog.isEmpty())
                                    {
                                        if (CommonUtil.debugEnabled())
                                        {
                                            LOGGER.debug(String.format("snmp device catalog: %s", deviceCatalog.encodePrettily()));
                                        }

                                        object.put(AIOpsObject.OBJECT_SNMP_DEVICE_CATALOG, deviceCatalog.getLong(ID))
                                                .put(AIOpsObject.OBJECT_MAKE_MODEL, deviceCatalog.getString(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_MODEL))
                                                .put(AIOpsObject.OBJECT_VENDOR, deviceCatalog.getString(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_VENDOR));
                                    }
                                    else
                                    {
                                        status.set(false);

                                        LOGGER.warn(String.format(ErrorMessageConstants.PROVISION_OBJECT_FAILED_NO_SNMP_DEVICE_CATALOG_FOUND, object.getString(AIOpsObject.OBJECT_NAME), object.getString(AIOpsObject.OBJECT_SYSTEM_OID)));

                                        promise.fail(String.format(ErrorMessageConstants.PROVISION_OBJECT_FAILED_NO_SNMP_DEVICE_CATALOG_FOUND, object.getString(AIOpsObject.OBJECT_NAME), object.getString(AIOpsObject.OBJECT_SYSTEM_OID)));

                                        vertx.eventBus().send(EventBusConstants.EVENT_FAIL, new JsonObject().put(EventBusConstants.EVENT_ID, eventId).put(ERROR_CODE, ErrorCodes.ERROR_CODE_OBJECT_PROVISION_NO_SNMP_CATALOG)
                                                .put(MESSAGE, String.format(ErrorMessageConstants.PROVISION_OBJECT_FAILED_NO_SNMP_DEVICE_CATALOG_FOUND, object.getString(AIOpsObject.OBJECT_NAME), object.getString(AIOpsObject.OBJECT_SYSTEM_OID))));

                                        publish(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_OBJECT_PROVISION_NO_SNMP_CATALOG)
                                                .put(MESSAGE, String.format(ErrorMessageConstants.PROVISION_OBJECT_FAILED_NO_SNMP_DEVICE_CATALOG_FOUND, object.getString(AIOpsObject.OBJECT_NAME), object.getString(AIOpsObject.OBJECT_SYSTEM_OID))), sessionId);
                                    }
                                }

                                if (status.get())
                                {
                                    var objects = object.containsKey(NMSConstants.OBJECTS) ? object.getJsonArray(NMSConstants.OBJECTS) : null;

                                    object.remove(NMSConstants.OBJECTS);

                                    object.remove(APIConstants.SESSION_ID);

                                    object.remove(EVENT_ID);

                                    if (category == NMSConstants.Category.CLOUD)
                                    {
                                        object.put(AIOpsObject.OBJECT_VENDOR, NMSConstants.getCloudType(type).getName());
                                    }

                                    var objectContext = object.getJsonObject(AIOpsObject.OBJECT_CONTEXT);

                                    if (objectContext != null && objectContext.containsKey(DISCOVERED_OBJECTS))
                                    {
                                        event.put(DISCOVERED_OBJECTS, objectContext.remove(DISCOVERED_OBJECTS));
                                    }

                                    var cloudInstanceStatus = objectContext != null && objectContext.containsKey(CLOUD_SERVICE_DOWN_INSTANCE_DISCOVERY) ? CommonUtil.getString(objectContext.remove(CLOUD_SERVICE_DOWN_INSTANCE_DISCOVERY)) : null;

                                    var pluginId = CommonUtil.getString(ObjectManagerCacheStore.getStore().getPluginIdByMetricPlugin(NMSConstants.getMetricPlugin(category == Category.CLOUD ? NMSConstants.getCloudType(type) : category == Category.SDN ? NMSConstants.getSDNType(type) : type)));

                                    var metricContext = objectContext != null && pluginId != null && objectContext.containsKey(pluginId) ? objectContext.getJsonObject(pluginId) : null;

                                    if (metricContext != null)
                                    {
                                        objectContext.remove(pluginId);

                                        if (rediscover)
                                        {
                                            for (var metric : MetricConfigStore.getStore().getItemsByObject(event.getLong(ID)))
                                            {
                                                if (NMSConstants.getMetricPlugin(category == Category.CLOUD ? NMSConstants.getCloudType(type) : type).equalsIgnoreCase(metric.getString(Metric.METRIC_PLUGIN)))
                                                {
                                                    metric.getJsonObject(Metric.METRIC_CONTEXT).put(pluginId, metricContext);

                                                    Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_METRIC,
                                                            new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, metric.getLong(ID)),
                                                            metric,
                                                            DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                                            result ->
                                                            {
                                                                if (result.succeeded())
                                                                {
                                                                    MetricConfigStore.getStore().updateItem(metric.getLong(ID));
                                                                }
                                                                else
                                                                {
                                                                    LOGGER.error(result.cause());
                                                                }
                                                            });
                                                }
                                            }
                                        }
                                    }

                                    if (CommonUtil.isNotNullOrEmpty(cloudInstanceStatus) && (type == Type.AWS_CLOUD || type == Type.AZURE_CLOUD))
                                    {
                                        objectContext.put(CLOUD_SERVICE_DOWN_INSTANCE_DISCOVERY, cloudInstanceStatus);
                                    }

                                    object.remove(EVENT_REPLY);

                                    removeFields(object);

                                    vertx.eventBus().send(EventBusConstants.EVENT_PROGRESS_UPDATE, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                                            .put(EventBusConstants.EVENT_CONTEXT, object));

                                    Bootstrap.configDBService().save(ConfigDBConstants.COLLECTION_OBJECT, object, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS, asyncResponse ->
                                    {

                                        if (asyncResponse.succeeded())
                                        {
                                            ObjectConfigStore.getStore().addItem(asyncResponse.result()).onComplete(response ->
                                            {

                                                if (response.succeeded())
                                                {
                                                    vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                                                            .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_OBJECT.name())
                                                            .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                                                            .put(ID, asyncResponse.result()));

                                                    vertx.eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                                                            .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_OBJECT.name())
                                                            .put(ID, asyncResponse.result()));

                                                    vertx.eventBus().send(EventBusConstants.EVENT_SUCCEED, new JsonObject().put(EventBusConstants.EVENT_ID, eventId));

                                                    LOGGER.info(String.format("object %s provisioned successfully...: %s", object.getString(AIOpsObject.OBJECT_NAME), asyncResponse.result()));

                                                    // Send message to add config device.
                                                    if (event.containsKey(Discovery.DISCOVERY_CONFIG_MANAGEMENT_STATUS))
                                                    {
                                                        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_CONFIG_CREATE, event.put(AIOpsObject.OBJECT_ID, object.getInteger(AIOpsObject.OBJECT_ID)));
                                                    }

                                                    if (DiscoveryMethod.AGENT.name().equalsIgnoreCase(event.getString(AIOpsObject.OBJECT_DISCOVERY_METHOD)))
                                                    {
                                                        var configs = new JsonObject(AgentConfigStore.getStore().getItem(event.getLong(AIOpsObject.OBJECT_AGENT)).getString(AGENT_CONFIGS));

                                                        if (YES.equalsIgnoreCase(configs.getJsonObject(AgentConstants.AGENT).getString(METRIC_AGENT_STATUS)) && RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, configs.getJsonObject(AgentConstants.AGENT).getString(AGENT_UUID)) == null)
                                                        {
                                                            LicenseCacheStore.getStore().update(NMSConstants.AGENTS, true);
                                                        }
                                                    }
                                                    else
                                                    {
                                                        LicenseCacheStore.getStore().update(NMSConstants.OBJECTS, true);
                                                    }


                                                    var metrics = new ArrayList<JsonObject>();

                                                    publish(event.put(STATUS, STATUS_SUCCEED)
                                                            .put(MESSAGE, String.format(InfoMessageConstants.OBJECT_PROVISION_SUCCEEDED, object.getString(AIOpsObject.OBJECT_NAME))), sessionId);

                                                    //means agent (window/linux/ server type...) let's go for app metric rediscover..
                                                    //why this odd condition of category match...bcz when we provision service check with agent discovery at that time object also contains
                                                    //object.agent key
                                                    if (object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVER.getName())
                                                            && object.containsKey(AIOpsObject.OBJECT_AGENT))
                                                    {
                                                        var agent = AgentConfigStore.getStore().getItem(object.getLong(AIOpsObject.OBJECT_AGENT));

                                                        metrics.add(new JsonObject().put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.AVAILABILITY.getName())
                                                                .put(Metric.METRIC_STATE, agent.getString(Agent.AGENT_STATUS_TYPE).equalsIgnoreCase(AgentConstants.AgentStatusType.PING.getName())
                                                                        ? NMSConstants.State.ENABLE.name() : NMSConstants.State.DISABLE.name())
                                                                .put(Metric.METRIC_OBJECT, asyncResponse.result())
                                                                .put(Metric.METRIC_POLLING_TIME, AgentConfigUtil.getAgentPingTimerSeconds())
                                                                .put(Metric.METRIC_DISCOVERY_METHOD, DiscoveryMethod.AGENT.name())
                                                                .put(Metric.METRIC_CONTEXT, new JsonObject().put(AIOpsObject.OBJECT_AGENT, object.getLong(AIOpsObject.OBJECT_AGENT)).put(AIOpsObject.OBJECT_DISCOVERY_METHOD, DiscoveryMethod.REMOTE.name()))
                                                                .put(Metric.METRIC_CREDENTIAL_PROFILE, NOT_AVAILABLE));

                                                        metrics.add(new JsonObject().put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NETWORK_SERVICE.getName())
                                                                .put(Metric.METRIC_OBJECT, asyncResponse.result())
                                                                .put(Metric.METRIC_DISCOVERY_METHOD, DiscoveryMethod.AGENT.name())
                                                                .put(Metric.METRIC_CONTEXT, new JsonObject().put(AIOpsObject.OBJECT_AGENT, object.getLong(AIOpsObject.OBJECT_AGENT)).put(AIOpsObject.OBJECT_DISCOVERY_METHOD, DiscoveryMethod.AGENT.name()))
                                                                .put(Metric.METRIC_CREDENTIAL_PROFILE, NOT_AVAILABLE));

                                                        var plugins = new ArrayList<String>(2);

                                                        if (type == Type.WINDOWS)
                                                        {
                                                            plugins.add(NMSConstants.MetricPlugin.WINDOWS_FILE.getName());

                                                            plugins.add(NMSConstants.MetricPlugin.WINDOWS_DIR.getName());
                                                        }
                                                        else
                                                        {
                                                            plugins.add(NMSConstants.MetricPlugin.LINUX_FILE.getName());

                                                            plugins.add(NMSConstants.MetricPlugin.LINUX_DIR.getName());
                                                        }

                                                        for (var plugin : plugins)
                                                        {
                                                            metrics.add(new JsonObject().put(Metric.METRIC_PLUGIN, plugin)
                                                                    .put(Metric.METRIC_OBJECT, asyncResponse.result())
                                                                    .put(Metric.METRIC_DISCOVERY_METHOD, DiscoveryMethod.AGENT.name())
                                                                    .put(Metric.METRIC_CONTEXT, new JsonObject().put(AIOpsObject.OBJECT_AGENT, object.getLong(AIOpsObject.OBJECT_AGENT)).put(AIOpsObject.OBJECT_DISCOVERY_METHOD, DiscoveryMethod.AGENT.name()))
                                                                    .put(Metric.METRIC_CREDENTIAL_PROFILE, NOT_AVAILABLE));
                                                        }

                                                        for (var item : ObjectManagerCacheStore.getStore().getItemsByObjectType(type))
                                                        {
                                                            if (!plugins.contains(item.getString(Metric.METRIC_PLUGIN)))
                                                            {
                                                                metrics.add(new JsonObject().put(Metric.METRIC_PLUGIN, item.getString(Metric.METRIC_PLUGIN))
                                                                        .put(APIConstants.SESSION_ID, sessionId)
                                                                        .put(Metric.METRIC_OBJECT, asyncResponse.result())
                                                                        .put(Metric.METRIC_DISCOVERY_METHOD, DiscoveryMethod.INLINE.name())
                                                                        .put(Metric.METRIC_CONTEXT, new JsonObject().put(AIOpsObject.OBJECT_AGENT, object.getLong(AIOpsObject.OBJECT_AGENT)).put(AIOpsObject.OBJECT_DISCOVERY_METHOD, DiscoveryMethod.AGENT.name()))
                                                                        .put(Metric.METRIC_CREDENTIAL_PROFILE, NOT_AVAILABLE));
                                                            }
                                                        }

                                                        //register agent event = send agent id here and ui will fire getAgent query with id so that they can get data of new registered agent

                                                        var cache = AgentCacheStore.getStore().getItem(object.getLong(AIOpsObject.OBJECT_AGENT));

                                                        if (cache.getValue(GlobalConstants.DURATION) != null)
                                                        {
                                                            cache.put(GlobalConstants.DURATION, DateTimeUtil.convertTime(cache.getLong(GlobalConstants.DURATION)));
                                                        }

                                                        EventBusConstants.publish(UI_NOTIFICATION_AGENT, new JsonObject().mergeIn(cache).put(EVENT_TYPE, EVENT_AGENT_REGISTRATION).put(STATUS, GlobalConstants.STATUS_UP)
                                                                .put(AGENT_UUID, agent.getString(AGENT_UUID)).put(ID, agent.getLong(ID)));

                                                        vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().mergeIn(cache).put(EVENT_TYPE, EVENT_AGENT_REGISTRATION).put(STATUS, STATUS_SUCCEED)
                                                                .put(MESSAGE, String.format(InfoMessageConstants.AGENT_REGISTRATION_SUCCEEDED, object.getString(AIOpsObject.OBJECT_NAME))));

                                                        ObjectStatusCacheStore.getStore().updateItem(asyncResponse.result(), STATUS_UP, DateTimeUtil.currentSeconds());

                                                        queueMACScannerEvent(object, event);

                                                        promise.complete();
                                                    }
                                                    else // means it's agent less and do metric provision ...
                                                    {
                                                        var otherPing = type == Type.PING && category == Category.OTHER;

                                                        if (!otherPing)
                                                        {
                                                            for (var item : ObjectManagerCacheStore.getStore().getItemsByObjectType(type))
                                                            {
                                                                var context = item.getJsonObject(Metric.METRIC_CONTEXT);

                                                                if (objects != null && (NMSConstants.isVirtualMachineMetric(item.getString(Metric.METRIC_PLUGIN))
                                                                        || NMSConstants.isAccessPointMetric(item.getString(Metric.METRIC_PLUGIN))))
                                                                {
                                                                    var errorMessage = NMSConstants.isVirtualMachineMetric(item.getString(Metric.METRIC_PLUGIN)) ? METRIC_PROVISION_VM_FAILED : METRIC_PROVISION_ACCESS_POINT_FAILED;

                                                                    var message = NMSConstants.isVirtualMachineMetric(item.getString(Metric.METRIC_PLUGIN)) ? METRIC_VM_PROVISION_SUCCEEDED : METRIC_ACCESS_POINT_PROVISION_SUCCEEDED;

                                                                    var remainingObjects = LicenseUtil.getRemainingObjects(item.getString(Metric.METRIC_PLUGIN));

                                                                    if (remainingObjects < objects.size())
                                                                    {
                                                                        var iterator = objects.iterator();

                                                                        var discoveredObjects = 0;

                                                                        while (iterator.hasNext())
                                                                        {
                                                                            var provisionObject = (JsonObject) iterator.next();

                                                                            if (discoveredObjects >= remainingObjects)   // remove extra qualified VMs
                                                                            {
                                                                                iterator.remove();

                                                                                vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION,
                                                                                        new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_OBJECT_PROVISION)
                                                                                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_LICENSE_LIMIT_EXCEEDED)
                                                                                                .put(MESSAGE, String.format(errorMessage, provisionObject.getString(AIOpsObject.OBJECT_NAME), event.getString(AIOpsObject.OBJECT_NAME), ErrorMessageConstants.LICENSE_LIMIT_EXCEEDED))
                                                                                                .put(STATUS, STATUS_FAIL));

                                                                                publish(new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_OBJECT_PROVISION)
                                                                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_LICENSE_LIMIT_EXCEEDED)
                                                                                        .put(MESSAGE, String.format(errorMessage, provisionObject.getString(AIOpsObject.OBJECT_NAME), event.getString(AIOpsObject.OBJECT_NAME), ErrorMessageConstants.LICENSE_LIMIT_EXCEEDED))
                                                                                        .put(STATUS, STATUS_FAIL), sessionId);

                                                                                LOGGER.warn(String.format(errorMessage, provisionObject.getString(AIOpsObject.OBJECT_NAME), event.getString(AIOpsObject.OBJECT_NAME), ErrorMessageConstants.LICENSE_LIMIT_EXCEEDED));
                                                                            }
                                                                            else
                                                                            {
                                                                                discoveredObjects++;
                                                                            }
                                                                        }
                                                                    }

                                                                    if (NMSConstants.isVirtualMachineMetric(item.getString(Metric.METRIC_PLUGIN)))
                                                                    {
                                                                        LicenseCacheStore.getStore().update(VMS, objects.size(), true);
                                                                    }
                                                                    else if (NMSConstants.isAccessPointMetric(item.getString(Metric.METRIC_PLUGIN)))
                                                                    {
                                                                        LicenseCacheStore.getStore().update(ACCESS_POINTS, objects.size(), true);
                                                                    }

                                                                    for (var index = 0; index < objects.size(); index++)
                                                                    {
                                                                        EventBusConstants.publish(sessionId, EventBusConstants.UI_NOTIFICATION_OBJECT_PROVISION_PROGRESS, event.put(STATUS, STATUS_SUCCEED)
                                                                                .put(MESSAGE, String.format(message, objects.getJsonObject(index).getString(AIOpsObject.OBJECT_NAME))));
                                                                    }

                                                                }

                                                                var metric = new JsonObject().put(Metric.METRIC_PLUGIN, item.getString(Metric.METRIC_PLUGIN))
                                                                        .put(APIConstants.SESSION_ID, sessionId)
                                                                        .put(Metric.METRIC_CONTEXT, context)
                                                                        .put(Metric.METRIC_OBJECT, asyncResponse.result()).put(Metric.METRIC_CREDENTIAL_PROFILE, credentialProfileId)
                                                                        .put(NMSConstants.OBJECTS, NMSConstants.isVirtualMachineMetric(item.getString(Metric.METRIC_PLUGIN)) || NMSConstants.isAccessPointMetric(item.getString(Metric.METRIC_PLUGIN)) ? objects : null);

                                                                if (metricContext != null && (category != Category.CLOUD
                                                                        || ((type == Type.AWS_CLOUD || type == Type.AZURE_CLOUD) && NMSConstants.getMetricPlugin(type).equalsIgnoreCase(item.getString(Metric.METRIC_PLUGIN)))))
                                                                {
                                                                    metric.getJsonObject(Metric.METRIC_CONTEXT).put(context.containsKey(PLUGIN_ID) ? CommonUtil.getString(context.getInteger(PLUGIN_ID)) : pluginId, metricContext);
                                                                }

                                                                metrics.add(metric);
                                                            }
                                                        }

                                                        if ((category == NMSConstants.Category.NETWORK && type != NMSConstants.Type.RUCKUS_WIRELESS))
                                                        {
                                                            //snmp interface metric provision
                                                            // for wireless devices, we got access points in discovered objects... it should not append in snmpinterface metric group... we are doing rediscovery for getting interfaces.

                                                            var context = new JsonObject().put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SNMP_INTERFACE.getName())
                                                                    .put(Metric.METRIC_OBJECT, asyncResponse.result()).put(Metric.METRIC_CREDENTIAL_PROFILE, credentialProfileId)
                                                                    .put(NMSConstants.OBJECTS, !NMSConstants.isWireless(type) ? objects : new JsonArray());

                                                            if (event.getJsonArray(DISCOVERED_OBJECTS) != null)
                                                            {
                                                                context.put(DISCOVERED_OBJECTS, event.getJsonArray(DISCOVERED_OBJECTS)); // for default SNMP Interfaces  discovered.objects : [] present in object.context key that coming from UI at the time of provision
                                                            }

                                                            if (metricContext != null)
                                                            {
                                                                context.put(Metric.METRIC_CONTEXT, new JsonObject().put(pluginId, metricContext));
                                                            }

                                                            metrics.add(context);

                                                            if (objects != null && !objects.isEmpty())
                                                            {
                                                                vertx.eventBus().send(EventBusConstants.EVENT_MAC_SCANNER_RESPONSE, new JsonObject().mergeIn(object).put(RESULT, new JsonObject().put(NMSConstants.OBJECTS, objects)));
                                                            }
                                                        }

                                                        if (category == NMSConstants.Category.SERVER
                                                                || category == NMSConstants.Category.VIRTUALIZATION
                                                                || category == NMSConstants.Category.NETWORK
                                                                || category == Category.HCI
                                                                || category == Category.SDN
                                                                || category == Category.STORAGE
                                                                || category == NMSConstants.Category.OTHER)
                                                        {
                                                            if (objectContext.containsKey(PING_CHECK_STATUS) && objectContext.getString(PING_CHECK_STATUS).equalsIgnoreCase(NO))
                                                            {
                                                                metrics.add(new JsonObject().put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.OBJECT_STATUS.getName())
                                                                        .put(Metric.METRIC_STATE, State.ENABLE.name())
                                                                        .put(Metric.METRIC_OBJECT, asyncResponse.result())
                                                                        .put(Metric.METRIC_CREDENTIAL_PROFILE, credentialProfileId));

                                                                metrics.add(new JsonObject().put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.AVAILABILITY.getName())
                                                                        .put(Metric.METRIC_STATE, State.DISABLE.name())
                                                                        .put(Metric.METRIC_OBJECT, asyncResponse.result())
                                                                        .put(Metric.METRIC_CREDENTIAL_PROFILE, credentialProfileId));
                                                            }
                                                            else
                                                            {
                                                                metrics.add(new JsonObject().put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.AVAILABILITY.getName())
                                                                        .put(Metric.METRIC_STATE, State.ENABLE.name())
                                                                        .put(Metric.METRIC_OBJECT, asyncResponse.result())
                                                                        .put(Metric.METRIC_CREDENTIAL_PROFILE, credentialProfileId));

                                                                metrics.add(new JsonObject().put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.OBJECT_STATUS.getName())
                                                                        .put(Metric.METRIC_STATE, State.DISABLE.name())
                                                                        .put(Metric.METRIC_OBJECT, asyncResponse.result())
                                                                        .put(Metric.METRIC_CREDENTIAL_PROFILE, credentialProfileId));
                                                            }

                                                            if (category == Category.SERVER)
                                                            {
                                                                metrics.add(new JsonObject().put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NETWORK_SERVICE.getName())
                                                                        .put(Metric.METRIC_OBJECT, asyncResponse.result())
                                                                        .put(Metric.METRIC_CREDENTIAL_PROFILE, NOT_AVAILABLE));
                                                            }

                                                            if (type == Type.EMAIL_GATEWAY && object.getString(AIOpsObject.OBJECT_VENDOR).equals("Symantec Corporation"))
                                                            {
                                                                metrics.add(new JsonObject().put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SYMANTEC_MESSAGING_GATEWAY.getName())
                                                                        .put(Metric.METRIC_OBJECT, asyncResponse.result()).put(Metric.METRIC_CREDENTIAL_PROFILE, credentialProfileId));

                                                            }

                                                            if (category == NMSConstants.Category.NETWORK && !NMSConstants.isWireless(type))
                                                            {
                                                                //for network device config
                                                                metrics.add(new JsonObject().put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SNMP_DEVICE.getName())
                                                                        .put(Metric.METRIC_OBJECT, asyncResponse.result()).put(Metric.METRIC_CREDENTIAL_PROFILE, credentialProfileId));

                                                                metrics.add(new JsonObject().put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SNMP_NETWORK_CONNECTION.getName())
                                                                        .put(Metric.METRIC_OBJECT, asyncResponse.result()).put(Metric.METRIC_CREDENTIAL_PROFILE, credentialProfileId));

                                                                var childEventId = CommonUtil.newEventId();

                                                                EventBusConstants.updateEvent(childEventId, String.format(InfoMessageConstants.EVENT_TRACKER_CHILD_EVENT_FORKED,
                                                                        childEventId, DateTimeUtil.timestamp()));

                                                                object.clear();

                                                                object.mergeIn(CredentialProfileConfigStore.getStore().getItem(credentialProfileId));

                                                                object.mergeIn(object.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                                                                object.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);

                                                                //network metric rediscovery - will rediscover VLAN,VRF,Routing,STP,VPN Related metric
                                                                object.mergeIn(ObjectConfigStore.getStore().getItem(asyncResponse.result()))
                                                                        .put(PluginEngineConstants.PLUGIN_ENGINE_REQUEST, PluginEngineConstants.PluginEngineRequest.NETWORK_METRIC.getName())
                                                                        .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_PLUGIN_ENGINE)
                                                                        .put(APIConstants.SESSION_ID, sessionId)
                                                                        .put(AIOpsObject.OBJECT_CREDENTIAL_PROFILE, credentialProfileId)
                                                                        .put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SNMP_DEVICE.getName())
                                                                        .put(GlobalConstants.TIMEOUT, ObjectManagerCacheStore.getStore().getTimeoutByMetricPlugin(NMSConstants.MetricPlugin.SNMP_DEVICE.getName()));

                                                                if (object.containsKey(AIOpsObject.OBJECT_CONTEXT))
                                                                {
                                                                    object.mergeIn(objectContext);

                                                                    object.remove(AIOpsObject.OBJECT_CONTEXT);
                                                                }

                                                                var context = new JsonObject().put(EventBusConstants.EVENT_ID, childEventId)
                                                                        .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_PLUGIN_ENGINE)
                                                                        .put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER)
                                                                        .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                                                                        .put(EventBusConstants.EVENT_CONTEXT, object);

                                                                vertx.eventBus().send(EventBusConstants.EVENT_ADD, context);

                                                                vertx.eventBus().send(EventBusConstants.EVENT_ROUTER, object.put(EventBusConstants.EVENT_ID, childEventId));

                                                                if (object.getString(AIOpsObject.OBJECT_VENDOR) != null)
                                                                {
                                                                    //list of oid groups by vendor and type from master json
                                                                    var oidGroups = SNMPTemplateOIDGroupCacheStore.getStore().getItemsByType(object.getString(AIOpsObject.OBJECT_VENDOR), type);

                                                                    if (oidGroups != null)
                                                                    {
                                                                        for (var index = 0; index < oidGroups.size(); index++)
                                                                        {
                                                                            childEventId = CommonUtil.newEventId();

                                                                            EventBusConstants.updateEvent(childEventId, String.format(InfoMessageConstants.EVENT_TRACKER_CHILD_EVENT_FORKED,
                                                                                    childEventId, DateTimeUtil.timestamp()));

                                                                            object.put(NMSConstants.SNMP_OID_GROUP, oidGroups.getJsonObject(index));

                                                                            vertx.eventBus().send(EventBusConstants.EVENT_ADD, context.put(EventBusConstants.EVENT_ID, childEventId)
                                                                                    .put(EventBusConstants.EVENT_CONTEXT, object));

                                                                            vertx.eventBus().send(EventBusConstants.EVENT_ROUTER, object.put(EventBusConstants.EVENT_ID, childEventId));
                                                                        }
                                                                    }
                                                                }

                                                                context.clear();

                                                                // for custom snmp metric group provision
                                                                context.put(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_ID, object.getLong(AIOpsObject.OBJECT_SNMP_DEVICE_CATALOG))
                                                                        .put(Metric.METRIC_CREDENTIAL_PROFILE, credentialProfileId)
                                                                        .put(ConfigDBConstants.FIELD_TYPE, ConfigDBConstants.ENTITY_TYPE_USER)
                                                                        .put(NMSConstants.OBJECTS, new JsonArray(new ArrayList<Long>(1)).add(asyncResponse.result()));

                                                                var oidGroups = SNMPOIDGroupConfigStore.getStore().getItemsByValue(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_ID, object.getLong(AIOpsObject.OBJECT_SNMP_DEVICE_CATALOG));

                                                                for (var index = 0; index < oidGroups.size(); index++)
                                                                {
                                                                    NMSConstants.sendSNMPMetricProvisionEvent(context.put(NMSConstants.SNMP_OID_GROUP, oidGroups.getJsonObject(index)));
                                                                }
                                                            }

                                                            if (category == Category.HCI)
                                                            {
                                                                // we have to provision main prism level object if not exist

                                                                if (ObjectConfigStore.getStore().getObjectIdByIP(object.getString(AIOpsObject.OBJECT_PARENT_IP)
                                                                        , Type.PRISM) == NOT_AVAILABLE)
                                                                {
                                                                    object.put(AIOpsObject.OBJECT_IP, object.getString(AIOpsObject.OBJECT_PARENT_IP));

                                                                    object.put(AIOpsObject.OBJECT_TARGET, object.getString(AIOpsObject.OBJECT_PARENT_IP));

                                                                    object.put(AIOpsObject.OBJECT_TYPE, Type.PRISM.getName());

                                                                    object.put(AIOpsObject.OBJECT_NAME, object.getString(AIOpsObject.OBJECT_TARGET));

                                                                    object.put(AIOpsObject.OBJECT_HOST, object.getString(AIOpsObject.OBJECT_TARGET));

                                                                    object.put(AIOpsObject.OBJECT_CREDENTIAL_PROFILE, credentialProfileId);

                                                                    object.remove("prism.cluster");

                                                                    object.remove("object.host.uuid");

                                                                    if (sessionId != null)
                                                                    {
                                                                        object.put(APIConstants.SESSION_ID, sessionId);
                                                                    }

                                                                    object.put(ID, CommonUtil.newId());

                                                                    provisionObject(object).onComplete(asyncResult -> promise.complete());
                                                                }

                                                                else
                                                                {
                                                                    promise.complete();
                                                                }
                                                            }
                                                            else
                                                            {
                                                                promise.complete();
                                                            }
                                                        }
                                                        else if (category == NMSConstants.Category.SERVICE_CHECK)
                                                        {
                                                            promise.complete();
                                                        }

                                                        else if (category == NMSConstants.Category.CLOUD)
                                                        {
                                                            // we have to provision main cloud level object if not exist

                                                            if (ObjectConfigStore.getStore().getObjectIdByAccountId(object.getString(AIOpsObject.OBJECT_ACCOUNT_ID)
                                                                    , NMSConstants.getCloudType(type)) == NOT_AVAILABLE)
                                                            {
                                                                var objectType = NMSConstants.getCloudType(type);

                                                                object.put(AIOpsObject.OBJECT_TARGET, objectType.getName() + " (" + object.getString(AIOpsObject.OBJECT_ACCOUNT_ID) + ")");

                                                                object.put(AIOpsObject.OBJECT_TYPE, objectType.getName());

                                                                object.put(AIOpsObject.OBJECT_NAME, object.getString(AIOpsObject.OBJECT_TARGET));

                                                                object.put(AIOpsObject.OBJECT_CREDENTIAL_PROFILE, credentialProfileId);

                                                                object.remove(AIOpsObject.OBJECT_REGION);

                                                                object.remove(AIOpsObject.OBJECT_RESOURCE_GROUP);

                                                                // for load balancer we put application/network in make model
                                                                object.remove(AIOpsObject.OBJECT_MAKE_MODEL);

                                                                if (sessionId != null)
                                                                {
                                                                    object.put(APIConstants.SESSION_ID, sessionId);

                                                                }

                                                                object.put(ID, CommonUtil.newId());

                                                                if (objectType == Type.AWS_CLOUD || objectType == Type.AZURE_CLOUD)
                                                                {
                                                                    if (CommonUtil.isNotNullOrEmpty(cloudInstanceStatus))
                                                                    {
                                                                        objectContext.put(CLOUD_SERVICE_DOWN_INSTANCE_DISCOVERY, cloudInstanceStatus);
                                                                    }

                                                                    if (metricContext != null)
                                                                    {
                                                                        objectContext.put(pluginId, metricContext);
                                                                    }
                                                                }

                                                                provisionObject(object).onComplete(asyncResult -> promise.complete());

                                                            }

                                                            else
                                                            {
                                                                promise.complete();
                                                            }
                                                        }
                                                    }

                                                    NMSConstants.updateMetrics(metrics, EventBusConstants.EVENT_METRIC_PROVISION);

                                                    vertx.eventBus().publish(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject()
                                                            .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_OBJECT.name())
                                                            .put(ID, asyncResponse.result()));
                                                }

                                                else
                                                {
                                                    vertx.eventBus().send(EventBusConstants.EVENT_FAIL, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                            .put(MESSAGE, String.format(ErrorMessageConstants.PROVISION_OBJECT_FAILED_INTERNAL_ERROR, object.getString(AIOpsObject.OBJECT_NAME), response.cause().getMessage()))
                                                            .put(ERROR, CommonUtil.formatStackTrace(response.cause().getStackTrace())));

                                                    publish(event.put(STATUS, STATUS_FAIL)
                                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                            .put(MESSAGE, String.format(ErrorMessageConstants.PROVISION_OBJECT_FAILED_INTERNAL_ERROR, object.getString(AIOpsObject.OBJECT_NAME), response.cause().getMessage()))
                                                            .put(ERROR, CommonUtil.formatStackTrace(response.cause().getStackTrace())), sessionId);

                                                    LOGGER.warn(String.format(OBJECT_PROVISION_FAILED, object.getString(AIOpsObject.OBJECT_NAME), response.cause().getMessage()));

                                                    promise.fail(String.format(OBJECT_PROVISION_FAILED, object.getString(AIOpsObject.OBJECT_NAME), response.cause().getMessage()));

                                                    Bootstrap.configDBService().delete(ConfigDBConstants.COLLECTION_OBJECT,
                                                            new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, asyncResponse.result()), event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS, result ->
                                                            {
                                                            });

                                                }

                                            });

                                        }

                                        else
                                        {

                                            vertx.eventBus().send(EventBusConstants.EVENT_FAIL, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                    .put(MESSAGE, String.format(ErrorMessageConstants.PROVISION_OBJECT_FAILED_INTERNAL_ERROR, object.getString(AIOpsObject.OBJECT_NAME), asyncResponse.cause().getMessage()))
                                                    .put(ERROR, CommonUtil.formatStackTrace(asyncResponse.cause().getStackTrace())));

                                            publish(event.put(STATUS, STATUS_FAIL).put(MESSAGE, String.format(ErrorMessageConstants.PROVISION_OBJECT_FAILED_INTERNAL_ERROR, object.getString(AIOpsObject.OBJECT_NAME), asyncResponse.cause().getMessage()))
                                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                    .put(ERROR, CommonUtil.formatStackTrace(asyncResponse.cause().getStackTrace())), sessionId);

                                            LOGGER.warn(String.format(OBJECT_PROVISION_FAILED, object.getString(AIOpsObject.OBJECT_NAME), asyncResponse.cause().getMessage()));

                                            promise.fail(String.format(OBJECT_PROVISION_FAILED, object.getString(AIOpsObject.OBJECT_NAME), asyncResponse.cause().getMessage()));
                                        }

                                    });
                                }

                            }

                            else
                            {
                                LOGGER.warn(String.format("object %s already provisioned...", object.getString(AIOpsObject.OBJECT_TARGET)));

                                vertx.eventBus().send(EventBusConstants.EVENT_PROGRESS_UPDATE, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                                        .put(EventBusConstants.EVENT_CONTEXT, object));

                                vertx.eventBus().send(EventBusConstants.EVENT_FAIL, new JsonObject().put(EventBusConstants.EVENT_ID, eventId).put(ERROR_CODE, ErrorCodes.ERROR_CODE_OBJECT_PROVISION_DUPLICATE)
                                        .put(MESSAGE, String.format(PROVISION_OBJECT_FAILED_DUPLICATE_OBJECT, object.getString(AIOpsObject.OBJECT_NAME))));


                                publish(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_OBJECT_PROVISION_DUPLICATE).put(MESSAGE, String.format(PROVISION_OBJECT_FAILED_DUPLICATE_OBJECT, object.getString(AIOpsObject.OBJECT_NAME))), sessionId);

                                promise.fail(String.format("object %s already provisioned...", object.getString(AIOpsObject.OBJECT_TARGET)));
                            }
                        }
                        else
                        {
                            LOGGER.warn(ErrorMessageConstants.LICENSE_LIMIT_EXCEEDED);

                            promise.fail(ErrorMessageConstants.LICENSE_LIMIT_EXCEEDED);

                            vertx.eventBus().send(EventBusConstants.EVENT_PROGRESS_UPDATE, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                                    .put(EventBusConstants.EVENT_CONTEXT, object));

                            vertx.eventBus().send(EventBusConstants.EVENT_FAIL, new JsonObject().put(EventBusConstants.EVENT_ID, eventId).put(ERROR_CODE, ErrorCodes.ERROR_CODE_LICENSE_LIMIT_EXCEEDED)
                                    .put(MESSAGE, String.format(ErrorMessageConstants.PROVISION_OBJECT_FAILED_INTERNAL_ERROR, object.getString(AIOpsObject.OBJECT_NAME), LICENSE_LIMIT_EXCEEDED)));

                            publish(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_LICENSE_LIMIT_EXCEEDED).put(MESSAGE, String.format(ErrorMessageConstants.PROVISION_OBJECT_FAILED_INTERNAL_ERROR, object.getString(AIOpsObject.OBJECT_NAME), LICENSE_LIMIT_EXCEEDED)), sessionId);

                            vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, event);
                        }
                    }

                    else
                    {
                        LOGGER.warn(String.format("credential profile %s not found...", credentialProfileId));

                        promise.fail(String.format("credential profile %s not found...", credentialProfileId));

                        vertx.eventBus().send(EventBusConstants.EVENT_PROGRESS_UPDATE, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                                .put(EventBusConstants.EVENT_CONTEXT, object));

                        vertx.eventBus().send(EventBusConstants.EVENT_FAIL, new JsonObject().put(EventBusConstants.EVENT_ID, eventId).put(ERROR_CODE, ErrorCodes.ERROR_CODE_OBJECT_PROVISION_NO_CREDENTIAL_PROFILE)
                                .put(MESSAGE, String.format(PROVISION_OBJECT_FAILED_CREDENTIAL_PROFILE_NOT_FOUND, object.getString(AIOpsObject.OBJECT_NAME))));

                        publish(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_OBJECT_PROVISION_NO_CREDENTIAL_PROFILE).put(MESSAGE, String.format(PROVISION_OBJECT_FAILED_CREDENTIAL_PROFILE_NOT_FOUND, object.getString(AIOpsObject.OBJECT_NAME))), sessionId);


                    }


                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            if (!promise.future().isComplete())
            {
                promise.fail(exception);
            }
        }

        return promise.future();
    }

    private void removeDuplicateObjects(JsonObject event, JsonArray objects, Map<String, JsonObject> existingObjects)
    {
        //task - 3668 ... Let QA find the issue in duplicate rediscovery event, and then we will take decision about this condition...as of now keep it is same
        if (existingObjects != null && !existingObjects.isEmpty())
        {
            //means event came from rediscover result page or discovery page then we have to filter the stuff....

            for (var index = 0; index < objects.size(); index++)
            {
                var object = objects.getJsonObject(index);

                /*
                 * MOTADATA-1095 : In case of change in already provisioned objects... rediscovered objects should be updated with existing update
                 * ex. interface with object.name "1" has alias.name is "old-if" and it has changed to "new-if" so after rediscover provision we have to remove existing object and update it with new object
                 * if we remove iterator.remove() than updated object with alias name "new-if" will not be reflected
                 * */

                if (existingObjects.containsKey(object.getString(AIOpsObject.OBJECT_NAME))) //compare with provision metric and if match found remove that object from list of provision objects
                {
                    object.put(INSTANCE_TAGS, existingObjects.get(object.getString(AIOpsObject.OBJECT_NAME)).containsKey(INSTANCE_TAGS) ? existingObjects.get(object.getString(AIOpsObject.OBJECT_NAME)).getJsonArray(INSTANCE_TAGS) : new JsonArray());

                    existingObjects.remove(object.getString(AIOpsObject.OBJECT_NAME));
                }
                else
                {
                    event.put(DISCOVERED_OBJECTS, 1);
                }
            }

            existingObjects.values().forEach(objects::add); //merge previously provisioned objects
        }
    }

    private int getObjectId(List<JsonObject> objects, JsonObject object, NMSConstants.Type type, boolean archived)
    {
        var id = NOT_AVAILABLE;

        if (!archived)
        {
            id = ObjectConfigStore.getStore().getObjectIdByObjectName(object.getString(AIOpsObject.OBJECT_NAME));
        }

        return id != NOT_AVAILABLE ? id : objects.stream()
                .filter(item -> !object.getString(AIOpsObject.OBJECT_DISCOVERY_METHOD).equalsIgnoreCase(DiscoveryMethod.AGENT.name())
                        || item.getLong(AIOpsObject.OBJECT_AGENT).equals(object.getLong(AIOpsObject.OBJECT_AGENT)))
                .filter(item -> type != Type.PING || item.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(object.getString(AIOpsObject.OBJECT_CATEGORY)))
                .filter(item -> type == NMSConstants.Type.URL ? item.getString(AIOpsObject.OBJECT_TARGET).equalsIgnoreCase(object.getString(AIOpsObject.OBJECT_TARGET))
                        : (item.getString(AIOpsObject.OBJECT_TARGET).equalsIgnoreCase(object.getString(AIOpsObject.OBJECT_TARGET))
                        || item.getString(AIOpsObject.OBJECT_IP).equalsIgnoreCase(object.getString(AIOpsObject.OBJECT_IP))))
                .filter(item -> type != NMSConstants.Type.PORT || item.getJsonObject(AIOpsObject.OBJECT_CONTEXT).getInteger(PORT).equals(object.getJsonObject(AIOpsObject.OBJECT_CONTEXT).getInteger(PORT)))
                .map(item -> item.getInteger(AIOpsObject.OBJECT_ID)).findFirst().orElse(NOT_AVAILABLE);
    }

    //Used to provision metric
    private void provisionMetric(Message<JsonObject> message)
    {
        try
        {
            var event = message.body();

            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug(String.format("metric provision request received: %s", event.encodePrettily()));
            }

            var metric = new JsonObject().mergeIn(ObjectManagerCacheStore.getStore().getItemByMetricPlugin(event.getString(Metric.METRIC_PLUGIN)));

            metric.put(Metric.METRIC_OBJECT, event.getLong(Metric.METRIC_OBJECT));

            if (event.containsKey(Metric.METRIC_NAME)) //SNMP template oid group....
            {
                metric.put(Metric.METRIC_NAME, event.getString(Metric.METRIC_NAME));
            }

            if (event.containsKey(Metric.METRIC_POLLING_TIME))
            {
                metric.put(Metric.METRIC_POLLING_TIME, event.getInteger(Metric.METRIC_POLLING_TIME));
            }

            if (event.containsKey(Metric.METRIC_STATE)) //metric disable if object disable
            {
                metric.put(Metric.METRIC_STATE, event.getString(Metric.METRIC_STATE));
            }

            if (metric.getString(Metric.METRIC_TYPE) == null) //for availability,network service, network interface, network metric etc..
            {
                metric.put(Metric.METRIC_TYPE, ObjectConfigStore.getStore().getItem(event.getLong(Metric.METRIC_OBJECT))
                        .getString(AIOpsObject.OBJECT_TYPE));
            }

            if (event.containsKey(Metric.METRIC_TYPE))
            {
                metric.put(Metric.METRIC_TYPE, event.getString(Metric.METRIC_TYPE));
            }

            if (event.containsKey(Metric.METRIC_CATEGORY))
            {
                metric.put(Metric.METRIC_CATEGORY, event.getString(Metric.METRIC_CATEGORY));
            }
            else
            {
                metric.put(Metric.METRIC_CATEGORY, ObjectConfigStore.getStore().getItem(event.getLong(Metric.METRIC_OBJECT)).getString(AIOpsObject.OBJECT_CATEGORY));
            }

            //#25478
            if (NMSConstants.RediscoverJob.valueOfName(event.getString(REDISCOVER_JOB)) == RediscoverJob.APP && event.containsKey(Metric.METRIC_OBJECT))
            {
                var objectType = ObjectConfigStore.getStore().getItem(event.getLong(Metric.METRIC_OBJECT)).getString(AIOpsObject.OBJECT_TYPE);

                if (PLUGINS_BY_OBJECT_TYPE.containsKey(objectType) && event.getJsonObject(Metric.METRIC_CONTEXT).containsKey(OBJECT))
                {
                    var object = event.getJsonObject(Metric.METRIC_CONTEXT).getJsonObject(OBJECT);

                    if (object != null)
                    {
                        var id = MetricConfigStore.getStore().getItemByMetricPlugin(event.getLong(Metric.METRIC_OBJECT), PLUGINS_BY_OBJECT_TYPE.get(objectType).getString(object.getString(AIOpsObject.OBJECT_TYPE)));

                        if (id != NOT_AVAILABLE)
                        {
                            var item = MetricConfigStore.getStore().getItem(id);

                            var context = item.getJsonObject(Metric.METRIC_CONTEXT);

                            if (!context.containsKey(OBJECTS))
                            {
                                context.put(OBJECTS, new JsonArray());
                            }

                            var found = false;

                            for (var index = 0; index < context.getJsonArray(OBJECTS).size(); index++)
                            {
                                if (context.getJsonArray(OBJECTS).getJsonObject(index).getString(AIOpsObject.OBJECT_NAME).equalsIgnoreCase(object.getString(AIOpsObject.OBJECT_NAME)))
                                {
                                    found = true;

                                    break;
                                }
                            }

                            if (!found)
                            {
                                context.getJsonArray(OBJECTS).add(object);
                            }

                            if (item.containsKey(METRIC_INSTANCES) && !item.getJsonArray(METRIC_INSTANCES).contains(object.getString(AIOpsObject.OBJECT_NAME)))
                            {
                                item.getJsonArray(METRIC_INSTANCES).add(object.getString(AIOpsObject.OBJECT_NAME));
                            }

                            Bootstrap.configDBService().save(ConfigDBConstants.COLLECTION_METRIC, item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                            {

                                if (result.succeeded())
                                {
                                    //25802 Bug it was not updating the item,now it will update the instancesByPlugin and thus you will get the data in Process widget
                                    MetricConfigStore.getStore().addItem(result.result());

                                    LOGGER.info(String.format("instance %s of metric %s of object %s added", object.getString(AIOpsObject.OBJECT_NAME), metric.getString(Metric.METRIC_NAME), item.getString(Metric.METRIC_NAME)));
                                }
                                else
                                {
                                    LOGGER.info(String.format("failed to add instance %s of metric %s of object %s", object.getString(AIOpsObject.OBJECT_NAME), metric.getString(Metric.METRIC_NAME), item.getString(Metric.METRIC_NAME)));
                                }
                            });
                        }

                    }
                }
            }

            var objects = event.getJsonArray(NMSConstants.OBJECTS);

            var discoveredObjects = event.getJsonArray(DISCOVERED_OBJECTS);

            if (metric.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()))
            {
                var type = NMSConstants.Type.valueOfName(metric.getString(Metric.METRIC_TYPE));

                if (!NMSConstants.isWireless(type))
                {

                    //if interface discovery is off or no interface found then disable metric..

                    if (objects == null || objects.isEmpty())
                    {
                        metric.put(Metric.METRIC_STATE, NMSConstants.State.DISABLE.name());
                    }
                    else
                    {
                        for (var index = 0; index < objects.size(); index++)
                        {
                            var object = objects.getJsonObject(index);

                            if (object.containsKey(NMSConstants.INTERFACE_IP))
                            {
                                object.put(NMSConstants.INTERFACE_LINK_TYPE, CommonUtil.isWANLink(object.getString(NMSConstants.INTERFACE_IP))
                                        ? NMSConstants.INTERFACE_LINK_TYPE_WAN : NMSConstants.INTERFACE_LINK_TYPE_LAN);

                                if (event.containsKey(AIOpsObject.OBJECT_IP) && object.getString(INTERFACE_IP).equalsIgnoreCase(event.getString(AIOpsObject.OBJECT_IP)))
                                {
                                    object.put(INTERFACE_MANAGEMENT_PORT, YES);
                                }
                            }
                        }
                    }

                }

            }

            if (event.containsKey(Metric.METRIC_CONTEXT)) //merge metric context...ex. custom metric provision event has custom metric context in metric.context
            {
                metric.getJsonObject(Metric.METRIC_CONTEXT).mergeIn(event.getJsonObject(Metric.METRIC_CONTEXT));
            }

            if (objects != null)
            {
                metric.getJsonObject(Metric.METRIC_CONTEXT).put(NMSConstants.OBJECTS, objects);
            }

            if (discoveredObjects != null && !discoveredObjects.isEmpty())
            {
                metric.getJsonObject(Metric.METRIC_CONTEXT).put(DISCOVERED_OBJECTS, discoveredObjects);
            }

            if (event.containsKey(Metric.METRIC_CREDENTIAL_PROFILE))
            {
                metric.put(Metric.METRIC_CREDENTIAL_PROFILE, event.getLong(Metric.METRIC_CREDENTIAL_PROFILE));

                if (event.getLong(Metric.METRIC_CREDENTIAL_PROFILE) != NOT_AVAILABLE)
                {
                    metric.put(Metric.METRIC_CREDENTIAL_PROFILE_PROTOCOL, CredentialProfileConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_CREDENTIAL_PROFILE)).getString(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL));
                }
            }

            if (event.containsKey(AIOpsObject.OBJECT_DISCOVERY_METHOD))
            {
                metric.put(Metric.METRIC_DISCOVERY_METHOD, event.getString(AIOpsObject.OBJECT_DISCOVERY_METHOD));

            }
            else if (event.containsKey(Metric.METRIC_DISCOVERY_METHOD) && event.getString(Metric.METRIC_DISCOVERY_METHOD).equalsIgnoreCase(DiscoveryMethod.INLINE.name()))
            {    // #24939

                metric.put(Metric.METRIC_DISCOVERY_METHOD, DiscoveryMethod.INLINE.name());

            }
            else if (event.containsKey(Metric.METRIC_DISCOVERY_METHOD) && event.getString(Metric.METRIC_DISCOVERY_METHOD).equalsIgnoreCase(DiscoveryMethod.AGENT.name()))       // #28048
            {
                metric.put(Metric.METRIC_DISCOVERY_METHOD, DiscoveryMethod.AGENT.name());
            }
            else
            {
                metric.put(Metric.METRIC_DISCOVERY_METHOD, DiscoveryMethod.REMOTE.name());

            }

            if (!metric.getJsonObject(Metric.METRIC_CONTEXT).containsKey(PLUGIN_ID))
            {
                var key = metric.getString(Metric.METRIC_NAME) + SEPARATOR + metric.getString(Metric.METRIC_TYPE);

                var pluginId = PluginIdCacheStore.getStore().getPluginId(key);

                if (pluginId == NOT_AVAILABLE)
                {
                    pluginId = PluginIdCacheStore.getStore().generateNextPluginId();

                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug(String.format("generated new plugin id %s for metric key %s ", pluginId, key));
                    }

                    PluginIdCacheStore.getStore().addItem(key, pluginId, metric.getString(Metric.METRIC_NAME));
                }

                metric.getJsonObject(Metric.METRIC_CONTEXT).put(PLUGIN_ID, pluginId);
            }

            if (event.containsKey(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD))
            {
                metric.put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD));
            }

            metric.getJsonObject(Metric.METRIC_CONTEXT).remove(PluginEngineConstants.PLUGIN_ENGINE);

            metric.remove(EVENT_REPLY);

            Bootstrap.configDBService().save(ConfigDBConstants.COLLECTION_METRIC, metric, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResponse ->
            {
                if (asyncResponse.succeeded())
                {
                    MetricConfigStore.getStore().addItem(asyncResponse.result()).onComplete(response ->
                    {

                        if (response.succeeded())
                        {
                            try
                            {
                                ObjectConfigStore.getStore().updateItemPlugin(metric.getJsonObject(Metric.METRIC_CONTEXT).getInteger(PLUGIN_ID), metric.getLong(Metric.METRIC_OBJECT));

                                vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                                        .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_METRIC.name())
                                        .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                                        .put(ID, asyncResponse.result()));

                                vertx.eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                                        .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_METRIC.name())
                                        .put(ID, asyncResponse.result()));

                                if (NMSConstants.isBaseTypeMetricPlugin(metric.getString(Metric.METRIC_PLUGIN)) && !metric.getString(Metric.METRIC_CATEGORY).equalsIgnoreCase(Category.SERVICE_CHECK.getName()) && !metric.getString(Metric.METRIC_TYPE).equalsIgnoreCase(Type.PING.getName()))
                                {
                                    vertx.eventBus().publish(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject()
                                            .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_METRIC.name())
                                            .put(ID, asyncResponse.result()));
                                }

                                if (isWANLinkMetric(metric.getString(Metric.METRIC_PLUGIN)))
                                {
                                    var availableObjects = new JsonArray();

                                    updateIPSLAMetrics(metric.getString(Metric.METRIC_PLUGIN), metric.getLong(Metric.METRIC_OBJECT), availableObjects);

                                    if (metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(OBJECTS) != null && !metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(OBJECTS).isEmpty())
                                    {
                                        availableObjects.addAll(metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(OBJECTS));
                                    }

                                    TagCacheStore.getStore().updateInstanceTags(asyncResponse.result(), availableObjects, NMSConstants.INSTANCE_TYPES.getOrDefault(metric.getString(Metric.METRIC_PLUGIN), IPSLA));
                                }

                                LOGGER.info(String.format("metric %s of object %s provisioned successfully...", metric.getString(Metric.METRIC_NAME), ObjectConfigStore.getStore().getObjectName(metric.getLong(Metric.METRIC_OBJECT))));


                                var object = ObjectConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_OBJECT));

                                //let's go for rediscovery of qualified metric plugins
                                if (NMSConstants.MetricPlugin.valueOfName(metric.getString(Metric.METRIC_PLUGIN)) != null)
                                {
                                    var context = new JsonObject().put(APIConstants.SESSION_ID, event.getString(APIConstants.SESSION_ID)).put(NMSConstants.AUTO_PROVISION_STATUS, YES);

                                    var items = new JsonArray();

                                    var metricPlugin = NMSConstants.MetricPlugin.valueOfName(metric.getString(Metric.METRIC_PLUGIN));

                                    switch (metricPlugin)
                                    {
                                        case LINUX_PROCESS, HP_UX_PROCESS, IBM_AIX_PROCESS, SOLARIS_PROCESS,
                                             WINDOWS_PROCESS ->
                                                items.add(NMSConstants.RediscoverJob.APP.getName()).add(NMSConstants.RediscoverJob.PROCESS.getName());

                                        case LINUX_FILE, WINDOWS_FILE, LINUX_DIR, WINDOWS_DIR ->
                                                items.add(NMSConstants.RediscoverJob.FILE_DIRECTORY.getName());

                                        case WINDOWS_SERVICE ->
                                                items.add(NMSConstants.RediscoverJob.APP.getName()).add(NMSConstants.RediscoverJob.WINDOWS_SERVICE.getName());

                                        case NETWORK_SERVICE ->
                                                items.add(NMSConstants.RediscoverJob.NETWORK_SERVICE.getName());

                                        case SNMP_INTERFACE ->
                                        {
                                            if (NMSConstants.Type.valueOfName(metric.getString(Metric.METRIC_TYPE)) == NMSConstants.Type.CISCO_WIRELESS ||
                                                    NMSConstants.Type.valueOfName(metric.getString(Metric.METRIC_TYPE)) == Type.ARUBA_WIRELESS)
                                            {
                                                items.add(NMSConstants.RediscoverJob.NETWORK_INTERFACE.getName());
                                            }
                                        }
                                        default ->
                                        {
                                        }
                                    }

                                    if (object != null && object.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name())
                                            && (ObjectStatusCacheStore.getStore().getItem(object.getLong(ID)) == null || ObjectStatusCacheStore.getStore().getItem(object.getLong(ID)).equalsIgnoreCase(STATUS_UP)))
                                    {
                                        context.mergeIn(metric).mergeIn(object);

                                        for (var index = 0; index < items.size(); index++)
                                        {
                                            vertx.eventBus().send(EventBusConstants.EVENT_METRIC_REDISCOVER, new JsonObject().mergeIn(context).put(ID, asyncResponse.result()).put(NMSConstants.REDISCOVER_JOB, items.getString(index)));
                                        }

                                        if ((metric.getString(Metric.METRIC_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVER.getName()) || metric.getString(Metric.METRIC_CATEGORY).equalsIgnoreCase(NMSConstants.Category.VIRTUALIZATION.getName())
                                                || metric.getString(Metric.METRIC_CATEGORY).equalsIgnoreCase(Category.HCI.getName())
                                                || metric.getString(Metric.METRIC_TYPE).equalsIgnoreCase(NMSConstants.Type.RUCKUS_WIRELESS.getName()) || metric.getString(Metric.METRIC_TYPE).equalsIgnoreCase(NMSConstants.Type.CISCO_UCS.getName()))
                                                && NMSConstants.isBaseTypeMetricPlugin(metric.getString(Metric.METRIC_PLUGIN)))
                                        {
                                            var credentialProfile = CredentialProfileConfigStore.getStore().getItem(context.getLong(Metric.METRIC_CREDENTIAL_PROFILE));

                                            if (credentialProfile != null)
                                            {
                                                APIUtil.removeDefaultParameters(credentialProfile);

                                                if (credentialProfile.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
                                                {
                                                    credentialProfile.mergeIn(credentialProfile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                                                    credentialProfile.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                                                }

                                                queueMACScannerEvent(new JsonObject().mergeIn(context).mergeIn(credentialProfile).put(Metric.METRIC_OBJECT, asyncResponse.result()), event);
                                            }
                                        }

                                        if (NMSConstants.APPLICATION_PLUGINS.contains(metricPlugin.getName()) && event.containsKey(NMSConstants.REDISCOVER_JOB) && event.getString(NMSConstants.REDISCOVER_JOB).equalsIgnoreCase(NMSConstants.RediscoverJob.APP.getName()))
                                        {
                                            var item = new JsonObject().mergeIn(object).put(AIOpsConstants.DEPENDENCY_SOURCE, object.getString(AIOpsObject.OBJECT_IP))
                                                    .put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                                                    .put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.ADD.getName())
                                                    .put(AIOpsConstants.DEPENDENCY_DESTINATION, metric.getString(Metric.METRIC_TYPE))
                                                    .put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.LEVELS_BY_APPLICATIONS.get(metric.getString(Metric.METRIC_TYPE)));

                                            if (event.containsKey(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD))
                                            {
                                                item.put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, event.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD));
                                            }

                                            vertx.eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(), item);
                                        }

                                        //if metric plugin is qualified for rediscover metrics than let's go for dependency building
                                        if (AIOpsConstants.hasDependencyProp(metric.getString(Metric.METRIC_PLUGIN), true))
                                        {
                                            notifyDependencyManager(metric, objects, event);
                                        }
                                    }
                                }

                                if (metric.getString(Metric.METRIC_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()) && !metric.getString(Metric.METRIC_DISCOVERY_METHOD).equalsIgnoreCase(DiscoveryMethod.INLINE.name()))
                                {
                                    MetricCacheStore.getStore().addMetric(asyncResponse.result(), 10);
                                }

                                if (NMSConstants.APPLICATION_PLUGINS.contains(metric.getString(Metric.METRIC_PLUGIN)))
                                {
                                    metric.mergeIn(metric.getJsonObject(Metric.METRIC_CONTEXT));

                                    if (metric.containsKey(PORT))
                                    {
                                        var applicationMapper = ApplicationMapperConfigStore.getStore().getItemByValue(ApplicationMapper.APPLICATION_MAPPER_PORT, metric.getInteger(PORT));

                                        if (applicationMapper == null)
                                        {
                                            Bootstrap.configDBService().save(ConfigDBConstants.COLLECTION_APPLICATION_MAPPER,
                                                    new JsonObject().put(APPLICATION_MAPPER_NAME, metric.getString(Metric.METRIC_TYPE)).put(APPLICATION_MAPPER_PROTOCOL, APPLICATION_MAPPER_PROTOCOL_TCP)
                                                            .put(ConfigDBConstants.FIELD_TYPE, ConfigDBConstants.ENTITY_TYPE_SYSTEM)
                                                            .put(APPLICATION_MAPPER_PORT, metric.getInteger(PORT)),
                                                    DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, handlerResult ->
                                                    {
                                                        if (handlerResult.succeeded())
                                                        {
                                                            ApplicationMapperConfigStore.getStore().addItem(handlerResult.result()).onComplete(result ->
                                                            {
                                                                var item = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(metric.getLong(Metric.METRIC_OBJECT), NMSConstants.MetricPlugin.NETWORK_SERVICE.getName()));

                                                                if (item != null && item.getJsonObject(Metric.METRIC_CONTEXT) != null
                                                                        && item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS) != null)
                                                                {
                                                                    item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS).
                                                                            add(new JsonObject().put(STATUS, STATUS_UP).put(AIOpsObject.OBJECT_NAME, metric.getInteger(PORT) + " (" + metric.getString(Metric.METRIC_NAME) + ")"));

                                                                    Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_METRIC,
                                                                            new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                                                            item,
                                                                            DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                                                            asyncResult -> MetricConfigStore.getStore().updateItem(item.getLong(ID)));
                                                                }
                                                            });
                                                        }
                                                        else
                                                        {
                                                            LOGGER.warn(String.format("failed to create application mapper port %s , reason : %s", metric.getInteger(PORT), handlerResult.cause().getMessage()));
                                                        }
                                                    });
                                        }
                                    }
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }

                            message.reply(new JsonObject().put(STATUS, STATUS_SUCCEED).put(ID, asyncResponse.result())
                                    .put(MESSAGE, String.format("metric %s of object %s provisioned successfully...", metric.getString(Metric.METRIC_NAME),
                                            ObjectConfigStore.getStore().getObjectName(metric.getLong(Metric.METRIC_OBJECT)))));
                        }

                        else
                        {
                            LOGGER.warn(String.format(METRIC_PROVISION_FAILED, metric.getString(Metric.METRIC_NAME),
                                    ObjectConfigStore.getStore().getObjectName(metric.getLong(Metric.METRIC_OBJECT)), response.cause().getMessage()));

                            Bootstrap.configDBService().delete(ConfigDBConstants.COLLECTION_METRIC, new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID)
                                    .put(VALUE, asyncResponse.result()), event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS, result ->
                            {
                            });

                            message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DATABASE_FETCH).put(ID, asyncResponse.result())
                                    .put(MESSAGE, String.format(METRIC_PROVISION_FAILED,
                                            metric.getString(Metric.METRIC_NAME), ObjectConfigStore.getStore()
                                                    .getObjectName(metric.getLong(Metric.METRIC_OBJECT)), response.cause().getMessage())));
                        }

                    });

                }

                else
                {
                    LOGGER.warn(String.format(METRIC_PROVISION_FAILED, metric.getString(Metric.METRIC_NAME),
                            ObjectConfigStore.getStore().getObjectName(metric.getLong(Metric.METRIC_OBJECT)), asyncResponse.cause().getMessage()));

                    message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DATABASE_SAVE).put(MESSAGE, String.format(METRIC_PROVISION_FAILED, metric.getString(Metric.METRIC_NAME)
                            , ObjectConfigStore.getStore().getObjectName(metric.getLong(Metric.METRIC_OBJECT)), asyncResponse.cause().getMessage())));
                }

            });
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, CommonUtil.formatStackTrace(exception.getStackTrace())));
        }
    }

    private void queueMACScannerEvent(JsonObject context, JsonObject event)
    {
        var runbook = RunbookPluginConfigStore.getStore().getItem(Runbook.RunbookPluginId.MAC_SCANNER.getName());

        if (runbook != null)
        {
            if (runbook.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT))
            {
                runbook.mergeIn(runbook.getJsonObject(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT));

                runbook.remove(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT);
            }

            runbook.remove(ID);

            if (context.containsKey(AIOpsObject.OBJECT_CONTEXT))
            {
                context.mergeIn(context.getJsonObject(AIOpsObject.OBJECT_CONTEXT));
            }

            context.mergeIn(runbook);

            var eventId = CommonUtil.newEventId();

            context.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_PLUGIN_ENGINE)
                    .put(PluginEngineConstants.PLUGIN_ENGINE_REQUEST, PluginEngineConstants.PluginEngineRequest.RUNBOOK.getName())
                    .put(EventBusConstants.EVENT_ID, eventId);

            vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                    .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_PLUGIN_ENGINE)
                    .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                    .put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER)
                    .put(EventBusConstants.EVENT_CONTEXT, context));

            vertx.eventBus().send(EventBusConstants.EVENT_ROUTER, context);
        }
    }

    private void notifyDependencyManager(JsonObject metric, JsonArray objects, JsonObject event)
    {
        try
        {
            var object = ObjectConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_OBJECT));

            if (object != null && objects != null && !objects.isEmpty())
            {
                vertx.eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(), object.mergeIn(metric)
                        .put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                        .put(AIOpsConstants.DEPENDENCY_SOURCE, object.getString(AIOpsObject.OBJECT_IP))
                        .put(AIOpsConstants.DEPENDENCY_LEVEL, NMSConstants.isVirtualMachineMetric(metric.getString(Metric.METRIC_PLUGIN))
                                ? AIOpsConstants.DependencyLevel.EIGHT.getName() : AIOpsConstants.DependencyLevel.FOUR.getName())
                        .put(NMSConstants.OBJECTS, objects)
                        .put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.ADD_MULTIPLES.getName()));

                LOGGER.info(String.format("adding objects to dependency : %s  of object : %s ", objects.encode(), object.getString(AIOpsObject.OBJECT_NAME)));

                if (event.containsKey(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT)) //for manually created dependency mapper if interface is not provisioned in that case we need to manually provision that and after that we can add it in dependency manager so pass all contexts in dependency.mapper.context key
                {
                    var context = event.getJsonObject(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT);

                    if (CommonUtil.isNotNullOrEmpty(context.getString(AIOpsConstants.DEPENDENCY_SOURCE)) && CommonUtil.isNotNullOrEmpty(context.getString(AIOpsConstants.DEPENDENCY_DESTINATION))
                            && CommonUtil.isNotNullOrEmpty(context.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD)) && context.containsKey(AIOpsConstants.DEPENDENCY_LEVEL))
                    {
                        var level = AIOpsConstants.DependencyLevel.valueOfName(CommonUtil.getByteValue(context.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));

                        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(),
                                new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, context.getString(AIOpsConstants.DEPENDENCY_SOURCE))
                                        .put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                                        .put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.ADD.getName())
                                        .put(AIOpsConstants.DEPENDENCY_DESTINATION, context.getString(AIOpsConstants.DEPENDENCY_DESTINATION))
                                        .put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, context.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD))
                                        .put(AIOpsConstants.DEPENDENCY_SOURCE_PORT, context.getString(AIOpsConstants.DEPENDENCY_SOURCE_PORT))
                                        .put(AIOpsConstants.DEPENDENCY_DESTINATION_PORT, context.getString(AIOpsConstants.DEPENDENCY_DESTINATION_PORT))
                                        .put(AIOpsConstants.DEPENDENCY_PARENT, level == AIOpsConstants.DependencyLevel.SIX ? context.getString(AIOpsConstants.DEPENDENCY_PARENT) : null)
                                        .put(AIOpsConstants.DEPENDENCY_LEVEL, level.getName()));

                        LOGGER.info(String.format("adding source : %s and destination : %s ", context.getString(AIOpsConstants.DEPENDENCY_SOURCE) + VALUE_SEPARATOR + context.getString(AIOpsConstants.DEPENDENCY_SOURCE_PORT), context.getString(AIOpsConstants.DEPENDENCY_DESTINATION) + VALUE_SEPARATOR + context.getString(AIOpsConstants.DEPENDENCY_DESTINATION_PORT)));
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }


    // Update object state to archive instead of dropping from collection
    // Delete config devices too when, we delete object
    private void unprovisionObject(Message<JsonObject> message)
    {
        try
        {
            var event = message.body();

            Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_OBJECT, new JsonObject()
                    .put(ConfigDBConstants.FIELD_NAME, ID)
                    .put(VALUE, event.getLong(ID)), new JsonObject().put(AIOpsObject.OBJECT_STATE, NMSConstants.State.ARCHIVE.name()).put("plugins", ObjectConfigStore.getStore().getPluginsById(event.getLong(ID))), event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS, result ->
            {
                try
                {
                    if (result.succeeded() && !result.result().isEmpty())
                    {
                        vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_OBJECT.name())
                                .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                                .put(AIOpsObject.OBJECT_ID, event.getLong(AIOpsObject.OBJECT_ID))
                                .put(NMSConstants.OBJECT, event)
                                .put(ID, event.getLong(ID)));

                        vertx.eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_OBJECT.name())
                                .put(AIOpsObject.OBJECT_ID, event.getLong(AIOpsObject.OBJECT_ID))
                                .put(NMSConstants.OBJECT, event)
                                .put(ID, event.getLong(ID)));

                        vertx.eventBus().publish(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject()
                                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.DELETE_OBJECT.name())
                                .put(AIOpsObject.OBJECT_ID, event.getLong(AIOpsObject.OBJECT_ID))
                                .put(NMSConstants.OBJECT, event)
                                .put(ID, event.getLong(ID)));

                        LOGGER.info(String.format("object %s deleted successfully...",
                                ObjectConfigStore.getStore().getObjectName(event.getLong(ID))));

                        ObjectConfigStore.getStore().deleteItem(event.getLong(ID));

                        ObjectStatusCacheStore.getStore().deleteItem(event.getLong(ID));

                        var metrics = new ArrayList<JsonObject>();

                        MetricConfigStore.getStore().getItemsByObject(event.getLong(ID))
                                .forEach(metric -> metrics.add(new JsonObject().put(ID, metric.getLong(ID))));

                        if (!event.containsKey(AIOpsObject.OBJECT_AGENT))
                        {
                            LicenseCacheStore.getStore().update(NMSConstants.OBJECTS, false);
                        }


                        NMSConstants.updateMetrics(metrics, EVENT_METRIC_UNPROVISION);

                        MetricConfigStore.getStore().deleteMetricObject(event.getLong(ID), event.getLong(AIOpsObject.OBJECT_ID));

                        //#28033
                        MACScannerConfigStore.getStore().flatItems(MACScanner.MAC_SCANNER_DEVICE_IP_ADDRESS, event.getString(AIOpsObject.OBJECT_IP), ID).stream().map(CommonUtil::getLong).forEach(item -> MACScannerConfigStore.getStore().deleteItem(item));

                        message.reply(new JsonObject().put(STATUS, STATUS_SUCCEED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(ID, event.getLong(ID))
                                .put(MESSAGE, String.format("object %s deleted successfully...", ObjectConfigStore.getStore().getObjectName(event.getLong(ID)))));

                        JobScheduler.deleteSchedulers(event, JobScheduler.JobType.MAINTENANCE.getName());

                        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_CONFIG_DELETE, event);
                    }
                    else
                    {
                        LOGGER.warn(String.format("failed to delete object %s , reason : %s",
                                ObjectConfigStore.getStore().getObjectName(event.getLong(ID)), result.cause().getMessage() != null ? result.cause().getMessage() : UNKNOWN));

                        message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ID, event.getLong(ID)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DATABASE_UPDATE).put(ERROR, result.cause().getMessage() != null ? result.cause().getMessage() : UNKNOWN)
                                .put(MESSAGE, String.format(ENTITY_DELETE_FAILED, ObjectConfigStore.getStore().getObjectName(event.getLong(ID)), result.cause().getMessage() != null ? result.cause().getMessage() : UNKNOWN)));
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void send(JsonArray objects, JsonObject metric)
    {

        try
        {
            if (ObjectConfigStore.getStore().existItem(metric.getLong(Metric.METRIC_OBJECT)))
            {
                var eventId = CommonUtil.newEventId();

                int port = NOT_AVAILABLE;

                if (metric.containsKey(Metric.METRIC_CONTEXT))
                {
                    if (metric.getJsonObject(Metric.METRIC_CONTEXT).containsKey(PORT))
                    {
                        port = metric.getJsonObject(Metric.METRIC_CONTEXT).getInteger(PORT);
                    }

                    metric.mergeIn(metric.getJsonObject(Metric.METRIC_CONTEXT));

                    metric.remove(Metric.METRIC_CONTEXT);
                }

                metric.remove(Metric.METRIC_POLLING_TIME);

                metric.remove(Metric.METRIC_STATE);

                var object = ObjectConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_OBJECT));

                if (object.containsKey(AIOpsObject.OBJECT_CONTEXT))
                {
                    object.mergeIn(object.getJsonObject(AIOpsObject.OBJECT_CONTEXT));

                    object.remove(AIOpsObject.OBJECT_CONTEXT);
                }

                var credential = CredentialProfileConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_CREDENTIAL_PROFILE, (long) NOT_AVAILABLE));

                if (credential != null && credential.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
                {
                    credential.mergeIn(credential.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                    credential.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                }

                var metricId = metric.getLong(ID);

                metric.mergeIn(object).put(ID, metricId);

                // in case of metric context having port when we merge in object the port is getting override hence need to put port here
                if (port != NOT_AVAILABLE)
                {
                    metric.put(PORT, port);
                }

                // remove already discovered object...

                if (objects != null && !objects.isEmpty()) // vm/interface we can not have predefined list...
                {
                    if (!metric.getString(NMSConstants.REDISCOVER_JOB).equalsIgnoreCase(NMSConstants.RediscoverJob.APP.getName()))
                    {
                        var existingObjects = MetricConfigStore.getStore().getObjects(metricId);

                        if (existingObjects != null)
                        {
                            existingObjects.keySet().forEach(objects::remove);
                        }
                    }

                    metric.put(NMSConstants.OBJECTS, metric.getString(NMSConstants.REDISCOVER_JOB).equalsIgnoreCase(NMSConstants.RediscoverJob.NETWORK_SERVICE.getName()) ? objects
                            : new JsonArray(objects.stream().map(objectName -> new JsonObject().put(AIOpsObject.OBJECT_NAME, objectName)).collect(Collectors.toList())));
                }

                if (metric.containsKey(EventBusConstants.EVENT_SCHEDULER))
                {
                    SchedulerCacheStore.getStore().updatePendingProbes(metric.getLong(EventBusConstants.EVENT_SCHEDULER), SchedulerCacheStore.getStore().getSchedulerPendingProbes(metric.getLong(EventBusConstants.EVENT_SCHEDULER)) + 1);
                }

                vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                        .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_REDISCOVER)
                        .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                        .put(USER_NAME, metric.containsKey(USER_NAME) ? metric.getString(USER_NAME) : DEFAULT_USER)
                        .put(EventBusConstants.EVENT_CONTEXT, metric));

                vertx.eventBus().send(EventBusConstants.EVENT_ROUTER, metric.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_REDISCOVER)
                        .mergeIn(credential)
                        .put(EventBusConstants.EVENT_ID, eventId)
                        .put(ID, metricId)); // put id once again because it was overridden by credential profile item....
            }

            else
            {
                LOGGER.warn(String.format("aborting rediscover job for metric %s, reason: object not found", metric.getString(Metric.METRIC_NAME)));
            }

        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

    private void publish(JsonObject event, String sessionId)
    {
        EventBusConstants.publish(sessionId, EventBusConstants.UI_NOTIFICATION_OBJECT_PROVISION_PROGRESS, event);

        vertx.eventBus().send(EventBusConstants.EVENT_REDISCOVER_SCHEDULER_UPDATE, event);
    }

    /**
     * Method to change config manage status when we change object status to disable/maintenance
     *
     * @param event  Event
     * @param status Config Manage Status
     */
    private void updateConfigManagementStatus(JsonObject event, String status)
    {
        var configId = ConfigurationConfigStore.getStore().getIdByObjectId(ObjectConfigStore.getStore().getObjectId(event.getLong(ID)));

        if (configId != NOT_AVAILABLE)
        {
            LOGGER.info(String.format("Sending config manage request with status : %s , for the object : %s", status, ObjectConfigStore.getStore().getObjectName(event.getLong(ID))));

            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_CONFIG_MANAGE, event.put(ID, configId).put(Configuration.CONFIG_MANAGEMENT_STATUS, status));
        }
    }

    /**
     * Method will remove unwanted field which is not required while creating AIOPS object
     *
     * @param object Object
     */
    private void removeFields(JsonObject object)
    {
        object.remove(Discovery.DISCOVERY_EMAIL_RECIPIENTS);

        object.remove(Discovery.DISCOVERY_SMS_RECIPIENTS);

        object.remove(Discovery.DISCOVERY_CONFIG_MANAGEMENT_STATUS);

        object.remove(Discovery.DISCOVERY_CREDENTIAL_PROFILES);
    }

    @Override
    public void stop(Promise<Void> promise)
    {
        eventEngine.stop(vertx, promise);

    }
}
