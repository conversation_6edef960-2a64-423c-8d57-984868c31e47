/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date            Author              Notes
 *  01-APR-2025     Deven Chopra        Initial Version
 *  21-Mar-2025     Pruthviraj      Data retention policy patch added
 */

package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.DataRetentionPolicy;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.DataRetentionPolicyConfigStore;
import com.mindarray.store.MetricPolicyConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.db.ConfigDBConstants.FIELD_NAME;

public class Patch8019 implements Patch
{

    private static final Logger LOGGER = new Logger(Patch8019.class, MOTADATA_PATCH, "Patch 8.0.19");

    private static final String VERSION = "8.0.19";

    @Override
    public String getVersion()
    {
        return VERSION;
    }

    @Override
    public Future<Void> doPatch()
    {

        var promise = Promise.<Void>promise();

        var futures = new ArrayList<Future<Void>>();

        LOGGER.info("executing patch 8.0.19");

        futures.add(deleteForecastPolicies());

        futures.add(updateRetentionProfile());

        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                promise.complete();

                LOGGER.info("successfully executed patch 8.0.19");
            }
            else
            {
                promise.fail(result.cause());

                LOGGER.error(result.cause());
            }
        });

        return promise.future();
    }

    private Future<Void> updateRetentionProfile()
    {
        var promise = Promise.<Void>promise();

        var item = DataRetentionPolicyConfigStore.getStore().getItem(GlobalConstants.DEFAULT_ID);

        if (item != null)
        {
            item.getJsonObject(DataRetentionPolicy.DATA_RETENTION_POLICY_CONTEXT).put("NETROUTE_METRIC", new JsonObject().put("raw", 7));

            Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_DATA_RETENTION_POLICY,
                    new JsonObject().put(FIELD_NAME, ID).put(VALUE, GlobalConstants.DEFAULT_ID),
                    item,
                    DEFAULT_USER,
                    SYSTEM_REMOTE_ADDRESS, result ->
                    {
                        if (result.succeeded())
                        {
                            DataRetentionPolicyConfigStore.getStore().updateItem(GlobalConstants.DEFAULT_ID).onComplete(handler ->
                            {
                                LOGGER.info("Data retention value changed for NETROUTE_METRIC ");

                                promise.complete();
                            });
                        }
                        else
                        {
                            LOGGER.warn(String.format("Failed to update Data retention with reason : %s", result.cause().getMessage()));

                            promise.fail(result.cause());
                        }
                    });
        }
        else
        {
            promise.fail("item not found");
        }

        return promise.future();
    }

    private Future<Void> deleteForecastPolicies()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var items = MetricPolicyConfigStore.getStore().getItemsByValue(PolicyEngineConstants.POLICY_TYPE, PolicyEngineConstants.PolicyType.FORECAST.getName());

            var promises = new ArrayList<Future<Void>>();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("deleting forecast policy %s : ", item.encode()));
                }

                var future = Promise.<Void>promise();

                promises.add(future.future());

                Bootstrap.configDBService.delete(ConfigDBConstants.COLLECTION_METRIC_POLICY,
                        new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                        DEFAULT_USER, MOTADATA_SYSTEM, result ->
                        {
                            if (result.succeeded())
                            {
                                MetricPolicyConfigStore.getStore().deleteItem(item.getLong(ID));

                                future.complete();
                            }
                            else
                            {
                                LOGGER.warn(String.format("failed to delete forecast policy : %s because of : %s ", item.getLong(ID), result.cause()));

                                future.fail(result.cause());
                            }
                        });
            }

            Future.join(promises).onComplete(result ->
            {
                if (result.succeeded())
                {
                    promise.complete();
                }
                else
                {
                    promise.fail(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            return Future.failedFuture(exception);
        }

        return promise.future();
    }
}
