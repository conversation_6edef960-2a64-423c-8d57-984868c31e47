/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */


package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.api.CredentialProfile;
import com.mindarray.api.Integration;
import com.mindarray.api.IntegrationProfile;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.integration.IntegrationConstants;
import com.mindarray.integration.ServiceOpsIntegration;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.CredentialProfileConfigStore;
import com.mindarray.store.IntegrationConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.OAuthUtil;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.db.ConfigDBConstants.COLLECTION_CREDENTIAL_PROFILE;
import static com.mindarray.db.ConfigDBConstants.FIELD_NAME;

public class Patch8016 implements Patch
{
    private static final Logger LOGGER = new Logger(Patch8016.class, MOTADATA_PATCH, "Patch 8.0.16");

    private static final String VERSION = "8.0.16";

    @Override
    public String getVersion()
    {
        return VERSION;
    }

    @Override
    public Future<Void> doPatch()
    {
        var promise = Promise.<Void>promise();

        var futures = new ArrayList<Future<Void>>();

        LOGGER.info("executing patch 8.0.16");

        futures.add(updateOAuthCredentialProfiles());

        futures.add(executeServiceOpsPatch());

        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                promise.complete();

                LOGGER.info("successfully executed patch 8.0.16");
            }
            else
            {
                promise.fail(result.cause());

                LOGGER.error(result.cause());
            }
        });

        return promise.future();
    }

    private Future<Void> updateOAuthCredentialProfiles()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var items = CredentialProfileConfigStore.getStore().getItems();

            var futures = new ArrayList<Future<Void>>();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                var valid = false;

                if (item.containsKey(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL) && NMSConstants.Protocol.HTTP_HTTPS.getName().equalsIgnoreCase(item.getString(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL))
                        && item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT).containsKey(OAuthUtil.AUTHENTICATION_TYPE) && NMSConstants.AuthenticationType.OAUTH.getName().equalsIgnoreCase(item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT).getString(OAuthUtil.AUTHENTICATION_TYPE)))
                {
                    valid = true;

                    item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT).put(OAuthUtil.GRANT_TYPE, NMSConstants.OAuthGrantType.AUTHORIZATION_CODE.getName());
                }

                if (valid)
                {
                    var future = Promise.<Void>promise();

                    futures.add(future.future());

                    Bootstrap.configDBService().update(COLLECTION_CREDENTIAL_PROFILE, new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)), item,
                            DEFAULT_USER, MOTADATA_SYSTEM, result ->
                            {
                                if (result.succeeded())
                                {
                                    CredentialProfileConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            LOGGER.info(String.format("updated Credential Profile : %s : item: %s", result.result(), CredentialProfileConfigStore.getStore().getItem(item.getLong(ID))));

                                            future.complete();
                                        }
                                        else
                                        {
                                            future.fail(asyncResult.cause());
                                        }

                                    });
                                }
                                else
                                {
                                    future.fail(result.cause());
                                }
                            });
                }
            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    LOGGER.info("Credential profile updated successfully!");

                    promise.complete();
                }
                else
                {
                    LOGGER.info("Failed to update Credential profile : " + result.cause());

                    promise.fail(result.cause());
                }
            });

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }


    private Future<Void> executeServiceOpsPatch()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var item = IntegrationConfigStore.getStore().getItem(IntegrationConstants.IntegrationId.SERVICEOPS.getName());

            var credentialProfile = new JsonObject();

            if (item.containsKey(Integration.INTEGRATION_CONTEXT)
                    && item.getJsonObject(Integration.INTEGRATION_CONTEXT) != null
                    && !item.getJsonObject(Integration.INTEGRATION_CONTEXT).isEmpty()
                    && item.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(GlobalConstants.TARGET)
                    && CommonUtil.isNotNullOrEmpty(item.getJsonObject(Integration.INTEGRATION_CONTEXT).getString(GlobalConstants.TARGET))
                    && !item.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE))
            {
                credentialProfile.put(CredentialProfile.CREDENTIAL_PROFILE_NAME, "ServiceOps Credential Profile")
                        .put(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL, NMSConstants.Protocol.HTTP_HTTPS.getName())
                        .put(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT, new JsonObject()
                                .put(OAuthUtil.AUTHENTICATION_TYPE, NMSConstants.AuthenticationType.OAUTH.getName())
                                .put(OAuthUtil.GRANT_TYPE, NMSConstants.OAuthGrantType.PASSWORD.getName())
                                .put(USERNAME, item.getJsonObject(Integration.INTEGRATION_CONTEXT).getString(USERNAME, EMPTY_VALUE))
                                .put(PASSWORD, item.getJsonObject(Integration.INTEGRATION_CONTEXT).getString(PASSWORD, EMPTY_VALUE))
                                .put(APIConstants.CLIENT_ID, item.getJsonObject(Integration.INTEGRATION_CONTEXT).getString(APIConstants.CLIENT_ID, EMPTY_VALUE))
                                .put(ServiceOpsIntegration.CLIENT_SECRET, item.getJsonObject(Integration.INTEGRATION_CONTEXT).getString(ServiceOpsIntegration.CLIENT_SECRET, EMPTY_VALUE)));


                item.getJsonObject(Integration.INTEGRATION_CONTEXT).remove(USERNAME);

                item.getJsonObject(Integration.INTEGRATION_CONTEXT).remove(PASSWORD);

                item.getJsonObject(Integration.INTEGRATION_CONTEXT).remove(APIConstants.CLIENT_ID);

                item.getJsonObject(Integration.INTEGRATION_CONTEXT).remove(ServiceOpsIntegration.CLIENT_SECRET);

                Bootstrap.configDBService().save(ConfigDBConstants.COLLECTION_CREDENTIAL_PROFILE, credentialProfile,
                        DEFAULT_USER, MOTADATA_SYSTEM, response ->
                        {
                            if (response.succeeded())
                            {
                                CredentialProfileConfigStore.getStore().addItem(response.result()).onComplete(asyncResponse ->
                                {
                                    if (asyncResponse.succeeded())
                                    {
                                        LOGGER.info(String.format("updated Credential Profile : %s : profile name: %s", response.result(), CredentialProfileConfigStore.getStore().getItem(response.result()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)));

                                        item.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, response.result());

                                        Bootstrap.configDBService.update(ConfigDBConstants.COLLECTION_INTEGRATION,
                                                new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                                item,
                                                DEFAULT_USER, MOTADATA_SYSTEM, result ->
                                                {
                                                    if (result.succeeded())
                                                    {
                                                        IntegrationConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                                        {
                                                            if (asyncResult.succeeded())
                                                            {
                                                                LOGGER.info(String.format("updated ServiceOps integration Configuration : %s : item: %s", item.getLong(ID), IntegrationConfigStore.getStore().getItem(item.getLong(ID))));

                                                                promise.complete();
                                                            }
                                                            else
                                                            {
                                                                LOGGER.warn(asyncResult.cause().getMessage());

                                                                promise.fail(asyncResult.cause());
                                                            }
                                                        });
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(String.format("failed to update ServiceOps integration Configuration : %s because of : %s ", item.getLong(ID), result.cause()));

                                                        promise.fail(result.cause());
                                                    }
                                                });
                                    }
                                    else
                                    {
                                        promise.fail(asyncResponse.cause());
                                    }

                                });
                            }
                            else
                            {
                                promise.fail(response.cause());
                            }
                        });
            }
            else
            {
                promise.complete();
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }
}
