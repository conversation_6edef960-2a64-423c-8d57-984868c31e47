/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */
package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.api.APIConstants;
import com.mindarray.api.ConfigTemplate;
import com.mindarray.api.Rebranding;
import com.mindarray.api.SNMPDeviceCatalog;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CipherUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.AIOpsObject.*;
import static com.mindarray.api.MetricPolicy.POLICY_RENOTIFY;
import static com.mindarray.db.ConfigDBConstants.*;
import static com.mindarray.policy.PolicyEngineConstants.*;

public class Patch806 implements Patch
{
    private static final Logger LOGGER = new Logger(Patch806.class, MOTADATA_PATCH, "Patch 8.0.6");

    private static final String VERSION = "8.0.6";
    private static final String SNMP_CHECK_RETRIES = "snmp.check.retries";
    private static final String RETRY_COUNT = "retry.count";
    private final Map<String, Long> lookups = new HashMap<>();

    @Override
    public String getVersion()
    {
        return VERSION;
    }

    @Override
    public Future<Void> doPatch()
    {
        var voidPromise = Promise.<Void>promise();

        var futures = new ArrayList<Future<Void>>();

        try
        {
            var oid = SNMPDeviceCatalogConfigStore.getStore().getItem().getString(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_OID);

            if (SNMPDeviceCatalogConfigStore.getStore().getItemsByValue(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_OID, oid).size() > 1)
            {
                var promise = Promise.<Void>promise();

                futures.add(promise.future());

                populateSNMPDevices().onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        updateReference().onComplete(asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                var promise1 = Promise.<Void>promise();

                                updateCatalog().onComplete(response ->
                                {
                                    if (response.succeeded())
                                    {
                                        promise1.complete();
                                    }
                                    else
                                    {
                                        promise1.fail(response.cause());
                                    }
                                });

                                var promise2 = Promise.<Void>promise();

                                updateTrap().onComplete(response ->
                                {
                                    if (response.succeeded())
                                    {
                                        promise2.complete();
                                    }
                                    else
                                    {
                                        promise2.fail(response.cause());
                                    }
                                });

                                Future.join(promise1.future(), promise2.future()).onComplete(response ->
                                {
                                    if (response.succeeded())
                                    {
                                        promise.complete();
                                    }
                                    else
                                    {
                                        promise.fail(response.cause());
                                    }
                                });
                            }
                            else
                            {
                                promise.fail(asyncResult.cause());
                            }
                        });
                    }
                    else
                    {
                        promise.fail(result.cause());
                    }
                });
            }
            else
            {
                LOGGER.info("SNMP Device catalogs contains default IDs");
            }

            // patch for notification improvement MOTADATA-1010

            var promise3 = Promise.<Void>promise();

            futures.add(promise3.future());

            LOGGER.info("updating all metric policies context for notification and renotification...");

            var promises = new ArrayList<Future<Void>>();

            var metricPolicies = MetricPolicyConfigStore.getStore().getItems();

            for (var index = 0; index < metricPolicies.size(); index++)
            {
                var policy = metricPolicies.getJsonObject(index);

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("updating metric policy %s : ", policy.encode()));
                }

                var promise = Promise.<Void>promise();

                promises.add(promise.future());

                Bootstrap.configDBService.update(ConfigDBConstants.COLLECTION_METRIC_POLICY,
                        new JsonObject().put(FIELD_NAME, ID).put(VALUE, policy.getLong(ID)),
                        updateMetricPolicy(policy),
                        DEFAULT_USER, MOTADATA_SYSTEM, result ->
                        {
                            if (result.succeeded())
                            {
                                MetricPolicyConfigStore.getStore().updateItem(policy.getLong(ID)).onComplete(asyncResult ->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        LOGGER.info(String.format("updated policy : %s : item: %s", policy.getLong(ID), MetricPolicyConfigStore.getStore().getItem(policy.getLong(ID))));

                                        promise.complete();
                                    }
                                    else
                                    {
                                        LOGGER.warn(asyncResult.cause().getMessage());

                                        promise.fail(asyncResult.cause());
                                    }
                                });
                            }
                            else
                            {
                                LOGGER.warn(String.format("failed to update policy : %s because of : %s ", policy.getLong(ID), result.cause()));

                                promise.fail(result.cause());
                            }
                        });
            }

            LOGGER.info("updating all event policies context for notification and renotification...");

            var eventPolicies = EventPolicyConfigStore.getStore().getItems();

            for (var index = 0; index < eventPolicies.size(); index++)
            {
                var policy = eventPolicies.getJsonObject(index);

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("updating event policy %s : ", policy.encode()));
                }

                var promise = Promise.<Void>promise();

                promises.add(promise.future());

                Bootstrap.configDBService.update(ConfigDBConstants.COLLECTION_EVENT_POLICY,
                        new JsonObject().put(FIELD_NAME, ID).put(VALUE, policy.getLong(ID)),
                        updateEventPolicy(policy),
                        DEFAULT_USER, MOTADATA_SYSTEM, result ->
                        {
                            if (result.succeeded())
                            {
                                EventPolicyConfigStore.getStore().updateItem(policy.getLong(ID)).onComplete(asyncResult ->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        LOGGER.info(String.format("updated policy : %s : item: %s", policy.getLong(ID), EventPolicyConfigStore.getStore().getItem(policy.getLong(ID))));

                                        promise.complete();
                                    }
                                    else
                                    {
                                        LOGGER.warn(asyncResult.cause().getMessage());

                                        promise.fail(asyncResult.cause());
                                    }
                                });
                            }
                            else
                            {
                                LOGGER.warn(String.format("failed to update policy : %s because of : %s ", policy.getLong(ID), result.cause()));

                                promise.fail(result.cause());
                            }
                        });
            }

            Future.join(promises).onComplete(result ->
            {
                if (result.succeeded())
                {
                    promise3.complete();
                }
                else
                {
                    promise3.fail(result.cause());
                }
            });

            var promise4 = Promise.<Void>promise();

            futures.add(promise4.future());

            updateObjectConfigStore().onComplete(result ->
            {
                if (result.succeeded())
                {
                    promise4.complete();
                }
                else
                {
                    promise4.fail(result.cause());
                }
            });

            var future = Promise.<Void>promise();

            futures.add(future.future());

            // previously UI was hitting the post request initially and then PUT request for update. because of that new ID was getting generated.
            // here we are updating single entry of rebranding with default ID. BUG : MOTADATA-1965

            var item = RebrandingConfigStore.getStore().getItem();

            LOGGER.info(String.format("performing patch on item : %s ", item.encode()));

            Bootstrap.configDBService().delete(COLLECTION_REBRANDING, new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
            {
                if (result.succeeded())
                {
                    RebrandingConfigStore.getStore().deleteItem(item.getLong(ID));

                    // this is because , previously we had this value of rebranding.logo. when responding result , it was passing null.

                    if (item.containsKey(Rebranding.REBRANDING_LOGO) && item.getString(Rebranding.REBRANDING_LOGO).equalsIgnoreCase("bc508eca-78f7-4bc7-82ab-8b7556c1eb20"))
                    {
                        item.put(Rebranding.REBRANDING_LOGO, EMPTY_VALUE);
                    }

                    item.put(ID, DEFAULT_ID);

                    Bootstrap.configDBService().save(COLLECTION_REBRANDING, item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            LOGGER.info(String.format("rebranding item : %s ", item.encode()));

                            RebrandingConfigStore.getStore().addItem(item.getLong(ID)).onComplete(voidAsyncResult -> future.complete());
                        }
                        else
                        {
                            LOGGER.warn("failed to update rebranding patch .... ");

                            future.fail(asyncResult.cause());
                        }
                    });
                }
                else
                {
                    LOGGER.error(result.cause());

                    future.fail(result.cause());
                }
            });

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    voidPromise.complete();
                }
                else
                {
                    voidPromise.fail(result.cause());
                }
            });

        }
        catch (Exception exception)
        {
            voidPromise.fail(exception);

            LOGGER.error(exception);
        }

        return voidPromise.future();
    }

    private Future<Void> updateReference()
    {
        var promise = Promise.<Void>promise();

        SNMPDeviceCatalogConfigStore.getStore().getReferenceCountsByItem().onComplete(response ->
        {
            try
            {
                LOGGER.info(String.format("SNMP Device catalog reference counts by item result %s", response.result()));

                if (response.succeeded())
                {
                    var ids = response.result().getMap().keySet();

                    var futures = new ArrayList<Future<Void>>();

                    for (var id : ids)
                    {
                        var future = Promise.<Void>promise();

                        futures.add(future.future());

                        SNMPDeviceCatalogConfigStore.getStore().getReferenceEntities(CommonUtil.getLong(id)).onComplete(async ->
                        {
                            try
                            {
                                LOGGER.info(String.format("SNMP Device catalog reference entities for id %s with result %s", id, async.result()));

                                var asyncFuture = new ArrayList<Future<Void>>();

                                if (async.succeeded())
                                {
                                    var result = async.result();

                                    if (result.containsKey(APIConstants.Entity.OBJECT.getName()))
                                    {
                                        var monitorFuture = Promise.<Void>promise();

                                        asyncFuture.add(monitorFuture.future());

                                        var monitors = result.getJsonArray(APIConstants.Entity.OBJECT.getName());

                                        var monitorIds = new JsonArray();

                                        if (!monitors.isEmpty())
                                        {
                                            var oid = monitors.getJsonObject(0).getString(OBJECT_SYSTEM_OID);

                                            var defaultId = lookups.get(oid);

                                            if (defaultId != null)
                                            {
                                                // this means that it is system created catalog.
                                                for (var index = 0; index < monitors.size(); index++)
                                                {
                                                    monitorIds.add(monitors.getJsonObject(index).getLong(ID));
                                                }

                                                Bootstrap.configDBService().updateAll(COLLECTION_OBJECT,
                                                        new JsonObject().put(FIELD_NAME, ID).put(VALUE, monitorIds),
                                                        new JsonObject().put(OBJECT_SNMP_DEVICE_CATALOG, defaultId),
                                                        DEFAULT_USER,
                                                        SYSTEM_REMOTE_ADDRESS,
                                                        asyncResult ->
                                                        {
                                                            try
                                                            {
                                                                if (asyncResult.succeeded())
                                                                {
                                                                    ObjectConfigStore.getStore().updateItems(monitorIds).onComplete(asyncResponse ->
                                                                    {
                                                                        if (asyncResponse.succeeded())
                                                                        {
                                                                            LOGGER.info(String.format("Updating SNMP Device catalog reference for monitors %s", monitorIds));

                                                                            monitorFuture.complete();
                                                                        }
                                                                        else
                                                                        {
                                                                            LOGGER.warn(String.format("Failed to update SNMP Device Catalog reference for monitors %s with reason %s", monitorIds, asyncResponse.cause().getMessage()));

                                                                            monitorFuture.fail(asyncResponse.cause());
                                                                        }
                                                                    });
                                                                }
                                                                else
                                                                {
                                                                    LOGGER.warn(String.format("Failed to update SNMP Device Catalog for monitors %s with reason %s", monitorIds, asyncResult.cause().getMessage()));

                                                                    monitorFuture.fail(asyncResult.cause());
                                                                }
                                                            }
                                                            catch (Exception exception)
                                                            {
                                                                LOGGER.error(exception);

                                                                monitorFuture.fail(exception);
                                                            }

                                                        });
                                            }
                                            else
                                            {
                                                // this means that it is user created snmp device catalog. We do not have to change its reference.
                                                monitorFuture.complete();

                                                LOGGER.warn(String.format("Catalog %s is a custom catalog and will not be qualified for monitors [%s]", SNMPDeviceCatalogConfigStore.getStore().getItem(CommonUtil.getLong(id)).encodePrettily(), monitors.encodePrettily()));
                                            }
                                        }
                                    }

                                    if (result.containsKey(APIConstants.Entity.CONFIG_TEMPLATE.getName()))
                                    {
                                        var templates = result.getJsonArray(APIConstants.Entity.CONFIG_TEMPLATE.getName());

                                        var updatedCatalogs = new JsonArray();

                                        if (!templates.isEmpty())
                                        {
                                            for (var index = 0; index < templates.size(); index++)
                                            {
                                                var configPromise = Promise.<Void>promise();

                                                asyncFuture.add(configPromise.future());

                                                var template = templates.getJsonObject(index);

                                                var catalogIds = templates.getJsonObject(index).getJsonArray(ConfigTemplate.CONFIG_TEMPLATE_CATALOG_IDS);

                                                for (var count = 0; count < catalogIds.size(); count++)
                                                {
                                                    var catalog = SNMPDeviceCatalogConfigStore.getStore().getItem(catalogIds.getLong(count));

                                                    if (catalog.getString(FIELD_TYPE).equalsIgnoreCase(ENTITY_TYPE_USER))
                                                    {
                                                        // this means that it is user created catalog.
                                                        updatedCatalogs.add(catalog.getLong(ID));
                                                    }
                                                    else if (lookups.get(catalog.getString(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_OID)) != null)
                                                    {
                                                        // this means that it is system created catalog
                                                        updatedCatalogs.add(lookups.get(catalog.getString(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_OID)));
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(String.format("No mapping for catalog %s found", catalog.encodePrettily()));
                                                    }
                                                }

                                                Bootstrap.configDBService().update(COLLECTION_CONFIG_TEMPLATE,
                                                        new JsonObject().put(FIELD_NAME, ID).put(VALUE, template.getLong(ID)),
                                                        new JsonObject().put(ConfigTemplate.CONFIG_TEMPLATE_CATALOG_IDS, updatedCatalogs),
                                                        DEFAULT_USER,
                                                        SYSTEM_REMOTE_ADDRESS,
                                                        asyncResult ->
                                                        {
                                                            if (asyncResult.succeeded())
                                                            {
                                                                ConfigTemplateConfigStore.getStore().updateItem(template.getLong(ID)).onComplete(asyncResponse ->
                                                                {
                                                                    if (asyncResponse.succeeded())
                                                                    {
                                                                        LOGGER.info(String.format("SNMP Device catalog reference for Config Template %s updated", template));

                                                                        configPromise.complete();
                                                                    }
                                                                    else
                                                                    {
                                                                        LOGGER.warn(String.format("SNMP Device Catalog reference for Config Template %s failed with reason %s", template, asyncResponse.cause().getMessage()));

                                                                        configPromise.fail(asyncResponse.cause());
                                                                    }
                                                                });
                                                            }
                                                            else
                                                            {
                                                                LOGGER.warn(String.format("Failed to update SNMP Device catalog reference for Config Template %s with reason %s", template, asyncResult.cause().getMessage()));

                                                                configPromise.fail(asyncResult.cause());
                                                            }
                                                        });
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    LOGGER.warn("Failed to get Reference Entities: " + async.cause().getMessage());
                                }

                                Future.join(asyncFuture).onComplete(result ->
                                {
                                    if (result.succeeded())
                                    {
                                        future.complete();
                                    }
                                    else
                                    {
                                        future.fail(result.cause());
                                    }
                                });
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                future.fail(exception);
                            }
                        });
                    }

                    var oidGroupPromise = Promise.<Void>promise();

                    futures.add(oidGroupPromise.future());

                    updateOIDGroup().onComplete(asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            oidGroupPromise.complete();
                        }
                        else
                        {
                            oidGroupPromise.fail(asyncResult.cause());
                        }
                    });

                    Future.join(futures).onComplete(result ->
                    {
                        if (result.succeeded())
                        {
                            promise.complete();
                        }
                        else
                        {
                            promise.fail(response.cause());
                        }
                    });
                }
                else
                {
                    promise.fail(response.cause());

                    LOGGER.warn("Failed to get Reference Counts By Item for SNMPDeviceCatalogConfigStore: " + response.cause().getMessage());
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                promise.fail(exception);
            }
        });

        return promise.future();
    }

    private JsonObject updateMetricPolicy(JsonObject policy)
    {
        var notifyContext = new JsonObject();

        var renotifyContext = new JsonObject();

        if (policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.AVAILABILITY.getName()))
        {
            if (policy.containsKey(POLICY_EMAIL_NOTIFICATION_RECIPIENTS) && !policy.getJsonArray(POLICY_EMAIL_NOTIFICATION_RECIPIENTS).isEmpty())
            {
                notifyContext.put(POLICY_EMAIL_NOTIFICATION_RECIPIENTS, policy.getJsonArray(POLICY_EMAIL_NOTIFICATION_RECIPIENTS)).put(POLICY_USER_NOTIFICATION_RECIPIENTS, new JsonArray().add(DEFAULT_USER)).put(POLICY_NOTIFICATION_SEVERITY, new JsonArray().add(Severity.DOWN.name()).add(Severity.CLEAR.name()));
            }

            if (policy.containsKey(POLICY_RENOTIFY))
            {
                if (policy.getString(POLICY_RENOTIFY).equalsIgnoreCase(NO))
                {
                    renotifyContext.put(POLICY_EMAIL_NOTIFICATION_RECIPIENTS, policy.getJsonArray(POLICY_EMAIL_NOTIFICATION_RECIPIENTS)).put(POLICY_USER_NOTIFICATION_RECIPIENTS, new JsonArray().add(DEFAULT_USER)).put(POLICY_NOTIFICATION_SEVERITY, Severity.DOWN.name()).put("policy.renotification.timer.seconds", 1800);

                    policy.put(POLICY_RENOTIFY, YES).put("policy.renotify.acknowledged", YES);
                }
                else
                {
                    policy.put(POLICY_RENOTIFY, NO);
                }
            }

            policy.put("policy.email.notification.subject", METRIC_POLICY_EMAIL_NOTIFICATION_DEFAULT_SUBJECT).put(POLICY_MESSAGE, METRIC_POLICY_EMAIL_NOTIFICATION_DEFAULT_MESSAGE).put(POLICY_NOTIFICATION_CONTEXT, !notifyContext.isEmpty() ? new JsonArray().add(notifyContext) : new JsonArray()).put(POLICY_RENOTIFICATION_CONTEXT, !renotifyContext.isEmpty() ? new JsonArray().add(renotifyContext) : new JsonArray());

        }
        else if (policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.ANOMALY.getName()) || policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.FORECAST.getName()) || policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.BASELINE.getName()))
        {
            if (policy.containsKey(POLICY_EMAIL_NOTIFICATION_RECIPIENTS) && !policy.getJsonArray(POLICY_EMAIL_NOTIFICATION_RECIPIENTS).isEmpty())
            {
                notifyContext.put(POLICY_EMAIL_NOTIFICATION_RECIPIENTS, policy.getJsonArray(POLICY_EMAIL_NOTIFICATION_RECIPIENTS)).put(POLICY_USER_NOTIFICATION_RECIPIENTS, new JsonArray().add(DEFAULT_USER)).put(POLICY_NOTIFICATION_SEVERITY, new JsonArray().add(Severity.CRITICAL.name()).add(Severity.WARNING.name()).add(Severity.MAJOR.name()));
            }

            if (policy.containsKey(POLICY_RENOTIFY))
            {
                if (policy.getString(POLICY_RENOTIFY).equalsIgnoreCase(NO))
                {
                    renotifyContext.put(POLICY_EMAIL_NOTIFICATION_RECIPIENTS, policy.getJsonArray(POLICY_EMAIL_NOTIFICATION_RECIPIENTS)).put(POLICY_USER_NOTIFICATION_RECIPIENTS, new JsonArray().add(DEFAULT_USER)).put(POLICY_NOTIFICATION_SEVERITY, Severity.CRITICAL.name()).put("policy.renotification.timer.seconds", 1800);

                    policy.put(POLICY_RENOTIFY, YES).put("policy.renotify.acknowledged", YES);
                }
                else
                {
                    policy.put(POLICY_RENOTIFY, NO);
                }
            }

            policy.put("policy.email.notification.subject", METRIC_POLICY_EMAIL_NOTIFICATION_DEFAULT_SUBJECT).put(POLICY_MESSAGE, "$$$policy.message$$$").put(POLICY_NOTIFICATION_CONTEXT, !notifyContext.isEmpty() ? new JsonArray().add(notifyContext) : new JsonArray()).put(POLICY_RENOTIFICATION_CONTEXT, !renotifyContext.isEmpty() ? new JsonArray().add(renotifyContext) : new JsonArray());

        }
        else
        {
            if (policy.containsKey(POLICY_EMAIL_NOTIFICATION_RECIPIENTS) && !policy.getJsonArray(POLICY_EMAIL_NOTIFICATION_RECIPIENTS).isEmpty())
            {
                notifyContext.put(POLICY_EMAIL_NOTIFICATION_RECIPIENTS, policy.getJsonArray(POLICY_EMAIL_NOTIFICATION_RECIPIENTS)).put(POLICY_USER_NOTIFICATION_RECIPIENTS, new JsonArray().add(DEFAULT_USER)).put(POLICY_NOTIFICATION_SEVERITY, new JsonArray().add(Severity.CRITICAL.name()).add(Severity.WARNING.name()).add(Severity.MAJOR.name()).add(Severity.CLEAR.name()));
            }

            if (policy.containsKey(POLICY_RENOTIFY))
            {
                if (policy.getString(POLICY_RENOTIFY).equalsIgnoreCase(NO))
                {
                    renotifyContext.put(POLICY_EMAIL_NOTIFICATION_RECIPIENTS, policy.getJsonArray(POLICY_EMAIL_NOTIFICATION_RECIPIENTS)).put(POLICY_USER_NOTIFICATION_RECIPIENTS, new JsonArray().add(DEFAULT_USER)).put(POLICY_NOTIFICATION_SEVERITY, Severity.CRITICAL.name()).put("policy.renotification.timer.seconds", 1800);

                    policy.put(POLICY_RENOTIFY, YES).put("policy.renotify.acknowledged", YES);
                }
                else
                {
                    policy.put(POLICY_RENOTIFY, NO);
                }
            }

            policy.put("policy.email.notification.subject", METRIC_POLICY_EMAIL_NOTIFICATION_DEFAULT_SUBJECT).put(POLICY_MESSAGE, METRIC_POLICY_EMAIL_NOTIFICATION_DEFAULT_MESSAGE).put(POLICY_NOTIFICATION_CONTEXT, !notifyContext.isEmpty() ? new JsonArray().add(notifyContext) : new JsonArray()).put(POLICY_RENOTIFICATION_CONTEXT, !renotifyContext.isEmpty() ? new JsonArray().add(renotifyContext) : new JsonArray());
        }

        return policy;
    }

    private JsonObject updateEventPolicy(JsonObject policy)
    {
        var notifyContext = new JsonObject();

        if (policy.containsKey(POLICY_EMAIL_NOTIFICATION_RECIPIENTS) && !policy.getJsonArray(POLICY_EMAIL_NOTIFICATION_RECIPIENTS).isEmpty())
        {
            notifyContext.put(POLICY_EMAIL_NOTIFICATION_RECIPIENTS, policy.getJsonArray(POLICY_EMAIL_NOTIFICATION_RECIPIENTS)).put(POLICY_USER_NOTIFICATION_RECIPIENTS, new JsonArray().add(DEFAULT_USER)).put(POLICY_NOTIFICATION_SEVERITY, new JsonArray().add(Severity.CRITICAL.name()).add(Severity.WARNING.name()).add(Severity.MAJOR.name()).add(Severity.CLEAR.name()));
        }

        if (policy.containsKey(POLICY_RENOTIFY) && policy.getString(POLICY_RENOTIFY).equalsIgnoreCase(YES))
        {
            policy.put(POLICY_RENOTIFY, NO);
        }

        if (policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.TRAP.getName()))
        {
            policy.put(POLICY_MESSAGE, FLOW_POLICY_EMAIL_NOTIFICATION_DEFAULT_MESSAGE);
        }
        else
        {
            policy.put(POLICY_MESSAGE, EVENT_POLICY_EMAIL_NOTIFICATION_DEFAULT_MESSAGE);
        }

        policy.put("policy.email.notification.subject", EVENT_POLICY_EMAIL_NOTIFICATION_DEFAULT_SUBJECT).put(POLICY_NOTIFICATION_CONTEXT, !notifyContext.isEmpty() ? new JsonArray().add(notifyContext) : new JsonArray());

        return policy;
    }

    private Future<Void> updateOIDGroup()
    {
        var promise = Promise.<Void>promise();

        var items = SNMPOIDGroupConfigStore.getStore().getItems();

        var futures = new ArrayList<Future<Void>>();

        for (var index = 0; index < items.size(); index++)
        {
            var future = Promise.<Void>promise();

            var item = items.getJsonObject(index);

            if (item.containsKey(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_ID) && item.getLong(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_ID) != null && SNMPDeviceCatalogConfigStore.getStore().getItem(item.getLong(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_ID)) != null)
            {
                futures.add(future.future());

                var oid = SNMPDeviceCatalogConfigStore.getStore().getItem(item.getLong(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_ID)).getString(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_OID);

                var defaultId = lookups.get(oid);

                if (defaultId != null)
                {
                    Bootstrap.configDBService().update(COLLECTION_SNMP_OID_GROUP,
                            new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                            new JsonObject().put(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_ID, defaultId),
                            DEFAULT_USER,
                            SYSTEM_REMOTE_ADDRESS,
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    SNMPOIDGroupConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            LOGGER.info(String.format("SNMP Device Catalog ID reference updated for %s oid group", item));

                                            future.complete();
                                        }
                                        else
                                        {
                                            LOGGER.warn(String.format("Failed to update SNMP Device Catalog ID reference for %s oid group with cause %s", item, asyncResult.cause().getMessage()));

                                            future.fail(asyncResult.cause());
                                        }
                                    });
                                }
                                else
                                {
                                    future.fail(result.cause());

                                    LOGGER.warn(String.format("Failed to update SNMP Device Catalog ID reference for %s oid group with cause %s", item, result.cause().getMessage()));
                                }
                            });
                }
                else
                {
                    future.complete();
                }
            }
        }

        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                promise.complete();
            }
            else
            {
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    private Future<Void> updateCatalog()
    {
        var voidPromise = Promise.<Void>promise();

        if (CommonUtil.traceEnabled())
        {
            LOGGER.info("Deleting all duplicate entry for SNMP device catalog");
        }

        var catalogs = SNMPDeviceCatalogConfigStore.getStore().getItemsByValue(FIELD_TYPE, ENTITY_TYPE_USER);

        Bootstrap.configDBService().drop(COLLECTION_SNMP_DEVICE_CATALOG, response ->
        {
            if (response.succeeded())
            {
                try
                {
                    LOGGER.info(String.format("Collection %s dropped", COLLECTION_SNMP_DEVICE_CATALOG));

                    SNMPDeviceCatalogConfigStore.getStore().deleteItems(SNMPDeviceCatalogConfigStore.getStore().getIds());

                    var items = new JsonArray(new CipherUtil().decrypt(FileUtils.readFileToString(new File(CURRENT_DIR + PATH_SEPARATOR + DB_DIR + PATH_SEPARATOR + "snmp-devices.db"), StandardCharsets.UTF_8)));

                    var promises = new ArrayList<Future<Void>>();

                    for (var index = 0; index < items.size(); index++)
                    {
                        var promise = Promise.<Void>promise();

                        promises.add(promise.future());

                        var item = items.getJsonObject(index);

                        item.put(ConfigDBConstants.FIELD_TYPE, item.getString(ConfigDBConstants.FIELD_TYPE, ConfigDBConstants.ENTITY_TYPE_SYSTEM));

                        Bootstrap.configDBService().upsert(COLLECTION_SNMP_DEVICE_CATALOG, (item.containsKey(ID) ? new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)) : null), item, DEFAULT_USER, MOTADATA_SYSTEM, asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                SNMPDeviceCatalogConfigStore.getStore().addItem(asyncResult.result().getLong(0)).onComplete(result ->
                                {
                                    if (result.succeeded())
                                    {
                                        promise.complete();
                                    }
                                    else
                                    {
                                        LOGGER.info(String.format("Failed to insert SNMPDeviceCatalog profile with reason %s", result.cause().getMessage()));

                                        promise.fail(result.cause());
                                    }
                                });
                            }
                            else
                            {
                                promise.fail(asyncResult.cause());

                                LOGGER.warn("SNMPDeviceCatalog profile insert failed with reason " + asyncResult.cause().getMessage());
                            }
                        });
                    }

                    for (var index = 0; index < catalogs.size(); index++)
                    {
                        var promise = Promise.<Void>promise();

                        promises.add(promise.future());

                        var item = catalogs.getJsonObject(index);

                        Bootstrap.configDBService().upsert(COLLECTION_SNMP_DEVICE_CATALOG, new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)), item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                LOGGER.info("User created snmp trap profile updated");

                                SNMPDeviceCatalogConfigStore.getStore().addItem(asyncResult.result().getLong(0)).onComplete(result ->
                                {
                                    if (result.succeeded())
                                    {
                                        promise.complete();
                                    }
                                    else
                                    {
                                        promise.fail(result.cause());
                                    }
                                });
                            }
                            else
                            {
                                promise.fail(asyncResult.cause());

                                LOGGER.warn("User created snmp trap profile update failed: " + asyncResult.cause().getMessage());
                            }
                        });
                    }

                    Future.join(promises).onComplete(result ->
                    {
                        if (result.succeeded())
                        {
                            voidPromise.complete();
                        }
                        else
                        {
                            voidPromise.fail(result.cause());
                        }
                    });
                }
                catch (Exception exception)
                {
                    voidPromise.fail(exception);

                    LOGGER.error(exception);
                }
            }
            else
            {
                voidPromise.fail(response.cause());

                LOGGER.warn("Failed to delete SNMP device catalog with reason: " + response.cause().getMessage());
            }
        });

        return voidPromise.future();
    }

    private Future<Void> updateTrap()
    {
        var voidPromise = Promise.<Void>promise();

        try
        {
            if (CommonUtil.traceEnabled())
            {
                LOGGER.info("Deleting all duplicate entry for SNMP trap profile");
            }

            var userCreatedTraps = SNMPTrapProfileConfigStore.getStore().getItemsByValue(FIELD_TYPE, ENTITY_TYPE_USER);

            Bootstrap.configDBService().drop(COLLECTION_SNMP_TRAP_PROFILE, response ->
            {
                if (response.succeeded())
                {
                    SNMPTrapProfileConfigStore.getStore().deleteItems(SNMPTrapProfileConfigStore.getStore().getIds());

                    var dir = new File(CURRENT_DIR + PATH_SEPARATOR + DB_DIR + PATH_SEPARATOR + "snmp-trap-profiles");

                    var promises = new ArrayList<Future<Void>>();

                    for (var file : dir.listFiles())
                    {
                        try
                        {
                            var traps = new JsonArray(new CipherUtil().decrypt(FileUtils.readFileToString(file, StandardCharsets.UTF_8)));

                            for (var index = 0; index < traps.size(); index++)
                            {
                                var promise = Promise.<Void>promise();

                                promises.add(promise.future());

                                var trap = traps.getJsonObject(index);

                                trap.put(ConfigDBConstants.FIELD_TYPE, trap.getString(ConfigDBConstants.FIELD_TYPE, ConfigDBConstants.ENTITY_TYPE_SYSTEM));

                                Bootstrap.configDBService().upsert(COLLECTION_SNMP_TRAP_PROFILE,
                                        (trap.containsKey(ID) ? new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, trap.getLong(ID)) : null),
                                        trap, DEFAULT_USER, MOTADATA_SYSTEM, asyncResult ->
                                        {
                                            if (asyncResult.succeeded())
                                            {
                                                SNMPTrapProfileConfigStore.getStore().addItem(asyncResult.result().getLong(0)).onComplete(result ->
                                                {
                                                    if (result.succeeded())
                                                    {
                                                        promise.complete();
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(String.format("Failed to add SNMP Trap profile %s with reason %s", asyncResult.result().getLong(0), result.cause()));

                                                        promise.fail(result.cause());
                                                    }
                                                });
                                            }
                                            else
                                            {
                                                promise.fail(asyncResult.cause());

                                                LOGGER.warn("Trap snmp trap profile update failed: " + asyncResult.cause().getMessage());
                                            }
                                        });
                            }

                            for (var index = 0; index < userCreatedTraps.size(); index++)
                            {
                                var promise = Promise.<Void>promise();

                                promises.add(promise.future());

                                var trap = userCreatedTraps.getJsonObject(index);

                                Bootstrap.configDBService().upsert(COLLECTION_SNMP_TRAP_PROFILE, new JsonObject().put(FIELD_NAME, ID).put(VALUE, trap.getLong(ID)), trap, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        SNMPTrapProfileConfigStore.getStore().addItem(asyncResult.result().getLong(0)).onComplete(result ->
                                        {
                                            if (result.succeeded())
                                            {
                                                LOGGER.info("User created snmp trap profile inserted");

                                                promise.complete();
                                            }
                                            else
                                            {
                                                LOGGER.warn(String.format("Failed to insert user created snmp trap profile with reason %s", result.cause().getMessage()));

                                                promise.fail(result.cause());
                                            }
                                        });
                                    }
                                    else
                                    {
                                        promise.fail(asyncResult.cause());

                                        LOGGER.warn("User created snmp trap profile update failed: " + asyncResult.cause().getMessage());
                                    }
                                });
                            }

                            Future.join(promises).onComplete(result ->
                            {
                                if (result.succeeded())
                                {
                                    voidPromise.complete();
                                }
                                else
                                {
                                    voidPromise.fail(result.cause());
                                }
                            });
                        }
                        catch (Exception exception)
                        {
                            voidPromise.fail(exception);

                            LOGGER.error(exception);
                        }
                    }
                }
                else
                {
                    voidPromise.fail(response.cause());

                    LOGGER.warn("Failed to delete SNMP trap profile with reason: " + response.cause().getMessage());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            voidPromise.fail(exception.getCause());
        }

        return voidPromise.future();
    }

    private Future<Void> populateSNMPDevices()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var catalogs = new JsonArray(new CipherUtil().decrypt(FileUtils.readFileToString(new File(CURRENT_DIR + PATH_SEPARATOR + DB_DIR + PATH_SEPARATOR + "snmp-devices.db"))));

            for (var index = 0; index < catalogs.size(); index++)
            {
                var catalog = catalogs.getJsonObject(index);

                lookups.put(catalog.getString(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_OID), catalog.getLong(ID));
            }

            promise.complete();
        }
        catch (Exception exception)
        {
            promise.fail(exception.getCause());

            LOGGER.error(exception);
        }

        return promise.future();
    }

    private Future<Void> updateObjectConfigStore()
    {
        var promise = Promise.<Void>promise();

        var futures = new ArrayList<Future<Void>>();

        var ids = ObjectConfigStore.getStore().getItemsByCategory(NMSConstants.Category.NETWORK);

        for (var index = 0; index < ids.size(); index++)
        {
            var future = Promise.<Void>promise();

            try
            {
                var item = ObjectConfigStore.getStore().getItem(ids.getLong(index));

                if (item.getJsonObject(OBJECT_CONTEXT) != null && item.getJsonObject(OBJECT_CONTEXT).containsKey(RETRY_COUNT))
                {
                    futures.add(future.future());

                    item.getJsonObject(OBJECT_CONTEXT).put(SNMP_CHECK_RETRIES, 2);

                    Bootstrap.configDBService().update(COLLECTION_OBJECT,
                            new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                            new JsonObject().put(OBJECT_CONTEXT, item.getJsonObject(OBJECT_CONTEXT)),
                            DEFAULT_USER,
                            SYSTEM_REMOTE_ADDRESS,
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    ObjectConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            LOGGER.trace(String.format("snmp.check.retries updated successfully in monitor %s", item));

                                            future.complete();
                                        }
                                        else
                                        {
                                            LOGGER.warn(String.format("Failed to update snmp.check.retries key in monitor %s with reason %s", item, asyncResult.cause()));

                                            future.fail(asyncResult.cause());
                                        }
                                    });
                                }
                                else
                                {
                                    LOGGER.warn(String.format("Failed to update snmp.check.retries key in monitor %s with reason %s", item, result.cause()));

                                    future.fail(result.cause());
                                }
                            });
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                future.fail(exception);
            }
        }

        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                promise.complete();
            }
            else
            {
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }
}
