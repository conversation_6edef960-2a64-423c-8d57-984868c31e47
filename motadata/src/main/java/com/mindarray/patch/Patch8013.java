/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.db.ConfigDBConstants.FIELD_NAME;

public class Patch8013 implements Patch
{
    private static final Logger LOGGER = new Logger(Patch8013.class, MOTADATA_PATCH, "Patch 8.0.13");

    private static final String VERSION = "8.0.13";

    @Override
    public String getVersion()
    {
        return VERSION;
    }

    @Override
    public Future<Void> doPatch()
    {
        var promise = Promise.<Void>promise();

        try
        {
            LOGGER.info("executing 8.0.13 patch");

            patch().onComplete(result ->
            {
                if (result.succeeded())
                {
                    TagCacheStore.getStore().initStore().onComplete(asyncResult ->
                    {

                        LOGGER.info("successfully executed 8.0.13 patch");

                        promise.complete();
                    });
                }
                else
                {
                    LOGGER.error(result.cause());

                    promise.fail(result.cause());
                }
            });

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    /*
     * The patch updates the Discovery Config Store, Object Config Store and Metric Config Store
     * "object.user.tags" and "object.system.tags" will be merged as "object.tags"
     * "instance.user.tags" renamed as "instance.tags"
     *
     * String tags are resolved with IDs of Tag Config Store
     * */
    private Future<Void> patch()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var futures = new ArrayList<Future<Void>>();

            //Refactor resolve tags in Discovery Config Store
            var items = DiscoveryConfigStore.getStore().getItems();

            if (items != null && !items.isEmpty())
            {
                LOGGER.info("executing patch for Discovery Config Store Items");

                for (var index = 0; index < items.size(); index++)
                {
                    try
                    {
                        var item = items.getJsonObject(index);

                        if (item.containsKey(Discovery.DISCOVERY_USER_TAGS) && item.getJsonArray(Discovery.DISCOVERY_USER_TAGS) != null && !item.getJsonArray(Discovery.DISCOVERY_USER_TAGS).isEmpty())
                        {
                            item.put(Discovery.DISCOVERY_USER_TAGS, TagConfigStore.getStore().addItems(item.getJsonArray(Discovery.DISCOVERY_USER_TAGS), Tag.TagType.OBJECT.getName(), ConfigDBConstants.ENTITY_TYPE_USER));

                            var future = Promise.<Void>promise();

                            futures.add(future.future());

                            Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_DISCOVERY, new JsonObject().put(FIELD_NAME, GlobalConstants.ID).put(VALUE, item.getLong(ID)),
                                    item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                                    {
                                        try
                                        {
                                            if (result.succeeded())
                                            {
                                                DiscoveryConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                                {
                                                    if (asyncResult.succeeded())
                                                    {
                                                        LOGGER.info(String.format("Successfully updated discovery %s", item.getString(Discovery.DISCOVERY_NAME, EMPTY_VALUE)));

                                                        future.complete();
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(String.format("Unable to update discovery store item %s with reason %s", item.encode(), asyncResult.cause().getMessage()));

                                                        future.fail(asyncResult.cause().getMessage());
                                                    }
                                                });
                                            }
                                            else
                                            {
                                                LOGGER.warn(String.format("Unable to update discovery config store item %s with reason %s", item.encode(), result.cause().getMessage()));

                                                future.fail(result.cause().getMessage());
                                            }
                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);

                                            future.fail(exception.getMessage());
                                        }
                                    });
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }
            }

            items = AgentConfigStore.getStore().getItems();

            if (items != null && !items.isEmpty())
            {
                LOGGER.info("executing patch for Agent Config Store Items");

                for (var index = 0; index < items.size(); index++)
                {
                    try
                    {
                        var item = items.getJsonObject(index);

                        if (item.containsKey(AIOpsObject.OBJECT_USER_TAGS) && item.getJsonArray(AIOpsObject.OBJECT_USER_TAGS) != null)
                        {
                            item.put(AIOpsObject.OBJECT_TAGS, TagConfigStore.getStore().addItems(item.getJsonArray(AIOpsObject.OBJECT_USER_TAGS), Tag.TagType.OBJECT.getName(), ConfigDBConstants.ENTITY_TYPE_USER));

                            var future = Promise.<Void>promise();

                            futures.add(future.future());

                            Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_AGENT, new JsonObject().put(FIELD_NAME, GlobalConstants.ID).put(VALUE, item.getLong(ID)),
                                    item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                                    {
                                        try
                                        {
                                            if (result.succeeded())
                                            {
                                                AgentConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                                {
                                                    if (asyncResult.succeeded())
                                                    {
                                                        LOGGER.info(String.format("Successfully updated agent %s", item.getString(Agent.AGENT_UUID, EMPTY_VALUE)));

                                                        future.complete();
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(String.format("Unable to update agent store item %s with reason %s", item.encode(), asyncResult.cause().getMessage()));

                                                        future.fail(asyncResult.cause().getMessage());
                                                    }
                                                });
                                            }
                                            else
                                            {
                                                LOGGER.warn(String.format("Unable to update agent config store item %s with reason %s", item.encode(), result.cause().getMessage()));

                                                future.fail(result.cause().getMessage());
                                            }
                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);

                                            future.fail(exception.getMessage());
                                        }
                                    });
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }
            }

            //Refactor User tags and System tags in Object Config Store
            items = ObjectConfigStore.getStore().getItems();

            if (items != null && !items.isEmpty())
            {
                LOGGER.info("executing patch for Object Config Store Items");

                for (var index = 0; index < items.size(); index++)
                {
                    try
                    {
                        var item = items.getJsonObject(index);

                        var tags = new JsonArray();

                        if (item.containsKey(AIOpsObject.OBJECT_USER_TAGS) && item.getJsonArray(AIOpsObject.OBJECT_USER_TAGS) != null && !item.getJsonArray(AIOpsObject.OBJECT_USER_TAGS).isEmpty())
                        {
                            tags = TagConfigStore.getStore().addItems(item.getJsonArray(AIOpsObject.OBJECT_USER_TAGS), Tag.TagType.OBJECT.getName(), ConfigDBConstants.ENTITY_TYPE_USER);
                        }

                        if (item.containsKey(AIOpsObject.OBJECT_SYSTEM_TAGS) && item.getJsonArray(AIOpsObject.OBJECT_SYSTEM_TAGS) != null && !item.getJsonArray(AIOpsObject.OBJECT_SYSTEM_TAGS).isEmpty())
                        {
                            var entities = TagConfigStore.getStore().addItems(item.getJsonArray(AIOpsObject.OBJECT_SYSTEM_TAGS), Tag.TagType.OBJECT.getName(), ConfigDBConstants.ENTITY_TYPE_SYSTEM);

                            for (var i = 0; i < entities.size(); i++)
                            {
                                if (!tags.contains(entities.getLong(i)))
                                {
                                    tags.add(entities.getLong(i));
                                }
                            }
                        }

                        item.put(AIOpsObject.OBJECT_TAGS, item.containsKey(AIOpsObject.OBJECT_TAGS) && item.getJsonArray(AIOpsObject.OBJECT_TAGS) != null ? tags.addAll(item.getJsonArray(AIOpsObject.OBJECT_TAGS)) : tags);

                        var future = Promise.<Void>promise();

                        futures.add(future.future());

                        Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_OBJECT, new JsonObject().put(FIELD_NAME, GlobalConstants.ID).put(VALUE, item.getLong(ID)),
                                item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                                {
                                    try
                                    {
                                        if (result.succeeded())
                                        {
                                            ObjectConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                            {
                                                if (asyncResult.succeeded())
                                                {
                                                    LOGGER.info(String.format("Successfully updated object %s", item.getString(AIOpsObject.OBJECT_NAME, EMPTY_VALUE)));

                                                    future.complete();
                                                }
                                                else
                                                {
                                                    LOGGER.warn(String.format("Unable to update object store item %s with reason %s", item.encode(), asyncResult.cause().getMessage()));

                                                    future.fail(asyncResult.cause().getMessage());
                                                }
                                            });
                                        }
                                        else
                                        {
                                            LOGGER.warn(String.format("Unable to update object config store item %s with reason %s", item.encode(), result.cause().getMessage()));

                                            future.fail(result.cause().getMessage());
                                        }
                                    }
                                    catch (Exception exception)
                                    {
                                        LOGGER.error(exception);

                                        future.fail(exception.getMessage());
                                    }
                                });
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }
            }

            //Refactor Instance tags in Metric Config Store
            items = MetricConfigStore.getStore().getItems();

            if (items != null && !items.isEmpty())
            {
                LOGGER.info("executing patch for Metric Config Store Items");

                for (var index = 0; index < items.size(); index++)
                {
                    try
                    {
                        var item = items.getJsonObject(index);

                        var context = item.getJsonObject(Metric.METRIC_CONTEXT);

                        if (context.containsKey(NMSConstants.OBJECTS))
                        {
                            var objects = context.getJsonArray(NMSConstants.OBJECTS);

                            if (objects != null && !objects.isEmpty())
                            {
                                var update = false;

                                for (var i = 0; i < objects.size(); i++)
                                {
                                    var object = objects.getJsonObject(i);

                                    if (object.containsKey("instance.user.tag"))
                                    {
                                        object.put(INSTANCE_TAGS, TagConfigStore.getStore().addItems(object.getJsonArray("instance.user.tag"), Tag.TagType.INSTANCE.getName(), ConfigDBConstants.ENTITY_TYPE_USER));

                                        object.remove("instance.user.tag");

                                        update = true;
                                    }
                                }

                                if (update)
                                {
                                    var future = Promise.<Void>promise();

                                    futures.add(future.future());

                                    Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_METRIC, new JsonObject().put(FIELD_NAME, GlobalConstants.ID).put(VALUE, item.getLong(ID)),
                                            item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                                            {
                                                try
                                                {
                                                    if (result.succeeded())
                                                    {
                                                        MetricConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                                        {
                                                            if (asyncResult.succeeded())
                                                            {
                                                                LOGGER.info(String.format("Successfully updated config metric %s", item.getString(Metric.METRIC_NAME, EMPTY_VALUE)));

                                                                future.complete();
                                                            }
                                                            else
                                                            {
                                                                LOGGER.warn(String.format("Unable to update metric config store item %s with reason %s", item.encode(), asyncResult.cause().getMessage()));

                                                                future.fail(asyncResult.cause().getMessage());
                                                            }
                                                        });
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(String.format("Unable to update metric config store item %s with reason %s", item.encode(), result.cause().getMessage()));

                                                        future.fail(result.cause().getMessage());
                                                    }
                                                }
                                                catch (Exception exception)
                                                {
                                                    LOGGER.error(exception);

                                                    future.fail(exception.getMessage());
                                                }
                                            });
                                }
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }
            }

            //Adding metric plugin key in Metric Plugin Config Store
            items = MetricPluginConfigStore.getStore().getItems();

            if (items != null && !items.isEmpty())
            {
                LOGGER.info("executing patch for Metric Plugin Config Store Items");

                for (var index = 0; index < items.size(); index++)
                {
                    try
                    {
                        var item = items.getJsonObject(index);

                        var context = item.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT);

                        if (item.containsKey(MetricPlugin.METRIC_PLUGIN_PROTOCOL))
                        {
                            context.put(Metric.METRIC_PLUGIN, item.getString(MetricPlugin.METRIC_PLUGIN_PROTOCOL));

                            item.put(MetricPlugin.METRIC_PLUGIN_CONTEXT, context);

                            var future = Promise.<Void>promise();

                            futures.add(future.future());

                            Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_METRIC_PLUGIN, new JsonObject().put(FIELD_NAME, GlobalConstants.ID).put(VALUE, item.getLong(ID)),
                                    item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                                    {
                                        try
                                        {
                                            if (result.succeeded())
                                            {
                                                MetricPluginConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                                {
                                                    if (asyncResult.succeeded())
                                                    {
                                                        LOGGER.info(String.format("Successfully updated metric plugin config item having plugin name %s", item.getString(MetricPlugin.METRIC_PLUGIN_NAME, EMPTY_VALUE)));

                                                        future.complete();
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(String.format("Unable to update metric plugin config item %s with reason %s", item.encode(), asyncResult.cause().getMessage()));

                                                        future.fail(asyncResult.cause().getMessage());
                                                    }
                                                });
                                            }
                                            else
                                            {
                                                LOGGER.warn(String.format("Unable to update metric plugin config store item %s with reason %s", item.encode(), result.cause().getMessage()));

                                                future.fail(result.cause().getMessage());
                                            }
                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);

                                            future.fail(exception.getMessage());
                                        }
                                    });
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }
            }

            Future.join(futures).onComplete(result -> promise.complete());
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }
}
