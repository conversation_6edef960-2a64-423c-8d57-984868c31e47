/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Metric;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.TagCacheStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.db.ConfigDBConstants.FIELD_NAME;

public class Patch809 implements Patch
{
    private static final Logger LOGGER = new Logger(Patch809.class, MOTADATA_PATCH, "Patch 8.0.9");

    private static final String VERSION = "8.0.9";

    @Override
    public String getVersion()
    {
        return VERSION;
    }

    @Override
    public Future<Void> doPatch()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var futures = new ArrayList<Future<Void>>();

            toLowerCase(futures);

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    TagCacheStore.getStore().initStore().onComplete(asyncResult -> promise.complete());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    /*
      It will convert Object(User Level Tags and System Level Tags) as well as Instance tag into lowercase and put it back to map as JsonArray for Collection to update in config store.
     */
    private void toLowerCase(ArrayList<Future<Void>> futures)
    {
        var items = ObjectConfigStore.getStore().getItems();

        if (items != null && !items.isEmpty())
        {
            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                var valid = false;

                /*
                    it will update user level tags to lowercase
                 */
                if (item.containsKey(AIOpsObject.OBJECT_USER_TAGS) && item.getJsonArray(AIOpsObject.OBJECT_USER_TAGS) != null && !item.getJsonArray(AIOpsObject.OBJECT_USER_TAGS).isEmpty())
                {
                    item.put(AIOpsObject.OBJECT_USER_TAGS, item.getJsonArray(AIOpsObject.OBJECT_USER_TAGS)
                            .stream()
                            .map(tag -> CommonUtil.getString(tag).toLowerCase())
                            .collect(JsonArray::new, JsonArray::add, JsonArray::add));

                    valid = true;
                }

                /*
                    it will update system level tags to lowercase
                 */
                if (item.containsKey(AIOpsObject.OBJECT_SYSTEM_TAGS) && item.getJsonArray(AIOpsObject.OBJECT_SYSTEM_TAGS) != null && !item.getJsonArray(AIOpsObject.OBJECT_SYSTEM_TAGS).isEmpty())
                {
                    item.put(AIOpsObject.OBJECT_SYSTEM_TAGS, item.getJsonArray(AIOpsObject.OBJECT_SYSTEM_TAGS)
                            .stream()
                            .map(tag -> CommonUtil.getString(tag).toLowerCase())
                            .collect(JsonArray::new, JsonArray::add, JsonArray::add));

                    valid = true;
                }

                if (valid)
                {
                    var future = Promise.<Void>promise();

                    futures.add(future.future());

                    Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_OBJECT, new JsonObject().put(FIELD_NAME, GlobalConstants.ID).put(VALUE, item.getLong(ID)),
                            item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                            {
                                if (result.succeeded())
                                {
                                    ObjectConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            LOGGER.info(String.format("Successfully updated object %s", item.getString(AIOpsObject.OBJECT_NAME, EMPTY_VALUE)));

                                            future.complete();
                                        }
                                        else
                                        {
                                            LOGGER.warn(String.format("Unable to update object store item %s with reason %s", item.encode(), asyncResult.cause().getMessage()));

                                            future.fail(asyncResult.cause().getMessage());
                                        }
                                    });
                                }
                                else
                                {
                                    LOGGER.warn(String.format("Unable to update object config store item %s with reason %s", item.encode(), result.cause().getMessage()));

                                    future.fail(result.cause().getMessage());
                                }
                            });
                }
            }
        }

        items = MetricConfigStore.getStore().getItems();

        if (items != null && !items.isEmpty())
        {
            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                var valid = false;

                var context = item.getJsonObject(Metric.METRIC_CONTEXT);

                if (context.containsKey(NMSConstants.OBJECTS))
                {
                    var objects = context.getJsonArray(NMSConstants.OBJECTS);

                    if (objects != null && !objects.isEmpty())
                    {
                        for (var i = 0; i < objects.size(); i++)
                        {
                            var object = objects.getJsonObject(i);

                            if (object.containsKey("instance.user.tag"))
                            {
                                object.put("instance.user.tag", object.getJsonArray("instance.user.tag")
                                        .stream()
                                        .map(tag -> CommonUtil.getString(tag).toLowerCase())
                                        .collect(JsonArray::new, JsonArray::add, JsonArray::add));

                                valid = true;
                            }
                        }

                    }

                }

                if (valid)
                {
                    var future = Promise.<Void>promise();

                    futures.add(future.future());

                    Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_METRIC, new JsonObject().put(FIELD_NAME, GlobalConstants.ID).put(VALUE, item.getLong(ID)),
                            item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                            {
                                if (result.succeeded())
                                {
                                    MetricConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            LOGGER.info(String.format("Successfully updated config metric %s", item.getString(Metric.METRIC_NAME, EMPTY_VALUE)));

                                            future.complete();
                                        }
                                        else
                                        {
                                            LOGGER.warn(String.format("Unable to update metric config store item %s with reason %s", item.encode(), asyncResult.cause().getMessage()));

                                            future.fail(asyncResult.cause().getMessage());
                                        }
                                    });
                                }
                                else
                                {
                                    LOGGER.warn(String.format("Unable to update metric config store item %s with reason %s", item.encode(), result.cause().getMessage()));

                                    future.fail(result.cause().getMessage());
                                }
                            });
                }
            }
        }
    }
}
