/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */
package com.mindarray.report;

import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Report;
import com.mindarray.api.Scheduler;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.notification.Notification;
import com.mindarray.store.ReportCacheStore;
import com.mindarray.store.ReportConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.text.StringSubstitutor;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.REPORT_NOTIFICATION_SUBJECT;
import static com.mindarray.api.APIConstants.AUTH_ACCESS_TOKEN;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.Report.REPORT_NAME;
import static com.mindarray.api.Scheduler.SCHEDULER_EMAIL_RECIPIENTS;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;

public class ReportingEngine extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(ReportingEngine.class, MOTADATA_REPORTING, "Reporting Engine");

    private static final int TIMEOUT_SECONDS = MotadataConfigUtil.getReportEngineTimeoutSeconds();

    private static final String BROWSER_PATH = MotadataConfigUtil.getBrowserBinPath();

    private static final int SOCKET_TIMEOUT_MILLIS = MotadataConfigUtil.getReportSocketTimeoutSeconds() * 1000;

    private static final SimpleDateFormat LOG_DATE_FORMAT = new SimpleDateFormat(GlobalConstants.LOGGER_DATE_FORMAT);

    private final Map<Long, Long> tickers = new ConcurrentHashMap<>();

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        vertx.eventBus().localConsumer(config().getString(EVENT_TYPE), this::process).exceptionHandler(LOGGER::error);

        vertx.eventBus().localConsumer(EVENT_REPORT_EXPORT, this::process).exceptionHandler(LOGGER::error);

        LOGGER.debug(config().getString(EventBusConstants.EVENT_TYPE) + " started successfully!!!");

        // clean up job
        vertx.setPeriodic(MotadataConfigUtil.devMode() ? 5000 : 30000, timer ->
        {
            try
            {
                var iterator = tickers.entrySet().iterator();

                while (iterator.hasNext())
                {
                    var item = iterator.next();

                    if (item.getValue() + (MotadataConfigUtil.devMode() ? 60 : 600) <= DateTimeUtil.currentSeconds()) // 10 minutes
                    {
                        iterator.remove();

                        ReportCacheStore.getStore().removeItem(item.getKey());

                        LOGGER.warn(String.format("%s is cleaned up due to timeout", item.getKey()));
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        promise.complete();
    }

    /**
     * MOTADATA-1454, date format is shown as per default admin user and not by format selected by specific user.
     * If report request is received from UI, fetch the user details from id and passed for headless authentication
     * -- Changed method to update report progress on UI. Before, It is not showing 40%, 50% and directly getting exporting..
     *
     * @param message Message
     */
    private void process(Message<JsonObject> message)
    {
        try
        {
            var event = message.body();

            var item = ReportConfigStore.getStore().getItem(event.getLong(ID));

            if (item != null)
            {
                try
                {
                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug(String.format("%s> %s report generation request received", item.getLong(ID), item.getString(REPORT_NAME)));
                    }

                    ReportCacheStore.getStore().updateItem(item.getLong(ID), new JsonObject().put(PROGRESS, 10).put(MESSAGE, "Request queued"));

                    tickers.put(item.getLong(ID), DateTimeUtil.currentSeconds());

                    var userId = item.getLong(Report.REPORT_USER);

                    if (EVENT_REPORT_EXPORT.equalsIgnoreCase(event.getString(EVENT_TYPE)))
                    {
                        userId = UserConfigStore.getStore().getItemByValue(USER_NAME, event.getString(USER_NAME)).getLong(ID);
                    }

                    LOGGER.debug(String.format("%s>Exporting report for : %s", item.getLong(ID), userId));

                    vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_AUTH_TOKEN_CREATE, new JsonObject().put(ID, userId))
                            .onComplete(reply ->
                            {
                                if (reply.succeeded())
                                {
                                    String fileName;

                                    if (event.containsKey(Report.REPORT_FORMAT))
                                    {
                                        item.put(Report.REPORT_FORMAT, event.getString(Report.REPORT_FORMAT));
                                    }

                                    if (item.containsKey(Report.REPORT_FORMAT) && !item.getString(Report.REPORT_FORMAT).equalsIgnoreCase("pdf"))
                                    {
                                        fileName = item.getString(REPORT_NAME).replaceAll("-", "").replaceAll(" +", "_") + "_" + DateTimeUtil.currentSeconds() + ".xlsx";
                                    }
                                    else
                                    {
                                        fileName = item.getString(REPORT_NAME).replaceAll("-", "").replaceAll(" +", "_") + "_" + DateTimeUtil.currentSeconds() + ".pdf";
                                    }

                                    event.put(EVENT_ID, CommonUtil.newEventId());

                                    vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                                            .put(EventBusConstants.EVENT_ID, event.getLong(EVENT_ID))
                                            .put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER)
                                            .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_ADD)
                                            .put(EventBusConstants.EVENT_TYPE, EVENT_REPORT));

                                    vertx.<Integer>executeBlocking(future ->
                                    {
                                        var result = reply.result().body();

                                        try
                                        {
                                            ReportCacheStore.getStore().updateItem(item.getLong(ID), new JsonObject().put(PROGRESS, 20).put(MESSAGE, "Auth token generated"));

                                            FileUtils.deleteQuietly(new File(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + REPORTS + PATH_SEPARATOR + fileName));    // delete file if exists...

                                            var arguments = new ArrayList<String>();

                                            var exitCode = DUMMY_NUMERIC_VALUE;

                                            arguments.add(CURRENT_DIR + PATH_SEPARATOR + REPORT_ENGINE_BIN);

                                            arguments.add("--credentials=" + new JsonObject().put("auth.token", new JsonObject()
                                                    .put("access_token", result.getString(AUTH_ACCESS_TOKEN))
                                                    .put("session_id", result.getString(SESSION_ID))).encode());

                                            arguments.add("--url=https://" + MotadataConfigUtil.getHTTPServerHost() + ":" + MotadataConfigUtil.getHTTPServerPort(BootstrapType.APP.name()) + "/reports/export/" + event.getLong(ID));

                                            arguments.add("--filename=" + fileName);

                                            if (item.containsKey(Report.REPORT_FORMAT) && !item.getString(Report.REPORT_FORMAT).equalsIgnoreCase("pdf"))
                                            {
                                                arguments.add("--xlsx");      // for xlsx format & file name should end with xlsx
                                            }

                                            arguments.add("--output=" + CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + REPORTS);

                                            arguments.add("--browser=" + BROWSER_PATH);

                                            arguments.add("--http-port=" + PortUtil.getAvailablePort());

                                            arguments.add("--socket-connection-timeout=" + SOCKET_TIMEOUT_MILLIS);

                                            arguments.add("--socket-event-timeout=" + SOCKET_TIMEOUT_MILLIS);

                                            arguments.add("--log-path=" + CURRENT_DIR + PATH_SEPARATOR + LOGS_DIR + PATH_SEPARATOR + MOTADATA_REPORTING);

                                            arguments.add("--template-path=" + CURRENT_DIR + PATH_SEPARATOR + "templates" + PATH_SEPARATOR + REPORTS);

                                            arguments.add("--log-prefix=" + LOG_DATE_FORMAT.format(new Date()) + "-Reporting Engine-" + item.getLong(ID));

//                                            arguments.add("--open-browser"); // for debug purpose ONLY

                                            var processBuilder = new ProcessBuilder(arguments);

                                            ReportCacheStore.getStore().updateItem(item.getLong(ID), new JsonObject().put(PROGRESS, 30).put(MESSAGE, "Exporting report"));

                                            if (CommonUtil.debugEnabled())
                                            {
                                                LOGGER.debug(String.format("%s> starting report-engine", item.getLong(ID)));
                                            }

                                            var process = processBuilder.start();

                                            WorkerUtil.setupCleanupTimer(TIMEOUT_SECONDS, process, processBuilder, event.getLong(EVENT_ID), null, null);

                                            try (var reader = new BufferedReader(new InputStreamReader(process.getInputStream())))
                                            {
                                                String line;

                                                while ((line = reader.readLine()) != null)
                                                {
                                                    try
                                                    {
                                                        for (var value : line.trim().split("\n"))
                                                        {
                                                            ReportCacheStore.getStore().updateItem(item.getLong(ID), new JsonObject(new String(Base64.getDecoder().decode(value.trim()))));
                                                        }
                                                    }
                                                    catch (Exception exception)
                                                    {
                                                        LOGGER.error(new Exception(exception.getMessage() + ": " + line));

                                                        exitCode = NOT_AVAILABLE;
                                                    }
                                                }
                                            }

                                            if (!process.isAlive())
                                            {
                                                exitCode = process.exitValue();
                                            }

                                            if (CommonUtil.debugEnabled())
                                            {
                                                LOGGER.debug(String.format("%s> report engine with PID %s exited with status code %s", item.getLong(ID), process.pid(), exitCode));
                                            }

                                            future.complete(exitCode);
                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);

                                            vertx.eventBus().send(EVENT_FAIL, new JsonObject().put(EVENT_ID, event.getLong(EVENT_ID))
                                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                    .put(MESSAGE, String.format(ErrorMessageConstants.REPORT_GENERATION_FAILED, item.getString(REPORT_NAME), exception.getMessage()))
                                                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));

                                            future.fail(exception);
                                        }
                                    }).onComplete(result ->
                                    {
                                        if (result.succeeded() && result.result() == 0 && vertx.fileSystem().existsBlocking(GlobalConstants.UPLOADS + PATH_SEPARATOR + REPORTS + PATH_SEPARATOR + fileName))
                                        {
                                            // post actions
                                            if (EVENT_REPORT_EXPORT.equalsIgnoreCase(event.getString(EVENT_TYPE))) // if report export from UI
                                            {
                                                EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), EventBusConstants.UI_NOTIFICATION_CSV_EXPORT_READY, event.put(GlobalConstants.FILE_NAME, URLEncoder.encode(REPORTS + PATH_SEPARATOR + fileName, StandardCharsets.UTF_8)).put(STATUS, STATUS_SUCCEED));

                                                if (CommonUtil.debugEnabled())
                                                {
                                                    LOGGER.debug(String.format("%s> exporting...", item.getLong(ID)));
                                                }
                                            }
                                            else if (EVENT_REPORT.equalsIgnoreCase(event.getString(EVENT_TYPE)))
                                            {
                                                var context = event.getJsonObject(EVENT_CONTEXT);

                                                if (context != null)
                                                {
                                                    Notification.sendEmail(new JsonObject().put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline")
                                                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(REPORTS + PATH_SEPARATOR + fileName).add("information.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                                            .put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format(REPORT_NOTIFICATION_SUBJECT, context.getString("subject")))
                                                            .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, context.getJsonArray("to"))
                                                            .put(Notification.EMAIL_NOTIFICATION_SENDER, context.getString("from"))
                                                            .put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject().put(TIME_STAMP, DateTimeUtil.timestamp()).put(REPORT_NAME, item.getString(REPORT_NAME)).put(MESSAGE, context.getString(MESSAGE).replaceAll("\n", "<br>")).getMap())
                                                                    .replace(Notification.EMAIL_NOTIFICATION_REPORT_HTML_TEMPLATE).getBytes(StandardCharsets.UTF_8)));

                                                    if (CommonUtil.debugEnabled())
                                                    {

                                                        LOGGER.debug(String.format("%s> emailing...", item.getLong(ID)));
                                                    }
                                                }
                                            }
                                            else if (event.containsKey(Scheduler.SCHEDULER_CONTEXT))
                                            {
                                                var context = event.getJsonObject(Scheduler.SCHEDULER_CONTEXT);

                                                if (context.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS) != null && !context.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS).isEmpty())
                                                {
                                                    Notification.sendEmail(new JsonObject().put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline")
                                                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(REPORTS + PATH_SEPARATOR + fileName).add("information.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                                            .put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject().put(TIME_STAMP, DateTimeUtil.timestamp()).put(REPORT_NAME, item.getString(REPORT_NAME)).put(MESSAGE, "Hello,<br>Kindly find attached Reports.<br>Thank you").getMap())
                                                                    .replace(Notification.EMAIL_NOTIFICATION_REPORT_HTML_TEMPLATE).getBytes(StandardCharsets.UTF_8))
                                                            .put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format(REPORT_NOTIFICATION_SUBJECT, item.getString(REPORT_NAME)))
                                                            .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, context.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS)));

                                                    if (CommonUtil.debugEnabled())
                                                    {
                                                        LOGGER.debug(String.format("%s> emailing...", item.getLong(ID)));
                                                    }
                                                }
                                            }

                                            vertx.eventBus().send(EventBusConstants.EVENT_SUCCEED, new JsonObject().put(EventBusConstants.EVENT_ID, event.getLong(EVENT_ID)));
                                        }
                                        else
                                        {
                                            LOGGER.warn(String.format(ErrorMessageConstants.REPORT_GENERATION_FAILED, item.getLong(ID), result.failed() ? result.cause().getMessage() : "file not found"));
                                        }

                                        ReportCacheStore.getStore().complete(item.getLong(ID));

                                        this.tickers.remove(item.getLong(ID));
                                    });
                                }
                                else
                                {
                                    LOGGER.warn(String.format(ErrorMessageConstants.REPORT_GENERATION_FAILED, item.getLong(ID), reply.cause().getMessage()));

                                    ReportCacheStore.getStore().complete(item.getLong(ID));   // clear memory

                                    this.tickers.remove(item.getLong(ID));
                                }
                            });
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    ReportCacheStore.getStore().complete(item.getLong(ID));   // clear memory

                    this.tickers.remove(item.getLong(ID));
                }
            }
            else
            {
                LOGGER.warn(String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, event.encode()));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

}
