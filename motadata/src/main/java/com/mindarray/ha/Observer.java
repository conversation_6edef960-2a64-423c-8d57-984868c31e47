
/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	25-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
*/

package com.mindarray.ha;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.datastore.ConfigDatastore;
import com.mindarray.datastore.ObserverDatastore;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.nms.NMSConstants;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID;
import static com.mindarray.compliance.ComplianceConstants.COMPLIANCE_DB;
import static com.mindarray.db.ConfigDBConstants.DB_QUERY;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.ha.HAConstants.*;

public class Observer extends AbstractVerticle

{
    private static final Logger LOGGER = new Logger(Observer.class, MOTADATA_HA, "Observer");

    private final Map<String, String> items = new HashMap<>(); //items by uuid store last read file

    private final JsonObject durations = new JsonObject();

    private EventEngine eventEngine;

    private String replyTopic;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            replyTopic = EventBusConstants.EVENT_HA_CONFIG_OBSERVER_SYNC + EventBusConstants.EVENT_REPLY;

            if (Bootstrap.bootstrapType().name().equalsIgnoreCase(BootstrapType.OBSERVER.name()))
            {
                try
                {
                    load();

                    vertx.eventBus().<JsonObject>localConsumer(EVENT_REGISTRATION, message ->
                    {
                        LOGGER.info(String.format("event %s ", message.body().encode()));

                        var uuid = message.body().getString(REMOTE_EVENT_PROCESSOR_UUID);

                        durations.put(uuid, new JsonObject().put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()).put(DURATION, DateTimeUtil.currentSeconds()));

                        if (!items.containsKey(uuid) && !items.containsValue(message.body().getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE)))
                        {
                            Bootstrap.startEngine(new ObserverDatastore(uuid), ObserverDatastore.class.getSimpleName() + " " + uuid, null)
                                    .onComplete(result ->
                                    {
                                        if (result.succeeded())
                                        {
                                            items.put(uuid, message.body().getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE));

                                            LOGGER.info(String.format("%s app registration event completed...", uuid));

                                            Bootstrap.vertx().fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "bookmarks.json", Buffer.buffer(new JsonObject().put("items", items).encodePrettily().getBytes()));
                                        }
                                        else
                                        {
                                            LOGGER.error(result.cause());
                                        }
                                    });
                        }
                        else
                        {
                            LOGGER.info(String.format("entry already exist : %s ", message.body().encode()));
                        }

                    }).exceptionHandler(LOGGER::error);

                    vertx.eventBus().<JsonObject>localConsumer(EVENT_HA_CONFIG_OBSERVER_SYNC, message ->
                    {
                        var event = message.body();

                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("request received for uuid %s operation type : %s ", event.getString(REMOTE_EVENT_PROCESSOR_UUID), event.getValue(HA_SYNC_OPERATION)));
                        }

                        switch (HAConstants.HASyncOperation.valueOfName(CommonUtil.getByteValue(event.getValue(HA_SYNC_OPERATION))))
                        {
                            case SAVE -> items.keySet().forEach(entry ->
                            {
                                if (!entry.equalsIgnoreCase(event.getString(REMOTE_EVENT_PROCESSOR_UUID)))
                                {
                                    if (MotadataConfigUtil.devMode())
                                    {
                                        log(event);
                                    }

                                    vertx.eventBus().send(EVENT_HA_CONFIG_MANGER_SYNC + DOT_SEPARATOR + entry, event);
                                }
                                else
                                {
                                    durations.put(event.getString(REMOTE_EVENT_PROCESSOR_UUID), durations.getJsonObject(event.getString(REMOTE_EVENT_PROCESSOR_UUID)).put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()));
                                }
                            });

                            case READ ->
                            {
                                if (items.containsKey(event.getString(REMOTE_EVENT_PROCESSOR_UUID)))
                                {
                                    vertx.eventBus().send(EVENT_HA_CONFIG_MANGER_SYNC + DOT_SEPARATOR + event.getString(REMOTE_EVENT_PROCESSOR_UUID) + ".active", event.getString(REMOTE_EVENT_PROCESSOR_UUID));

                                    // update duration
                                    durations.put(event.getString(REMOTE_EVENT_PROCESSOR_UUID), durations.getJsonObject(event.getString(REMOTE_EVENT_PROCESSOR_UUID)).put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()));
                                }
                                else
                                {
                                    vertx.eventBus().send(EVENT_PUBLICATION_MOTADATA_OBSERVER, new JsonObject()
                                            .put(EVENT_TOPIC, REMOTE_EVENT_PROCESSOR_TOPIC)
                                            .put(EVENT_COPY_REQUIRED, false)
                                            .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID))
                                            .put(EVENT_TYPE, EVENT_HA_ACKNOWLEDGEMENT)
                                            .put(HA_SYNC_TYPE, HASyncType.CONFIG.getName()));
                                }
                            }

                            default ->
                            {
                                // do nothing
                            }
                        }
                    });

                    vertx.eventBus().<JsonObject>localConsumer(EVENT_HA_CACHE_OBSERVER_SYNC, message ->
                    {
                        var event = message.body();

                        switch (HAConstants.HASyncOperation.valueOfName(CommonUtil.getByteValue(event.getValue(HA_SYNC_OPERATION))))
                        {
                            case WRITE -> items.keySet().forEach(entry ->
                            {
                                if (!entry.equalsIgnoreCase(event.getString(REMOTE_EVENT_PROCESSOR_UUID)))
                                {
                                    if (CommonUtil.debugEnabled())
                                    {
                                        LOGGER.debug(String.format("writing cache %s for uuid %s ", event.getString(CACHE_NAME), entry));
                                    }

                                    vertx.eventBus().send(EVENT_HA_CACHE_MANAGER_SYNC + DOT_SEPARATOR + entry, event);
                                }
                                else
                                {
                                    durations.put(event.getString(REMOTE_EVENT_PROCESSOR_UUID), durations.getJsonObject(event.getString(REMOTE_EVENT_PROCESSOR_UUID)).put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()));
                                }
                            });

                            case READ ->
                            {
                                vertx.eventBus().send(EVENT_HA_CACHE_MANAGER_SYNC + DOT_SEPARATOR + event.getString(REMOTE_EVENT_PROCESSOR_UUID), event);

                                // update duration
                                durations.put(event.getString(REMOTE_EVENT_PROCESSOR_UUID), durations.getJsonObject(event.getString(REMOTE_EVENT_PROCESSOR_UUID)).put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()));
                            }

                            default ->
                            {
                                // do nothing
                            }
                        }
                    });

                    var ipAddress = CommonUtil.getIPAddresses().getFirst();

                    var hostName = CommonUtil.getHostName();

                    vertx.setPeriodic(30 * 1000L, timer ->
                    {
                        try
                        {
                            for (var entry : items.entrySet())
                            {
                                var duration = durations.getJsonObject(entry.getKey());

                                // if diff is greater than 60, update duration for first time to calculate down duration. for secondary,failover APP
                                if (duration != null && DateTimeUtil.currentSeconds() - duration.getLong(EVENT_TIMESTAMP, DUMMY_ID) > 60L && (!duration.containsKey(STATUS) || !duration.getString(STATUS).equalsIgnoreCase(NMSConstants.STATE_NOT_RUNNING)))
                                {
                                    durations.put(entry.getKey(), duration.put(STATUS, NMSConstants.STATE_NOT_RUNNING).put(DURATION, DateTimeUtil.currentSeconds()));
                                }
                            }

                            // publish to APP(s)

                            for (var entry : items.entrySet())
                            {
                                vertx.eventBus().send(EVENT_PUBLICATION_MOTADATA_OBSERVER, new JsonObject()
                                        .put(EVENT_TOPIC, REMOTE_EVENT_PROCESSOR_TOPIC)
                                        .put(EVENT_COPY_REQUIRED, false)
                                        .put(DURATION, durations)
                                        .put(REMOTE_EVENT_PROCESSOR_UUID, entry.getKey())
                                        .put(AIOpsObject.OBJECT_IP, ipAddress)
                                        .put(AIOpsObject.OBJECT_HOST, hostName)
                                        .put(EVENT_TYPE, EVENT_OBSERVER_HEARTBEAT));

                                LOGGER.info(String.format("heartbeat send to app : %s ", entry.getKey()));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }

                        Bootstrap.vertx().executeBlocking(future ->
                        {
                            try
                            {
                                if (!vertx.fileSystem().existsBlocking(OBSERVER_CACHE))
                                {
                                    vertx.fileSystem().createFileBlocking(OBSERVER_CACHE);
                                }

                                if (!durations.isEmpty())
                                {
                                    var bytes = CodecUtil.compress(durations.encode());

                                    if (bytes != null && bytes.length > 0)
                                    {
                                        vertx.fileSystem().writeFileBlocking(OBSERVER_CACHE, Buffer.buffer(bytes));

                                        LOGGER.info(String.format("observer cache has been written with bytes size : %s ", bytes.length));
                                    }
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }

                            future.complete();

                        });
                    });
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

                promise.complete();
            }
            else
            {
                eventEngine = new EventEngine().setEventType(EVENT_HA_CONFIG_OBSERVER_SYNC)
                        .setPersistEventOffset(true).setBlockingEvent(true).setEventQueueSize(1).setEventHandler(this::decodeEvent).setLogger(LOGGER).start(vertx, promise);

                //receive response from observer server to decode cache event and update cache file
                vertx.eventBus().<JsonObject>localConsumer(EVENT_HA_CACHE_OBSERVER_SYNC, message -> updateCache(message.body()));

                if (Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.PRIMARY.name()))
                {
                    //primary app server write config event

                    Bootstrap.startEngine(new ConfigDatastore(Bootstrap.getRegistrationId()), ConfigDatastore.class.getSimpleName(), null)
                            .onComplete(result ->
                            {
                                if (result.succeeded())
                                {
                                    // initial read request to observer server for latest config and cache event.

                                    vertx.setPeriodic(MotadataConfigUtil.getConfigDBPollTimerSeconds(), timer ->
                                    {
                                        if (MotadataConfigUtil.getEnvironmentType().equalsIgnoreCase(ENV_TEST) || PortUtil.isConnected(MotadataConfigUtil.getRemoteEventObserverPublisher(), MotadataConfigUtil.getMotadataObserverEventPublisherPort(), 3))
                                        {
                                            vertx.eventBus().send(EVENT_HA_OBSERVER,
                                                    new JsonObject().put(EVENT_TYPE, EVENT_HA_CONFIG_OBSERVER_SYNC)
                                                            .put(HA_SYNC_OPERATION, HAConstants.HASyncOperation.READ.getName())
                                                            .put(INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                                            .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()));

                                            vertx.eventBus().send(EVENT_HA_OBSERVER,
                                                    new JsonObject().put(EVENT_TYPE, EVENT_HA_CACHE_OBSERVER_SYNC)
                                                            .put(HA_SYNC_OPERATION, HAConstants.HASyncOperation.READ.getName())
                                                            .put(INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                                            .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()));

                                            LOGGER.info(String.format("%s app server sent event request to observer", InstallationMode.PRIMARY.name()));
                                        }
                                        else
                                        {
                                            LOGGER.warn(String.format("Observer server is down , address : %s ", MotadataConfigUtil.getRemoteEventObserverPublisher() + ":" + MotadataConfigUtil.getMotadataObserverEventPublisherPort()));
                                        }

                                        if (Bootstrap.syncDone())
                                        {
                                            vertx.cancelTimer(timer);
                                        }
                                    });
                                }
                                else
                                {
                                    LOGGER.error(result.cause());
                                }
                            });
                }
                else
                {
                    //secondary and failover app server read request to observer server for latest config and cache event

                    vertx.setPeriodic(MotadataConfigUtil.getConfigDBPollTimerSeconds(), timer ->
                    {
                        if (PortUtil.isConnected(MotadataConfigUtil.getRemoteEventObserverPublisher(), MotadataConfigUtil.getMotadataObserverEventPublisherPort(), 3))
                        {
                            vertx.eventBus().send(EVENT_HA_OBSERVER, new JsonObject().put(EVENT_TYPE, EVENT_HA_CONFIG_OBSERVER_SYNC)
                                    .put(HA_SYNC_OPERATION, HAConstants.HASyncOperation.READ.getName()).put(INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                    .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()));

                            vertx.eventBus().send(EVENT_HA_OBSERVER, new JsonObject().put(EVENT_TYPE, EVENT_HA_CACHE_OBSERVER_SYNC)
                                    .put(HA_SYNC_OPERATION, HAConstants.HASyncOperation.READ.getName()).put(INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                    .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()));
                        }
                        else
                        {
                            LOGGER.warn(String.format("Observer server is down , address : %s ", MotadataConfigUtil.getRemoteEventObserverPublisher() + ":" + MotadataConfigUtil.getMotadataObserverEventPublisherPort()));
                        }
                    });
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }
    }

    private void load()
    {
        try
        {
            var file = new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "bookmarks.json");

            if (file.exists() && file.length() > 0)
            {
                var context = new JsonObject(FileUtils.readFileToString(file, StandardCharsets.UTF_8));

                if (!context.isEmpty())
                {
                    for (var item : context.getJsonObject("items"))
                    {
                        Bootstrap.startEngine(new ObserverDatastore(item.getKey()), ObserverDatastore.class.getSimpleName() + " " + item.getKey(), null)
                                .onComplete(result ->
                                {
                                    if (result.succeeded())
                                    {
                                        items.put(item.getKey(), CommonUtil.getString(item.getValue()));

                                        LOGGER.info(String.format("%s app loaded .. : %s ", item.getKey(), ObserverDatastore.class.getSimpleName() + " " + item.getKey()));
                                    }
                                    else
                                    {
                                        LOGGER.error(result.cause());
                                    }
                                });
                    }

                    Bootstrap.vertx().executeBlocking(future ->
                    {
                        try
                        {
                            var buffer = Bootstrap.vertx().fileSystem().existsBlocking(OBSERVER_CACHE) ? Bootstrap.vertx().fileSystem().readFileBlocking(OBSERVER_CACHE) : null;

                            if (buffer != null && buffer.length() > 0)
                            {
                                var cache = CodecUtil.toJSONObject(buffer.getBytes());

                                durations.mergeIn(cache);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }

                        future.complete();
                    });
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void updateCache(JsonObject context)
    {
        try
        {
            LOGGER.info(String.format("received updated cache file %s ", context.getString(CACHE_NAME)));

            Bootstrap.vertx().fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + context.getString(CACHE_NAME), Buffer.buffer(context.getBinary(RESULT)));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void update(JsonObject context)
    {
        try
        {
            context.put(GlobalConstants.REMOTE_ADDRESS, MOTADATA_OBSERVER);

            if (context.containsKey(HA_SYNC_OPERATION))
            {
                if (context.containsKey(DATABASE))
                {
                    LOGGER.info(String.format("request received to update %s ", context.getString(DATABASE)));

                    if (context.getString(DATABASE).equalsIgnoreCase(BackupProfile.BackupProfileType.CONFIG_DB.getName()))
                    {
                        updateConfigDB(context);
                    }
                    else if (context.getString(DATABASE).equalsIgnoreCase(COMPLIANCE_DB))
                    {
                        updateComplianceDB(context);
                    }
                }
                else
                {
                    LOGGER.warn(String.format("Invalid context : %s ", context.encode()));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            vertx.eventBus().send(replyTopic, context);
        }
    }

    private void updateConfigDB(JsonObject context)
    {
        var collection = context.getString(APIConstants.ENTITY_COLLECTION);

        var type = HAConstants.HASyncOperation.valueOfName(CommonUtil.getByteValue(context.getValue(HA_SYNC_OPERATION)));

        if (collection != null && CommonUtil.debugEnabled())
        {
            LOGGER.debug(String.format("performing config DB operation %s on collection %s", type, collection));
        }

        switch (type)
        {
            case SAVE ->
            {
                if (!context.containsKey(EventBusConstants.EVENT_ROUTER_CONFIG))
                {
                    Bootstrap.configDBService().save(collection, context.getJsonArray(ConfigDBConstants.DB_DOCUMENTS).getJsonObject(0), context.getString(User.USER_NAME), context.getString(GlobalConstants.REMOTE_ADDRESS),
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    if (CommonUtil.debugEnabled())
                                    {
                                        LOGGER.debug(String.format("collection %s , type %s successfully", collection, type));
                                    }

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace(String.format("context : %s", CommonUtil.removeSensitiveFields(context.getJsonArray(ConfigDBConstants.DB_DOCUMENTS).getJsonObject(0), true).encode()));
                                    }
                                }
                                else
                                {
                                    LOGGER.error(result.cause());

                                    LOGGER.warn(String.format("error found in %s ", context.encodePrettily()));
                                }

                                vertx.eventBus().send(replyTopic, context);
                            });
                }
                else
                {
                    var cacheFile = context.getString(CACHE_NAME);

                    if (CommonUtil.isNotNullOrEmpty(cacheFile) && context.getBinary(GlobalConstants.RESULT) != null && context.getBinary(RESULT).length > 0)
                    {
                        if (CommonUtil.debugEnabled())
                        {
                            LOGGER.debug(String.format("received updated cache file %s ", cacheFile));
                        }

                        Bootstrap.vertx().fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + cacheFile,
                                Buffer.buffer(context.getBinary(RESULT)));
                    }

                    vertx.eventBus().send(replyTopic, context);
                }
            }

            case SAVE_ALL ->
                    Bootstrap.configDBService().saveAll(collection, context.getJsonArray(ConfigDBConstants.DB_DOCUMENTS), context.getString(User.USER_NAME), context.getString(GlobalConstants.REMOTE_ADDRESS),
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    if (CommonUtil.debugEnabled())
                                    {
                                        LOGGER.debug(String.format("collection %s , type %s successfully", collection, type));
                                    }

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace(String.format("context : %s", CommonUtil.removeSensitiveFields(context.getJsonArray(ConfigDBConstants.DB_DOCUMENTS).getJsonObject(0), true).encode()));
                                    }
                                }
                                else
                                {
                                    LOGGER.error(result.cause());

                                    LOGGER.warn(String.format("error found in %s ", context.encodePrettily()));
                                }

                                vertx.eventBus().send(replyTopic, context);
                            });

            case UPDATE ->
                    Bootstrap.configDBService().update(collection, context.getJsonObject(ConfigDBConstants.DB_QUERY), context.getJsonArray(ConfigDBConstants.DB_DOCUMENTS).getJsonObject(0), context.getString(User.USER_NAME), context.getString(GlobalConstants.REMOTE_ADDRESS),
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    if (CommonUtil.debugEnabled())
                                    {
                                        LOGGER.debug(String.format("collection %s , type %s successfully", collection, type));
                                    }

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace(String.format("context : %s", CommonUtil.removeSensitiveFields(context.getJsonArray(ConfigDBConstants.DB_DOCUMENTS).getJsonObject(0), true).encode()));
                                    }
                                }
                                else
                                {
                                    LOGGER.error(result.cause());

                                    LOGGER.warn(String.format("error found in %s ", context.encodePrettily()));
                                }

                                vertx.eventBus().send(replyTopic, context);
                            });

            case UPDATE_ALL ->
                    Bootstrap.configDBService().updateAll(collection, context.getJsonObject(ConfigDBConstants.DB_QUERY), context.getJsonArray(ConfigDBConstants.DB_DOCUMENTS).getJsonObject(0), context.getString(User.USER_NAME), context.getString(GlobalConstants.REMOTE_ADDRESS),
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    if (CommonUtil.debugEnabled())
                                    {
                                        LOGGER.debug(String.format("collection %s , type %s successfully", collection, type));
                                    }

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace(String.format("context : %s", CommonUtil.removeSensitiveFields(context.getJsonArray(ConfigDBConstants.DB_DOCUMENTS).getJsonObject(0), true).encode()));
                                    }
                                }
                                else
                                {
                                    LOGGER.error(result.cause());

                                    LOGGER.warn(String.format("error found in %s ", context.encodePrettily()));
                                }

                                vertx.eventBus().send(replyTopic, context);
                            });

            case UPSERT ->
                    Bootstrap.configDBService().upsert(collection, context.getJsonObject(DB_QUERY), context.getJsonArray(ConfigDBConstants.DB_DOCUMENTS).getJsonObject(0), DEFAULT_USER, context.getString(GlobalConstants.REMOTE_ADDRESS),
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    if (CommonUtil.debugEnabled())
                                    {
                                        LOGGER.debug(String.format("collection %s , type %s successfully", collection, type));

                                    }

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace(String.format("context : %s", CommonUtil.removeSensitiveFields(context.getJsonArray(ConfigDBConstants.DB_DOCUMENTS).getJsonObject(0), true).encode()));
                                    }
                                }
                                else
                                {
                                    LOGGER.error(result.cause());

                                    LOGGER.warn(String.format("error found in %s ", context.encodePrettily()));
                                }

                                vertx.eventBus().send(replyTopic, context);
                            });

            case DELETE ->
                    Bootstrap.configDBService().delete(collection, context.getJsonObject(ConfigDBConstants.DB_QUERY), context.getString(User.USER_NAME), context.getString(GlobalConstants.REMOTE_ADDRESS),
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    if (CommonUtil.debugEnabled())
                                    {
                                        LOGGER.debug(String.format("collection %s , type %s successfully", collection, type));
                                    }

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace(String.format("context : %s", CommonUtil.removeSensitiveFields(context, true).encode()));
                                    }
                                }
                                else
                                {
                                    LOGGER.error(result.cause());

                                    LOGGER.warn(String.format("error found in %s ", context.encodePrettily()));
                                }

                                vertx.eventBus().send(replyTopic, context);
                            });

            case DELETE_ALL ->
                    Bootstrap.configDBService().deleteAll(collection, context.getJsonObject(ConfigDBConstants.DB_QUERY), context.getString(User.USER_NAME), context.getString(GlobalConstants.REMOTE_ADDRESS),
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    if (CommonUtil.debugEnabled())
                                    {
                                        LOGGER.trace(String.format("collection %s , type %s successfully", collection, type));
                                    }

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace(String.format("context : %s", CommonUtil.removeSensitiveFields(context, true).encode()));
                                    }
                                }
                                else
                                {
                                    LOGGER.error(result.cause());

                                    LOGGER.warn(String.format("error found in %s ", context.encodePrettily()));
                                }

                                vertx.eventBus().send(replyTopic, context);
                            });

            case DROP -> Bootstrap.configDBService().drop(collection,
                    result ->
                    {
                        if (result.failed())
                        {
                            LOGGER.error(result.cause());

                            LOGGER.warn(String.format("error found in %s ", context.encodePrettily()));
                        }

                        vertx.eventBus().send(replyTopic, context);
                    });

            default -> vertx.eventBus().send(replyTopic, context);
        }
    }

    private void updateComplianceDB(JsonObject context)
    {
        var collection = context.getString(APIConstants.ENTITY_COLLECTION);

        var type = HAConstants.HASyncOperation.valueOfName(CommonUtil.getByteValue(context.getValue(HA_SYNC_OPERATION)));

        LOGGER.info(String.format("performing compliance DB operation %s on collection %s", type, collection));

        switch (type)
        {
            case SAVE ->
                    Bootstrap.complianceDBService().save(collection, context.getJsonArray(ConfigDBConstants.DB_DOCUMENTS).getJsonObject(0), context.getString(User.USER_NAME), context.getString(GlobalConstants.REMOTE_ADDRESS), result ->
                    {
                        if (result.succeeded())
                        {
                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("collection %s , type %s successfully", collection, type));
                            }

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("context : %s", CommonUtil.removeSensitiveFields(context.getJsonArray(ConfigDBConstants.DB_DOCUMENTS).getJsonObject(0), true).encode()));
                            }
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            LOGGER.warn(String.format("Failed to update compliance DB context :%s ", CommonUtil.removeSensitiveFields(context, true).encode()));
                        }

                        vertx.eventBus().send(replyTopic, context);
                    });

            case SAVE_ALL ->
                    Bootstrap.complianceDBService().saveAll(collection, context.getJsonArray(ConfigDBConstants.DB_DOCUMENTS), context.getString(User.USER_NAME), context.getString(GlobalConstants.REMOTE_ADDRESS), result ->
                    {
                        if (result.succeeded())
                        {
                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("collection %s , type %s successfully", collection, type));
                            }

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("context : %s", CommonUtil.removeSensitiveFields(context, true).encode()));
                            }
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            LOGGER.warn(String.format("Failed to update compliance DB context :%s ", CommonUtil.removeSensitiveFields(context, true).encode()));
                        }

                        vertx.eventBus().send(replyTopic, context);
                    });

            case DELETE ->
                    Bootstrap.complianceDBService().delete(collection, context.getJsonObject(ConfigDBConstants.DB_QUERY), context.getString(User.USER_NAME), context.getString(GlobalConstants.REMOTE_ADDRESS), result ->
                    {
                        if (result.succeeded())
                        {
                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("collection %s , type %s successfully", collection, type));
                            }

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("context : %s", CommonUtil.removeSensitiveFields(context, true).encode()));
                            }
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            LOGGER.warn(String.format("Failed to update compliance DB context :%s ", CommonUtil.removeSensitiveFields(context, true).encode()));
                        }

                        vertx.eventBus().send(replyTopic, context);
                    });

            default -> vertx.eventBus().send(replyTopic, context);
        }
    }

    private void decodeEvent(JsonObject event)
    {
        try
        {
            var buffer = Buffer.buffer(event.getBinary(EVENT_CONTEXT));

            update(new JsonObject(new String(buffer.getBytes(12, 12 + buffer.getIntLE(8))))); // decode row bytes
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            vertx.eventBus().send(replyTopic, event);
        }
    }

    private void log(JsonObject event)
    {
        var buffer = Buffer.buffer(event.getBinary(EVENT_CONTEXT));

        if (CommonUtil.debugEnabled())
        {
            LOGGER.debug(String.format("context received :  %s ", new JsonObject(new String(buffer.getBytes(12, 12 + buffer.getIntLE(8)))).encode()));
        }
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        if (eventEngine != null)
        {
            eventEngine.stop(vertx, promise);
        }
        else
        {
            promise.complete();
        }
    }
}