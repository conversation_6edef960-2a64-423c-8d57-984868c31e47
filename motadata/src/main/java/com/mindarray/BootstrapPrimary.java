/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*  Change Logs:
 *  Date			Author			  Notes
 *  6-Feb-2025		<PERSON><PERSON>wari		  MOTADATA-4878: deploying 3 different event publisher verticle for different port for metric, event and availability
 *  2025-02-06      Umang Sharma      Added Compliance
 *  28-Feb-2025     Smit Prajapati    MOTADATA-4956: Rule Based Tagging
 *  24-Mar-2025     Yash <PERSON>wari       MOTADATA-5432: Started API Server as worker verticle
 *  25-Mar-2025     Smit Prajapati    MOTADATA-5435: Flow back-pressure mechanism.
 *  20-Feb-2025     Pruthviraj        NetRoute related verticle added
 *  18-Apr-2025     Vismit            Added ConfigNotifiationEngine starting logic
 *  21-May-2025		Viram Katudiya	  Handled the corner case when Primary Service failed to start
 * */

package com.mindarray;

import com.mindarray.agent.AgentEventProcessor;
import com.mindarray.agent.MetricAgentEventProcessor;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.aiops.AvailabilityCorrelationEngine;
import com.mindarray.aiops.DependencyManager;
import com.mindarray.aiops.DependencyQueryProcessor;
import com.mindarray.api.*;
import com.mindarray.cache.CacheServiceProvider;
import com.mindarray.compliance.ComplianceManager;
import com.mindarray.compliance.ComplianceRuleEngine;
import com.mindarray.config.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.datastore.DatastoreManager;
import com.mindarray.db.ConfigDBChangeEventProcessor;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.db.ConfigDBInitializer;
import com.mindarray.db.ConfigDBServiceProvider;
import com.mindarray.eventbus.*;
import com.mindarray.flow.FlowCacheProcessor;
import com.mindarray.flow.FlowListener;
import com.mindarray.flow.FlowProcessor;
import com.mindarray.flow.FlowStatCalculator;
import com.mindarray.ha.HAConstants;
import com.mindarray.ha.Observer;
import com.mindarray.integration.*;
import com.mindarray.job.JobScheduler;
import com.mindarray.log.*;
import com.mindarray.log.LogCollector;
import com.mindarray.log.LogForwarder;
import com.mindarray.log.LogParser;
import com.mindarray.manager.ManagerResponseProcessor;
import com.mindarray.netroute.NetRouteResponseProcessor;
import com.mindarray.netroute.NetRouteScheduler;
import com.mindarray.netroute.NetRouteStatusCalculator;
import com.mindarray.nms.*;
import com.mindarray.notification.NotificationEngine;
import com.mindarray.plugin.PluginEngine;
import com.mindarray.plugin.PluginEngineResponseProcessor;
import com.mindarray.policy.*;
import com.mindarray.report.ReportManager;
import com.mindarray.report.ReportResponseProcessor;
import com.mindarray.report.ReportingEngine;
import com.mindarray.runbook.RunbookEngine;
import com.mindarray.store.*;
import com.mindarray.streaming.StreamingEngine;
import com.mindarray.util.*;
import com.mindarray.visualization.*;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.mindarray.Bootstrap.*;
import static com.mindarray.ErrorCodes.ERROR_CODE_NOT_COMPATIBLE;
import static com.mindarray.ErrorCodes.ERROR_CODE_UPGRADE_REQUIRED;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.Agent.AGENT_UUID;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.ha.HAConstants.CACHE_NAME;
import static com.mindarray.manager.MotadataAppManager.HEARTBEAT_STATE;
import static com.mindarray.nms.SNMPTrapProcessor.SNMP_TRAP_ACKS;
import static com.mindarray.report.ReportConstants.REPORT_ID;

public class BootstrapPrimary
{
    private static final Logger LOGGER = new Logger(BootstrapPrimary.class, GlobalConstants.MOTADATA_SYSTEM, "Bootstrap Primary");
    private static final AtomicBoolean INIT_DONE = new AtomicBoolean(false);

    public void start()
    {
        try
        {
            if (MotadataConfigUtil.getEnvironmentType().equalsIgnoreCase(ENV_TEST) || PortUtil.isConnected(MotadataConfigUtil.getRemoteEventObserverPublisher(), MotadataConfigUtil.getMotadataObserverEventPublisherPort(), 3))
            {
                DatastoreConstants.loadConfigs();

                Bootstrap.logSystemConfig();

                PortUtil.init();

                WebClientUtil.init();

                ProcessUtil.setProcessors();

                EventBusConstants.setManagerRegistrationId();

                startEngine(new ConfigDBServiceProvider(), ConfigDBServiceProvider.class.getSimpleName(), null)
                        .compose(future -> startEngine(new CacheServiceProvider(), CacheServiceProvider.class.getSimpleName(), null))
                        .compose(future -> startEngine(new SSHClientManager(), SSHClientManager.class.getSimpleName(), null))
                        .compose(future -> startEngine(new RemoteEventForwarder(MotadataConfigUtil.getMotadataObserverEventSubscriberPort(), MotadataConfigUtil.getRemoteEventObserverSubscriber()), RemoteEventForwarder.class.getSimpleName() + " " + MotadataConfigUtil.getMotadataObserverEventSubscriberPort(), null))
                        .compose(future -> startEngine(new RemoteEventSubscriber(MotadataConfigUtil.getMotadataObserverEventPublisherPort(), MotadataConfigUtil.getRemoteEventObserverPublisher()), RemoteEventSubscriber.class.getSimpleName() + " " + MotadataConfigUtil.getMotadataObserverEventPublisherPort(), null))
                        .compose(future -> startEngine(new AuditUtil(), AuditUtil.class.getSimpleName(), null))
                        .compose(future -> startEngine(new DatastoreManager(), DatastoreManager.class.getSimpleName(), null))
                        .compose(future -> startEngine(new Observer(), Observer.class.getSimpleName(), null))
                        .onComplete(result ->
                        {
                            if (result.succeeded())
                            {
                                sync().onComplete(futureResult ->
                                {
                                    var asyncFutures = new ArrayList<Future<Object>>();

                                    var publisherPort = CommonUtil.getEventPublisherPort();

                                    for (var index = 0; index < MotadataConfigUtil.getEventPublishers(); index++)
                                    {
                                        var promise = Promise.promise();

                                        asyncFutures.add(promise.future());

                                        startEngine(new EventPublisher(publisherPort + index, "Event Publisher " + (publisherPort + index)), EventPublisher.class.getSimpleName(), null, EVENT_PUBLICATION).onComplete(asyncResult ->
                                        {
                                            if (asyncResult.succeeded())
                                            {
                                                promise.complete();
                                            }
                                            else
                                            {
                                                promise.fail(asyncResult.cause());
                                            }
                                        });
                                    }

                                    Future.join(asyncFutures)
                                            .compose(future -> startEngine(new EventPublisher(MotadataConfigUtil.getMotadataManagerEventPublisherPort(), "Motadata Manager Publisher"), EventPublisher.class.getSimpleName() + " " + MotadataConfigUtil.getMotadataManagerEventPublisherPort(), null, EVENT_PUBLICATION_MOTADATA_MANAGER))
                                            .compose(future -> startWorkerEngine(new EventSubscriber(MotadataConfigUtil.getMotadataManagerEventSubscriberPort(), "Motadata Manager Subscriber"), EventSubscriber.class.getSimpleName() + " " + MotadataConfigUtil.getMotadataManagerEventSubscriberPort(), null))
                                            .compose(future -> Bootstrap.startEngine(new EventPublisher(MotadataConfigUtil.getDatastoreWriterPort(), "Datastore Writer Event Publisher"), EventPublisher.class.getSimpleName() + " " + MotadataConfigUtil.getDatastoreWriterPort(), null, EVENT_PUBLICATION_DATASTORE_WRITE))
                                            .compose(future -> Bootstrap.startEngine(new EventPublisher(MotadataConfigUtil.getMetricDatastoreWriterPort(), "Metric Datastore Writer Event Publisher"), EventPublisher.class.getSimpleName() + " " + MotadataConfigUtil.getMetricDatastoreWriterPort(), null, EVENT_PUBLICATION_DATASTORE_WRITE + DOT_SEPARATOR + DatastoreConstants.DatastoreCategory.METRIC.getName()))
                                            .compose(future -> Bootstrap.startEngine(new EventPublisher(MotadataConfigUtil.getEventDatastoreWriterPort(), "Event Datastore Writer Event Publisher"), EventPublisher.class.getSimpleName() + " " + MotadataConfigUtil.getEventDatastoreWriterPort(), null, EVENT_PUBLICATION_DATASTORE_WRITE + DOT_SEPARATOR + DatastoreConstants.DatastoreCategory.EVENT.getName()))
                                            .compose(future -> Bootstrap.startEngine(new EventPublisher(MotadataConfigUtil.getAvailabilityDatastoreWriterPort(), "Availability Datastore Writer Event Publisher"), EventPublisher.class.getSimpleName() + " " + MotadataConfigUtil.getAvailabilityDatastoreWriterPort(), null, EVENT_PUBLICATION_DATASTORE_WRITE + DOT_SEPARATOR + DatastoreConstants.DatastoreCategory.AVAILABILITY.getName()))
                                            .compose(future -> startEngine(new EventPublisher(MotadataConfigUtil.getDatastoreReaderPort(), "Datastore Reader Event Publisher"), EventPublisher.class.getSimpleName() + " " + MotadataConfigUtil.getDatastoreReaderPort(), null, EVENT_PUBLICATION_DATASTORE_READ))
                                            .compose(future -> startWorkerEngine(new EventSubscriber(MotadataConfigUtil.getDatastoreEventSubscriberPort(), "Data Store Event Subscriber"), EventSubscriber.class.getSimpleName() + " " + MotadataConfigUtil.getDatastoreEventSubscriberPort(), null))
                                            .onComplete(asyncResult ->
                                            {
                                                if (asyncResult.succeeded())
                                                {
                                                    var responses = new ArrayList<Future<Object>>();

                                                    var subscriberPort = CommonUtil.getEventSubscriberPort();

                                                    for (var index = 0; index < MotadataConfigUtil.getEventSubscribers(); index++)
                                                    {
                                                        var promise = Promise.promise();

                                                        responses.add(promise.future());

                                                        startWorkerEngine(new EventSubscriber((subscriberPort + index), "Event Subscriber " + (subscriberPort + index)), EventSubscriber.class.getSimpleName() + " " + (subscriberPort + index), null).onComplete(asyncResponse ->
                                                        {
                                                            if (asyncResponse.succeeded())
                                                            {
                                                                promise.complete();
                                                            }
                                                            else
                                                            {
                                                                promise.fail(asyncResponse.cause());
                                                            }
                                                        });
                                                    }

                                                    Future.join(responses).onComplete(response ->
                                                            {

                                                        if (response.succeeded())
                                                        {
                                                            initConfig(true);
                                                        }
                                                        else
                                                        {
                                                            stop(response.cause());
                                                        }
                                                    });

                                                }
                                                else
                                                {
                                                    stop(asyncResult.cause());
                                                }
                                            });
                                });
                            }
                            else
                            {
                                stop(result.cause());
                            }
                        });
            }
            else
            {
                stop("motadata observer server is not reachable...");

                LOGGER.info(String.format("motadata observer server is not reachable... %s:%s ", MotadataConfigUtil.getRemoteEventObserverPublisher(), MotadataConfigUtil.getMotadataObserverEventPublisherPort()));

                System.exit(0);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void initConfig(boolean init)
    {
        var asyncPromise = Promise.<Boolean>promise();

        var promise = Promise.<Void>promise();

        LOGGER.info("initializing config db");

        Bootstrap.configDBService().getOne(ConfigDBConstants.COLLECTION_SYSTEM,
                result ->
                {
                    if (result.succeeded() && result.result().containsKey(INSTALLATION_DATE))
                    {
                        asyncPromise.complete(false);
                    }
                    else
                    {
                        asyncPromise.complete(true);
                    }
                });

        asyncPromise.future().onComplete(asyncResponse ->
        {
            LOGGER.info("verifying license...");

            LicenseUtil.init(asyncResponse.result()).onComplete(asyncPromiseResult ->
            {
                if (asyncPromiseResult.succeeded())
                {
                    LOGGER.info("license verified successfully...");

                    LicenseUtil.load(asyncPromiseResult.result());

                    ConfigDBInitializer.init().onComplete(result ->
                    {
                        if (result.succeeded())
                        {
                            LOGGER.info("loading all config/cache store");

                            // init all stores...
                            initStores().onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    LOGGER.info("all config/cache stores loaded successfully...");

                                    Bootstrap.patch(result.result()).onComplete(future ->
                                    {
                                        if (future.succeeded())
                                        {
                                            var asyncFutures = new ArrayList<Future<Void>>();

                                            for (var index = 0; index < MotadataConfigUtil.getHTTPServerInstances(); index++)
                                            {
                                                var asyncFuture = Promise.<Void>promise();

                                                asyncFutures.add(asyncFuture.future());

                                                Bootstrap.startWorkerEngine(new APIServer(), APIServer.class.getSimpleName() + " " + index, null).onComplete(voidResult ->
                                                {
                                                    if (voidResult.succeeded())
                                                    {
                                                        asyncFuture.complete();
                                                    }
                                                    else
                                                    {
                                                        asyncFuture.fail(voidResult.cause());
                                                    }
                                                });
                                            }

                                            Future.join(asyncFutures).onComplete(compositeFuture ->
                                            {
                                                if (compositeFuture.succeeded())
                                                {
                                                    LOGGER.info("HTTP server started successfully...");

                                                    EventBusConstants.setRemoteEventProcessorRegistrationId();

                                                    promise.complete();

                                                    if (init)
                                                    {
                                                        init();
                                                    }
                                                }
                                                else
                                                {
                                                    promise.fail(compositeFuture.cause());

                                                    stop(compositeFuture.cause());
                                                }
                                            });
                                        }
                                        else
                                        {
                                            Bootstrap.stop(future.cause());

                                            promise.fail(future.cause());
                                        }
                                    });
                                }
                                else
                                {
                                    stop(asyncResult.cause());

                                    promise.fail(asyncResult.cause());
                                }
                            });
                        }
                        else
                        {
                            promise.fail(result.cause());

                            stop(result.cause());
                        }
                    });
                }
            });
        });

        promise.future();
    }

    private void init()
    {
        try
        {
            startPrimary();
        }
        catch (Exception exception)
        {
            stop(exception);
        }
    }

    /*
     *   Port details for default:
     *
     *   event.publisher: 9444-9448 , for motadata.manager 9440, for Datastore 9455
     *   event.subscriber: 9449-9453, for motadata.manager 9441, for Datastore 9456
     * */
    private void startPrimary()
    {
        try
        {
            Bootstrap.startEngine(new PluginEngineResponseProcessor(), PluginEngineResponseProcessor.class.getSimpleName(), null)
                    //.compose(future -> Bootstrap.startEngine(new RunbookEngine(), RunbookEngine.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new NotificationEngine(), NotificationEngine.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new EventTracker(), EventTracker.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_ROUTER, AIOpsObject.OBJECT_TARGET, MotadataConfigUtil.getRouterInstances(), RemoteEventRouter.class.getCanonicalName(), false), RemoteEventRouter.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new ManagerResponseProcessor(), ManagerResponseProcessor.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new ProxyServiceUtil(), ProxyServiceUtil.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new EmailUtil(), EmailUtil.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new OAuthUtil(), OAuthUtil.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new DNSUtil(), DNSUtil.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new UserNotificationUtil(), UserNotificationUtil.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new DependencyQueryProcessor(), DependencyQueryProcessor.class.getSimpleName(), null))
                    //.compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.CROSS_DOMAIN.getName(), AIOpsConstants.DEPENDENCY_SOURCE, MotadataConfigUtil.getDependencyCrossDomainInstances(), DependencyManager.class.getCanonicalName(), false), EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.CROSS_DOMAIN.getName(), null))
                    .compose(future -> Bootstrap.startEngine(new HealthUtil(), HealthUtil.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new VisualizationMetricResponseProcessor(), VisualizationMetricResponseProcessor.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new VisualizationEventResponseProcessor(), VisualizationEventResponseProcessor.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new VisualizationEventHistoryResponseProcessor(), VisualizationEventHistoryResponseProcessor.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new VisualizationCacheEngine(), VisualizationCacheEngine.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_VISUALIZATION, ID, MotadataConfigUtil.getVisualizationManagerInstances(), VisualizationManager.class.getCanonicalName(), true), VisualizationManager.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_VISUALIZATION_METRIC, ID, MotadataConfigUtil.getVisualizationManagerInstances(), VisualizationMetricManager.class.getCanonicalName(), true), VisualizationMetricManager.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EVENT_VISUALIZATION_NETROUTE, ID, MotadataConfigUtil.getVisualizationNetRouteManagerInstances(), VisualizationNetRouteManager.class.getCanonicalName(), true), VisualizationNetRouteManager.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_VISUALIZATION_AVAILABILITY, ID, MotadataConfigUtil.getVisualizationManagerInstances(), VisualizationAvailabilityManager.class.getCanonicalName(), true), VisualizationAvailabilityManager.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_VISUALIZATION_EVENT, ID, MotadataConfigUtil.getVisualizationManagerInstances(), VisualizationEventManager.class.getCanonicalName(), true), VisualizationEventManager.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_VISUALIZATION_EVENT_HISTORY, ID, MotadataConfigUtil.getVisualizationManagerInstances(), VisualizationEventHistoryManager.class.getCanonicalName(), true), VisualizationEventHistoryManager.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_VISUALIZATION_COMPLIANCE, ID, MotadataConfigUtil.getVisualizationManagerInstances(), VisualizationComplianceManager.class.getCanonicalName(), true), VisualizationComplianceManager.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new StreamingEngine(), StreamingEngine.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new UIActionEventHandler(), UIActionEventHandler.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new ConfigDBChangeEventProcessor(), ConfigDBChangeEventProcessor.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new SeverityChangeEventProcessor(), SeverityChangeEventProcessor.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new ObjectStatusChangeEventProcessor(), ObjectStatusChangeEventProcessor.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new LocalEventChangeNotificationHandler(), LocalEventChangeNotificationHandler.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_REPORT, ID, MotadataConfigUtil.getReportEngineInstances(), ReportingEngine.class.getCanonicalName(), false), ReportingEngine.class.getSimpleName(), null))
                    .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_REPORT_RENDER, REPORT_ID, MotadataConfigUtil.getReportManagerInstances(), ReportManager.class.getCanonicalName(), false), ReportManager.class.getSimpleName(), null)
                            .compose(result -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_REPORT_RESPONSE_PROCESSOR, ID, MotadataConfigUtil.getReportManagerInstances(), ReportResponseProcessor.class.getCanonicalName(), false), ReportResponseProcessor.class.getSimpleName(), null))
                            .onComplete(result ->
                            {
                                if (result.succeeded())
                                {
                                    LOGGER.info("howdy, Report Engine started successfully... :)");
                                }
                                else
                                {
                                    Bootstrap.stop(result.cause());
                                }
                            }))
                    .onComplete(asyncResponse ->
                    {
                        if (asyncResponse.succeeded())
                        {
                            try
                            {
                                var futures = new ArrayList<Future<Object>>();

                                EventBusConstants.setRemoteEventProcessorRegistrationId();

                                EventBusConstants.register(new CipherUtil());

                                GeoDBUtil.init(Bootstrap.vertx());

                                SingleSignOnUtil.init();

                                Bootstrap.vertx().setPeriodic(MotadataConfigUtil.getDependencyFlushTimerSeconds() * 1000L, timer -> Bootstrap.writeDependencies(AIOpsConstants.DependencyType.CROSS_DOMAIN));

                                Bootstrap.vertx().setPeriodic(MotadataConfigUtil.getLogPatternFlushTimerSeconds() * 1000L, timer -> LogPatternCacheStore.getStore().dump(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + LogEngineConstants.LOG_PATTERNS));

                                Bootstrap.vertx().setPeriodic(MotadataConfigUtil.getEventOrdinalFlushTimerSeconds() * 1000L, timer -> EventOrdinalCacheStore.getStore().dump(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + LogEngineConstants.EVENT_ORDINALS));

                                Bootstrap.vertx().setPeriodic(MotadataConfigUtil.getTrapAckCacheFlushTimerSeconds() * 1000L, timer -> TrapCacheStore.getStore().dump(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + SNMP_TRAP_ACKS));

                                Bootstrap.vertx().setPeriodic(MotadataConfigUtil.getEventPolicyCacheFlushTimerSeconds() * 1000L, timer ->
                                        EventPolicyCacheStore.getStore().dump(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + PolicyEngineConstants.EVENT_POLICY_TRIGGER_TICKS));

                                Bootstrap.vertx().setPeriodic(MotadataConfigUtil.getStatusFlapCacheFlushTimerSeconds() * 1000L, timer -> ObjectStatusCacheStore.getStore().dump(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + NMSConstants.STATUS_FLAPS));

                                Bootstrap.vertx().setTimer(TimeUnit.MINUTES.toMillis(2), timer -> runCompatibilityCheck());

                                HAConstants.switchOverIP(HAConstants.HASyncOperation.ATTACHE.getName());

                                if (LicenseUtil.MONITORING_ENABLED.get()) //check for nms module...
                                {
                                    var promise = Promise.promise();

                                    futures.add(promise.future());

                                    Bootstrap.startEngine(new AgentEventProcessor(), AgentEventProcessor.class.getSimpleName(), null)
                                            .compose(future -> Bootstrap.startEngine(new SNMPTrapProcessor(), SNMPTrapProcessor.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new ObjectManager(), ObjectManager.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new ObjectStatusCalculator(), ObjectStatusCalculator.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_METRIC_ENRICHER, AIOpsObject.OBJECT_ID, MotadataConfigUtil.getMetricEnricherInstances(), MetricEnricher.class.getCanonicalName(), true), MetricEnricher.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new ResponseProcessor(), ResponseProcessor.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new MetricAgentEventProcessor(), MetricAgentEventProcessor.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new DiscoveryEngine(), DiscoveryEngine.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new TopologyEngine(), TopologyEngine.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new RediscoverEngine(), RediscoverEngine.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new PluginEngine(), PluginEngine.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new MetricPoller(), MetricPoller.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new AutoScaler(), AutoScaler.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new VisualizationQueryHelper(), VisualizationQueryHelper.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new MetricScheduler(), MetricScheduler.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new NetRouteScheduler(), NetRouteScheduler.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new NetRouteResponseProcessor(), NetRouteResponseProcessor.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new NetRouteStatusCalculator(), NetRouteStatusCalculator.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new NetRoutePolicyTriggerDurationCalculator(), NetRoutePolicyTriggerDurationCalculator.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_METRIC_POLICY, AIOpsObject.OBJECT_ID, MotadataConfigUtil.getMetricPolicyWorkers(), MetricPolicyInspector.class.getCanonicalName(), true), MetricPolicyInspector.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_NETROUTE_POLICY, NetRoute.NETROUTE_ID, MotadataConfigUtil.getNetRoutePolicyWorkers(), NetRoutePolicyInspector.class.getCanonicalName(), true), NetRoutePolicyInspector.class.getSimpleName(), null))
                                            //.compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_RULE_ENGINE, Metric.METRIC_OBJECT, CommonUtil.getRuleEngineInstances(), "com.mindarray.aiops.RuleEngine"), EventBusConstants.EVENT_RULE_ENGINE, null))
                                            .compose(future -> Bootstrap.startEngine(new MetricPolicyTriggerDurationCalculator(), MetricPolicyTriggerDurationCalculator.class.getSimpleName(), null))
                                            //.compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(), AIOpsConstants.DEPENDENCY_SOURCE, MotadataConfigUtil.getDependencyLocalDomainInstances(), DependencyManager.class.getCanonicalName(), false), EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(), null))
                                            .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_AVAILABILITY_CORRELATION, Metric.METRIC_OBJECT, MotadataConfigUtil.getAvailabilityCorrelationEngineInstances(), AvailabilityCorrelationEngine.class.getCanonicalName(), true), EventBusConstants.EVENT_AVAILABILITY_CORRELATION, null))
                                            .compose(future -> Bootstrap.startWorkerEngine(new JVMStatUtil(), JVMStatUtil.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startWorkerEngine(new DiagnosticUtil(), DiagnosticUtil.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startWorkerEngine(new TagUtil(), TagUtil.class.getSimpleName(), null))
                                            .onComplete(result ->
                                            {
                                                if (result.failed())
                                                {
                                                    Bootstrap.stop(result.cause());
                                                }

                                                else
                                                {
                                                    if ((LicenseUtil.LICENSE_EDITION.get() == LicenseUtil.LicenseEdition.HYBRID_LITE || LicenseUtil.LICENSE_EDITION.get() == LicenseUtil.LicenseEdition.HYBRID || LicenseUtil.LICENSE_EDITION.get() == LicenseUtil.LicenseEdition.OBSERVABILITY))//as we will be starting aiops verticle only if following license edition available
                                                    {
                                                        if (LicenseUtil.LICENSE_EDITION.get() == LicenseUtil.LicenseEdition.OBSERVABILITY)
                                                        {
                                                            startEngine(new LocalEventRouter(EventBusConstants.EVENT_AIOPS_METRIC_POLICY_MANAGER, AIOpsObject.OBJECT_ID, 1, AIOpsMetricPolicyManager.class.getCanonicalName(), false), AIOpsMetricPolicyManager.class.getSimpleName(), null)
                                                                    .compose(future -> startEngine(new LocalEventRouter(EventBusConstants.EVENT_AIOPS_METRIC_POLICY, AIOpsObject.OBJECT_ID, MotadataConfigUtil.getAIOpsMetricPolicyWorkers(), AIOpsMetricPolicyInspector.class.getCanonicalName(), true), AIOpsMetricPolicyInspector.class.getSimpleName(), null).onComplete(asyncResult ->
                                                                    {
                                                                        if (asyncResult.failed())
                                                                        {
                                                                            stop(asyncResult.cause());
                                                                        }

                                                                        else
                                                                        {
                                                                            promise.complete();
                                                                        }
                                                                    }));
                                                        }

                                                        //It would be for both
                                                        startEngine(new LocalEventRouter(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(), AIOpsConstants.DEPENDENCY_SOURCE, MotadataConfigUtil.getDependencyLocalDomainInstances(), DependencyManager.class.getCanonicalName(), false), EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(), null)
                                                                .compose(future -> startEngine(new TopologyEngine(), TopologyEngine.class.getSimpleName(), null))
                                                                .compose(future -> startEngine(new LocalEventRouter(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.CROSS_DOMAIN.getName(), AIOpsConstants.DEPENDENCY_SOURCE, MotadataConfigUtil.getDependencyCrossDomainInstances(), DependencyManager.class.getCanonicalName(), false), EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.CROSS_DOMAIN.getName(), null))
                                                                .compose(future -> startEngine(new RunbookEngine(), RunbookEngine.class.getSimpleName(), null))
                                                                .compose(future -> startEngine(new IntegrationEngine(), IntegrationEngine.class.getSimpleName(), null))
                                                                .compose(future -> startEngine(new ServiceOpsIntegration(), ServiceOpsIntegration.class.getSimpleName(), null))
                                                                .compose(future -> startEngine(new ServiceNowIntegration(), ServiceNowIntegration.class.getSimpleName(), null))
                                                                .compose(future -> startEngine(new TeamsIntegration(), TeamsIntegration.class.getSimpleName(), null))
                                                                .compose(future -> startEngine(new ************************(), ************************.class.getSimpleName(), null))
                                                                .onComplete(asyncResult ->
                                                                {
                                                                    if (asyncResult.failed())
                                                                    {
                                                                        stop(asyncResult.cause());
                                                                    }

                                                                    else
                                                                    {
                                                                        promise.complete();
                                                                    }
                                                                });
                                                    }
                                                    else
                                                    {
                                                        promise.complete();
                                                    }

                                                    LOGGER.info(String.format(InfoMessageConstants.MOTADATA_ENGINE_START_SUCCEEDED, BootstrapType.APP.name()));

                                                    INIT_DONE.set(true);

                                                    Bootstrap.vertx().setPeriodic(MotadataConfigUtil.getDependencyFlushTimerSeconds() * 1000L, timer -> Bootstrap.writeDependencies(AIOpsConstants.DependencyType.LOCAL_DOMAIN));

                                                    Bootstrap.vertx().setPeriodic(MotadataConfigUtil.getCorrelatedResourceFlushTimerSeconds() * 1000L, timer -> Bootstrap.writeCorrelatedResources());

                                                }
                                            });
                                }

                                if (LicenseUtil.CONFIG_MANAGEMENT_ENABLED.get())
                                {
                                    var promise = Promise.promise();

                                    futures.add(promise.future());

                                    Bootstrap.startEngine(new com.mindarray.config.ConfigManager(), com.mindarray.config.ConfigManager.class.getSimpleName(), null)
                                            .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_CONFIG_RESPONSE_PROCESSOR, Configuration.CONFIG_OBJECT, MotadataConfigUtil.getConfigManagementResponseProcessorWorkers(), ConfigResponseProcessor.class.getCanonicalName(), true), ConfigResponseProcessor.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_CONFIG_UPGRADE_MANAGER, Configuration.CONFIG_OBJECT, MotadataConfigUtil.getConfigUpgradeMaxWorkers(), ConfigUpgradeManager.class.getCanonicalName(), true), ConfigUpgradeManager.class.getSimpleName(), null))
                                            .compose(future -> startEngine(new ConfigOperationTracker(), ConfigOperationTracker.class.getSimpleName(), null))
                                            .onComplete(result ->
                                            {
                                                if (result.failed())
                                                {
                                                    Bootstrap.stop(result.cause());
                                                }
                                                else
                                                {
                                                    LOGGER.info(String.format("howdy, Motadata config engine %s started successfully... :)", BootstrapType.APP.name()));

                                                    promise.complete();

                                                    INIT_DONE.set(true);

                                                    // Starting file transfer server service. Default will be OFF
                                                    // Note : We are not completing promise based on the service status. just triggering command...
                                                    startService(ConfigConstants.FILE_TRANSFER_SERVICE_COMMAND_ARGS, "file-transfer-service");
                                                }
                                            });

                                }

                                if (LicenseUtil.COMPLIANCE_ENABLED.get())
                                {
                                    var promise = Promise.promise();

                                    futures.add(promise.future());

                                    Bootstrap.startEngine(new ComplianceManager(), ComplianceManager.class.getSimpleName(), null)
                                            .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_RULE_ENGINE, ID, MotadataConfigUtil.getComplianceRuleEngineWorkers(), ComplianceRuleEngine.class.getCanonicalName(), false), ComplianceRuleEngine.class.getSimpleName(), null))
                                            .onComplete(result ->
                                            {
                                                if (result.failed())
                                                {
                                                    Bootstrap.stop(result.cause());
                                                }
                                                else
                                                {
                                                    LOGGER.info(String.format("howdy, Motadata compliance engine %s started successfully... :)", BootstrapType.APP.name()));

                                                    promise.complete();

                                                    INIT_DONE.set(true);
                                                }
                                            });
                                }

                                if (LicenseUtil.LOG_ENABLED.get()) //check for log module...
                                {
                                    if (MotadataConfigUtil.logProcessorEnabled())
                                    {
                                        // 1. Log forwarder
                                        // 2. Log Processor
                                        // 3. Log Agent Processor
                                        // 4. TCP Listener
                                        // 5. UDP Listener
                                        Bootstrap.startEngine(new LocalEventRouter(EVENT_LOG_PROCESS, EventBusConstants.EVENT_SOURCE, 1, LogProcessor.class.getCanonicalName(), true, true, EventRouter.ROUND_ROBIN), LogProcessor.class.getSimpleName(), null)
//                                                .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_LOG_PATTERN, EventBusConstants.EVENT_SOURCE, MotadataConfigUtil.getLogPatternBuilders(), LogPatternBuilder.class.getCanonicalName(), true, false, true), LogPatternBuilder.class.getSimpleName(), null))
                                                .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EVENT_LOG, EventBusConstants.EVENT_SOURCE, MotadataConfigUtil.getLogParsers(), LogParser.class.getCanonicalName(), true, true, EventRouter.ROUND_ROBIN), LogParser.class.getSimpleName(), null))
                                                .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_LOG_STAT, LogEngineConstants.EVENT_STAT_KEY, MotadataConfigUtil.getLogParsers(), LogStatCalculator.class.getCanonicalName(), true, false), LogStatCalculator.class.getSimpleName(), null))
                                                .compose(future -> Bootstrap.startWorkerEngine(new TCPLogListener(), TCPLogListener.class.getSimpleName(), null))
                                                .compose(future -> Bootstrap.startWorkerEngine(new UDPLogListener(), UDPLogListener.class.getSimpleName(), null))
                                                .compose(future -> Bootstrap.startWorkerEngine(new UDPLogCollector(), UDPLogCollector.class.getSimpleName(), null))
                                                .compose(future -> Bootstrap.startEngine(new LogCollector(), LogCollector.class.getSimpleName(), null))
                                                .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_LOG_FORWARDER, EventBusConstants.EVENT_SOURCE, MotadataConfigUtil.getLogParsers(), LogForwarder.class.getCanonicalName(), true, false, EventRouter.ROUND_ROBIN), LogForwarder.class.getSimpleName(), null))
                                                .onComplete(asyncResult ->
                                                {
                                                    if (asyncResult.succeeded())
                                                    {
                                                        LOGGER.info("howdy, log engine started successfully... :)");
                                                    }
                                                    else
                                                    {
                                                        Bootstrap.stop(asyncResult.cause());
                                                    }
                                                });
                                    }
                                    else
                                    {
                                        // 1. Log Processor
                                        // 2. Log Agent Processor
                                        // 3. TCP Listener
                                        // 4. UDP Listener
                                        Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_LOG, EventBusConstants.EVENT_SOURCE, MotadataConfigUtil.getLogParsers(), LogParser.class.getCanonicalName(), true, true, EventRouter.ROUND_ROBIN), LogParser.class.getSimpleName(), null)
//                                                .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_LOG_PATTERN, EventBusConstants.EVENT_SOURCE, MotadataConfigUtil.getLogPatternBuilders(), LogPatternBuilder.class.getCanonicalName(), true, false, true), LogPatternBuilder.class.getSimpleName(), null))
                                                .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_LOG_STAT, LogEngineConstants.EVENT_STAT_KEY, MotadataConfigUtil.getLogParsers(), LogStatCalculator.class.getCanonicalName(), true, false), LogStatCalculator.class.getSimpleName(), null))
                                                .compose(future -> Bootstrap.startWorkerEngine(new TCPLogListener(), TCPLogListener.class.getSimpleName(), null))
                                                .compose(future -> Bootstrap.startWorkerEngine(new UDPLogListener(), UDPLogListener.class.getSimpleName(), null))
                                                .compose(future -> Bootstrap.startWorkerEngine(new UDPLogCollector(), UDPLogCollector.class.getSimpleName(), null))
                                                .compose(future -> Bootstrap.startEngine(new LogCollector(), LogCollector.class.getSimpleName(), null))
                                                .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_LOG_FORWARDER, EventBusConstants.EVENT_SOURCE, MotadataConfigUtil.getLogParsers(), LogForwarder.class.getCanonicalName(), true, false, EventRouter.ROUND_ROBIN), LogForwarder.class.getSimpleName(), null))
                                                .onComplete(asyncResult ->
                                                {
                                                    if (asyncResult.succeeded())
                                                    {
                                                        LOGGER.info("howdy, log engine started successfully... :)");
                                                    }
                                                    else
                                                    {
                                                        Bootstrap.stop(asyncResult.cause());
                                                    }
                                                });
                                    }
                                }

                                if (LicenseUtil.FLOW_ENABLED.get()) //check for flow module...
                                {
                                    Bootstrap.startEngine(new FlowListener(), FlowListener.class.getSimpleName(), null)
                                            .compose(future -> Bootstrap.startWorkerEngine(new FlowCacheProcessor(), FlowCacheProcessor.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_FLOW, "peer_ip_src", MotadataConfigUtil.getFlowProcessors(), FlowProcessor.class.getCanonicalName(), true, true, EventRouter.ROUND_ROBIN), FlowProcessor.class.getSimpleName(), null))
                                            .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EVENT_FLOW_STAT, EVENT_SOURCE, MotadataConfigUtil.getFlowProcessors(), FlowStatCalculator.class.getCanonicalName(), false), FlowStatCalculator.class.getSimpleName(), null))
                                            .onComplete(result ->
                                            {
                                                if (result.succeeded())
                                                {
                                                    LOGGER.info("howdy, flow engine started successfully... :)");
                                                }
                                                else
                                                {
                                                    Bootstrap.stop(result.cause());
                                                }
                                            });
                                }

                                var promise = Promise.promise();

                                futures.add(promise.future());
                                // every 10 sec update log/volume usage data to config db
                                Bootstrap.vertx().setPeriodic(10000, timer -> LicenseUtil.writeLicenseUsage());

                                Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_EVENT_POLICY_QUALIFY, EventBusConstants.EVENT_SOURCE,
                                                MotadataConfigUtil.getEventPolicyQualifierWorkers() * (LicenseUtil.LOG_ENABLED.get() && LicenseUtil.FLOW_ENABLED.get() ? 2 : 1), EventPolicyQualifier.class.getCanonicalName(), true, true, EventRouter.ROUND_ROBIN), EventPolicyQualifier.class.getSimpleName(), null)
                                        .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_EVENT_POLICY, PolicyEngineConstants.POLICY_ID,
                                                MotadataConfigUtil.getEventPolicyWorkers() * (LicenseUtil.LOG_ENABLED.get() && LicenseUtil.FLOW_ENABLED.get() ? 2 : 1), EventPolicyInspector.class.getCanonicalName(), true), EventPolicyInspector.class.getSimpleName(), null))
                                        .onComplete(result ->
                                        {
                                            if (result.succeeded())
                                            {
                                                promise.complete();

                                                LOGGER.info("howdy, event policy inspection engine started successfully... :)");
                                            }
                                            else
                                            {
                                                Bootstrap.stop(result.cause());
                                            }
                                        });

                                vertx().eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
                                {
                                    if (message.body() != null && EventBusConstants.ChangeNotificationType.valueOf(message.body().getString(CHANGE_NOTIFICATION_TYPE)) == ChangeNotificationType.UPDATE_CACHE)
                                    {
                                        if (EventPolicyCacheStore.getStore().dirty())
                                        {
                                            EventPolicyCacheStore.getStore().setDirty(false);

                                            HAConstants.notifyObserver(new JsonObject().put(CACHE_NAME, PolicyEngineConstants.EVENT_POLICY_TRIGGER_TICKS).put(RESULT, vertx().fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + PolicyEngineConstants.EVENT_POLICY_TRIGGER_TICKS)));
                                        }

                                        if (EventOrdinalCacheStore.getStore().dirty())
                                        {
                                            EventOrdinalCacheStore.getStore().setDirty(false);

                                            HAConstants.notifyObserver(new JsonObject().put(CACHE_NAME, LogEngineConstants.EVENT_ORDINALS).put(RESULT, vertx().fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + LogEngineConstants.EVENT_ORDINALS)));
                                        }

                                        if (TrapCacheStore.getStore().dirty())
                                        {
                                            TrapCacheStore.getStore().setDirty(false);

                                            HAConstants.notifyObserver(new JsonObject().put(CACHE_NAME, SNMP_TRAP_ACKS).put(RESULT, vertx().fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + SNMP_TRAP_ACKS)));
                                        }

                                        if (LogPatternCacheStore.getStore().dirty())
                                        {
                                            LogPatternCacheStore.getStore().setDirty(false);

                                            HAConstants.notifyObserver(new JsonObject().put(CACHE_NAME, LogEngineConstants.LOG_PATTERNS).put(RESULT, vertx().fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + LogEngineConstants.LOG_PATTERNS)));
                                        }
                                    }
                                }).exceptionHandler(LOGGER::error);


                                //as we have policy suppressed for event and metric both so as we are publishing event from scheduler so once policy verticles are deployed than initializing all jobs..
                                Future.join(futures).onComplete(result ->
                                {
                                    if (result.succeeded())
                                    {
                                        LogUtil.resetLogLevel(MotadataConfigUtil.getLogLevelResetTimerSeconds());

                                        JobScheduler.init();
                                    }
                                    else
                                    {
                                        Bootstrap.stop(result.cause());
                                    }
                                });
                            }
                            catch (Exception exception)
                            {
                                Bootstrap.stop(exception);
                            }
                        }

                        else
                        {
                            Bootstrap.stop(asyncResponse.cause());
                        }
                    });
        }
        catch (Exception exception)
        {
            stop(exception);
        }
    }

    private Future<Void> startService(List<String> arguments, String serviceName)
    {
        var promise = Promise.<Void>promise();

        try
        {
            new Thread(() -> ProcessUtil.start(serviceName, arguments, GlobalConstants.CURRENT_DIR).onComplete(result ->
                    {
                        if (result.succeeded())
                        {
                            promise.complete();
                        }
                        else
                        {
                            promise.fail(result.cause());
                        }
                    }
            ), serviceName).start();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    private Future<Void> sync()
    {
        var promise = Promise.<Void>promise();

        vertx().setPeriodic(10 * 1000, timer ->
        {
            LOGGER.info(String.format("%s server waiting for config sync ...", InstallationMode.PRIMARY.name()));

            if (MotadataConfigUtil.getEnvironmentType().equalsIgnoreCase(ENV_TEST))
            {
                vertx().cancelTimer(timer);

                promise.complete();
            }
            else
            {
                if (PortUtil.isConnected(MotadataConfigUtil.getRemoteEventObserverPublisher(), MotadataConfigUtil.getMotadataObserverEventPublisherPort(), 3))
                {
                    if (syncDone())
                    {
                        vertx().cancelTimer(timer);

                        LOGGER.info(String.format("%s server config sync completed...", InstallationMode.PRIMARY.name()));

                        promise.complete();
                    }
                }
                else
                {
                    LOGGER.warn(String.format("Observer server is down , address : %s ", MotadataConfigUtil.getRemoteEventObserverPublisher() + ":" + MotadataConfigUtil.getMotadataObserverEventPublisherPort()));
                }
            }
        });

        return promise.future();
    }

    public void runCompatibilityCheck()
    {
        LOGGER.info("executing compatibility check");

        vertx().executeBlocking(handler ->
        {
            for (var artifact : ArtifactConfigStore.getStore().flatMap().values())
            {
                try
                {
                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(artifact);
                    }

                    var artifactType = artifact.getString(MotadataApp.ARTIFACT_TYPE);

                    // skip health agent and standalone db from this check
                    if (!artifact.getString(MotadataApp.ARTIFACT_ID, EMPTY_VALUE).equalsIgnoreCase(Bootstrap.getRegistrationId()))
                    {
                        if (!MotadataConfigUtil.compatible(artifactType, artifact.getString(MotadataApp.ARTIFACT_VERSION)))
                        {
                            LOGGER.info(String.format("artifact type %s with uuid %s is not compatible, artifact : %s", artifactType, artifact.getString(MotadataApp.ARTIFACT_ID), artifact.encode()));

                            artifact.put(ERROR_CODE, ERROR_CODE_NOT_COMPATIBLE);

                            if (artifactType.equalsIgnoreCase(BootstrapType.AGENT.name()))
                            {
                                var agent = AgentConfigStore.getStore().getItem(artifact.getLong(ID));

                                if (agent != null && !agent.isEmpty())
                                {
                                    if (AgentCacheStore.getStore().getItem(agent.getLong(ID)).containsKey(HEARTBEAT_STATE) && AgentCacheStore.getStore().getItem(agent.getLong(ID)).getString(HEARTBEAT_STATE).equalsIgnoreCase(NMSConstants.STATE_RUNNING))
                                    {
                                        LOGGER.info(String.format("stopping  %s", artifactType));

                                        Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, new JsonObject().put(EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC)
                                                .put(EVENT_TYPE, EVENT_AGENT_STOP).put(AGENT_UUID, artifact.getString(MotadataApp.ARTIFACT_ID))
                                                .put(ID, artifact.getLong(ID)));
                                    }
                                }
                            }
                            else
                            {
                                var item = RemoteEventProcessorConfigStore.getStore().getItem(artifact.getLong(ID));

                                if (item != null && !item.isEmpty())
                                {
                                    if (RemoteEventProcessorCacheStore.getStore().getItem(item.getLong(ID)).containsKey(HEARTBEAT_STATE) && RemoteEventProcessorCacheStore.getStore().getItem(item.getLong(ID)).getString(HEARTBEAT_STATE).equalsIgnoreCase(NMSConstants.STATE_RUNNING))
                                    {
                                        LOGGER.info(String.format("stopping  %s", artifactType));

                                        Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, new JsonObject().put(EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC)
                                                .put(EVENT_TYPE, EventBusConstants.EVENT_REMOTE_PROCESSOR_STOP).put(REMOTE_EVENT_PROCESSOR_UUID, artifact.getString(MotadataApp.ARTIFACT_ID)).put(SYSTEM_BOOTSTRAP_TYPE, artifactType.toUpperCase())
                                                .put(ID, artifact.getLong(ID)));
                                    }
                                }
                            }

                            Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_ARTIFACT.name()).put(EVENT_CONTEXT, artifact).put(EVENT_COPY_REQUIRED, false));
                        }
                        else if (MotadataConfigUtil.upgradable(MotadataConfigUtil.getVersion(), artifact.getString(MotadataApp.ARTIFACT_VERSION)))
                        {
                            LOGGER.debug(String.format("upgrade available for artifact type %s with uuid %s, artifact : %s", artifactType, artifact.getString(MotadataApp.ARTIFACT_ID), artifact.encode()));

                            Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_ARTIFACT.name()).put(EVENT_CONTEXT, artifact.put(ERROR_CODE, ERROR_CODE_UPGRADE_REQUIRED)).put(EVENT_COPY_REQUIRED, false));
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }
        });
    }
}
