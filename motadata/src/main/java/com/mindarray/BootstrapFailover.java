/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray;

import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.datastore.DatastoreManager;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.db.ConfigDBInitializer;
import com.mindarray.db.ConfigDBServiceProvider;
import com.mindarray.eventbus.*;
import com.mindarray.ha.Observer;
import com.mindarray.util.*;
import io.vertx.core.Future;
import io.vertx.core.Promise;

import java.util.ArrayList;

import static com.mindarray.Bootstrap.startEngine;
import static com.mindarray.Bootstrap.stop;
import static com.mindarray.GlobalConstants.INSTALLATION_DATE;

public class BootstrapFailover
{
    private static final Logger LOGGER = new Logger(BootstrapFailover.class, GlobalConstants.MOTADATA_SYSTEM, "Bootstrap Failover");

    public void start()
    {
        try
        {
            if (PortUtil.isConnected(MotadataConfigUtil.getRemoteEventObserverPublisher(), MotadataConfigUtil.getMotadataObserverEventPublisherPort(), 3))
            {
                DatastoreConstants.loadConfigs();

                Bootstrap.logSystemConfig();

                ProcessUtil.setProcessors();

                EventBusConstants.setRemoteEventProcessorRegistrationId();

                startEngine(new ConfigDBServiceProvider(), ConfigDBServiceProvider.class.getSimpleName(), null)
                        .compose(future -> startEngine(new RemoteEventForwarder(MotadataConfigUtil.getMotadataObserverEventSubscriberPort(), MotadataConfigUtil.getRemoteEventObserverSubscriber()), RemoteEventForwarder.class.getSimpleName() + " " + MotadataConfigUtil.getMotadataObserverEventSubscriberPort(), null))
                        .compose(future -> startEngine(new RemoteEventSubscriber(MotadataConfigUtil.getMotadataObserverEventPublisherPort(), MotadataConfigUtil.getRemoteEventObserverPublisher()), RemoteEventSubscriber.class.getSimpleName() + " " + MotadataConfigUtil.getMotadataObserverEventPublisherPort(), null))
                        .compose(future -> startEngine(new DatastoreManager(), DatastoreManager.class.getSimpleName(), null))
                        .onComplete(promiseResult ->
                        {
                            if (promiseResult.succeeded())
                            {
                                var asyncFuture = Promise.<Boolean>promise();

                                Bootstrap.configDBService().getOne(ConfigDBConstants.COLLECTION_SYSTEM,
                                        result ->
                                        {
                                            if (result.succeeded() && result.result().containsKey(INSTALLATION_DATE))
                                            {
                                                asyncFuture.complete(false);
                                            }
                                            else
                                            {
                                                asyncFuture.complete(true);
                                            }
                                        });

                                asyncFuture.future().onComplete(asyncResponse ->
                                        LicenseUtil.init(asyncResponse.result()).onComplete(asyncPromiseResult ->
                                        {
                                            if (asyncPromiseResult.succeeded())
                                            {
                                                try
                                                {
                                                    LOGGER.info("license verified successfully...");

                                                    LicenseUtil.load(asyncPromiseResult.result());

                                                    ConfigDBInitializer.init().onComplete(futureResult ->
                                                    {
                                                        if (futureResult.succeeded())
                                                        {
                                                            var asyncPromise = Promise.promise();

                                                            // init all stores...
                                                            Bootstrap.initStores().onComplete(response ->
                                                            {
                                                                if (response.succeeded())
                                                                {
                                                                    LOGGER.info("all config/cache stores loaded successfully...");

                                                                    asyncPromise.complete();
                                                                }
                                                                else
                                                                {
                                                                    asyncPromise.fail(response.cause());
                                                                }
                                                            });

                                                            asyncPromise.future().onComplete(handler ->
                                                            {
                                                                if (handler.succeeded())
                                                                {
                                                                    var futures = new ArrayList<Future<Object>>();

                                                                    var forwarderPort = CommonUtil.getEventPublisherPort();

                                                                    for (var index = 0; index < MotadataConfigUtil.getEventPublishers(); index++)
                                                                    {
                                                                        var promise = Promise.promise();

                                                                        futures.add(promise.future());

                                                                        Bootstrap.startEngine(new RemoteEventForwarder(forwarderPort + index), RemoteEventForwarder.class.getSimpleName() + " " + (forwarderPort + index), null).onComplete(asyncResult ->
                                                                        {
                                                                            if (asyncResult.succeeded())
                                                                            {
                                                                                promise.complete();
                                                                            }
                                                                            else
                                                                            {
                                                                                promise.fail(asyncResult.cause());
                                                                            }
                                                                        });
                                                                    }

                                                                    Future.join(futures).onComplete(asyncResult ->
                                                                    {
                                                                        if (asyncResult.succeeded())
                                                                        {
                                                                            var asyncResults = new ArrayList<Future<Object>>();

                                                                            var subscriberPort = CommonUtil.getEventSubscriberPort();

                                                                            for (var index = 0; index < MotadataConfigUtil.getEventSubscribers(); index++)
                                                                            {
                                                                                var promise = Promise.promise();

                                                                                asyncResults.add(promise.future());

                                                                                Bootstrap.startEngine(new RemoteEventSubscriber(subscriberPort + index), RemoteEventSubscriber.class.getSimpleName() + " " + (subscriberPort + index), null).onComplete(voidAsyncResult ->
                                                                                {
                                                                                    if (voidAsyncResult.succeeded())
                                                                                    {
                                                                                        promise.complete();
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        promise.fail(voidAsyncResult.cause());
                                                                                    }
                                                                                });
                                                                            }

                                                                            Future.join(asyncResults).onComplete(asyncFutureResult ->
                                                                                    startEngine(new Observer(), Observer.class.getCanonicalName(), null)
                                                                                            .compose(future -> startEngine(new RemoteEventChangeNotificationHandler(), RemoteEventChangeNotificationHandler.class.getSimpleName(), null))
                                                                                            .compose(future -> startEngine(new LocalEventChangeNotificationHandler(), LocalEventChangeNotificationHandler.class.getSimpleName(), null))
                                                                                            .compose(future -> startEngine(new RemoteSessionManager(), RemoteSessionManager.class.getSimpleName(), null))
                                                                                            .compose(future -> Bootstrap.startWorkerEngine(new JVMStatUtil(), JVMStatUtil.class.getSimpleName(), null))
                                                                                            .compose(future -> Bootstrap.startWorkerEngine(new DiagnosticUtil(), DiagnosticUtil.class.getSimpleName(), null))
                                                                                            .onComplete(result ->
                                                                                            {
                                                                                                try
                                                                                                {
                                                                                                    if (result.succeeded())
                                                                                                    {
                                                                                                        EventBusConstants.register(new CipherUtil());

                                                                                                        LogUtil.resetLogLevel(MotadataConfigUtil.getLogLevelResetTimerSeconds());

                                                                                                        LOGGER.info(String.format(InfoMessageConstants.MOTADATA_ENGINE_START_SUCCEEDED, Bootstrap.getInstallationMode()));
                                                                                                    }
                                                                                                    else
                                                                                                    {
                                                                                                        LOGGER.error(result.cause());

                                                                                                        stop(result.cause());
                                                                                                    }
                                                                                                }
                                                                                                catch (Exception exception)
                                                                                                {
                                                                                                    LOGGER.error(exception);
                                                                                                }
                                                                                            }));


                                                                        }
                                                                        else
                                                                        {
                                                                            LOGGER.error(asyncResult.cause());

                                                                            System.exit(0);
                                                                        }
                                                                    });
                                                                }
                                                                else
                                                                {
                                                                    Bootstrap.stop(handler.cause());
                                                                }
                                                            });
                                                        }
                                                        else
                                                        {
                                                            Bootstrap.stop(futureResult.cause());
                                                        }
                                                    });
                                                }
                                                catch (Exception exception)
                                                {
                                                    stop(exception);
                                                }
                                            }
                                            else
                                            {
                                                stop(asyncPromiseResult.cause());
                                            }
                                        }));
                            }
                            else
                            {
                                Bootstrap.stop(promiseResult.cause());
                            }
                        });
            }
            else
            {
                stop("motadata observer server is not reachable...");

                System.exit(0);
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
