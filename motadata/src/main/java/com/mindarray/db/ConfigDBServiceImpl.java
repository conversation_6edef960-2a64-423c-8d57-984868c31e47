/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.db;

import com.mindarray.GlobalConstants;
import com.mindarray.api.BackupProfile;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AsyncResult;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import io.vertx.core.Vertx;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.h2.mvstore.MVStore;
import org.zeroturnaround.zip.ZipUtil;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.EVENT_AUDIT;

public class ConfigDBServiceImpl implements ConfigDBService
{
    private static final Logger LOGGER = new Logger(ConfigDBServiceImpl.class, GlobalConstants.MOTADATA_DB, "Config DB Service");
    private final Vertx vertx;
    private MVStore configDB;

    ConfigDBServiceImpl(Vertx vertx, Handler<AsyncResult
            <ConfigDBService>> handler)
    {

        this.vertx = vertx;

        vertx.executeBlocking(future ->
                {
                    try (var resourceAsStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("db.auth"))
                    {
                        var buffer = Buffer.buffer(IOUtils.toString(Objects.requireNonNull(resourceAsStream), StandardCharsets.UTF_8));

                        configDB = new MVStore.Builder().fileName(GlobalConstants.CURRENT_DIR +
                                        GlobalConstants.PATH_SEPARATOR +
                                        GlobalConstants.CONFIG_DIR +
                                        GlobalConstants.PATH_SEPARATOR +
                                        "motadata")
                                .encryptionKey(new String(Base64.getDecoder().decode(buffer.getBytes())).toCharArray())
                                .backgroundExceptionHandler((thread, exception) -> LOGGER.error(exception))
                                .autoCommitDisabled()
                                .open();

                        future.complete();
                    }
                    catch (Exception exception)
                    {
                        future.fail(exception);

                        LOGGER.error(exception);
                    }
                },
                true,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(this));
                    }
                    else
                    {
                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

    }

    @Override
    public ConfigDBService save(String collection, JsonObject document, String user, String remoteIP, Handler<AsyncResult<Long>> handler)
    {
        vertx.<Long>executeBlocking(future ->
                {
                    var id = document.containsKey(GlobalConstants.ID) ? document.getLong(GlobalConstants.ID) : CommonUtil.newId();

                    if (!document.containsKey(ConfigDBConstants.FIELD_TYPE))
                    {
                        document.put(ConfigDBConstants.FIELD_TYPE, ConfigDBConstants.ENTITY_TYPE_USER);
                    }

                    configDB.openMap(collection).put(id, document.put(GlobalConstants.ID, id).encode());

                    configDB.commit();

                    future.complete(id);
                },

                false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_SYSTEM) && !remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            HAConstants.sync(collection, null, document, user, remoteIP, HAConstants.HASyncOperation.SAVE.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());
                        }

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            notify(remoteIP, user, REQUEST_CREATE, collection, Boolean.TRUE, null, null, document);
                        }
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            notify(remoteIP, user, REQUEST_CREATE, collection, Boolean.FALSE, result.cause().getMessage(), null, null);
                        }
                    }
                });

        return this;
    }

    @Override
    public ConfigDBService saveAll(String collection, JsonArray documents, String user, String remoteIP, Handler<AsyncResult<JsonArray>> handler)
    {
        var createdItems = new JsonArray();

        vertx.<JsonArray>executeBlocking(future ->
                {
                    var documentIds = new JsonArray();

                    for (var index = 0; index < documents.size(); index++)
                    {
                        var document = documents.getJsonObject(index);

                        if (!document.containsKey(ConfigDBConstants.FIELD_TYPE))
                        {
                            document.put(ConfigDBConstants.FIELD_TYPE, ConfigDBConstants.ENTITY_TYPE_USER);
                        }

                        var id = document.containsKey(GlobalConstants.ID) ? document.getLong(GlobalConstants.ID) : CommonUtil.newId();

                        configDB.openMap(collection).put(id, document.put(GlobalConstants.ID, id).encode());

                        documentIds.add(id);

                        createdItems.add(document);
                    }

                    configDB.commit();

                    future.complete(documentIds);
                },
                false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_SYSTEM) && !remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            HAConstants.sync(collection, createdItems, user, HAConstants.HASyncOperation.SAVE_ALL.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());
                        }

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            for (var index = 0; index < createdItems.size(); index++)
                            {
                                notify(remoteIP, user, REQUEST_CREATE, collection, Boolean.TRUE, null, null, createdItems.getJsonObject(index));
                            }
                        }
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));

                        notify(remoteIP, user, REQUEST_CREATE, collection, Boolean.FALSE, result.cause().getMessage(), null, null);
                    }
                });

        return this;
    }

    @Override
    public ConfigDBService get(String collection, JsonObject query, Handler<AsyncResult<JsonArray>> handler)
    {
        vertx.<JsonArray>executeBlocking(future ->
                {
                    var items = new JsonArray();

                    if (query != null && !query.isEmpty())
                    {
                        if (query.getString(ConfigDBConstants.FIELD_NAME).equalsIgnoreCase(GlobalConstants.ID))
                        {
                            if (query.getValue(VALUE) instanceof JsonArray)
                            {
                                var ids = query.getJsonArray(VALUE);

                                configDB.openMap(collection).forEach((key, value) ->
                                {
                                    if (ids.contains(key))
                                    {
                                        items.add(transform(new JsonObject(CommonUtil.getString(value))));
                                    }
                                });
                            }

                            else
                            {
                                items.add(transform(new JsonObject(CommonUtil.getString(configDB.openMap(collection).get(query.getLong(VALUE))))));
                            }
                        }
                        else
                        {
                            if (query.getValue(VALUE) instanceof JsonArray)
                            {
                                configDB.openMap(collection).forEach((key, value) ->
                                {
                                    var document = new JsonObject(CommonUtil.getString(value));

                                    if (document.containsKey(query.getString(ConfigDBConstants.FIELD_NAME))
                                            && query.getJsonArray(VALUE).contains(document.getValue(query.getString(ConfigDBConstants.FIELD_NAME))))
                                    {
                                        items.add(transform(document));
                                    }
                                });
                            }
                            else
                            {
                                configDB.openMap(collection).forEach((key, value) ->
                                {
                                    var document = new JsonObject(CommonUtil.getString(value));

                                    if (document.containsKey(query.getString(ConfigDBConstants.FIELD_NAME))
                                            && document.getValue(query.getString(ConfigDBConstants.FIELD_NAME)).equals(query.getValue(VALUE)))
                                    {
                                        items.add(transform(document));
                                    }
                                });
                            }
                        }
                    }

                    else
                    {
                        configDB.openMap(collection).values().forEach(value -> items.add(transform(new JsonObject(CommonUtil.getString(value)))));
                    }
                    future.complete(items);

                },
                true,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });
        return this;
    }

    private JsonObject transform(JsonObject document)
    {
        var item = new JsonObject();

        for (var entry : document.getMap().entrySet())
        {
            if (entry.getValue() instanceof List)
            {
                item.put(entry.getKey(), new JsonArray((List) entry.getValue()));
            }
            else if (entry.getValue() instanceof Map)
            {
                item.put(entry.getKey(), new JsonObject((Map) entry.getValue()));
            }
            else
            {
                item.put(entry.getKey(), entry.getValue());
            }
        }

        return item;
    }

    @Override
    public ConfigDBService getById(String collection, long id, Handler<AsyncResult<JsonObject>> handler)
    {

        getOneByQuery(collection, new JsonObject().put(ConfigDBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, id), result ->
        {
            if (result.succeeded())
            {
                handler.handle(Future.succeededFuture(result.result()));
            }
            else
            {
                LOGGER.error(result.cause());

                handler.handle(Future.failedFuture(result.cause()));
            }
        });

        return this;
    }

    @Override
    public ConfigDBService getOneByQuery(String collection, JsonObject query, Handler<AsyncResult<JsonObject>> handler)
    {
        get(collection, query, result ->
        {
            if (result.succeeded())
            {
                if (!result.result().isEmpty())
                {
                    handler.handle(Future.succeededFuture(result.result().getJsonObject(0)));
                }
                else
                {
                    handler.handle(Future.succeededFuture(new JsonObject()));
                }
            }
            else
            {
                LOGGER.error(result.cause());

                handler.handle(Future.failedFuture(result.cause()));
            }
        });

        return this;
    }

    @Override
    public ConfigDBService getOne(String collection, Handler<AsyncResult<JsonObject>> handler)
    {
        vertx.<JsonObject>executeBlocking(future ->
                {
                    if (!configDB.openMap(collection).isEmpty())
                    {
                        var id = CommonUtil.getLong(configDB.openMap(collection).firstKey());

                        future.complete(new JsonObject(CommonUtil.getString(configDB.openMap(collection).get(id))));
                    }
                    else
                    {
                        future.complete(new JsonObject());
                    }
                },
                false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(transform(result.result())));
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

        return this;
    }

    @Override
    public ConfigDBService getAll(String collection, Handler<AsyncResult<JsonArray>> handler)
    {
        get(collection, null, result ->
        {
            if (result.succeeded())
            {
                handler.handle(Future.succeededFuture(result.result()));
            }
            else
            {
                LOGGER.error(result.cause());

                handler.handle(Future.failedFuture(result.cause()));
            }
        });

        return this;

    }

    @Override
    public ConfigDBService delete(String collection, JsonObject query, String user, String remoteIP, Handler<AsyncResult<JsonArray>> handler)
    {
        var items = new JsonArray();

        vertx.<JsonArray>executeBlocking(future ->
                {
                    var documents = new ArrayList<Long>();

                    if (query.getString(ConfigDBConstants.FIELD_NAME).equalsIgnoreCase(GlobalConstants.ID))
                    {
                        var id = query.getLong(VALUE);

                        items.add(new JsonObject(configDB.openMap(collection).get(id).toString()));

                        configDB.openMap(collection).remove(id);

                        documents.add(id);
                    }
                    else
                    {
                        configDB.openMap(collection).forEach((key, value) ->
                        {
                            var item = new JsonObject(CommonUtil.getString(value));

                            if (!item.getString(ConfigDBConstants.FIELD_TYPE).equalsIgnoreCase(ConfigDBConstants.ENTITY_TYPE_SYSTEM) && item.containsKey(query.getString(ConfigDBConstants.FIELD_NAME))
                                    && item.getValue(query.getString(ConfigDBConstants.FIELD_NAME)).equals(query.getValue(VALUE)))
                            {
                                {
                                    documents.add(CommonUtil.getLong(key));
                                }
                            }
                        });

                        documents.forEach(document ->
                        {
                            items.add(new JsonObject(configDB.openMap(collection).get(document).toString()));

                            configDB.openMap(collection).remove(document);
                        });
                    }

                    configDB.commit();

                    future.complete(new JsonArray(documents));
                },

                true,

                result ->
                {
                    if (result.succeeded())
                    {
                        if (!remoteIP.equalsIgnoreCase(MOTADATA_SYSTEM) && !remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            HAConstants.sync(collection, query, null, user, remoteIP, HAConstants.HASyncOperation.DELETE.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());
                        }

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            notify(remoteIP, user, REQUEST_DELETE, collection, Boolean.TRUE, null, null, items.isEmpty() ? null : items.getJsonObject(0));
                        }

                        handler.handle(Future.succeededFuture(result.result()));
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            notify(remoteIP, user, REQUEST_DELETE, collection, Boolean.TRUE, result.cause().getMessage(), null, null);
                        }

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

        return this;
    }

    @Override
    public ConfigDBService deleteAll(String collection, JsonObject query, String user, String remoteIP, Handler<AsyncResult<JsonArray>> handler)
    {
        var items = new JsonArray();

        vertx.<JsonArray>executeBlocking(future ->
                {

                    var documents = new ArrayList<Long>();

                    if (query.getString(ConfigDBConstants.FIELD_NAME).equalsIgnoreCase(GlobalConstants.ID))
                    {
                        query.getJsonArray(VALUE).forEach(value ->
                        {
                            var id = CommonUtil.getLong(value);

                            items.add(new JsonObject(configDB.openMap(collection).get(id).toString()));

                            configDB.openMap(collection).remove(id);

                            documents.add(id);
                        });
                    }
                    else
                    {
                        var done = new AtomicBoolean(false);

                        configDB.openMap(collection).forEach((key, val) ->
                        {
                            var item = new JsonObject(CommonUtil.getString(val));

                            if (!item.getString(ConfigDBConstants.FIELD_TYPE).equalsIgnoreCase(ConfigDBConstants.ENTITY_TYPE_SYSTEM))
                            {
                                var values = (JsonArray) query.getValue(VALUE);

                                done.set(false);

                                values.stream().takeWhile(value -> !done.get()).forEach(value ->
                                {
                                    if (item.containsKey(query.getString(ConfigDBConstants.FIELD_NAME))
                                            && item.getValue(query.getString(ConfigDBConstants.FIELD_NAME)).equals(value))
                                    {
                                        documents.add(CommonUtil.getLong(key));

                                        done.set(true);
                                    }
                                });
                            }
                        });

                        documents.forEach(document ->
                        {
                            items.add(new JsonObject(configDB.openMap(collection).get(document).toString()));

                            configDB.openMap(collection).remove(document);
                        });
                    }

                    configDB.commit();

                    future.complete(new JsonArray(documents));
                },

                true,

                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_SYSTEM) && !remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            HAConstants.sync(collection, query, null, user, remoteIP, HAConstants.HASyncOperation.DELETE_ALL.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());
                        }

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            for (var index = 0; index < result.result().size(); index++)
                            {
                                notify(remoteIP, user, REQUEST_DELETE, collection, Boolean.TRUE, null, null, items.getJsonObject(index));
                            }
                        }
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            notify(remoteIP, user, REQUEST_DELETE, collection, Boolean.FALSE, result.cause().getMessage(), null, null);
                        }
                    }
                });

        return this;
    }

    @Override
    public ConfigDBService drop(String collection, Handler<AsyncResult<Void>> handler)
    {
        var sync = new AtomicBoolean();

        vertx.<Void>executeBlocking(future ->
        {
            if (configDB.hasMap(collection))
            {
                sync.set(true);

                configDB.removeMap(collection);

                configDB.commit();
            }

            future.complete();

        }, false, result ->
        {
            handler.handle(Future.succeededFuture(result.result()));

            if (sync.get())
            {
                HAConstants.sync(collection, null, null, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, HAConstants.HASyncOperation.DROP.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());

                notify(SYSTEM_REMOTE_ADDRESS, DEFAULT_USER, REQUEST_DROP, collection, Boolean.TRUE, null, null, null);
            }
        });


        return this;
    }

    @Override
    public ConfigDBService update(String collection, JsonObject query, JsonObject document, String user, String remoteIP, Handler<AsyncResult<JsonArray>> handler)
    {
        var oldItem = new JsonObject();

        vertx.<JsonArray>executeBlocking(future ->
                {
                    var documents = new ArrayList<Long>();

                    this.getOneByQuery(collection, query, result ->
                    {
                        if (result.succeeded())
                        {
                            oldItem.mergeIn(result.result());

                            var garbageFields = (JsonArray) document.remove(ConfigDBConstants.GARBAGE_FIELDS);

                            if (query != null && !query.isEmpty())
                            {
                                if (query.getString(ConfigDBConstants.FIELD_NAME).equalsIgnoreCase(GlobalConstants.ID))
                                {
                                    var item = new JsonObject(CommonUtil.getString(configDB.openMap(collection).get(query.getLong(VALUE))));

                                    removeGarbageFields(collection, query.getLong(VALUE), document, item.mergeIn(document), documents, garbageFields);
                                }
                                else
                                {
                                    configDB.openMap(collection).forEach((key, value) ->
                                    {
                                        var item = new JsonObject(CommonUtil.getString(value));

                                        if (item.containsKey(query.getString(ConfigDBConstants.FIELD_NAME))
                                                && item.getValue(query.getString(ConfigDBConstants.FIELD_NAME)).equals(query.getValue(VALUE)))
                                        {
                                            removeGarbageFields(collection, CommonUtil.getLong(key), document, item.mergeIn(document), documents, garbageFields);
                                        }
                                    });
                                }
                            }
                            else
                            {
                                configDB.openMap(collection).forEach((key, value) -> removeGarbageFields(collection, CommonUtil.getLong(key), document, new JsonObject(CommonUtil.getString(value)).mergeIn(document), documents, garbageFields));
                            }

                            configDB.commit();

                            future.complete(new JsonArray(documents));
                        }
                        else
                        {
                            future.fail(result.cause());
                        }
                    });
                },

                false,

                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_SYSTEM) && !remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            HAConstants.sync(collection, query, document, user, remoteIP, HAConstants.HASyncOperation.UPDATE.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());
                        }

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            notify(remoteIP, user, REQUEST_UPDATE, collection, Boolean.TRUE, null, oldItem, document);
                        }
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            notify(remoteIP, user, REQUEST_UPDATE, collection, Boolean.FALSE, result.cause().getMessage(), null, null);
                        }
                    }

                });

        return this;
    }

    //this method is strictly used during configdbinitializer
    @Override
    public ConfigDBService upsert(String collection, JsonObject query, JsonObject document, String user, String remoteIP, Handler<AsyncResult<JsonArray>> handler)
    {
        if (query != null && !query.isEmpty()) //without query it will be assumed that new data insertion is there
        {
            var oldItem = new JsonObject();

            vertx.<JsonArray>executeBlocking(future ->
                    {
                        var documents = new ArrayList<Long>();

                        this.getOneByQuery(collection, query, result ->
                        {
                            var garbageFields = (JsonArray) document.remove(ConfigDBConstants.GARBAGE_FIELDS);

                            if (result.succeeded())
                            {
                                oldItem.mergeIn(result.result());

                                if (query.getString(ConfigDBConstants.FIELD_NAME).equalsIgnoreCase(GlobalConstants.ID))
                                {
                                    var item = configDB.openMap(collection).containsKey(query.getLong(VALUE)) ? new JsonObject(CommonUtil.getString(configDB.openMap(collection).get(query.getLong(VALUE)))) : new JsonObject();

                                    removeGarbageFields(collection, query.getLong(VALUE), document, item.mergeIn(document), documents, garbageFields);
                                }
                                else
                                {
                                    configDB.openMap(collection).forEach((key, value) ->
                                    {
                                        var item = new JsonObject(CommonUtil.getString(value));

                                        if (item.containsKey(query.getString(ConfigDBConstants.FIELD_NAME))
                                                && item.getValue(query.getString(ConfigDBConstants.FIELD_NAME)).equals(query.getValue(VALUE)))
                                        {
                                            removeGarbageFields(collection, CommonUtil.getLong(key), document, item.mergeIn(document), documents, garbageFields);
                                        }

                                    });

                                }

                                configDB.commit();

                                future.complete(new JsonArray(documents));
                            }

                            else
                            {
                                //means id is available but it needs to saved into db
                                if (query.getString(ConfigDBConstants.FIELD_NAME).equalsIgnoreCase(GlobalConstants.ID))
                                {
                                    var item = configDB.openMap(collection).containsKey(query.getLong(VALUE)) ? new JsonObject(CommonUtil.getString(configDB.openMap(collection).get(query.getLong(VALUE)))) : new JsonObject();

                                    removeGarbageFields(collection, query.getLong(VALUE), document, item.mergeIn(document), documents, garbageFields);
                                }

                                configDB.commit();

                                future.complete(new JsonArray(documents));
                            }
                        });
                    },

                    false,

                    result ->
                    {
                        if (result.succeeded())
                        {
                            handler.handle(Future.succeededFuture(result.result()));

                            if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                            {
                                notify(remoteIP, user, REQUEST_UPDATE, collection, Boolean.TRUE, null, oldItem, document);
                            }

                            if (!remoteIP.equalsIgnoreCase(MOTADATA_SYSTEM) && !remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                            {
                                HAConstants.sync(collection, query, document, user, remoteIP, HAConstants.HASyncOperation.UPSERT.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());
                            }
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            handler.handle(Future.failedFuture(result.cause()));

                            if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                            {
                                notify(remoteIP, user, REQUEST_UPDATE, collection, Boolean.FALSE, result.cause().getMessage(), null, null);
                            }
                        }

                    });
        }
        else
        {

            save(collection, document, user, remoteIP, result ->
            {
                if (result.succeeded())
                {
                    handler.handle(Future.succeededFuture(new JsonArray().add(result.result())));
                }

                else
                {
                    LOGGER.error(result.cause());

                    handler.handle(Future.failedFuture(result.cause()));
                }
            });
        }

        return this;
    }

    //It will remove key from item if filter value is not null.
    private void removeGarbageFields(String collection, long id, JsonObject updatedItem, JsonObject item, List<Long> documents, JsonArray garbageFields)
    {
        if (garbageFields != null)
        {
            for (var index = 0; index < garbageFields.size(); index++)
            {
                if (item.containsKey(garbageFields.getString(index)))
                {
                    item.remove(garbageFields.getString(index));

                    updatedItem.put(garbageFields.getString(index), EMPTY_VALUE);
                }
            }
        }

        configDB.openMap(collection).put(id, item.encode());

        documents.add(id);
    }

    @Override
    public ConfigDBService updateAll(String collection, JsonObject query, JsonObject document, String user, String remoteIP, Handler<AsyncResult<JsonArray>> handler)
    {
        var oldItems = new ArrayList<JsonObject>();

        vertx.<JsonArray>executeBlocking(future ->
                {
                    var documents = new ArrayList<Long>();

                    if (query != null && !query.isEmpty())
                    {

                        if (query.getString(ConfigDBConstants.FIELD_NAME).equalsIgnoreCase(GlobalConstants.ID))
                        {
                            query.getJsonArray(VALUE).forEach(value ->
                            {
                                var id = CommonUtil.getLong(value);

                                var item = new JsonObject(CommonUtil.getString(configDB.openMap(collection).get(id)));

                                oldItems.add(new JsonObject().mergeIn(item));

                                configDB.openMap(collection).put(id, item.mergeIn(document).encode());

                                documents.add(id);
                            });

                        }

                        else
                        {

                            var done = new AtomicBoolean(false);

                            configDB.openMap(collection).forEach((key, val) ->
                            {

                                var item = new JsonObject(CommonUtil.getString(val));

                                var id = CommonUtil.getLong(key);

                                var values = (JsonArray) query.getValue(VALUE);

                                done.set(false);

                                values.stream().takeWhile(value -> !done.get()).forEach(value ->
                                {
                                    if (item.containsKey(query.getString(ConfigDBConstants.FIELD_NAME))
                                            && item.getValue(query.getString(ConfigDBConstants.FIELD_NAME)).equals(value))
                                    {
                                        configDB.openMap(collection).put(id, item.mergeIn(document).encode());

                                        documents.add(id);

                                        done.set(true);

                                    }
                                });
                            });

                        }

                    }

                    else
                    {
                        configDB.openMap(collection).forEach((key, value) ->
                        {

                            var item = new JsonObject(CommonUtil.getString(value));

                            var id = CommonUtil.getLong(key);

                            configDB.openMap(collection).put(id, item.mergeIn(document).encode());

                            documents.add(id);

                        });
                    }

                    configDB.commit();

                    future.complete(new JsonArray(documents));
                },
                false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_SYSTEM) && !remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            HAConstants.sync(collection, query, document, user, remoteIP, HAConstants.HASyncOperation.UPDATE_ALL.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());
                        }

                        for (var index = 0; index < result.result().size(); index++)
                        {
                            notify(remoteIP, user, REQUEST_UPDATE, collection, Boolean.TRUE, null, oldItems.get(index), document);
                        }
                    }

                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            notify(remoteIP, user, REQUEST_UPDATE, collection, Boolean.FALSE, result.cause().getMessage(), null, null);
                        }
                    }
                });

        return this;
    }

    @Override
    public ConfigDBService compact()
    {
        try
        {
            vertx.<Void>executeBlocking(future -> configDB.compactFile(3600), false, result ->
            {
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return this;
    }

    @Override
    public void close(Handler<AsyncResult<Void>> handler)
    {
        vertx.<JsonObject>executeBlocking(future ->
                {

                    if (configDB != null)
                    {
                        configDB.commit();

                        configDB.close();
                    }

                    future.complete();

                },

                result ->
                {

                    if (result.succeeded())
                    {

                        handler.handle(Future.succeededFuture());

                    }

                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

    }

    private void notify(String remoteIP, String user, String request, String collection, boolean status, String cause, JsonObject oldProps, JsonObject newProps)
    {
        var event = new JsonObject().put(REMOTE_ADDRESS, remoteIP).put(USER_NAME, user).put(ENTITY_COLLECTION, collection)
                .put(REQUEST, request).put(STATUS, status);

        if (cause != null)
        {
            event.put(ERROR, cause);
        }

        if (oldProps != null && !oldProps.isEmpty())
        {
            event.put(CONFIG_OLD_PROPS, oldProps);
        }

        if (newProps != null && !newProps.isEmpty())
        {
            event.put(CONFIG_UPDATED_PROPS, newProps);
        }

        if (!request.equalsIgnoreCase(REQUEST_DROP))
        {
            vertx.eventBus().send(EVENT_AUDIT, event);
        }
    }

    @Override
    public void backup(JsonObject context, Handler<AsyncResult<JsonObject>> handler)
    {
        vertx.<JsonObject>executeBlocking(future ->
        {
            MVStore store = null;

            try
            {
                var configDir = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR);

                // backup zip : ConfigDB-backup-8.0.0-timestamp.zip
                var backupDir = new File(ConfigDBConstants.CONFIG_DB_BACKUP_PATH + PATH_SEPARATOR + "ConfigDB-backup" + DASH_SEPARATOR + MotadataConfigUtil.getVersion() + DASH_SEPARATOR + context.getLong(ConfigDBConstants.BACKUP_START_TIME));

                backupDir.mkdirs();

                try
                {
                    for (var currentFile : Objects.requireNonNull(configDir.listFiles()))
                    {
                        if (!ConfigDBConstants.BACKUP_EXCLUSION_FILES.contains(currentFile.getName()) && !currentFile.getName().endsWith("position"))
                        {
                            var backupFile = new File(backupDir.getAbsolutePath() + PATH_SEPARATOR + currentFile.getName());

                            if (backupFile.createNewFile())
                            {
                                FileUtils.copyFile(currentFile, backupFile);

                                if (CommonUtil.traceEnabled())
                                {
                                    LOGGER.trace(String.format("%s file copied for backup", currentFile.getName()));
                                }

                            }
                        }
                    }

                    FileUtils.copyFileToDirectory(new File(CURRENT_DIR + PATH_SEPARATOR + VERSION_FILE), backupDir, false);
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

                try (var resourceAsStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("db.auth"))
                {
                    var buffer = Buffer.buffer(IOUtils.toString(Objects.requireNonNull(resourceAsStream), StandardCharsets.UTF_8));

                    store = new MVStore.Builder().fileName(backupDir.getAbsolutePath() + PATH_SEPARATOR + "motadata")
                            .encryptionKey(new String(Base64.getDecoder().decode(buffer.getBytes())).toCharArray())
                            .backgroundExceptionHandler((thread, exception) -> LOGGER.error(exception))
                            .autoCommitDisabled()
                            .open();

                    for (var collection : configDB.getMapNames())
                    {
                        for (var entry : configDB.openMap(collection).entrySet())
                        {
                            store.openMap(collection).put(CommonUtil.getLong(entry.getKey()), CommonUtil.getString(entry.getValue()));
                        }

                        store.commit();

                        if (CommonUtil.traceEnabled())
                        {

                            LOGGER.trace(String.format("collection %s with %s items copied for backup", collection, store.openMap(collection).size()));
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

                if (backupDir.exists() && backupDir.length() > 0)
                {
                    var zipFile = new File(backupDir.getAbsolutePath() + ".zip");

                    ZipUtil.pack(backupDir, zipFile, false);

                    FileUtils.deleteQuietly(backupDir);

                    context.put(GlobalConstants.SRC_FILE_PATH, zipFile.getAbsolutePath());

                    context.put(DatastoreConstants.BACKUP_SIZE_BYTES, zipFile.length() / (1024 * 1024));

                    future.complete(context);
                }
                else
                {
                    future.fail("backup directory does not exist");
                }
            }
            catch (Exception exception)
            {
                future.fail(exception);

                LOGGER.error(exception);
            }
            finally
            {
                if (store != null)
                {
                    store.close();
                }
            }
        }, true, result ->
        {
            if (result.succeeded())
            {
                handler.handle(Future.succeededFuture(result.result()));
            }
            else
            {
                handler.handle(Future.failedFuture(result.cause()));
            }
        });
    }
}
