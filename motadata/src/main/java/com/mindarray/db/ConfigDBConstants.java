/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *     Change Logs:
 *     Date            Author                   Notes
 *     28-Feb-2025     Smit Prajapati           MOTADATA-4956: COLLECTION_TAG_RULE for Rule Based Tagging
 *     5-Mar-2025      Bharat                   MOTADATA-4740: Two factor authentication 2FA
 *     2-Apr-2025      Bharat                   MOTADATA-5637: Domain Mapping in Flow - Initial Version
 *     20-Feb-2025     Pruthviraj               MOTADATA-5285: NetRoute and NetRoute policy added
 *     22-Apr-2025     Bharat                   MOTADATA-5822: Metric Explorer Enhancements
 */

package com.mindarray.db;

import java.util.Set;

import static com.mindarray.GlobalConstants.CURRENT_DIR;
import static com.mindarray.GlobalConstants.PATH_SEPARATOR;

public final class ConfigDBConstants
{
    public static final String COLLECTION_BACKUP_SNAPSHOTS = "backup.snapshots_";
    //Configuration Collections
    public static final String COLLECTION_CONFIGURATION = "configuration";
    public static final String COLLECTION_CONFIG_TEMPLATE = "config.template";
    // collection format -> version_fileType
    public static final String COLLECTION_CONFIG_RESULT = "config.result_%s_%s";
    public static final String COLLECTION_STORAGE_PROFILE = "storage.profile";
    public static final String COLLECTION_BACKUP_PROFILE = "backup.profile";
    public static final Set<String> BACKUP_EXCLUSION_FILES = Set.of("license-last-usage", "columns.json", "horizontal-aggregations.json", "motadata-datastore.json", "vertical-aggregations.json", "motadata.json", "agent.json", "registration.json");
    // backup file paths
    public static final String CONFIG_DB_BACKUP_PATH = CURRENT_DIR + PATH_SEPARATOR + "config-db-backups";
    public static final String DATASTORE_BACKUP_PATH = CURRENT_DIR + PATH_SEPARATOR + "datastore-backups";
    public static final String BACKUP_START_TIME = "backup.start.time";
    public static final String GARBAGE_FIELDS = "garbage.fields";

    //common const
    //config db const...
    public static final String ENTITY_TYPE_SYSTEM = "0";
    public static final String ENTITY_TYPE_USER = "1";
    public static final String FIELD_NAME = "field";
    public static final String FIELD_TYPE = "_type";
    public static final String ENTRIES = "entries";
    public static final String TYPE = "type";
    public static final String RECORDS = "records";
    public static final String INLINE = "inline";
    public static final String FILE = "file";
    public static final String DIRECTORY = "directory";
    public static final String SCRIPT = "script";
    public static final String ENCODED = "encoded";
    //collection config db const...
    public static final String COLLECTION_DNS_RECORD = "dns.record";
    public static final String COLLECTION_SYSTEM = "system";
    public static final String COLLECTION_PLUGIN_ID = "plugin.id";
    public static final String COLLECTION_MAIL_SERVER = "mail.server";
    public static final String COLLECTION_SMS_GATEWAY = "sms.gateway";
    public static final String COLLECTION_REBRANDING = "rebranding";
    public static final String COLLECTION_USER = "user";
    public static final String COLLECTION_TOKEN = "token";
    public static final String COLLECTION_PERSONAL_ACCESS_TOKEN = "personal.access.token";
    public static final String COLLECTION_INTEGRATION_PROFILE = "integration.profile";
    public static final String COLLECTION_USER_ROLE = "user.role";
    public static final String COLLECTION_GROUP = "group";
    public static final String COLLECTION_PASSWORD_POLICY = "password.policy";
    public static final String COLLECTION_REMOTE_EVENT_PROCESSOR = "remote.event.processor";
    public static final String COLLECTION_LDAP_SERVER = "ldap.server";
    public static final String COLLECTION_CREDENTIAL_PROFILE = "credential.profile";
    public static final String COLLECTION_INTEGRATION = "integration";
    public static final String COLLECTION_DISCOVERY = "discovery";
    public static final String COLLECTION_DISCOVERY_RESULT = "discovery.result_";
    public static final String COLLECTION_OBJECT = "object";
    public static final String COLLECTION_SNMP_DEVICE_CATALOG = "snmp.device.catalog";
    public static final String COLLECTION_SNMP_OID_GROUP = "oid.group";
    public static final String COLLECTION_AGENT = "agent";
    public static final String COLLECTION_BUSINESS_HOUR = "business.hour";
    public static final String COLLECTION_SYSTEM_PROCESS = "system.process";
    public static final String COLLECTION_SYSTEM_SERVICE = "system.service";
    public static final String COLLECTION_METRIC = "metric";
    public static final String COLLECTION_METRIC_EXPLORER = "metric.explorer";
    public static final String COLLECTION_SCHEDULER = "scheduler";
    public static final String COLLECTION_METRIC_PLUGIN = "metric.plugin";
    public static final String COLLECTION_RUNBOOK_PLUGIN = "runbook.plugin";
    public static final String COLLECTION_SNMP_TRAP_PROFILE = "snmp.trap.profile";
    public static final String COLLECTION_SNMP_TRAP_FORWARDER = "snmp.trap.forwarder";
    public static final String COLLECTION_SNMP_TRAP_LISTENER = "snmp.trap.listener";
    public static final String COLLECTION_CUSTOM_MONITORING_FIELD = "custom.monitoring.field";
    public static final String COLLECTION_SYSTEM_FILE = "system.file";
    public static final String COLLECTION_PROXY_SERVER = "proxy.server";
    public static final String COLLECTION_TWO_FACTOR_AUTHENTICATION = "two.factor.authentication";
    public static final String COLLECTION_TOPOLOGY_PLUGIN = "topology.plugin";
    public static final String COLLECTION_MAC_SCANNER = "mac.scanner";
    public static final String COLLECTION_DEPENDENCY = "dependency";
    public static final String COLLECTION_ARTIFACT = "artifact";
    public static final String COLLECTION_TAG_RULE = "tag.rule";

    //dependency const
    public static final String COLLECTION_CORRELATION = "correlation";
    public static final String COLLECTION_DEPENDENCY_MAPPER = "dependency.mapper";
    public static final String COLLECTION_DASHBOARD = "dashboard";
    public static final String COLLECTION_WIDGET = "widget";
    public static final String COLLECTION_TEMPLATE = "template";
    //widget dashboard collection
    public static final String COLLECTION_LOG_PARSER = "log.parser";
    public static final String COLLECTION_LOG_COLLECTOR = "log.collector";
    public static final String COLLECTION_LOG_PARSER_PLUGIN = "log.parser.plugin";
    public static final String COLLECTION_EVENT_SOURCE = "event.source";
    public static final String COLLECTION_TAG = "tag";
    public static final String COLLECTION_LOG_FORWARDER = "log.forwarder";
    public static final String COLLECTION_REPORT = "report";
    //log parser collection
    public static final String COLLECTION_FLOW_SAMPLING_RATE = "flow.sampling.rate";
    public static final String COLLECTION_FLOW_IP_GROUP = "flow.ip.group";
    public static final String COLLECTION_FLOW_SETTINGS = "flow.setting";
    public static final String COLLECTION_APPLICATION_MAPPER = "application.mapper";
    public static final String COLLECTION_PROTOCOL_MAPPER = "protocol.mapper";
    public static final String COLLECTION_FLOW_IP_MAPPER = "flow.ip.mapper";
    public static final String COLLECTION_FLOW_AS_MAPPER = "flow.as.mapper";
    public static final String COLLECTION_FLOW_DOMAIN_MAPPER = "flow.domain.mapper";
    public static final String COLLECTION_FLOW_GEOLOCATION_MAPPER = "flow.geolocation.mapper";


    //flow collection
    public static final String COLLECTION_METRIC_POLICY = "metric.policy";
    public static final String COLLECTION_EVENT_POLICY = "event.policy";
    public static final String COLLECTION_DATA_RETENTION_POLICY = "data.retention.policy";
    public static final String COLLECTION_SINGLE_SIGN_ON = "single.sign.on";
    //backup constant Collection
    public static final String BACKUP_END_TIME = "backup.end.time";
    public static final String BACKUP_FILE = "backup.file";
    public static final int MAX_LOCAL_BACKUP_COPIES = 7;

    // Compliance Collections
    public static final String COLLECTION_COMPLIANCE_RULE = "compliance.rule";
    public static final String COLLECTION_COMPLIANCE_BENCHMARK = "compliance.benchmark";
    public static final String COLLECTION_COMPLIANCE_POLICY = "compliance.policy";
    public static final String COLLECTION_COMPLIANCE_WEIGHTED_CALCULATION = "compliance.weighted.calculation";

    // NetRoute Collection
    public static final String COLLECTION_NETROUTE = "netroute";
    public static final String COLLECTION_NETROUTE_POLICY = "netroute.policy";


    //HA Const

    public static final String DB_QUERY = "db.query";
    public static final String DB_DOCUMENTS = "db.documents";

    private ConfigDBConstants()
    {
    }
}
