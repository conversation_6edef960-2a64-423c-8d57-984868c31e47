/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.util;

import io.vertx.core.buffer.Buffer;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;

public class ByteUtil
{
    public static int writeByte(byte[] bytes, byte value, int offset)
    {
        bytes[offset] = value;

        return offset + 1;
    }

    public static int writeBytes(byte[] bytes, byte[] valueBytes, int offset)
    {
        for (var i = 0; i < valueBytes.length; i++)
        {
            bytes[offset] = valueBytes[0];

            offset = offset + 1;
        }

        return offset;
    }

    public static int writeShort(byte[] bytes, short value, int offset)
    {
        bytes[0] = (byte) (value & 0xFF);

        bytes[1] = (byte) ((value >> 8) & 0xFF);

        return offset + 2;
    }

    public static int writeInt(byte[] bytes, int value, int offset)
    {
        bytes[0] = (byte) (value & 0xFF);

        bytes[1] = (byte) ((value >> 8) & 0xFF);

        bytes[2] = (byte) ((value >> 16) & 0xFF);

        bytes[3] = (byte) ((value >> 24) & 0xFF);

        return offset + 4;
    }

    public static int writeLong(byte[] bytes, long value, int offset)
    {
        bytes[0] = (byte) (value & 0xFF);

        bytes[1] = (byte) ((value >> 8) & 0xFF);

        bytes[2] = (byte) ((value >> 16) & 0xFF);

        bytes[3] = (byte) ((value >> 24) & 0xFF);

        bytes[4] = (byte) ((value >> 32) & 0xFF);

        bytes[5] = (byte) ((value >> 40) & 0xFF);

        bytes[6] = (byte) ((value >> 48) & 0xFF);

        bytes[7] = (byte) ((value >> 56) & 0xFF);

        return offset + 8;
    }

    public static int writeFloat(byte[] bytes, float value, int offset)
    {
        var bits = Float.floatToIntBits(value);

        return writeInt(bytes, bits, offset);
    }

    public static int writeDouble(byte[] bytes, double value, int offset)
    {
        var bits = Double.doubleToLongBits(value);

        return writeLong(bytes, bits, offset);
    }


    public static void writeDouble(Buffer buffer, double value)
    {

        var bits = Double.doubleToLongBits(value);

        buffer.appendByte((byte) (bits & 0xFF));
        buffer.appendByte((byte) ((bits >> 8) & 0xFF));
        buffer.appendByte((byte) ((bits >> 16) & 0xFF));
        buffer.appendByte((byte) ((bits >> 24) & 0xFF));
        buffer.appendByte((byte) ((bits >> 32) & 0xFF));
        buffer.appendByte((byte) ((bits >> 40) & 0xFF));
        buffer.appendByte((byte) ((bits >> 48) & 0xFF));
        buffer.appendByte((byte) ((bits >> 56) & 0xFF));
    }

    public static double readDouble(Buffer buffer, int position)
    {

        return ByteBuffer.wrap(buffer.getBytes(position, position + 8)).order(ByteOrder.LITTLE_ENDIAN).getDouble();
    }

    public static long readLongValue(byte[] bytes)
    {
        return (bytes[0] & 0xFF) | ((bytes[1] & 0xFF) << 8) | ((bytes[2] & 0xFF) << 16) | ((long) (bytes[3] & 0xFF) << 24) |
                ((long) (bytes[4] & 0xFF) << 32) | ((long) (bytes[5] & 0xFF) << 40) | ((long) (bytes[6] & 0xFF) << 48) | ((long) (bytes[7] & 0xFF) << 56);
    }

    public static long readLongValue(int position, byte[] bytes)
    {
        return (bytes[position] & 0xFF) | ((bytes[position + 1] & 0xFF) << 8) | ((bytes[position + 2] & 0xFF) << 16) | ((long) (bytes[position + 3] & 0xFF) << 24) |
                ((long) (bytes[position + 4] & 0xFF) << 32) | ((long) (bytes[position + 5] & 0xFF) << 40) | ((long) (bytes[position + 6] & 0xFF) << 48) | ((long) (bytes[position + 7] & 0xFF) << 56);
    }

    public static int readIntValue(byte[] bytes)
    {
        return (bytes[0] & 0xFF) | ((bytes[1] & 0xFF) << 8) | ((bytes[2] & 0xFF) << 16) | ((bytes[3] & 0xFF) << 24);
    }

    public static int readIntValue(int position, byte[] bytes)
    {
        return (bytes[position] & 0xFF) | ((bytes[position + 1] & 0xFF) << 8) | ((bytes[position + 2] & 0xFF) << 16) | ((bytes[position + 3] & 0xFF) << 24);
    }

    public static short readShortValue(byte[] bytes)
    {
        return (short) ((bytes[0] & 0xFF) | ((bytes[1] & 0xFF) << 8));
    }

    public static byte[] readBytes(byte[] bytes, int startIndex, int endIndex)
    {
        var bufferBytes = new byte[endIndex - startIndex];

        for (int index = startIndex, tempIndex = 0; index < endIndex; index++, tempIndex++)
        {
            bufferBytes[tempIndex] = bytes[index];
        }

        return bufferBytes;
    }

    public static String readStringValue(byte[] bytes, int startIndex, int endIndex)
    {
        var result = "";

        result = new String(bytes, startIndex, endIndex, StandardCharsets.UTF_8);

        return result;
    }

}
