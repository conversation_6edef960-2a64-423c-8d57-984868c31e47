/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.util;

import org.apache.spark.util.sketch.CountMinSketch;

public class TopKUtil
{
    private final CountMinSketch countMinSketch;

    private final int topNLimit;

    private final Element[] elements;

    private int size;

    public TopKUtil(int topNLimit)
    {
        countMinSketch = CountMinSketch.create(0.0001, 0.99, 1);

        this.elements = new Element[topNLimit];

        size = 0;

        this.topNLimit = topNLimit;
    }

    public void add(String value)
    {
        countMinSketch.addString(value);

        add(value, countMinSketch.estimateCount(value));
    }

    private void add(String value, long frequency)
    {
        var add = true;

        var sort = false;

        for (var element : elements)
        {
            var valid = true;

            if (element == null)
            {
                valid = false;
            }
            else if (element.getValue().equalsIgnoreCase(value))
            {
                element.setFrequency(frequency);

                add = false;

                sort = true;

                valid = false;
            }

            if (!valid)
            {
                break;
            }
        }

        if (add && size == topNLimit)
        {
            size--;
        }

        if (add)
        {
            elements[size] = new Element(value, frequency);

            size++;

            if (size > 1)
            {
                sort = true;
            }
        }

        if (sort)
        {
            sort(size);
        }
    }

    // an index in element[],n is size of elements
    private void sort(Element[] element, int n, int i)
    {
        var smallestElement = i; // Initialize smallest as root

        var leftChildElement = 2 * i + 1; // left = 2*i + 1

        var rightChildElement = 2 * i + 2; // right = 2*i + 2

        // If left child is smaller than root
        if (leftChildElement < n && element[leftChildElement].getFrequency() < element[smallestElement].getFrequency())
        {
            smallestElement = leftChildElement;
        }

        // If right child is smaller than smallest so far
        if (rightChildElement < n && element[rightChildElement].getFrequency() < element[smallestElement].getFrequency())
        {
            smallestElement = rightChildElement;
        }

        // If smallest is not root
        if (smallestElement != i)
        {
            var temp = element[i];

            element[i] = element[smallestElement];

            element[smallestElement] = temp;

            // Recursively heapify the affected sub-tree
            sort(element, n, smallestElement);
        }
    }

    // main function to do heap sort
    private void sort(int n)
    {
        // Build heap (rearrange array)
        for (var i = n / 2 - 1; i >= 0; i--)
        {
            sort(elements, n, i);
        }

        // One by one extract an element from heap
        for (var i = n - 1; i >= 0; i--)
        {
            // Move current root to end
            var element = elements[0];

            elements[0] = elements[i];

            elements[i] = element;

            // call min heapify on the reduced heap
            sort(elements, i, 0);
        }
    }

    public Element[] getTopK()
    {
        return this.elements;
    }

    public static class Element
    {
        private final String value;

        private long frequency;

        public Element(String value, long frequency)
        {
            this.value = value;

            this.frequency = frequency;
        }

        public String getValue()
        {
            return value;
        }

        public long getFrequency()
        {
            return frequency;
        }

        public void setFrequency(long frequency)
        {
            this.frequency = frequency;
        }
    }
}