/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			Notes
 *  19-Feb-2025		Chandresh		MOTADATA-3680: Overloaded replaceScriptVariables method for handling script variable replacement for a custom key
 *
 */

package com.mindarray.util;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.store.GroupConfigStore;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.client.HttpResponse;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;

import java.io.File;
import java.io.FileWriter;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.attribute.PosixFilePermissions;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

import static com.mindarray.GlobalConstants.*;

/**
 * Utility class providing a wide range of helper methods used throughout the Motadata application.
 * <p>
 * This class contains static utility methods for:
 * - Type conversion and validation (getLong, getString, getInteger, etc.)
 * - IP address manipulation and validation
 * - Configuration management
 * - File operations
 * - System variable replacement
 * - JSON manipulation
 * - Network interface discovery
 * - Group hierarchy normalization
 * - Unique ID generation
 * - Security-related operations (masking sensitive fields)
 * <p>
 * The class is designed to be used as a static utility, and therefore has a private constructor
 * to prevent instantiation.
 */
public class CommonUtil
{
    private static final AtomicLong COUNTER = new AtomicLong(System.currentTimeMillis() - 1672531200000L);//as in will consider millis from 1 jan 2023
    private static final AtomicLong EVENT_COUNTER = new AtomicLong(System.currentTimeMillis() - 1672531200000L);
    private static final Logger LOGGER = new Logger(CommonUtil.class, GlobalConstants.MOTADATA_UTIL, "Common Util");
    private static int logLevel;
    private static long systemInstallationTimeSeconds;

    private CommonUtil()
    {
    }

    /**
     * Writes a JSON configuration object to a file in the config directory.
     * <p>
     * This method serializes the provided JsonObject to a pretty-printed JSON string
     * and writes it to a file in the standard config directory.
     *
     * @param configFile The name of the configuration file to write to
     * @param context    The JsonObject containing the configuration data to write
     * @return true if the write operation was successful, false if an error occurred
     */
    public static boolean dumpConfigs(String configFile, JsonObject context)
    {
        var result = true;

        var filePath = CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + configFile;

        try (var writer = new FileWriter(filePath))
        {
            writer.write(Json.encodePrettily(context));

            writer.flush();
        }

        catch (Exception exception)
        {
            result = false;

            LOGGER.error(exception);
        }

        return result;
    }

    /**
     * Checks if debug logging is enabled based on the current log level.
     * <p>
     * Debug logging is enabled when the current log level is set to DEBUG or a more detailed level.
     * This method is used throughout the application to conditionally execute debug logging code.
     *
     * @return true if debug logging is enabled, false otherwise
     */
    public static boolean debugEnabled()
    {
        return GlobalConstants.LOG_LEVEL_DEBUG >= logLevel;
    }

    /**
     * Checks if trace logging is enabled based on the current log level.
     * <p>
     * Trace logging is enabled when the current log level is set to TRACE.
     * This is the most detailed level of logging and is typically used only during development
     * or troubleshooting specific issues.
     *
     * @return true if trace logging is enabled, false otherwise
     */
    public static boolean traceEnabled()
    {
        return GlobalConstants.LOG_LEVEL_TRACE >= logLevel;
    }

    /**
     * Gets the current log level setting.
     * <p>
     * The log level determines which types of log messages are recorded.
     * Higher log levels include more detailed information but can impact performance.
     *
     * @return The current log level as an integer constant
     */
    public static int getLogLevel()
    {
        return logLevel;
    }

    /**
     * Sets the application-wide log level.
     * <p>
     * This method is typically called during application initialization
     * or when configuration changes are made.
     *
     * @param level The log level to set (use constants from GlobalConstants like LOG_LEVEL_INFO)
     */
    public static void setLogLevel(int level)
    {
        logLevel = level;
    }

    /**
     * Converts an object to a long value.
     * <p>
     * This method handles various input types and special cases:
     * - If the input is already a Long, it's returned directly
     * - If the input is null or empty, DUMMY_ID is returned
     * - Special values like "Infinity", "-Infinity", and "NaN" are ignored
     * - Other values are parsed using BigDecimal for precision
     *
     * @param target The object to convert to a long
     * @return The long value of the object, or DUMMY_ID if conversion fails
     */
    public static long getLong(Object target)
    {
        long value = DUMMY_ID;

        if (target instanceof Long)
        {
            return (long) target;
        }

        if (target != null && !target.toString().isEmpty() && !target.toString().equalsIgnoreCase("-Infinity")
                && !target.toString().equalsIgnoreCase("Infinity") && !target.toString().equalsIgnoreCase("NaN"))
        {
            value = Math.round(new BigDecimal(target.toString()).doubleValue());
        }

        return value;
    }

    /**
     * Converts an object to a long value with a specified default.
     * <p>
     * This method handles various input types and special cases:
     * - If the input is already a Long, it's returned directly
     * - If the input is null or empty, the provided defaultValue is returned
     * - Special values like "Infinity", "-Infinity", and "NaN" are ignored
     * - Other values are parsed using BigDecimal for precision
     *
     * @param target       The object to convert to a long
     * @param defaultValue The default value to return if conversion fails
     * @return The long value of the object, or defaultValue if conversion fails
     */
    public static long getLong(Object target, long defaultValue)
    {
        var value = defaultValue;

        if (target instanceof Long)
        {
            return (long) target;
        }

        if (target != null && !target.toString().isEmpty() && !target.toString().equalsIgnoreCase("-Infinity")
                && !target.toString().equalsIgnoreCase("Infinity") && !target.toString().equalsIgnoreCase("NaN"))
        {
            value = Math.round(new BigDecimal(target.toString()).doubleValue());
        }

        return value;
    }

    /**
     * Converts an object to a trimmed string value.
     *
     * @param target The object to convert to a string
     * @return The string value of the object, or null if the input is null
     */
    public static String getString(Object target)
    {
        String value = null;

        if (target != null)
        {
            value = String.valueOf(target).trim();
        }
        return value;
    }

    /**
     * Converts an object to a Byte value.
     *
     * @param target The object to convert to a Byte
     * @return The Byte value of the object, or null if the input is null
     */
    public static Byte getByteValue(Object target)
    {
        Byte value = null;

        if (target != null)
        {
            value = Byte.valueOf(getString(target));
        }
        return value;
    }

    /**
     * Converts an object to an integer value with a default of 0.
     *
     * @param target The object to convert to an integer
     * @return The integer value of the object, or 0 if conversion fails
     */
    public static int getInteger(Object target)
    {
        return getInteger(target, 0);
    }

    /**
     * Converts an object to an integer value with a specified default.
     *
     * @param target       The object to convert to an integer
     * @param defaultValue The default value to return if conversion fails
     * @return The integer value of the object, or defaultValue if conversion fails
     */
    public static int getInteger(Object target, int defaultValue)
    {
        var value = defaultValue;

        if (target != null)
        {
            value = Integer.parseInt(target.toString());
        }
        return value;
    }

    /**
     * Converts an object to a short value.
     *
     * @param target The object to convert to a short
     * @return The short value of the object, or 0 if conversion fails
     */
    public static short getShort(Object target)
    {
        short value = 0;

        if (target != null)
        {
            value = Short.parseShort(target.toString());
        }
        return value;
    }

    /**
     * Converts an object to a float value with 2 decimal precision.
     * <p>
     * This method handles various input types and special cases:
     * - If the input is null or empty, 0f is returned
     * - Special values like "Infinity", "-Infinity", and "NaN" are ignored
     * - Other values are parsed using BigDecimal with HALF_UP rounding to 2 decimal places
     *
     * @param target The object to convert to a float
     * @return The float value of the object with 2 decimal precision, or 0f if conversion fails
     */
    public static float getFloat(Object target)
    {
        var value = 0f;

        if (target != null && !target.toString().isEmpty() && !target.toString().equalsIgnoreCase("-Infinity")
                && !target.toString().equalsIgnoreCase("Infinity") && !target.toString().equalsIgnoreCase("NaN"))
        {
            value = (new BigDecimal(target.toString()).setScale(2, RoundingMode.HALF_UP)).floatValue();
        }

        return value;
    }

    /**
     * Converts an object to a double value with 2 decimal precision.
     * <p>
     * This method handles various input types and special cases:
     * - If the input is null or empty, 0d is returned
     * - Special values like "Infinity", "-Infinity", and "NaN" are ignored
     * - Other values are parsed using BigDecimal with HALF_UP rounding to 2 decimal places
     *
     * @param target The object to convert to a double
     * @return The double value of the object with 2 decimal precision, or 0d if conversion fails
     */
    public static double getDouble(Object target)
    {
        var value = 0d;

        if (target != null && !target.toString().isEmpty() && !target.toString().equalsIgnoreCase("-Infinity")
                && !target.toString().equalsIgnoreCase("Infinity") && !target.toString().equalsIgnoreCase("NaN"))
        {
            value = (new BigDecimal(target.toString()).setScale(2, RoundingMode.HALF_UP)).doubleValue();
        }

        return value;
    }

    /**
     * Checks if a string is not null and not empty.
     *
     * @param s The string to check
     * @return true if the string is not null and not empty, false otherwise
     */
    public static boolean isNotNullOrEmpty(String s)
    {
        return s != null && !s.isEmpty();
    }

    /**
     * Checks if a JsonArray is not null and not empty.
     *
     * @param item The JsonArray to check
     * @return true if the JsonArray is not null and not empty, false otherwise
     */
    public static boolean isNotNullOrEmpty(JsonArray item)
    {
        return item != null && !item.isEmpty();
    }

    /**
     * Checks if a string is null or empty.
     *
     * @param s The string to check
     * @return true if the string is null or empty, false otherwise
     */
    public static boolean isNullOrEmpty(String s)
    {
        return s == null || s.isEmpty();
    }

    /**
     * Gets the port number used for publishing events based on the current bootstrap type.
     * <p>
     * This method determines the appropriate event publisher port by checking the application's
     * bootstrap type and delegating to the corresponding configuration utility:
     * - For AGENT bootstrap type, uses AgentConfigUtil
     * - For other bootstrap types (FLOW_COLLECTOR, EVENT_PROCESSOR, etc.), uses MotadataConfigUtil
     * - If no specific bootstrap type matches, returns the default port 9444
     *
     * @return The configured event publisher port number
     */
    public static int getEventPublisherPort()
    {
        if (Bootstrap.bootstrapType() == BootstrapType.AGENT)
        {
            return AgentConfigUtil.getEventPublisherPort();
        }
        else if (Bootstrap.bootstrapType() == BootstrapType.FLOW_COLLECTOR ||
                Bootstrap.bootstrapType() == BootstrapType.EVENT_PROCESSOR ||
                Bootstrap.bootstrapType() == BootstrapType.EVENT_COLLECTOR ||
                Bootstrap.bootstrapType() == BootstrapType.COLLECTOR ||
                Bootstrap.bootstrapType() == BootstrapType.APP || Bootstrap.bootstrapType() == BootstrapType.MANAGER)
        {
            return MotadataConfigUtil.getEventPublisherPort();
        }

        return 9444;
    }

    /**
     * Gets the host address for the remote event subscriber based on the current bootstrap type.
     * <p>
     * This method determines the appropriate event subscriber host by checking the application's
     * bootstrap type and delegating to the corresponding configuration utility:
     * - For MANAGER bootstrap type with AGENT system bootstrap type, uses AgentConfigUtil
     * - For MANAGER bootstrap type with other system bootstrap types, uses MotadataConfigUtil
     * - For AGENT bootstrap type, uses AgentConfigUtil
     * - For all other bootstrap types, uses MotadataConfigUtil
     * <p>
     * This host address is used to establish connections for subscribing to events from remote publishers.
     *
     * @return The configured remote event subscriber host address
     */
    public static String getRemoteEventSubscriber()
    {
        String host;

        if (Bootstrap.bootstrapType() == BootstrapType.MANAGER)
        {
            if (MotadataConfigUtil.getSystemBootstrapType().equalsIgnoreCase(BootstrapType.AGENT.name()))
            {
                host = AgentConfigUtil.getRemoteEventSubscriber();
            }
            else
            {
                host = MotadataConfigUtil.getRemoteEventSubscriber();
            }
        }
        else if (Bootstrap.bootstrapType() == BootstrapType.AGENT)
        {
            host = AgentConfigUtil.getRemoteEventSubscriber();
        }
        else
        {
            host = MotadataConfigUtil.getRemoteEventSubscriber();
        }

        return host;
    }

    /**
     * Gets the host address for the remote event publisher based on the current bootstrap type.
     * <p>
     * This method determines the appropriate event publisher host by checking the application's
     * bootstrap type and delegating to the corresponding configuration utility:
     * - For MANAGER bootstrap type with AGENT system bootstrap type, uses AgentConfigUtil
     * - For MANAGER bootstrap type with other system bootstrap types, uses MotadataConfigUtil
     * - For AGENT bootstrap type, uses AgentConfigUtil
     * - For all other bootstrap types, uses MotadataConfigUtil
     * <p>
     * This host address is used to establish connections for publishing events to remote subscribers.
     *
     * @return The configured remote event publisher host address
     */
    public static String getRemoteEventPublisher()
    {
        String host;

        if (Bootstrap.bootstrapType() == BootstrapType.MANAGER)
        {
            if (MotadataConfigUtil.getSystemBootstrapType().equalsIgnoreCase(BootstrapType.AGENT.name()))
            {
                host = AgentConfigUtil.getRemoteEventPublisher();
            }
            else
            {
                host = MotadataConfigUtil.getRemoteEventPublisher();
            }
        }
        else if (Bootstrap.bootstrapType() == BootstrapType.AGENT)
        {
            host = AgentConfigUtil.getRemoteEventPublisher();
        }
        else
        {
            host = MotadataConfigUtil.getRemoteEventPublisher();
        }

        return host;
    }

    /**
     * Gets the port number used for subscribing to events based on the current bootstrap type.
     * <p>
     * This method determines the appropriate event subscriber port by checking the application's
     * bootstrap type and delegating to the corresponding configuration utility:
     * - For AGENT bootstrap type, uses AgentConfigUtil
     * - For APP, MANAGER, FLOW_COLLECTOR, EVENT_PROCESSOR, EVENT_COLLECTOR, and COLLECTOR
     * bootstrap types, uses MotadataConfigUtil
     * - If no specific bootstrap type matches, returns the default port 9449
     * <p>
     * This port is used by the event bus to listen for incoming events from publishers.
     *
     * @return The configured event subscriber port number
     */
    public static int getEventSubscriberPort()
    {
        if (Bootstrap.bootstrapType() == BootstrapType.AGENT)
        {
            return AgentConfigUtil.getEventSubscriberPort();
        }
        else if (Bootstrap.bootstrapType() == BootstrapType.APP)
        {
            return MotadataConfigUtil.getEventSubscriberPort();
        }
        else if (Bootstrap.bootstrapType() == BootstrapType.MANAGER)
        {
            return MotadataConfigUtil.getEventSubscriberPort();
        }
        else if (Bootstrap.bootstrapType() == BootstrapType.FLOW_COLLECTOR ||
                Bootstrap.bootstrapType() == BootstrapType.EVENT_PROCESSOR ||
                Bootstrap.bootstrapType() == BootstrapType.EVENT_COLLECTOR ||
                Bootstrap.bootstrapType() == BootstrapType.COLLECTOR)
        {
            return MotadataConfigUtil.getEventSubscriberPort();
        }

        return 9449;
    }

    /**
     * Loads and parses an entity schema from a JSON file in the classpath.
     * <p>
     * This method attempts to load a schema definition for the specified entity from
     * the "entity-schemas" directory in the classpath. The schema is expected to be
     * in JSON format and named according to the pattern "[entity].json".
     * <p>
     * Entity schemas define the structure, validation rules, and metadata for domain
     * entities in the application. They are used for validation, serialization, and
     * other entity-related operations.
     *
     * @param entity The name of the entity to load the schema for (without the .json extension)
     * @return A JsonObject containing the parsed schema, or null if the entity is null
     * or the schema could not be loaded
     */
    public static JsonObject getEntitySchema(String entity)
    {
        JsonObject schema = null;

        if (entity != null)
        {
            try (InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("entity-schemas" + PATH_SEPARATOR + entity + ".json"))
            {
                schema = new JsonObject(IOUtils.toString(Objects.requireNonNull(inputStream), StandardCharsets.UTF_8));
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

        }

        return schema;
    }

    /**
     * Retrieves the hostname of the local machine.
     * <p>
     * This method attempts to get the hostname of the machine where the application
     * is running by using InetAddress.getLocalHost(). If the operation fails for any
     * reason (network configuration issues, security restrictions, etc.), it returns
     * "localhost" as a fallback value.
     * <p>
     * The hostname is useful for identifying the machine in logs, network communications,
     * and distributed system configurations.
     *
     * @return The hostname of the local machine, or "localhost" if the hostname cannot be determined
     */
    public static String getHostName()
    {
        String hostName = "localhost";

        try
        {
            InetAddress ip = InetAddress.getLocalHost();

            hostName = ip.getHostName();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return hostName;
    }

    /**
     * Formats a stack trace array into a human-readable string.
     * <p>
     * This method converts an array of StackTraceElement objects into a formatted string
     * that follows the standard Java stack trace format, with each element on a new line
     * and prefixed with "\tat ". This format is consistent with how exceptions print
     * their stack traces in Java.
     * <p>
     * The method is useful for logging, debugging, and error reporting where you need
     * to include stack trace information in a string format.
     *
     * @param stackTraceElements The array of StackTraceElement objects to format
     * @return A formatted string representation of the stack trace, or an empty string
     * if the input is null or an error occurs during formatting
     */
    public static String formatStackTrace(StackTraceElement[] stackTraceElements)
    {
        var stackTrace = new StringBuilder();

        try
        {
            if (stackTraceElements != null)
            {
                for (var stackTraceElement : stackTraceElements)
                {
                    stackTrace.append("\tat ").append(stackTraceElement).append(GlobalConstants.NEW_LINE);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return stackTrace.toString();
    }

    /**
     * Removes sensitive fields from a Map and returns the result as a JsonObject.
     * <p>
     * This is a convenience method that converts the Map to a JsonObject and
     * delegates to the JsonObject version of this method.
     *
     * @param context The Map containing potentially sensitive data
     * @param deep    Whether to perform deep removal of sensitive fields in nested objects
     * @return A new JsonObject with sensitive fields removed
     */
    public static JsonObject removeSensitiveFields(Map<String, Object> context, boolean deep)
    {
        return removeSensitiveFields(new JsonObject(context), deep);
    }

    /**
     * Removes sensitive fields from a JsonObject.
     * <p>
     * This method creates a copy of the input JsonObject and removes all fields
     * that are considered sensitive (as defined in GlobalConstants.getSensitiveFields()).
     * If deep is true, it will also process nested JsonObjects recursively.
     *
     * @param context The JsonObject containing potentially sensitive data
     * @param deep    Whether to perform deep removal of sensitive fields in nested objects
     * @return A new JsonObject with sensitive fields removed
     */
    public static JsonObject removeSensitiveFields(JsonObject context, boolean deep)
    {
        var result = new JsonObject().mergeIn(context);

        for (var key : GlobalConstants.getSensitiveFields())
        {
            if (deep)
            {
                if (result.getValue(key) instanceof JsonObject || result.getValue(key) instanceof Map)
                {
                    // Recursively process nested objects, but not deeply
                    result.put(key, removeSensitiveFields(result.getJsonObject(key), false));
                }
                else
                {
                    result.remove(key);
                }
            }
            else
            {
                result.remove(key);
            }
        }

        return result;
    }

    /**
     * Masks sensitive fields in a JsonObject with asterisks.
     * <p>
     * This method creates a copy of the input JsonObject and replaces the values
     * of all fields that are considered sensitive (as defined in
     * GlobalConstants.getSensitiveFields()) with "****". This is useful for logging
     * or displaying data that may contain sensitive information.
     * <p>
     * Note: This method was added for MOTADATA-3678 to ensure sensitive fields
     * display as masked values in audit logs rather than being completely removed.
     *
     * @param context The JsonObject containing potentially sensitive data
     * @return A new JsonObject with sensitive fields masked
     */
    public static JsonObject maskSensitiveFields(JsonObject context)
    {
        var result = new JsonObject().mergeIn(context);

        for (var field : GlobalConstants.getSensitiveFields())
        {
            if (result.containsKey(field))
            {
                result.put(field, "****");
            }
        }

        return result;
    }

    /**
     * Converts an IP address to its long numeric representation.
     * <p>
     * This method takes an InetAddress object and converts it to a long value
     * by shifting and combining the octets. This is useful for IP address
     * comparisons and range calculations.
     *
     * @param ip The InetAddress to convert
     * @return The long representation of the IP address
     */
    public static long convertIPToLong(InetAddress ip)
    {
        var octets = ip.getAddress();

        long result = 0;

        for (byte octet : octets)
        {
            result <<= 8;  // Shift left by 8 bits (1 byte)
            result |= octet & 0xff;  // Bitwise OR with the current octet (ensuring unsigned byte)
        }
        return result;
    }

    /**
     * Calculates all IP addresses in a range between two IP addresses.
     * <p>
     * This method generates a set of all individual IP addresses that fall within
     * the specified range (inclusive of start and end addresses). It handles both
     * IPv4 and IPv6 addresses, though the algorithm is optimized for IPv4.
     * <p>
     * The method works by:
     * 1. Breaking down the start and end IPs into their octet components
     * 2. Iterating through all possible combinations between the start and end values
     * 3. Constructing string representations of each IP in the range
     * <p>
     * Note: For large IP ranges, this method may consume significant memory.
     *
     * @param startAddress The starting IP address of the range
     * @param endAddress   The ending IP address of the range
     * @return A Set of Strings containing all IP addresses in the range
     */
    public static Set<String> calculateIPRange(InetAddress startAddress, InetAddress endAddress)
    {
        int firstByteInt;

        int secondByteInt;

        int thirdByteInt;

        int fourthByteInt;

        int firstByteIntOfLast;

        int secondByteIntOfLast;

        int thirdByteIntOfLast;

        int fourthByteIntOfLast;

        int secondLastByte;

        int thirdLastByte;

        int fourthLastByte;

        var nodes = new HashSet<String>();

        var startOctetBytes = getIPOctetBytes(startAddress);

        var endOctetBytes = getIPOctetBytes(endAddress);

        try
        {
            if (startOctetBytes != null && endOctetBytes != null)
            {
                // convert start ip address in to integer separate byte

                firstByteInt = Integer.parseInt(startOctetBytes[NMSConstants.FIRST_BYTE_OF_IP_ADDRESS_INDEX]);

                secondByteInt = Integer.parseInt(startOctetBytes[NMSConstants.SECOND_BYTE_OF_IP_ADDRESS_INDEX]);

                thirdByteInt = Integer.parseInt(startOctetBytes[NMSConstants.THIRD_BYTE_OF_IP_ADDRESS_INDEX]);

                fourthByteInt = Integer.parseInt(startOctetBytes[NMSConstants.FOURTH_BYTE_OF_IP_ADDRESS_INDEX]);

                // convert last ip address in to integer separate byte

                firstByteIntOfLast = Integer.parseInt(endOctetBytes[NMSConstants.FIRST_BYTE_OF_IP_ADDRESS_INDEX]);

                secondByteIntOfLast = Integer.parseInt(endOctetBytes[NMSConstants.SECOND_BYTE_OF_IP_ADDRESS_INDEX]);

                thirdByteIntOfLast = Integer.parseInt(endOctetBytes[NMSConstants.THIRD_BYTE_OF_IP_ADDRESS_INDEX]);

                fourthByteIntOfLast = Integer.parseInt(endOctetBytes[NMSConstants.FOURTH_BYTE_OF_IP_ADDRESS_INDEX]);

                for (; firstByteInt <= firstByteIntOfLast; firstByteInt++)
                {
                    if (firstByteInt == firstByteIntOfLast)
                    {
                        secondLastByte = secondByteIntOfLast;
                    }
                    else
                    {
                        secondLastByte = 255;
                    }

                    for (; secondByteInt <= secondLastByte; secondByteInt++)
                    {

                        if (secondByteInt == secondByteIntOfLast)
                        {
                            thirdLastByte = thirdByteIntOfLast;
                        }
                        else
                        {
                            thirdLastByte = 255;
                        }
                        for (; thirdByteInt <= thirdLastByte; thirdByteInt++)
                        {

                            if (thirdByteInt == thirdByteIntOfLast)
                            {
                                fourthLastByte = fourthByteIntOfLast;
                            }
                            else
                            {
                                fourthLastByte = 255;
                            }
                            for (; fourthByteInt <= fourthLastByte; fourthByteInt++)
                            {
                                nodes.add(firstByteInt + "." + secondByteInt + "." + thirdByteInt + "." + fourthByteInt);
                            }

                            if (fourthByteInt == 256)
                            {
                                fourthByteInt = 0;
                            }
                        }

                        if (thirdByteInt == 256)
                        {
                            thirdByteInt = 0;
                        }
                    }

                    if (secondByteInt == 256)
                    {
                        secondByteInt = 0;
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return nodes;
    }

    /**
     * Splits an IP address into its octet components.
     * <p>
     * This method takes an InetAddress object and returns an array of strings
     * representing each octet of the IP address. For IPv4 addresses, this will
     * typically be a 4-element array (e.g., ["192", "168", "1", "1"]).
     * <p>
     * The method uses the standard dot notation as the separator for IPv4 addresses.
     * For IPv6 addresses, the behavior depends on the string representation.
     *
     * @param inetAddress The InetAddress to split into octets
     * @return An array of strings representing the octets of the IP address,
     * or null if the operation fails
     */
    public static String[] getIPOctetBytes(InetAddress inetAddress)
    {
        String[] octetBytes = null;

        String ipAddress;

        try
        {
            ipAddress = inetAddress.getHostAddress();

            if (ipAddress != null)
            {
                octetBytes = ipAddress.split(NMSConstants.IP_OCTET_BYTE_REGEX_PATTERN);
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return octetBytes;
    }

    /**
     * Generates a new unique ID for general use in the application.
     * <p>
     * This method uses an AtomicLong counter to ensure thread-safety and
     * uniqueness of the generated IDs. The counter is initialized with a timestamp
     * offset from January 1, 2023.
     *
     * @return A new unique long ID
     */
    public static long newId()
    {
        return COUNTER.incrementAndGet();
    }

    /**
     * Resets the event ID counter to the current time offset.
     * <p>
     * This method resets the event ID counter to the current time in milliseconds
     * minus an offset from January 1, 2023. This helps prevent duplicate event IDs
     * if the system processes a large number of events (exceeding 90 million per day).
     */
    public static void resetEventId()
    {
        // Reset to current time minus offset from Jan 1, 2023
        // This helps prevent duplicate event IDs if daily events exceed 90 million
        EVENT_COUNTER.set(System.currentTimeMillis() - 1672531200000L);
    }

    /**
     * Generates a new unique ID for events in the application.
     * <p>
     * This method uses a separate AtomicLong counter specifically for event IDs
     * to ensure thread-safety and uniqueness. The counter is initialized with a
     * timestamp offset from January 1, 2023.
     *
     * @return A new unique long ID for an event
     */
    public static long newEventId()
    {
        return EVENT_COUNTER.incrementAndGet();
    }

    /**
     * Replaces variables in a script with their actual values based on the plugin type.
     * <p>
     * This is a convenience method that determines the appropriate script key based on
     * whether this is a custom plugin or not, and then delegates to the more specific
     * replaceScriptVariables method. For custom plugins, it uses PluginEngineConstants.SCRIPT
     * as the key; otherwise, it uses PluginEngineConstants.PARSING_SCRIPT.
     * <p>
     * Variable replacement allows scripts to use placeholders that are replaced with
     * actual values at runtime, making scripts more flexible and reusable.
     *
     * @param context      The JsonObject containing the script and variable values
     * @param customPlugin Flag indicating whether this is a custom plugin (true) or a standard plugin (false)
     * @return The script with all variables replaced, or an empty string if an error occurs
     */
    public static String replaceScriptVariables(JsonObject context, boolean customPlugin)
    {
        return replaceScriptVariables(customPlugin ? PluginEngineConstants.SCRIPT : PluginEngineConstants.PARSING_SCRIPT, context);
    }

    /**
     * Replaces variables in a script with their actual values based on the plugin type.
     * <p>
     * This method extracts a script from the context using the specified key, then
     * replaces variables in the script based on the plugin type (metric, runbook, or topology).
     * For each plugin type, it:
     * 1. Merges any plugin-specific context into the main context
     * 2. Calls replaceSystemVariables with the appropriate variables for that plugin type
     * <p>
     * The method handles three plugin types:
     * - Metric plugins: Used for collecting metric data from systems
     * - Runbook plugins: Used for executing automated procedures
     * - Topology plugins: Used for discovering network topology
     *
     * @param key     The key to use for retrieving the script from the context
     * @param context The JsonObject containing the script, variables, and other context information
     * @return The script with all variables replaced, or an empty string if the key doesn't exist
     * or an error occurs during processing
     */
    public static String replaceScriptVariables(String key, JsonObject context)
    {
        var result = EMPTY_VALUE;

        try
        {
            if (context.containsKey(key))
            {
                if (context.containsKey(MetricPlugin.METRIC_PLUGIN_TYPE))
                {
                    if (context.containsKey(MetricPlugin.METRIC_PLUGIN_CONTEXT))
                    {
                        context.mergeIn(context.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT));

                        context.remove(MetricPlugin.METRIC_PLUGIN_CONTEXT);
                    }

                    result = replaceSystemVariables(context.getString(key),
                            context.getJsonObject(MetricPlugin.METRIC_PLUGIN_VARIABLES), context);
                }
                else if (context.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_TYPE))
                {
                    if (context.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT))
                    {
                        context.mergeIn(context.getJsonObject(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT));

                        context.remove(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT);
                    }

                    result = replaceSystemVariables(context.getString(key),
                            context.getJsonObject(RunbookPlugin.RUNBOOK_PLUGIN_VARIABLES), context);
                }
                else if (context.containsKey(TopologyPlugin.TOPOLOGY_PLUGIN_TYPE))
                {
                    if (context.containsKey(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT))
                    {
                        context.mergeIn(context.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT));

                        context.remove(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);
                    }

                    result = replaceSystemVariables(context.getString(key),
                            context.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_VARIABLES), context);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    /**
     * Replaces system variables and custom variables in a string with their actual values.
     * <p>
     * This method performs two types of replacements:
     * 1. System variables: Predefined variables from NMSConstants.SystemVariable enum
     * (like OBJECT_IP, USERNAME, PORT, etc.) are replaced with values from the context
     * 2. Custom variables: Variables in the format "$$$variable$$$" are replaced with
     * values from the variables JsonObject
     * <p>
     * The method handles various object properties (IP, target, host, type, category, etc.)
     * and ensures that replacements only occur if the corresponding values exist in the context.
     * This is particularly useful for scripts and templates that need to be parameterized.
     *
     * @param input     The input string containing variables to be replaced
     * @param variables A JsonObject containing custom variable names and their values
     * @param context   A JsonObject containing system variable values
     * @return The input string with all variables replaced with their actual values
     */
    public static String replaceSystemVariables(String input, JsonObject variables, JsonObject context)
    {
        for (var variable : NMSConstants.SystemVariable.values())
        {
            input = switch (variable)
            {
                case OBJECT_IP ->
                        context.getString(AIOpsObject.OBJECT_IP) != null ? input.replace(variable.getName(), context.getString(AIOpsObject.OBJECT_IP)) : input;

                case OBJECT_TARGET ->
                        context.getString(AIOpsObject.OBJECT_TARGET) != null ? input.replace(variable.getName(), context.getString(AIOpsObject.OBJECT_TARGET)) : input;

                case OBJECT_HOST ->
                        context.getString(AIOpsObject.OBJECT_HOST) != null ? input.replace(variable.getName(), context.getString(AIOpsObject.OBJECT_HOST)) : input;

                case OBJECT_TYPE ->
                        context.getString(AIOpsObject.OBJECT_TYPE) != null ? input.replace(variable.getName(), context.getString(AIOpsObject.OBJECT_TYPE)) : input;

                case OBJECT_CATEGORY ->
                        context.getString(AIOpsObject.OBJECT_CATEGORY) != null ? input.replace(variable.getName(), context.getString(AIOpsObject.OBJECT_CATEGORY)) : input;

                case USERNAME ->
                        context.getString(GlobalConstants.USERNAME) != null ? input.replace(variable.getName(), context.getString(GlobalConstants.USERNAME)) : input;

                case PASSWORD ->
                        context.getString(PASSWORD) != null ? input.replace(variable.getName(), context.getString(PASSWORD)) : input;

                case OBJECT_NAME ->
                        context.getString(AIOpsObject.OBJECT_NAME) != null ? input.replace(variable.getName(), context.getString(AIOpsObject.OBJECT_NAME)) : input;

                case PORT ->
                        context.getValue(PORT) != null ? input.replace(variable.getName(), getString(context.getValue(PORT))) : input;

                case DATABASE ->
                        context.getString(DATABASE) != null ? input.replace(variable.getName(), context.getString(DATABASE)) : input;

                case INSTANCE ->
                        context.getString(INSTANCE) != null ? input.replace(variable.getName(), context.getString(INSTANCE)) : input;

                default -> input;
            };
        }

        if (variables != null)
        {
            for (var variable : variables)
            {
                input = input.replace("$$$" + variable.getKey() + "$$$", getString(variable.getValue()));
            }
        }

        return input;
    }

    /**
     * Determines if an IP address is a WAN (Wide Area Network) link.
     * <p>
     * This method checks if the given IP address is not in any of the following categories:
     * - Site-local address (e.g., 192.168.x.x, 10.x.x.x, 172.16.x.x-172.31.x.x)
     * - Any local address (0.0.0.0)
     * - Link-local address (169.254.x.x)
     * - Loopback address (127.x.x.x)
     * - Multicast address (*********-***************)
     * <p>
     * If the IP is not in any of these categories, it's considered a WAN link.
     * <p>
     * Note: This method currently only supports IPv4 addresses.
     *
     * @param ip The IP address to check
     * @return true if the IP is a WAN link, false otherwise or if an error occurs
     */
    public static boolean isWANLink(String ip)
    {
        try
        {
            var address = (Inet4Address) InetAddress.getByName(ip);

            return !(address.isSiteLocalAddress() || address.isAnyLocalAddress() ||
                    address.isLinkLocalAddress() || address.isLoopbackAddress() ||
                    address.isMulticastAddress());
        }
        catch (Exception exception)
        {
            return false;
        }
    }

    /**
     * Determines if parsing is required for a given plugin execution context.
     * <p>
     * This method checks several conditions to determine if parsing is needed:
     * 1. The plugin must not be a custom plugin
     * 2. The command status must be "succeed"
     * 3. One of the following must be true:
     * a. A parsing script is present (in which case the plugin engine is set based on script language)
     * b. The event type is a metric poll (in which case JSON object validation is performed)
     * c. The event type is topology (in which case JSON array validation is performed)
     *
     * @param context The plugin execution context to check
     * @return true if parsing is required, false otherwise
     */
    public static boolean parsingRequired(JsonObject context)
    {
        var result = false;

        // Check if plugin is not custom, command status is "succeed", and determine parsing requirements
        if (!PluginEngineConstants.isCustomPlugin(context) &&
                context.containsKey(STATUS) &&
                context.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
        {
            if (isNotNullOrEmpty(context.getString(PluginEngineConstants.PARSING_SCRIPT)))
            {
                // If parsing script is present, set result to true and update plugin engine
                result = true;
                context.put(PluginEngineConstants.PLUGIN_ENGINE, context.getString(PluginEngineConstants.SCRIPT_LANGUAGE));
            }
            else if (context.getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.EVENT_METRIC_POLL))
            {
                // For metric poll events, validate JSON object format
                NMSConstants.validateJSONObject(context);
            }
            else if (context.getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.EVENT_TOPOLOGY))
            {
                // For topology events, validate JSON array format
                NMSConstants.validateJSONArray(context);
            }
        }

        return result;
    }

    /**
     * Retrieves a list of IP addresses for the local machine's network interfaces.
     * <p>
     * This method:
     * 1. Enumerates all network interfaces on the local machine
     * 2. Filters out loopback, down, and virtual interfaces
     * 3. Checks for valid hardware addresses (MAC addresses)
     * 4. Collects IPv4 addresses with broadcast capability
     * 5. Excludes the VIP (Virtual IP) address configured in the system
     * <p>
     * The method is useful for identifying the machine's actual network addresses
     * for communication, monitoring, or identification purposes.
     *
     * @return A List of Strings containing the IP addresses of the local machine
     */
    public static List<String> getIPAddresses()
    {
        var ipAddresses = new ArrayList<String>();

        try
        {
            var networkInterfaces = NetworkInterface.getNetworkInterfaces();
            var hardwareId = new StringBuilder();

            while (networkInterfaces.hasMoreElements())
            {
                var networkInterface = networkInterfaces.nextElement();

                // Skip loopback, down, and virtual interfaces
                if (!networkInterface.isLoopback() && networkInterface.isUp() && !networkInterface.isVirtual())
                {
                    var hardwareAddress = networkInterface.getHardwareAddress();

                    if (hardwareAddress != null)
                    {
                        // Format the MAC address
                        hardwareId.setLength(0);
                        for (var i = 0; i < hardwareAddress.length; i++)
                        {
                            hardwareId.append(String.format("%02X%s", hardwareAddress[i],
                                    (i < hardwareAddress.length - 1) ? ":" : GlobalConstants.EMPTY_VALUE));
                        }

                        // Check for valid MAC address (not all zeros)
                        if (!hardwareId.isEmpty() && !hardwareId.toString().contains("00:00:00:00"))
                        {
                            // Filter for IPv4 addresses with broadcast capability
                            networkInterface.getInterfaceAddresses().stream()
                                    .filter(interfaceAddress -> !interfaceAddress.toString().contains(":") &&
                                            interfaceAddress.getBroadcast() != null)
                                    .forEach(interfaceAddress ->
                                    {
                                        var ip = interfaceAddress.getAddress().toString()
                                                .replace("/", GlobalConstants.EMPTY_VALUE).trim();

                                        // Exclude VIP address
                                        if (!MotadataConfigUtil.getVIPIPAddress().trim().equalsIgnoreCase(ip))
                                        {
                                            ipAddresses.add(ip);
                                        }
                                    });
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return ipAddresses;
    }

    /**
     * Builds a string representation of a group hierarchy in a normalized format.
     * <p>
     * This method recursively traverses a group hierarchy, starting from the given group
     * and moving up through its parent groups. It builds a string representation with
     * the format "RootGroup > ParentGroup > ChildGroup", where groups are separated by
     * " > " and ordered from the root (highest level) to the leaf (lowest level).
     * <p>
     * The method uses recursion to navigate up the hierarchy:
     * 1. If the group has a parent (FIELD_PARENT_GROUP > 0), it prepends the current group
     * name to the accumulated name and recursively processes the parent group
     * 2. When it reaches a group with no parent (root group), it appends the entire
     * accumulated hierarchy to the StringBuilder
     *
     * @param group   The group to process (typically a leaf or child group)
     * @param builder The StringBuilder to append the normalized hierarchy string to
     * @param name    The accumulated group name hierarchy (null for the initial call)
     */
    public static void normalizeGroupHierarchy(JsonObject group, StringBuilder builder, String name)
    {
        if (group != null)
        {
            if (group.getLong(Group.FIELD_PARENT_GROUP) > 0)
            {
                name = " > " + group.getString(Group.FIELD_GROUP_NAME) + (name != null ? name : "");

                normalizeGroupHierarchy(GroupConfigStore.getStore().getItem(group.getLong(Group.FIELD_PARENT_GROUP)), builder, name);
            }
            else
            {
                builder.append(group.getString(Group.FIELD_GROUP_NAME)).append(name != null ? name : "");
            }
        }
    }

    /**
     * Gets the system installation time in seconds since epoch.
     * <p>
     * This method returns the timestamp when the system was installed, which is stored
     * as seconds since the Unix epoch (January 1, 1970, 00:00:00 GMT). This value is
     * useful for determining the age of the installation, calculating uptime statistics,
     * and for license or maintenance-related operations.
     *
     * @return The system installation time in seconds since epoch
     */
    public static long getSystemInstallationTimeSeconds()
    {
        return systemInstallationTimeSeconds;
    }

    /**
     * Sets the system installation time in seconds since epoch.
     * <p>
     * This method stores the timestamp when the system was installed, which should be
     * provided as seconds since the Unix epoch (January 1, 1970, 00:00:00 GMT). This
     * is typically called once during system initialization or when the installation
     * timestamp is retrieved from configuration.
     *
     * @param seconds The system installation time in seconds since epoch
     */
    public static void setSystemInstallationTimeSeconds(long seconds)
    {
        systemInstallationTimeSeconds = seconds;
    }

    /**
     * Copies a file or directory to a specified destination.
     * <p>
     * This method copies a file or directory from a source path to a destination path,
     * with the destination filename specified in the context. The method:
     * 1. Validates that required parameters (DEST_FILE_NAME and SRC_FILE_PATH) are present
     * 2. Constructs the full destination path based on STORAGE_PATH (if provided)
     * 3. Checks if the source exists and is a file or directory
     * 4. Performs the appropriate copy operation (file or directory)
     * 5. Logs the result and updates the context with error messages if needed
     * <p>
     * The method handles both files and directories appropriately, using different
     * copy methods for each type.
     *
     * @param context A JsonObject containing the copy parameters:
     *                - SRC_FILE_PATH: Path to the source file or directory
     *                - DEST_FILE_NAME: Name to use for the destination file or directory
     *                - STORAGE_PATH: (Optional) Base path for the destination
     *                - HOST: Host identifier for logging purposes
     * @return true if the copy operation was successful, false otherwise
     */
    public static boolean copy(JsonObject context)
    {
        var status = false;

        try
        {
            if (context.containsKey(DEST_FILE_NAME) && context.containsKey(SRC_FILE_PATH))
            {
                var source = new File(context.getString(SRC_FILE_PATH));

                var destination = context.containsKey(STORAGE_PATH) ? context.getString(STORAGE_PATH).startsWith(PATH_SEPARATOR) ? context.getString(STORAGE_PATH) : PATH_SEPARATOR + context.getString(STORAGE_PATH) : PATH_SEPARATOR;

                destination = destination.endsWith(PATH_SEPARATOR) ? destination + context.getString(DEST_FILE_NAME) : destination + GlobalConstants.PATH_SEPARATOR + context.getString(DEST_FILE_NAME);

                if (source.exists())
                {
                    if (source.isFile())
                    {
                        FileUtils.copyFile(source, new File(destination));

                        LOGGER.info(String.format("File uploaded successfully for the host : %s, Name : %s", context.getString(GlobalConstants.HOST), source.getName()));
                    }
                    else if (source.isDirectory())
                    {
                        FileUtils.copyDirectory(source, new File(destination));

                        LOGGER.info(String.format("Directory uploaded successfully for the host : %s, Name : %s", context.getString(GlobalConstants.HOST), source.getName()));
                    }

                    status = true;
                }
                else
                {
                    context.put(GlobalConstants.MESSAGE, ErrorMessageConstants.STORAGE_SRC_FILE_NOT_FOUND);
                }
            }
            else
            {
                context.put(GlobalConstants.MESSAGE, ErrorMessageConstants.STORAGE_FILE_UPLOAD_PARAM_MISSING);
            }
        }
        catch (Exception exception)
        {
            LOGGER.warn(String.format("Failed to upload file for the host : %s, reason : %s", context.getString(GlobalConstants.HOST), exception.getMessage()));

            context.put(GlobalConstants.MESSAGE, exception.getMessage());

            LOGGER.error(exception);
        }

        return status;
    }

    /**
     * Gets the number of process detection attempts based on the bootstrap type.
     * <p>
     * This method determines how many times the system should attempt to detect
     * a process during startup or monitoring operations. The number of attempts
     * varies based on the bootstrap type:
     * - For APP, COLLECTOR, EVENT_COLLECTOR, FLOW_COLLECTOR, and EVENT_PROCESSOR,
     * it uses the value from MotadataConfigUtil
     * - For AGENT, it uses the value from AgentConfigUtil
     * - For DATASTORE, it uses a datastore-specific value from MotadataConfigUtil
     * - For other bootstrap types, it uses a default value of 60 attempts
     * <p>
     * Process detection is used during system startup to ensure that required
     * processes are running before proceeding with initialization.
     *
     * @param bootSequence The bootstrap type as a string (e.g., "APP", "AGENT", "DATASTORE")
     * @return The number of process detection attempts to make
     */
    public static int getProcessDetectionAttempts(String bootSequence)
    {
        var processDetectionAttempts = 60;

        if (bootSequence.equalsIgnoreCase(GlobalConstants.BootstrapType.APP.name())
                || bootSequence.equalsIgnoreCase(BootstrapType.COLLECTOR.name())
                || bootSequence.equalsIgnoreCase(BootstrapType.EVENT_COLLECTOR.name())
                || bootSequence.equalsIgnoreCase(BootstrapType.FLOW_COLLECTOR.name())
                || bootSequence.equalsIgnoreCase(BootstrapType.EVENT_PROCESSOR.name()))
        {
            processDetectionAttempts = MotadataConfigUtil.getProcessDetectionAttempts();
        }
        else if (bootSequence.equalsIgnoreCase(GlobalConstants.BootstrapType.AGENT.name()))
        {
            processDetectionAttempts = AgentConfigUtil.getProcessDetectionAttempts();
        }
        else if (bootSequence.equalsIgnoreCase(BootstrapType.DATASTORE.name()))
        {
            processDetectionAttempts = MotadataConfigUtil.getDatastoreProcessDetectionAttempts();
        }

        return processDetectionAttempts;
    }

    /**
     * Gets the timeout duration for shutdown hooks based on the bootstrap type.
     * <p>
     * This method determines how long the system should wait for shutdown hooks
     * to complete before forcibly terminating. The timeout varies based on the
     * bootstrap type:
     * - For AGENT, it uses the value from AgentConfigUtil
     * - For all other bootstrap types, it uses the value from MotadataConfigUtil
     * <p>
     * Shutdown hooks are used to perform cleanup operations when the application
     * is shutting down, such as closing connections, flushing buffers, and saving
     * state. This timeout ensures that the application doesn't hang indefinitely
     * during shutdown if a hook is taking too long.
     *
     * @return The shutdown hook timeout in seconds
     */
    public static long getShutdownHookTimeoutSeconds()
    {
        if (Bootstrap.bootstrapType() == BootstrapType.AGENT)
        {
            return AgentConfigUtil.getShutdownHookTimeoutSeconds();
        }
        else
        {
            return MotadataConfigUtil.getShutdownHookTimeoutSeconds();
        }
    }

    /**
     * Sets POSIX file permissions on a file.
     * <p>
     * This method applies POSIX-style permissions to a file using the Java NIO Files API.
     * The permissions string should be in the format used by the chmod command, such as
     * "rwxr-xr--" (owner: read/write/execute, group: read/execute, others: read).
     * <p>
     * This method is useful for ensuring that files have the correct permissions after
     * they are created or modified, especially for executable scripts or sensitive
     * configuration files.
     * <p>
     * Note: This method only works on file systems that support POSIX file permissions.
     * On non-POSIX file systems (like Windows NTFS without WSL), the method will log
     * an error but not throw an exception.
     *
     * @param file        The file to set permissions on
     * @param permissions A string representing the POSIX permissions (e.g., "rwxr-xr--")
     */
    public static void setFilePermissions(File file, String permissions)
    {
        try
        {
            Files.setPosixFilePermissions(file.toPath(), PosixFilePermissions.fromString(permissions));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Checks if an HTTP response contains a valid JSON object.
     * <p>
     * This method attempts to parse the response body as a JsonObject and returns
     * true if the parsing succeeds. It's useful for validating API responses before
     * attempting to process them as JSON.
     * <p>
     * The method handles exceptions internally, so it won't throw an exception if
     * the response body isn't valid JSON. Instead, it will return false, making it
     * safe to use in conditional statements without try-catch blocks.
     *
     * @param response The HTTP response to check
     * @return true if the response body is a valid JSON object, false otherwise
     */
    public static boolean validJSONResponse(HttpResponse<Buffer> response)
    {
        try
        {
            return response.bodyAsJsonObject() != null;
        }
        catch (Exception exception)
        {
            return false;
        }
    }

    /**
     * Checks if a string value can be parsed as a numeric value.
     * <p>
     * This method attempts to convert the string to a double using the getDouble method,
     * which handles various numeric formats and edge cases. It returns true only if the
     * conversion succeeds without exceptions.
     * <p>
     * The method specifically filters out string values like "2d" and "2f" (hexadecimal
     * notations) that might be mistakenly treated as numbers in some contexts but would
     * cause runtime errors in numeric operations. This makes it more reliable than simple
     * regex-based checks for determining if a string is truly a valid number.
     *
     * @param value The string value to check
     * @return true if the string can be parsed as a numeric value, false otherwise
     */
    public static boolean isNumeric(String value)
    {
        try
        {
            getDouble(value);

            return true;
        }
        catch (Exception exception)
        {
            return false;
        }
    }
}
