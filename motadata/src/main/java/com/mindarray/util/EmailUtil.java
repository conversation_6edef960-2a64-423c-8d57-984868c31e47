/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.util;

import com.mindarray.*;
import com.mindarray.api.CredentialProfile;
import com.mindarray.api.MailServerConfiguration;
import com.mindarray.api.ProxyServer;
import com.mindarray.api.Rebranding;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.notification.Notification;
import com.mindarray.store.CredentialProfileConfigStore;
import com.mindarray.store.MailServerConfigStore;
import com.mindarray.store.RebrandingConfigStore;
import io.vertx.circuitbreaker.CircuitBreaker;
import io.vertx.circuitbreaker.CircuitBreakerOptions;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.mail.*;
import org.apache.commons.text.StringSubstitutor;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Map;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.MailServerConfiguration.*;


public class EmailUtil extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(EmailUtil.class, GlobalConstants.MOTADATA_UTIL, "Email Util");

    private final CircuitBreaker circuitBreaker = CircuitBreaker.create("mail-server-circuit-breaker", Bootstrap.vertx(),
            new CircuitBreakerOptions()
                    .setMaxFailures(5) // number of failure before opening the circuit
                    .setTimeout(30000) // consider a failure if the operation does not succeed in time
                    .setFallbackOnFailure(true)// do we call the fallback on failure
                    .setResetTimeout(60000) // time spent in open state before attempting to re-try
    ).openHandler(handler ->
    {

        EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_SERVICE_HEALTH, new JsonObject().put(STATUS, STATUS_FAIL)
                .put(MESSAGE, ErrorMessageConstants.MAIL_SERVICE_FAILED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_EMAIL_SERVICE_DOWN));

        LOGGER.warn(ErrorMessageConstants.MAIL_SERVICE_FAILED);

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(STATUS, STATUS_FAIL).put(EventBusConstants.EVENT_TYPE, Notification.NotificationType.EMAIL.getName()).put(MESSAGE, ErrorMessageConstants.MAIL_SERVICE_FAILED));

    }).closeHandler(handler ->
    {

        EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_SERVICE_HEALTH, new JsonObject().put(STATUS, STATUS_SUCCEED)
                .put(MESSAGE, InfoMessageConstants.MAIL_SERVICE_RESTORED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS));

        LOGGER.info(InfoMessageConstants.MAIL_SERVICE_RESTORED);

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(STATUS, STATUS_SUCCEED).put(EventBusConstants.EVENT_TYPE, Notification.NotificationType.EMAIL.getName()).put(MESSAGE, InfoMessageConstants.MAIL_SERVICE_RESTORED));

    }).fallback(handler -> handler.initCause(new Exception(ErrorMessageConstants.MAIL_SERVICE_FAILED)));
    private MailClient mailClient;

    // email protocol const

    private void init(MailConfig configs)
    {
        try
        {
            var item = MailServerConfigStore.getStore().getItem();

            var credentialProfile = CredentialProfileConfigStore.getStore().getItem(item.getLong(MAIL_SERVER_CREDENTIAL_PROFILE));

            var authMethods = "LOGIN";

            var login = LoginOption.DISABLED;

            configs.setHostname(item.getString(MailServerConfiguration.MAIL_SERVER_HOST))
                    .setPort(item.getInteger(MailServerConfiguration.MAIL_SERVER_PORT)).setMaxPoolSize(MotadataConfigUtil.getSMTPMaxConnections());

            if (item.getValue(ProxyServer.PROXY_ENABLED) != null
                    && item.getString(ProxyServer.PROXY_ENABLED).equalsIgnoreCase(YES)
                    && WebClientUtil.getProxyOptions() != null)
            {
                configs.setProxyOptions(WebClientUtil.getProxyOptions());
            }

            if (credentialProfile != null)
            {
                var context = credentialProfile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);

                if (CommonUtil.isNotNullOrEmpty(context.getString(USERNAME)) && (CommonUtil.isNotNullOrEmpty(context.getString(PASSWORD)) || CommonUtil.isNotNullOrEmpty(context.getString(OAuthUtil.ACCESS_TOKEN))))
                {
                    if (NMSConstants.Protocol.UNKNOWN.getName().equalsIgnoreCase(credentialProfile.getString(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL)))
                    {
                        if (item.containsKey(MailServerConfiguration.MAIL_SERVER_AUTH_STATUS) && item.getString(MailServerConfiguration.MAIL_SERVER_AUTH_STATUS).equalsIgnoreCase(YES))
                        {
                            configs.setPassword(context.getString(PASSWORD));

                            configs.setUsername(context.getString(USERNAME));

                            login = LoginOption.REQUIRED;
                        }
                    }
                    else
                    {
                        configs.setPassword(context.getString(OAuthUtil.ACCESS_TOKEN));

                        configs.setUsername(context.getString(USERNAME));

                        authMethods = OAuthUtil.AUTH_METHOD_OAUTH;

                        login = LoginOption.REQUIRED;
                    }
                }
            }

            if (CommonUtil.isNotNullOrEmpty(item.getString(MAIL_SERVER_PROTOCOL)))
            {
                configs.setSsl(item.getString(MAIL_SERVER_PROTOCOL).equalsIgnoreCase(MailServerConfiguration.MAIL_SERVER_PROTOCOL_SSL));

                if (item.getString(MAIL_SERVER_PROTOCOL).equalsIgnoreCase(MailServerConfiguration.MAIL_SERVER_PROTOCOL_TLS))
                {
                    configs.setStarttls(StartTLSOptions.REQUIRED);
                }
                else
                {
                    configs.setStarttls(StartTLSOptions.OPTIONAL);
                }
            }

            configs.setLogin(login);

            configs.setAuthMethods(authMethods);

            mailClient = MailClient.create(vertx, configs);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    public void stop(Promise<Void> promise)
    {
        if (mailClient != null)
        {
            mailClient.close();
        }

        circuitBreaker.close();

        promise.complete();
    }

    @Override
    public void start(Promise<Void> promise) throws Exception
    {

        if (!MailServerConfigStore.getStore().getItems().isEmpty())
        {
            var configs = new MailConfig().setTrustAll(true);

            init(configs);

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
            {
                var event = message.body();

                if (EventBusConstants.ChangeNotificationType.valueOf(event.getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE)) == EventBusConstants.ChangeNotificationType.MAIL_SERVER)
                {
                    mailClient.close();

                    init(configs);
                }

            }).exceptionHandler(LOGGER::error);

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_EMAIL_NOTIFICATION, message ->
            {
                var event = message.body();

                try
                {
                    var mailMessage = new MailMessage();

                    var mailAttachments = new ArrayList<MailAttachment>();

                    var rebrandingItem = RebrandingConfigStore.getStore().getItem();

                    mailMessage.setSubject(event.getString(Notification.EMAIL_NOTIFICATION_SUBJECT));

                    mailMessage.setTo(event.getJsonArray(Notification.EMAIL_NOTIFICATION_RECIPIENTS).getList());

                    var item = MailServerConfigStore.getStore().getItem();

                    var credentialProfile = CredentialProfileConfigStore.getStore().getItem(item.getLong(MAIL_SERVER_CREDENTIAL_PROFILE));

                    if (event.containsKey(Notification.EMAIL_NOTIFICATION_CONTENT))
                    {
                        if (rebrandingItem != null && rebrandingItem.containsKey(Rebranding.REBRANDING_LOGO_NAME) && rebrandingItem.getString(Rebranding.REBRANDING_LOGO_NAME) != null)
                        {
                            event.put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(Map.of("logo", rebrandingItem.getString(Rebranding.REBRANDING_LOGO_NAME), "link", "#", "disclaimer", Notification.EMAIL_DISCLAIMER_REBRANDED)).replace(new String(event.getBinary(Notification.EMAIL_NOTIFICATION_CONTENT))).getBytes(StandardCharsets.UTF_8));
                        }
                        else
                        {
                            event.put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(Map.of("logo", "logo.png", "link", "https://www.motadata.com", "disclaimer", Notification.EMAIL_DISCLAIMER_DEFAULT)).replace(new String(event.getBinary(Notification.EMAIL_NOTIFICATION_CONTENT))).getBytes(StandardCharsets.UTF_8));
                        }

                        mailMessage.setHtml(new String(event.getBinary(Notification.EMAIL_NOTIFICATION_CONTENT)));

                        for (var attachment : event.getJsonArray(Notification.EMAIL_NOTIFICATION_ATTACHMENTS))
                        {
                            try
                            {
                                var path = "";

                                var fileName = "";

                                if (attachment.toString().equalsIgnoreCase("logo.png") && rebrandingItem != null && rebrandingItem.containsKey(Rebranding.REBRANDING_LOGO_NAME) && rebrandingItem.getString(Rebranding.REBRANDING_LOGO_NAME) != null && rebrandingItem.containsKey(Rebranding.REBRANDING_LOGO) && rebrandingItem.getString(Rebranding.REBRANDING_LOGO) != null)
                                {
                                    path = CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + rebrandingItem.getString(Rebranding.REBRANDING_LOGO);

                                    fileName = rebrandingItem.getString(Rebranding.REBRANDING_LOGO_NAME);
                                }
                                else
                                {
                                    path = CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + CommonUtil.getString(attachment);

                                    // ref: Files.getFileExtension(path);
                                    fileName = new File(path).getName();
                                }

                                var index = fileName.lastIndexOf('.');

                                var mailAttachment = MailAttachment.create();

                                mailAttachment.setContentType(Notification.ContentType.getType((index == -1) ? "" : fileName.substring(index + 1)));

                                mailAttachment.setData(vertx.fileSystem().readFileBlocking(path));

                                mailAttachment.setDisposition(event.getString(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE));

                                mailAttachment.setName(fileName);

                                mailAttachment.setContentId("<" + fileName + ">");

                                mailAttachments.add(mailAttachment);

                            }

                            catch (Exception exception)
                            {
                                message.fail(GlobalConstants.NOT_AVAILABLE, exception.getMessage());

                                LOGGER.error(exception);
                            }
                        }

                        mailMessage.setInlineAttachment(mailAttachments);
                    }

                    else
                    {
                        mailMessage.setText(event.getString(Notification.EMAIL_NOTIFICATION_MESSAGE));
                    }

                    //sender address
                    if (CommonUtil.isNotNullOrEmpty(item.getString(MAIL_SERVER_SENDER)))
                    {
                        mailMessage.setFrom(item.getString(MAIL_SERVER_SENDER));
                    }

                    else if (credentialProfile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT).getString(USERNAME) != null)
                    {
                        mailMessage.setFrom(credentialProfile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT).getString(USERNAME));
                    }

                    else
                    {
                        //for none type
                        if (item.getString(MailServerConfiguration.MAIL_SERVER_SENDER) != null && !item.getString(MailServerConfiguration.MAIL_SERVER_SENDER).isEmpty())
                        {
                            mailMessage.setFrom(item.getString(MailServerConfiguration.MAIL_SERVER_SENDER));
                        }

                        else
                        {
                            mailMessage.setFrom("<EMAIL>"); // if none type protocol and sender field is empty at that time set static email address to send notification
                        }
                    }

                    circuitBreaker.<JsonObject>execute(future -> mailClient.sendMail(mailMessage, result ->
                    {
                        if (result.succeeded())
                        {
                            future.complete(result.result().toJson());
                        }

                        else
                        {
                            if (!NMSConstants.Protocol.UNKNOWN.getName().equalsIgnoreCase(credentialProfile.getString(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL)))
                            {
                                if (OAuthUtil.OAUTH_PROVIDER_ERROR_CODES.stream().anyMatch(object -> result.cause().getMessage().contains(object)))
                                {

                                    Bootstrap.vertx().eventBus().request(EventBusConstants.EVENT_OAUTH_TOKEN_GENERATE, credentialProfile,
                                            new DeliveryOptions().setSendTimeout(15 * 1000L),
                                            asyncResult ->
                                            {
                                                if (asyncResult.succeeded())
                                                {
                                                    configs.setPassword(asyncResult.result().body().toString());

                                                    mailClient.sendMail(mailMessage, retryResult ->
                                                    {
                                                        if (retryResult.succeeded())
                                                        {
                                                            future.complete(retryResult.result().toJson());
                                                        }
                                                        else
                                                        {
                                                            future.fail(retryResult.cause());

                                                            LOGGER.error(retryResult.cause());
                                                        }
                                                    });
                                                }
                                                else
                                                {
                                                    LOGGER.error(asyncResult.cause());
                                                }
                                            });
                                }
                                else
                                {
                                    future.fail(result.cause());

                                    LOGGER.error(result.cause());
                                }
                            }
                            else
                            {
                                future.fail(result.cause());

                                LOGGER.error(result.cause());
                            }
                        }
                    })).onComplete(result ->
                    {

                        if (result.succeeded())
                        {
                            message.reply(result.result().put(STATUS, STATUS_SUCCEED));
                        }

                        else
                        {
                            message.fail(GlobalConstants.NOT_AVAILABLE, result.cause().getMessage());
                        }
                    });
                }

                catch (Exception exception)
                {
                    message.fail(GlobalConstants.NOT_AVAILABLE, exception.getMessage());

                    LOGGER.error(exception);
                }
            }).exceptionHandler(LOGGER::error);
        }

        promise.complete();
    }
}