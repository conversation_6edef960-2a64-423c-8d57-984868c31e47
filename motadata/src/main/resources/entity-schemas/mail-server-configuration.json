{"entity": "Mail Server Configuration", "collection": "mail_server", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "mail.server.host", "title": "SMTP Server", "type": "string", "rules": ["required"]}, {"name": "mail.server.port", "title": "SMTP Server Port", "type": "numeric", "rules": ["required"]}, {"name": "mail.server.sender", "title": "Email", "type": "string"}, {"name": "mail.server.protocol", "title": "Security Type", "type": "string", "rules": ["required"]}, {"name": "mail.server.credential.profile", "title": "Credential Profile", "type": "string", "rules": ["required"]}], "entries": [{"type": "inline", "records": [{"id": 10000000000001, "mail.server.port": 25, "mail.server.protocol": "None", "mail.server.credential.profile": 10000000000002}], "version": "1.0"}]}