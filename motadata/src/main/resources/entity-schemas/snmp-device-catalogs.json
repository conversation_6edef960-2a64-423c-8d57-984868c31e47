{"entity": "SNMP Device Catalog", "collection": "snmp.device.catalog", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "snmp.device.catalog.oid", "title": "System OID", "type": "string", "rules": ["required"]}, {"name": "snmp.device.catalog.model", "title": "Make & Model", "type": "string", "rules": ["required"]}, {"name": "snmp.device.catalog.type", "title": "Device Type", "type": "string", "rules": ["required"]}, {"name": "snmp.device.catalog.vendor", "title": "<PERSON><PERSON><PERSON>", "type": "string", "rules": ["required"]}], "entries": [{"type": "file", "records": ["snmp-devices.db"], "version": "1.3"}, {"type": "file", "records": ["snmp-devices.db"], "version": "1.4"}, {"type": "file", "records": ["snmp-devices.db"], "version": "1.5"}, {"type": "file", "records": ["snmp-devices.db"], "version": "1.7"}, {"type": "file", "records": ["snmp-devices.db"], "version": "1.8"}, {"type": "file", "records": ["snmp-devices.db"], "version": "1.9"}]}