{"entity": "Event Sources", "collection": "event.source", "version": "1.0", "author": "Vvek", "props": [{"name": "event.source", "title": "Source IP", "type": "string", "rules": ["required"]}, {"name": "source.groups", "title": "Group(s)", "type": "list", "rules": ["required"]}, {"name": "event.source.type", "title": "Type", "type": "string", "rules": []}, {"name": "event.timezone", "title": "Timezone", "type": "string", "rules": []}]}