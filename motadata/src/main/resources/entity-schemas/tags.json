{"entity": "Tag", "collection": "tag", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "tag", "title": "Tag Name", "type": "string", "rules": ["required"]}, {"name": "tag.type", "title": "Tag Type", "type": "string"}], "entries": [{"type": "inline", "records": [{"tag": "Profile Applicability:Level 1", "tag.type": ["Compliance"], "id": 2078693705}, {"tag": "Profile Applicability:Level 2", "tag.type": ["Compliance"], "id": 2078693706}, {"tag": "Technology:Cisco Router", "tag.type": ["Compliance"], "id": 696588062}, {"tag": "Vendor:Cisco Systems", "tag.type": ["Compliance"], "id": 479946283}, {"tag": "OS:IOS-XE", "tag.type": ["Compliance"], "id": -329601245}, {"tag": "OS Version:16.x", "tag.type": ["Compliance"], "id": -947527379}, {"tag": "framework:cis", "tag.type": ["Compliance"], "id": 1682219345}, {"tag": "benchmark version:2.1.0", "tag.type": ["Compliance"], "id": -2082102542}, {"tag": "vendor:cisco systems", "tag.type": ["Compliance"], "id": 2113825355}], "version": "1.2"}]}