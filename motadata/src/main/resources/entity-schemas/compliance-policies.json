{"entity": "Compliance Policy", "collection": "compliance.policy", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "compliance.policy.name", "title": "Compliance Policy Name", "type": "string", "rules": ["required", "unique"]}, {"name": "compliance.policy.description", "title": "Compliance Description", "type": "string"}, {"name": "compliance.policy.tags", "title": "Tags", "type": "list"}, {"name": "compliance.policy.generate.report", "title": "Generate Report", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "compliance.policy.context", "title": "Context", "type": "map", "rules": ["required"]}, {"name": "compliance.policy.email.recipients", "title": "Compliance Policy Email Recipient(s)", "type": "list"}, {"name": "compliance.policy.sms.recipients", "title": "Compliance Policy SMS Recipient(s)", "type": "list"}]}