{"entity": "Dependency Mapper", "collection": "dependency.mapper", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "dependency.mapper.parent", "title": "<PERSON><PERSON>", "type": "string", "rules": ["required"]}, {"name": "dependency.mapper.child", "title": "<PERSON>ce", "type": "string", "rules": ["required"]}, {"name": "dependency.mapper.type", "title": "Type", "type": "string", "rules": ["required"], "values": ["Network", "Application"]}, {"name": "dependency.mapper.archived", "title": "Archived", "type": "string", "values": ["yes", "no"]}]}