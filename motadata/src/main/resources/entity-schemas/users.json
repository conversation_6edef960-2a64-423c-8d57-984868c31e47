{"entity": "User", "collection": "_user", "version": "1.0", "author": "Alpesh Dhamelia", "props": [{"name": "user.name", "title": "User Name", "type": "string", "rules": ["required", "unique"]}, {"name": "user.type", "title": "User Type", "type": "string", "default": "System", "private": true}, {"name": "user.first.name", "title": "First Name", "type": "string", "rules": ["required"]}, {"name": "user.last.name", "title": "Last Name", "type": "string", "rules": ["required"]}, {"name": "user.email", "title": "Email", "type": "string", "rules": ["required"]}, {"name": "user.mobile", "title": "Mobile", "type": "numeric"}, {"name": "user.password", "title": "Password", "type": "password", "rules": ["required"]}, {"name": "user.status", "title": "Status", "type": "string", "rules": ["required"]}, {"name": "user.role", "title": "User Role", "type": "numeric", "rules": ["required"]}, {"name": "user.password.last.updated.time", "title": "User Password Last Update Time", "type": "numeric"}, {"name": "user.groups", "title": "User Group(s)", "type": "list", "rules": ["required"]}], "entries": [{"type": "inline", "records": [{"user.name": "admin", "user.type": "System", "user.first.name": "motadata", "user.last.name": "admin", "user.password": "flsTVAS7R94buE4Av1x36g==", "user.status": "yes", "user.role": 10000000000001, "user.groups": [10000000000001], "user.preferences": {"user.home.screen": 10000000001026}, "id": 10000000000001}], "version": "1.1"}]}