{"entity": "Agent Configuration", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "system.log.level", "title": "Log Level", "type": "numeric", "rules": ["required", "minimum"], "value": 0}, {"name": "cache.file.max.size.threshold.mb", "title": "<PERSON><PERSON> <PERSON> (MB)", "type": "numeric", "rules": ["required", "minimum"], "value": 100}, {"name": "agent.health.window.count", "title": "Health Window Count", "type": "numeric", "rules": ["required", "minimum"], "value": 1}, {"name": "metric.agent.memory.warning.threshold.mb", "title": "Metric Agent Memory Warning Threshold (MB)", "type": "numeric", "rules": ["required", "minimum"], "value": 1}, {"name": "log.agent.memory.warning.threshold.mb", "title": "Log Agent Memory Warning Threshold (MB)", "type": "numeric", "rules": ["required", "minimum"], "value": 1}, {"name": "packet.agent.memory.warning.threshold.mb", "title": "Packet Agent Memory Warning Threshold (MB)", "type": "numeric", "rules": ["required"]}, {"name": "metric.agent.memory.critical.threshold.mb", "title": "Metric Agent Memory Critical Threshold (MB)", "type": "numeric", "rules": ["required", "minimum"], "value": 1}, {"name": "log.agent.memory.critical.threshold.mb", "title": "Log Agent Memory Critical Threshold (MB)", "type": "numeric", "rules": ["required", "minimum"], "value": 1}, {"name": "packet.agent.memory.critical.threshold.mb", "title": "Packet Agent Memory Critical Threshold (MB)", "type": "numeric", "rules": ["required", "minimum"], "value": 1}, {"name": "metric.agent.cpu.warning.percent", "title": "Metric Agent CPU Warning Threshold (%)", "type": "numeric", "rules": ["required", "minimum"], "value": 1}, {"name": "log.agent.cpu.warning.percent", "title": "Log Agent CPU Warning Threshold (%)", "type": "numeric", "rules": ["required"]}, {"name": "packet.agent.cpu.warning.percent", "title": "Packet Agent CPU Warning Threshold (%)", "type": "numeric", "rules": ["required", "minimum"], "value": 1}, {"name": "metric.agent.cpu.critical.percent", "title": "Metric Agent CPU Critical Threshold (%)", "type": "numeric", "rules": ["required", "minimum"], "value": 1}, {"name": "log.agent.cpu.critical.percent", "title": "Log Agent CPU Critical Threshold (%)", "type": "numeric", "rules": ["required", "minimum"], "value": 1}, {"name": "packet.agent.cpu.critical.percent", "title": "Packet Agent CPU Critical Threshold (%)", "type": "numeric", "rules": ["required", "minimum"], "value": 1}, {"name": "metric.agent.status", "title": "Metric Agent Status", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "log.agent.status", "title": "Log Agent Status", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "packet.agent.status", "title": "Packet Agent Status", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "agent.state", "title": "Agent State", "type": "string", "rules": ["required"], "values": ["ENABLE", "DISABLE"]}, {"name": "agent.max.event.backlog.queue.size", "title": "Agent <PERSON> Event Backlog", "type": "numeric", "rules": ["required", "minimum"], "value": 10000}, {"name": "agent.id", "title": "Agent ID", "type": "string", "rules": ["required"]}, {"name": "agent.process.detection.attempts", "title": "Agent Process Detection Attempts", "type": "numeric", "rules": ["required", "minimum"], "value": 1}, {"name": "http.server.port", "title": "HTTP Server Port", "type": "numeric", "rules": ["required"]}, {"name": "agent.health.inspection.timer.seconds", "title": "Agent Health Inspection Timer (Seconds)", "type": "numeric", "rules": ["required", "minimum"], "value": 1}, {"name": "cache.flush.timer.seconds", "title": "<PERSON><PERSON> (Seconds)", "type": "numeric", "rules": ["required", "minimum"], "value": 1}, {"name": "agent.log.retention.days", "title": "Agent <PERSON><PERSON> (Days)", "type": "numeric", "rules": ["required", "minimum"], "value": 1}, {"name": "event.publisher.host", "title": "Event Publisher", "type": "string", "rules": ["required"]}, {"name": "event.subscriber.host", "title": "Event Subscriber", "type": "string", "rules": ["required"]}, {"name": "event.publisher.port", "title": "Event Publisher Port", "type": "numeric", "rules": ["required"]}, {"name": "event.subscriber.port", "title": "Event Subscriber Port", "type": "numeric", "rules": ["required"]}, {"name": "agent.deactivation.status", "title": "Agent Deactivate Status", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "multiline.log.status", "title": "Multiline Log Status", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "multiline.log.files", "title": "Multiline Log Files", "type": "list"}, {"name": "event.log.source.status", "title": "Event Log Source Status", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "log.filters", "title": "Log Filters", "type": "list"}, {"name": "watcher.create.event", "title": "Watcher Create Event", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "log.dirs", "title": "Log Directories", "type": "list"}, {"name": "event.log.sources", "title": "Event Log Sources", "type": "list"}, {"name": "log.position.write.timer.seconds", "title": "Log Position Write Interval (Seconds)", "type": "numeric", "rules": ["required", "minimum"], "value": 1}, {"name": "read.existing.events", "title": "Read Existing Events", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "max.workers", "title": "Max Workers", "type": "numeric", "rules": ["required", "minimum"], "value": 1}, {"name": "cache.max.size.bytes", "title": "<PERSON><PERSON> (Bytes)", "type": "numeric", "rules": ["required", "range"], "value": [4096, 104857600]}, {"name": "ignore.invalid.log.file", "title": "Ignore Invalid Log File", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "worker.max.queue.size", "title": "Worker Maximum Queue", "type": "numeric", "rules": ["required", "range"], "value": [1, 1000]}, {"name": "worker.max.page.size.bytes", "title": "Worker Maximum Page Size (Bytes)", "type": "numeric", "rules": ["required", "range-divisible"], "value": [4096, 1048576], "divide-value": 4096}, {"name": "cpu.memory.metric.poll.timer.seconds", "title": "CPU Memory Metric Poll Interval (Seconds)", "type": "numeric", "rules": ["required", "minimum"], "value": 1}, {"name": "system.info.metric.poll.timer.seconds", "title": "System Info Metric Poll Interval (Seconds)", "type": "numeric", "rules": ["required", "minimum"], "value": 600}, {"name": "system.load.metric.poll.timer.seconds", "title": "System Load Metric Poll Interval (Seconds)", "type": "numeric", "rules": ["required", "minimum"], "value": 5}, {"name": "process.metric.poll.timer.seconds", "title": "Process Metric Poll Interval (Seconds)", "type": "numeric", "rules": ["required", "minimum"], "value": 60}, {"name": "disk.metric.poll.timer.seconds", "title": "Disk Metric Poll Interval (Seconds)", "type": "numeric", "rules": ["required", "minimum"], "value": 60}, {"name": "network.metric.poll.timer.seconds", "title": "Network Metric Poll Interval (Seconds)", "type": "numeric", "rules": ["required", "minimum"], "value": 60}, {"name": "service.metric.poll.timer.seconds", "title": "Service Metric Poll Interval (Seconds)", "type": "numeric", "rules": ["required", "minimum"], "value": 60}, {"name": "cpu.core.metric.poll.timer.seconds", "title": "CPU Core Metric Poll Interval (Seconds)", "type": "numeric", "rules": ["required", "minimum"], "value": 5}, {"name": "process.metric.status", "title": "Process Metric Status", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "process.connection.status", "title": "Process Connection Status", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "cpu.memory.metric.status", "title": "CPU Memory Metric Status", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "system.info.metric.status", "title": "System Info Metric Status", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "system.load.metric.status", "title": "System Load Metric Status", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "disk.metric.status", "title": "Disk Metric Status", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "network.metric.status", "title": "Network Metric Status", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "service.metric.status", "title": "Service Metric Status", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "cpu.core.metric.status", "title": "CPU Core Metric Status", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "system.metric.status", "title": "System Metric Status", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "processes", "title": "Processes", "type": "list"}, {"name": "services", "title": "Services", "type": "list"}, {"name": "disks", "title": "Disks", "type": "list"}, {"name": "interfaces", "title": "Interfaces", "type": "list"}, {"name": "applications", "title": "Applications", "type": "list"}, {"name": "agent.business.hour.profile", "title": "Agent Business Hour", "type": "map"}, {"name": "escape.characters", "title": "Escape Characters", "type": "map"}]}