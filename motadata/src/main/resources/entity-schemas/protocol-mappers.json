{"entity": "Protocol Mapper", "collection": "protocol.mapper", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "protocol.mapper.name", "title": "Protocol Name", "type": "string", "rules": ["required", "unique"]}, {"name": "protocol.mapper.number", "title": "Number", "type": "numeric", "rules": ["required"]}], "entries": [{"type": "inline", "records": [{"id": 10000000000001, "protocol.mapper.number": 0, "protocol.mapper.name": "HOPOPT"}, {"id": 10000000000002, "protocol.mapper.number": 1, "protocol.mapper.name": "ICMP"}, {"id": 10000000000003, "protocol.mapper.number": 2, "protocol.mapper.name": "IGMP"}, {"id": 10000000000004, "protocol.mapper.number": 3, "protocol.mapper.name": "GGP"}, {"id": 10000000000005, "protocol.mapper.number": 4, "protocol.mapper.name": "IP-in-IP"}, {"id": 10000000000006, "protocol.mapper.number": 5, "protocol.mapper.name": "ST"}, {"id": 10000000000007, "protocol.mapper.number": 6, "protocol.mapper.name": "TCP"}, {"id": 10000000000008, "protocol.mapper.number": 7, "protocol.mapper.name": "CBT"}, {"id": 10000000000009, "protocol.mapper.number": 8, "protocol.mapper.name": "EGP"}, {"id": 10000000000010, "protocol.mapper.number": 9, "protocol.mapper.name": "IGP"}, {"id": 10000000000011, "protocol.mapper.number": 10, "protocol.mapper.name": "BBN-RCC-MON"}, {"id": 10000000000012, "protocol.mapper.number": 11, "protocol.mapper.name": "NVP-II"}, {"id": 10000000000013, "protocol.mapper.number": 12, "protocol.mapper.name": "PUP"}, {"id": 10000000000014, "protocol.mapper.number": 13, "protocol.mapper.name": "ARGUS"}, {"id": 10000000000015, "protocol.mapper.number": 14, "protocol.mapper.name": "EMCON"}, {"id": 10000000000016, "protocol.mapper.number": 15, "protocol.mapper.name": "XNET"}, {"id": 10000000000017, "protocol.mapper.number": 16, "protocol.mapper.name": "CHAOS"}, {"id": 10000000000018, "protocol.mapper.number": 17, "protocol.mapper.name": "UDP"}, {"id": 10000000000019, "protocol.mapper.number": 18, "protocol.mapper.name": "MUX"}, {"id": 10000000000020, "protocol.mapper.number": 19, "protocol.mapper.name": "DCN-MEAS"}, {"id": 10000000000021, "protocol.mapper.number": 20, "protocol.mapper.name": "HMP"}, {"id": 10000000000022, "protocol.mapper.number": 21, "protocol.mapper.name": "PRM"}, {"id": 10000000000023, "protocol.mapper.number": 22, "protocol.mapper.name": "XNS-IDP"}, {"id": 10000000000024, "protocol.mapper.number": 23, "protocol.mapper.name": "TRUNK-1"}, {"id": 10000000000025, "protocol.mapper.number": 24, "protocol.mapper.name": "TRUNK-2"}, {"id": 10000000000026, "protocol.mapper.number": 25, "protocol.mapper.name": "LEAF-1"}, {"id": 10000000000027, "protocol.mapper.number": 26, "protocol.mapper.name": "LEAF-2"}, {"id": 10000000000028, "protocol.mapper.number": 27, "protocol.mapper.name": "RDP"}, {"id": 10000000000029, "protocol.mapper.number": 28, "protocol.mapper.name": "IRTP"}, {"id": 10000000000030, "protocol.mapper.number": 29, "protocol.mapper.name": "ISO-TP4"}, {"id": 10000000000031, "protocol.mapper.number": 30, "protocol.mapper.name": "NETBLT"}, {"id": 10000000000032, "protocol.mapper.number": 31, "protocol.mapper.name": "MFE-NSP"}, {"id": 10000000000033, "protocol.mapper.number": 32, "protocol.mapper.name": "MERIT-INP"}, {"id": 10000000000034, "protocol.mapper.number": 33, "protocol.mapper.name": "DCCP"}, {"id": 10000000000035, "protocol.mapper.number": 34, "protocol.mapper.name": "3PC"}, {"id": 10000000000036, "protocol.mapper.number": 35, "protocol.mapper.name": "IDPR"}, {"id": 10000000000037, "protocol.mapper.number": 36, "protocol.mapper.name": "XTP"}, {"id": 10000000000038, "protocol.mapper.number": 37, "protocol.mapper.name": "DDP"}, {"id": 10000000000039, "protocol.mapper.number": 38, "protocol.mapper.name": "IDPR-CMTP"}, {"id": 10000000000040, "protocol.mapper.number": 39, "protocol.mapper.name": "TP++"}, {"id": 10000000000041, "protocol.mapper.number": 40, "protocol.mapper.name": "IL"}, {"id": 10000000000042, "protocol.mapper.number": 41, "protocol.mapper.name": "IPv6"}, {"id": 10000000000043, "protocol.mapper.number": 42, "protocol.mapper.name": "SDRP"}, {"id": 10000000000044, "protocol.mapper.number": 43, "protocol.mapper.name": "IPv6-Route"}, {"id": 10000000000045, "protocol.mapper.number": 44, "protocol.mapper.name": "IPv6-Frag"}, {"id": 10000000000046, "protocol.mapper.number": 45, "protocol.mapper.name": "IDRP"}, {"id": 10000000000047, "protocol.mapper.number": 46, "protocol.mapper.name": "RSVP"}, {"id": 10000000000048, "protocol.mapper.number": 47, "protocol.mapper.name": "GRE"}, {"id": 10000000000049, "protocol.mapper.number": 48, "protocol.mapper.name": "DSR"}, {"id": 10000000000050, "protocol.mapper.number": 49, "protocol.mapper.name": "BNA"}, {"id": 10000000000051, "protocol.mapper.number": 50, "protocol.mapper.name": "ESP"}, {"id": 10000000000052, "protocol.mapper.number": 51, "protocol.mapper.name": "AH"}, {"id": 10000000000053, "protocol.mapper.number": 52, "protocol.mapper.name": "I-NLSP"}, {"id": 10000000000054, "protocol.mapper.number": 53, "protocol.mapper.name": "SwIPe"}, {"id": 10000000000055, "protocol.mapper.number": 54, "protocol.mapper.name": "NARP"}, {"id": 10000000000056, "protocol.mapper.number": 55, "protocol.mapper.name": "MOBILE"}, {"id": 10000000000057, "protocol.mapper.number": 56, "protocol.mapper.name": "TLSP"}, {"id": 10000000000058, "protocol.mapper.number": 57, "protocol.mapper.name": "SKIP"}, {"id": 10000000000059, "protocol.mapper.number": 58, "protocol.mapper.name": "IPv6-ICMP"}, {"id": 10000000000060, "protocol.mapper.number": 59, "protocol.mapper.name": "IPv6-NoNxt"}, {"id": 10000000000061, "protocol.mapper.number": 60, "protocol.mapper.name": "IPv6-Opts"}, {"id": 10000000000062, "protocol.mapper.number": 62, "protocol.mapper.name": "CFTP"}, {"id": 10000000000063, "protocol.mapper.number": 64, "protocol.mapper.name": "SAT-EXPAK"}, {"id": 10000000000064, "protocol.mapper.number": 65, "protocol.mapper.name": "KRYPTOLAN"}, {"id": 10000000000065, "protocol.mapper.number": 66, "protocol.mapper.name": "RVD"}, {"id": 10000000000066, "protocol.mapper.number": 67, "protocol.mapper.name": "IPPC"}, {"id": 10000000000067, "protocol.mapper.number": 69, "protocol.mapper.name": "SAT-MON"}, {"id": 10000000000068, "protocol.mapper.number": 70, "protocol.mapper.name": "VISA"}, {"id": 10000000000069, "protocol.mapper.number": 71, "protocol.mapper.name": "IPCU"}, {"id": 10000000000070, "protocol.mapper.number": 72, "protocol.mapper.name": "CPNX"}, {"id": 10000000000071, "protocol.mapper.number": 73, "protocol.mapper.name": "CPHB"}, {"id": 10000000000072, "protocol.mapper.number": 74, "protocol.mapper.name": "WSN"}, {"id": 10000000000073, "protocol.mapper.number": 75, "protocol.mapper.name": "PVP"}, {"id": 10000000000074, "protocol.mapper.number": 76, "protocol.mapper.name": "BR-SAT-MON"}, {"id": 10000000000075, "protocol.mapper.number": 77, "protocol.mapper.name": "SUN-ND"}, {"id": 10000000000076, "protocol.mapper.number": 78, "protocol.mapper.name": "WB-MON"}, {"id": 10000000000077, "protocol.mapper.number": 79, "protocol.mapper.name": "WB-EXPAK"}, {"id": 10000000000078, "protocol.mapper.number": 80, "protocol.mapper.name": "ISO-IP"}, {"id": 10000000000079, "protocol.mapper.number": 81, "protocol.mapper.name": "VMTP"}, {"id": 10000000000080, "protocol.mapper.number": 82, "protocol.mapper.name": "SECURE-VMTP"}, {"id": 10000000000081, "protocol.mapper.number": 83, "protocol.mapper.name": "VINES"}, {"id": 10000000000082, "protocol.mapper.number": 84, "protocol.mapper.name": "TTP"}, {"id": 10000000000083, "protocol.mapper.number": 84, "protocol.mapper.name": "IPTM"}, {"id": 10000000000084, "protocol.mapper.number": 85, "protocol.mapper.name": "NSFNET-IGP"}, {"id": 10000000000085, "protocol.mapper.number": 86, "protocol.mapper.name": "DGP"}, {"id": 10000000000086, "protocol.mapper.number": 87, "protocol.mapper.name": "TCF"}, {"id": 10000000000087, "protocol.mapper.number": 88, "protocol.mapper.name": "EIGRP"}, {"id": 10000000000088, "protocol.mapper.number": 89, "protocol.mapper.name": "OSPF"}, {"id": 10000000000089, "protocol.mapper.number": 90, "protocol.mapper.name": "Sprite-RPC"}, {"id": 10000000000090, "protocol.mapper.number": 91, "protocol.mapper.name": "LARP"}, {"id": 10000000000091, "protocol.mapper.number": 92, "protocol.mapper.name": "MTP"}, {"id": 10000000000092, "protocol.mapper.number": 93, "protocol.mapper.name": "AX.25"}, {"id": 10000000000093, "protocol.mapper.number": 94, "protocol.mapper.name": "OS"}, {"id": 10000000000094, "protocol.mapper.number": 95, "protocol.mapper.name": "MICP"}, {"id": 10000000000095, "protocol.mapper.number": 96, "protocol.mapper.name": "SCC-SP"}, {"id": 10000000000096, "protocol.mapper.number": 97, "protocol.mapper.name": "ETHERIP"}, {"id": 10000000000097, "protocol.mapper.number": 98, "protocol.mapper.name": "ENCAP"}, {"id": 10000000000098, "protocol.mapper.number": 100, "protocol.mapper.name": "GMTP"}, {"id": 10000000000099, "protocol.mapper.number": 101, "protocol.mapper.name": "IFMP"}, {"id": 10000000000100, "protocol.mapper.number": 102, "protocol.mapper.name": "PNNI"}, {"id": 10000000000101, "protocol.mapper.number": 103, "protocol.mapper.name": "PIM"}, {"id": 10000000000102, "protocol.mapper.number": 104, "protocol.mapper.name": "ARIS"}, {"id": 10000000000103, "protocol.mapper.number": 105, "protocol.mapper.name": "SCPS"}, {"id": 10000000000104, "protocol.mapper.number": 106, "protocol.mapper.name": "QNX"}, {"id": 10000000000105, "protocol.mapper.number": 107, "protocol.mapper.name": "A/N"}, {"id": 10000000000106, "protocol.mapper.number": 108, "protocol.mapper.name": "IPComp"}, {"id": 10000000000107, "protocol.mapper.number": 109, "protocol.mapper.name": "SNP"}, {"id": 10000000000108, "protocol.mapper.number": 110, "protocol.mapper.name": "Compaq-Peer"}, {"id": 10000000000109, "protocol.mapper.number": 111, "protocol.mapper.name": "IPX-in-IP"}, {"id": 10000000000110, "protocol.mapper.number": 112, "protocol.mapper.name": "VRRP"}, {"id": 10000000000111, "protocol.mapper.number": 113, "protocol.mapper.name": "PGM"}, {"id": 10000000000112, "protocol.mapper.number": 115, "protocol.mapper.name": "L2TP"}, {"id": 10000000000113, "protocol.mapper.number": 116, "protocol.mapper.name": "DDX"}, {"id": 10000000000114, "protocol.mapper.number": 117, "protocol.mapper.name": "IATP"}, {"id": 10000000000115, "protocol.mapper.number": 118, "protocol.mapper.name": "STP"}, {"id": 10000000000116, "protocol.mapper.number": 119, "protocol.mapper.name": "SRP"}, {"id": 10000000000117, "protocol.mapper.number": 120, "protocol.mapper.name": "UTI"}, {"id": 10000000000118, "protocol.mapper.number": 121, "protocol.mapper.name": "SMP"}, {"id": 10000000000119, "protocol.mapper.number": 122, "protocol.mapper.name": "SM"}, {"id": 10000000000120, "protocol.mapper.number": 123, "protocol.mapper.name": "PTP"}, {"id": 10000000000121, "protocol.mapper.number": 124, "protocol.mapper.name": "IS-IS"}, {"id": 10000000000122, "protocol.mapper.number": 125, "protocol.mapper.name": "FIRE"}, {"id": 10000000000123, "protocol.mapper.number": 126, "protocol.mapper.name": "CRTP"}, {"id": 10000000000124, "protocol.mapper.number": 127, "protocol.mapper.name": "CRUDP"}, {"id": 10000000000125, "protocol.mapper.number": 128, "protocol.mapper.name": "SSCOPMCE"}, {"id": 10000000000126, "protocol.mapper.number": 129, "protocol.mapper.name": "IPLT"}, {"id": 10000000000127, "protocol.mapper.number": 130, "protocol.mapper.name": "SPS"}, {"id": 10000000000128, "protocol.mapper.number": 131, "protocol.mapper.name": "PIPE"}, {"id": 10000000000129, "protocol.mapper.number": 132, "protocol.mapper.name": "SCTP"}, {"id": 10000000000130, "protocol.mapper.number": 133, "protocol.mapper.name": "FC"}, {"id": 10000000000131, "protocol.mapper.number": 134, "protocol.mapper.name": "RSVP-E2E-IGNORE"}, {"id": 10000000000132, "protocol.mapper.number": 135, "protocol.mapper.name": "Mobility"}, {"id": 10000000000133, "protocol.mapper.number": 136, "protocol.mapper.name": "UDPLite"}, {"id": 10000000000134, "protocol.mapper.number": 137, "protocol.mapper.name": "MPLS-in-IP"}, {"id": 10000000000135, "protocol.mapper.number": 138, "protocol.mapper.name": "manet"}, {"id": 10000000000136, "protocol.mapper.number": 139, "protocol.mapper.name": "HIP"}, {"id": 10000000000137, "protocol.mapper.number": 140, "protocol.mapper.name": "Shim6"}, {"id": 10000000000138, "protocol.mapper.number": 141, "protocol.mapper.name": "WESP"}, {"id": 10000000000139, "protocol.mapper.number": 142, "protocol.mapper.name": "ROHC"}, {"id": 10000000000140, "protocol.mapper.number": 143, "protocol.mapper.name": "Ethernet"}], "version": "1.0"}]}