{"entity": "LDAP Server", "collection": "ldap.server", "version": "1.0", "author": "<PERSON>", "props": [{"name": "ldap.server.primary.host", "title": "Host", "type": "string", "rules": ["required"]}, {"name": "ldap.server.fqdn", "title": "FQDN", "type": "string", "rules": ["required"]}, {"name": "ldap.server.port", "title": "Port", "type": "numeric", "rules": ["required"]}, {"name": "ldap.server.username", "title": "Username", "type": "string", "rules": ["required"]}, {"name": "ldap.server.password", "title": "Password", "type": "password", "rules": ["required"]}, {"name": "ldap.server.enable.auth", "title": "Enable LDAP <PERSON>th", "type": "string", "rules": ["required"]}, {"name": "ldap.server.auto.sync", "title": "Auto sync", "type": "string", "rules": ["required"]}, {"name": "ldap.server.sync.interval", "title": "Schedule Every", "type": "numeric", "prerequisites": [{"rule": "ldap.server.auto.sync", "value": "yes"}], "rules": ["required"], "values": [8, 12, 24, 48]}, {"name": "ldap.server.protocol", "title": "Server Protocol", "type": "string", "rules": ["required"], "values": ["PLAIN", "SSL"]}, {"name": "ldap.server.type", "title": "Server Type", "type": "string", "rules": ["required"], "values": ["MICROSOFT AD", "OPEN LDAP"]}, {"name": "ldap.server.user.groups", "title": "LDAP Groups", "type": "list"}]}