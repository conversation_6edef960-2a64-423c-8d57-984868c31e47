{"entity": "Event Policy", "collection": "event.policy", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "policy.name", "title": "Policy Name", "type": "string", "rules": ["required"]}, {"name": "policy.type", "title": "Policy Type", "type": "string", "rules": ["required"]}, {"name": "policy.tags", "title": "Policy Tag(s)", "type": "list"}, {"name": "policy.scheduled", "title": "Policy Scheduled", "type": "string"}, {"name": "policy.rolling.window", "title": "Policy Rolling Window", "type": "numeric"}, {"name": "policy.rolling.window.unit", "title": "Policy Rolling Window Unit", "type": "string"}, {"name": "scheduler.start.date", "title": "Policy Scheduler Start Date", "type": "string"}, {"name": "scheduler.times", "title": "Policy Scheduler Time(s)", "values": ["00:00", "00:05", "00:10", "00:15", "00:20", "00:25", "00:30", "00:35", "00:40", "00:45", "00:50", "00:55", "01:00", "01:05", "01:10", "01:15", "01:20", "01:25", "01:30", "01:35", "01:40", "01:45", "01:50", "01:55", "02:00", "02:05", "02:10", "02:15", "02:20", "02:25", "02:30", "02:35", "02:40", "02:45", "02:50", "02:55", "03:00", "03:05", "03:10", "03:15", "03:20", "03:25", "03:30", "03:35", "03:40", "03:45", "03:50", "03:55", "04:00", "04:05", "04:10", "04:15", "04:20", "04:25", "04:30", "04:35", "04:40", "04:45", "04:50", "04:55", "05:00", "05:05", "05:10", "05:15", "05:20", "05:25", "05:30", "05:35", "05:40", "05:45", "05:50", "05:55", "06:00", "06:05", "06:10", "06:15", "06:20", "06:25", "06:30", "06:35", "06:40", "06:45", "06:50", "06:55", "07:00", "07:05", "07:10", "07:15", "07:20", "07:25", "07:30", "07:35", "07:40", "07:45", "07:50", "07:55", "08:00", "08:05", "08:10", "08:15", "08:20", "08:25", "08:30", "08:35", "08:40", "08:45", "08:50", "08:55", "09:00", "09:05", "09:10", "09:15", "09:20", "09:25", "09:30", "09:35", "09:40", "09:45", "09:50", "09:55", "10:00", "10:05", "10:10", "10:15", "10:20", "10:25", "10:30", "10:35", "10:40", "10:45", "10:50", "10:55", "11:00", "11:05", "11:10", "11:15", "11:20", "11:25", "11:30", "11:35", "11:40", "11:45", "11:50", "11:55", "12:00", "12:05", "12:10", "12:15", "12:20", "12:25", "12:30", "12:35", "12:40", "12:45", "12:50", "12:55", "13:00", "13:05", "13:10", "13:15", "13:20", "13:25", "13:30", "13:35", "13:40", "13:45", "13:50", "13:55", "14:00", "14:05", "14:10", "14:15", "14:20", "14:25", "14:30", "14:35", "14:40", "14:45", "14:50", "14:55", "15:00", "15:05", "15:10", "15:15", "15:20", "15:25", "15:30", "15:35", "15:40", "15:45", "15:50", "15:55", "16:00", "16:05", "16:10", "16:15", "16:20", "16:25", "16:30", "16:35", "16:40", "16:45", "16:50", "16:55", "17:00", "17:05", "17:10", "17:15", "17:20", "17:25", "17:30", "17:35", "17:40", "17:45", "17:50", "17:55", "18:00", "18:05", "18:10", "18:15", "18:20", "18:25", "18:30", "18:35", "18:40", "18:45", "18:50", "18:55", "19:00", "19:05", "19:10", "19:15", "19:20", "19:25", "19:30", "19:35", "19:40", "19:45", "19:50", "19:55", "20:00", "20:05", "20:10", "20:15", "20:20", "20:25", "20:30", "20:35", "20:40", "20:45", "20:50", "20:55", "21:00", "21:05", "21:10", "21:15", "21:20", "21:25", "21:30", "21:35", "21:40", "21:45", "21:50", "21:55", "22:00", "22:05", "22:10", "22:15", "22:20", "22:25", "22:30", "22:35", "22:40", "22:45", "22:50", "22:55", "23:00", "23:05", "23:10", "23:15", "23:20", "23:25", "23:30", "23:35", "23:40", "23:45", "23:50", "23:55"], "type": "list"}, {"name": "scheduler.timeline", "title": "Policy Scheduler Timeline", "type": "string", "values": ["Once", "Daily", "Weekly", "Monthly"]}, {"name": "scheduler.days", "title": "Policy Select Day(s)", "values": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "type": "list", "prerequisites": [{"rule": "scheduler.timeline", "value": "Weekly"}]}, {"name": "scheduler.months", "title": "Policy Select Month(s)", "values": ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"], "type": "list", "prerequisites": [{"rule": "scheduler.timeline", "value": "Monthly"}]}, {"name": "scheduler.dates", "title": "Policy Select Date(s)", "values": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30"], "type": "list", "prerequisites": [{"rule": "scheduler.timeline", "value": "Monthly"}]}, {"name": "policy.context", "title": "Policy Context", "type": "map", "rules": ["required"]}, {"name": "policy.title", "title": "Policy Title", "type": "string"}, {"name": "policy.message", "title": "Policy Message", "type": "string"}, {"name": "policy.sms.notification.recipients", "title": "Policy SMS Notification Recipient(s)", "type": "list"}, {"name": "policy.suppress.action", "title": "Policy Suppress Action", "type": "string"}, {"name": "policy.suppress.window", "title": "Policy Suppress Window", "type": "numeric"}, {"name": "policy.suppress.window.unit", "title": "Policy Suppress Window Unit", "type": "string"}, {"name": "policy.actions", "title": "Policy Action(s)", "type": "map"}], "entries": [{"type": "inline", "records": [{"policy.name": "Windows User Account Enabled", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Windows", "Windows Cluster"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "4722"}, {"operand": "message", "operator": "contain", "value": "Enabled"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": **********, "policy.state": "no", "_type": "1", "id": **************}, {"policy.name": "Windows User Account Locked Out", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Windows", "Windows Cluster"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "4740"}, {"operand": "message", "operator": "contain", "value": "Locked Out"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": **********, "policy.state": "no", "_type": "1", "id": **************}, {"policy.name": "Windows User Account Deleted", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Windows", "Windows Cluster"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "4726"}, {"operand": "message", "operator": "contain", "value": "Deleted"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": **********, "policy.state": "no", "_type": "1", "id": **************}, {"policy.name": "Windows User Account Disabled", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Windows", "Windows Cluster"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "4725"}, {"operand": "message", "operator": "contain", "value": "Disabled"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": **********, "policy.state": "no", "_type": "1", "id": **************}, {"policy.name": "Windows User Account Password Reset Failed", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Windows", "Windows Cluster"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "4724"}, {"operand": "message", "operator": "contain", "value": "Password Reset"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": **********, "policy.state": "no", "_type": "1", "id": **************}, {"policy.name": "Windows User Account Unlocked", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Windows", "Windows Cluster"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "4767"}, {"operand": "message", "operator": "contain", "value": "Unlocked"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": **********, "policy.state": "no", "_type": "1", "id": **************}, {"policy.name": "Windows User Account Name Changed", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Windows", "Windows Cluster"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "4781"}, {"operand": "message", "operator": "contain", "value": "Name Change"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": **********, "policy.state": "no", "_type": "1", "id": **************}, {"policy.name": "Windows User Account Password Changed", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Windows", "Windows Cluster"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "4723"}, {"operand": "message", "operator": "contain", "value": "Password Change"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685361220, "policy.state": "no", "_type": "1", "id": 10000000000008}, {"policy.name": "Windows Registry Value Deleted", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Windows Cluster", "Windows"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "4657"}, {"operand": "message", "operator": "contain", "value": "deleted"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685360422, "policy.state": "no", "_type": "1", "id": 10000000000009}, {"policy.name": "Windows Registry Value Modified", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Windows", "Windows Cluster"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "4657"}, {"operand": "message", "operator": "contain", "value": "modified"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": **********, "policy.state": "no", "_type": "1", "id": **************}, {"policy.name": "Windows User Account Access Modified", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Windows Cluster", "Windows"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "ACL Modification"}, {"operand": "message", "operator": "contain", "value": "4780"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": **********, "policy.state": "no", "_type": "1", "id": **************}, {"policy.name": "Windows User Account Changed", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Windows", "Windows Cluster"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "4738"}, {"operand": "message", "operator": "contain", "value": "Changed"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": **********, "policy.state": "no", "_type": "1", "id": **************}, {"policy.name": "Windows User Account Created", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Windows", "Windows Cluster"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "4720"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685360705, "policy.state": "no", "_type": "1", "id": 10000000000013}, {"policy.name": "Windows Administrator Lo<PERSON> Failed", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Windows", "Windows Cluster"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "failed"}, {"operand": "message", "operator": "contain", "value": "administrator"}]}]}}}, "policy.message": "", "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685355269, "policy.state": "no", "_type": "1", "id": 10000000000014}, {"policy.name": "Windows Registry Value Created", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Windows", "Windows Cluster"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "4657"}, {"operand": "message", "operator": "contain", "value": "created"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685360366, "policy.state": "no", "_type": "1", "id": 10000000000015}, {"policy.name": "Apache Tomcat - Failed Authentication Attempts", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Apache Tomcat"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Failed login attempt"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685511087, "policy.state": "no", "_type": "1", "id": 10000000000016}, {"policy.name": "Syslog - User add failed", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "failed adding user"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685353466, "policy.state": "no", "_type": "1", "id": 10000000000017}, {"policy.name": "Syslog - User entered incorrect password", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Invalid credentials"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685353790, "policy.state": "no", "_type": "1", "id": 10000000000021}, {"policy.name": "Syslog - User missed the password more than one time", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "more authentication failures;"}, {"operand": "message", "operator": "contain", "value": "REPEATED login failures"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685353887, "policy.state": "no", "_type": "1", "id": 10000000000022}, {"policy.name": "Syslog - Three failed attempts to run sudo", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "3 incorrect password attempts"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685352816, "policy.state": "no", "_type": "1", "id": 10000000000023}, {"policy.name": "Syslog - Timeout while logging in (sshd)", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "fatal: Timeout before authentication for"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685352874, "policy.state": "no", "_type": "1", "id": 10000000000024}, {"policy.name": "Syslog - System running out of memory", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Out of Memory:"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685352762, "policy.state": "no", "_type": "1", "id": 10000000000025}, {"policy.name": "Syslog - Unknown PAM module, PAM misconfiguration", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "error: PAM: <PERSON><PERSON><PERSON> is unknown for"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685353018, "policy.state": "no", "_type": "1", "id": 10000000000026}, {"policy.name": "Syslog - Unauthorized user attempted to use sudo", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "incorrect password attempt"}, {"operand": "message", "operator": "contain", "value": "user NOT in sudoers"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685352944, "policy.state": "no", "_type": "1", "id": 10000000000028}, {"policy.name": "Syslog - SSHD key error", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "error: buffer_get_bignum2_ret: negative numbers not supported"}, {"operand": "message", "operator": "contain", "value": "fatal: buffer_get_bignum2: buffer error"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685348824, "policy.state": "no", "_type": "1", "id": 10000000000029}, {"policy.name": "Syslog - Syslogd exiting (logging stopped)", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "exiting on signal"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685348945, "policy.state": "no", "_type": "1", "id": 10000000000030}, {"policy.name": "Syslog - SSHD failed to create a session", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Failed to create session:"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685348659, "policy.state": "no", "_type": "1", "id": 10000000000031}, {"policy.name": "Syslog - SSHD is not accepting connections", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "error: connect to  port failed: Connection refused"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685348714, "policy.state": "no", "_type": "1", "id": 10000000000032}, {"policy.name": "Syslog - System is shutting down", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Kernel log daemon terminating"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685349359, "policy.state": "no", "_type": "1", "id": 10000000000033}, {"policy.name": "Syslog - Syslogd restarted", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "Syslogd restart"}, {"operand": "message", "operator": "contain", "value": "restart"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685349100, "policy.state": "no", "_type": "1", "id": 10000000000034}, {"policy.name": "Syslog - System disconnected from sshd", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Received disconnect from"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685349276, "policy.state": "no", "_type": "1", "id": 10000000000035}, {"policy.name": "Syslog - SSH no route to host", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "No route to host"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685345842, "policy.state": "no", "_type": "1", "id": 10000000000036}, {"policy.name": "Syslog - SSH transport endpoint is not connected", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Transport endpoint is not connected"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685345898, "policy.state": "no", "_type": "1", "id": 10000000000037}, {"policy.name": "Syslog - SSH corrupted MAC on input", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Corrupted MAC on input"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685345717, "policy.state": "no", "_type": "1", "id": 10000000000038}, {"policy.name": "Syslog - SSH get remote port failed", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "get_remote_port failed"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685345780, "policy.state": "no", "_type": "1", "id": 10000000000039}, {"policy.name": "Syslog - SSHD brute force trying to get access to the system", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 6, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "illegal user"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685346102, "policy.state": "no", "_type": "1", "id": 10000000000040}, {"policy.name": "Syslog - SSHD could not negotiate with client", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "MAJOR", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "Unable to negotiate with"}, {"operand": "message", "operator": "contain", "value": "Unable to negotiate a key"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685346204, "policy.state": "no", "_type": "1", "id": 10000000000041}, {"policy.name": "Syslog - SSHD authentication failed", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "Failed"}, {"operand": "message", "operator": "contain", "value": "error: PAM: Authentication"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685345969, "policy.state": "no", "_type": "1", "id": 10000000000042}, {"policy.name": "Syslog - Reverse lookup error (bad ISP or attack)", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "reverse mapping"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685344204, "policy.state": "no", "_type": "1", "id": 10000000000043}, {"policy.name": "Syslog - SSH bad packet length", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Bad packet length"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685344288, "policy.state": "no", "_type": "1", "id": 10000000000044}, {"policy.name": "Syslog - Process exiting (killed)", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "killed by SIGTERM"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685344129, "policy.state": "no", "_type": "1", "id": 10000000000045}, {"policy.name": "Syslog - SSH connection reset by peer", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Connection reset by peer"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685345608, "policy.state": "no", "_type": "1", "id": 10000000000046}, {"policy.name": "Syslog - SSH connection refused", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Connection refused"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685344528, "policy.state": "no", "_type": "1", "id": 10000000000047}, {"policy.name": "Syslog - Possible scan or breakin attempt", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "fatal: Timeout before authentication for"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685343902, "policy.state": "no", "_type": "1", "id": 10000000000048}, {"policy.name": "Syslog - Possible breakin attempt", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "reverse mapping"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685343562, "policy.state": "no", "_type": "1", "id": 10000000000049}, {"policy.name": "Syslog - Possible port forwarding failure", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "error: connect_to"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685343633, "policy.state": "no", "_type": "1", "id": 10000000000050}, {"policy.name": "Syslog - Physical root login", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "ROOT LOGIN on"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685343214, "policy.state": "no", "_type": "1", "id": 10000000000051}, {"policy.name": "Syslog - Possible attack on the ssh server", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Bad protocol version identification"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685343425, "policy.state": "no", "_type": "1", "id": 10000000000052}, {"policy.name": "Syslog - Maximum authentication attempts exceeded", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "error: maximum authentication attempts exceeded"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685109342, "policy.state": "no", "_type": "1", "id": 10000000000053}, {"policy.name": "Syslog - Multiple access attempts using a denied user", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 6, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "not allowed because"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685109534, "policy.state": "no", "_type": "1", "id": 10000000000054}, {"policy.name": "Syslog - Group (or user) deleted from the system", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "delete user"}, {"operand": "message", "operator": "contain", "value": "account deleted"}, {"operand": "message", "operator": "contain", "value": "remove group"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": **********, "policy.state": "no", "_type": "1", "id": **************}, {"policy.name": "Syslog - Failed attempt to run sudo", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "incorrect password attempt"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685108880, "policy.state": "no", "_type": "1", "id": 10000000000056}, {"policy.name": "Syslog - File system full", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "file system full"}, {"operand": "message", "operator": "contain", "value": "No space left on device"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685108936, "policy.state": "no", "_type": "1", "id": 10000000000057}, {"policy.name": "Syslog - Kernel device error", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "ipw2200: Firmware error detected"}, {"operand": "message", "operator": "contain", "value": "ACPI Error"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685109229, "policy.state": "no", "_type": "1", "id": 10000000000058}, {"policy.name": "Syslog - Kernel Input/Output error", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "I/O error: dev"}, {"operand": "message", "operator": "contain", "value": "end_request: I/O error, dev"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685109290, "policy.state": "no", "_type": "1", "id": 10000000000059}, {"policy.name": "Syslog - Illegal root login", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "ILLEGAL ROOT LOGIN"}, {"operand": "message", "operator": "contain", "value": "ROOT LOGIN REFUSED"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685109126, "policy.state": "no", "_type": "1", "id": 10000000000060}, {"policy.name": "Syslog - Attempt to start sshd when something already bound to the port", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "failed: Address already in use"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685105382, "policy.state": "no", "_type": "1", "id": 10000000000061}, {"policy.name": "Syslog - Authentication services were not able to retrieve user credentials", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Authentication service cannot retrieve user credentials"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685105594, "policy.state": "no", "_type": "1", "id": 10000000000062}, {"policy.name": "Syslog - A user has attempted to su to an unknown class", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "unknown class"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685105120, "policy.state": "no", "_type": "1", "id": 10000000000063}, {"policy.name": "Syslog - Attempt to login using a denied user", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "not allowed because"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685105188, "policy.state": "no", "_type": "1", "id": 10000000000064}, {"policy.name": "Syslog - Corrupted bytes on SSHD", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Corrupted check bytes on"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685108484, "policy.state": "no", "_type": "1", "id": 10000000000065}, {"policy.name": "Syslog - Error message from the kernel", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Oversized packet received from"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685108828, "policy.state": "no", "_type": "1", "id": 10000000000066}, {"policy.name": "Syslog - Connection blocked by Tcp Wrappers", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "refused connect from"}, {"operand": "message", "operator": "contain", "value": "libwrap refused connection"}, {"operand": "message", "operator": "contain", "value": "Connection from denied"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685105846, "policy.state": "no", "_type": "1", "id": 10000000000067}, {"policy.name": "Syslog - Connection reset or aborted", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "error: setsockopt SO_KEEPALIVE: Connection reset by peer"}, {"operand": "message", "operator": "contain", "value": "error: accept: Software caused connection abort"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685108192, "policy.state": "no", "_type": "1", "id": 10000000000068}, {"policy.name": "Nginx Error Log - Incomplete client request", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["<PERSON><PERSON><PERSON>"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "accept() failed (53: Software caused connection abort)"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685104080, "policy.state": "no", "_type": "1", "id": 10000000000069}, {"policy.name": "Nginx Error Log - Initial 401 authentication request", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["<PERSON><PERSON><PERSON>"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "no user/password was provided for basic authentication"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685104127, "policy.state": "no", "_type": "1", "id": 10000000000070}, {"policy.name": "Nginx Error Log - Access Forbidden", "policy.type": "Log", "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["<PERSON><PERSON><PERSON>"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "access forbidden by rule"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685103875, "policy.state": "no", "_type": "1", "id": 10000000000071, "policy.tags": ["<PERSON><PERSON><PERSON>"]}, {"policy.name": "Nginx Error Log - Common cache error when files were removed", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["<PERSON><PERSON><PERSON>"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "failed (2: No such file or directory)"}, {"operand": "message", "operator": "contain", "value": "failed (2: The system cannot find the file specified)"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685103944, "policy.state": "no", "_type": "1", "id": 10000000000072}, {"policy.name": "Nginx Error Log - Web authentication failed", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["<PERSON><PERSON><PERSON>"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "password mismatch, client"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685104901, "policy.state": "no", "_type": "1", "id": 10000000000073}, {"policy.name": "Nginx Error Log - Invalid URI, file name too long", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["<PERSON><PERSON><PERSON>"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "failed (36: File name too long)"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685104609, "policy.state": "no", "_type": "1", "id": 10000000000074}, {"policy.name": "Nginx Error Log - Multiple web authentication failures", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["<PERSON><PERSON><PERSON>"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 6, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "password mismatch"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685104706, "policy.state": "no", "_type": "1", "id": 10000000000075}, {"policy.name": "Apache Error Log - Code Red attack", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Apache HTTP"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Client sent malformed Host header"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685101914, "policy.state": "no", "_type": "1", "id": 10000000000076}, {"policy.name": "Apache Error Log - Invalid URI (bad client request)", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Apache HTTP"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Invalid URI in request"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685102002, "policy.state": "no", "_type": "1", "id": 10000000000077}, {"policy.name": "Apache Error Log - Attempt to access forbidden file or directory", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Apache HTTP"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "denied by server configuration"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685101630, "policy.state": "no", "_type": "1", "id": 10000000000078}, {"policy.name": "Apache Error Log - Attempt to login using a non-existent user", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Apache HTTP"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "user"}, {"operand": "message", "operator": "contain", "value": "not found"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685101790, "policy.state": "no", "_type": "1", "id": 10000000000079}, {"policy.name": "Apache Error Log - Multiple Invalid URI requests", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Apache HTTP"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 8, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Invalid URI in request"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685102828, "policy.state": "no", "_type": "1", "id": 10000000000080}, {"policy.name": "Apache Error Log - User authentication failed", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Apache HTTP"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "authentication failed"}, {"operand": "message", "operator": "contain", "value": "authentication failure"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685102896, "policy.state": "no", "_type": "1", "id": 10000000000081}, {"policy.name": "Apache Error Log - Modsecurity access denied", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Apache HTTP"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "mod_security-message: Access denied"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685102112, "policy.state": "no", "_type": "1", "id": 10000000000082}, {"policy.name": "Apache Error Log - Multiple attempts blocked by Mod Security", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Apache HTTP"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 6, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "mod_security: Access denied"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685102271, "policy.state": "no", "_type": "1", "id": 10000000000083}, {"policy.name": "Apache Error Log - Access attempt blocked by Mod Security", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Apache HTTP"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "mod_security: Access denied"}, {"operand": "message", "operator": "contain", "value": "ModSecurity: Access denied"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685100720, "policy.state": "no", "_type": "1", "id": 10000000000084}, {"policy.name": "Linux Root Activity", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "root"}, {"operand": "message", "operator": "contain", "value": "sudo"}, {"operand": "message", "operator": "contain", "value": "su"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685091717, "policy.state": "no", "_type": "1", "id": 10000000000085}, {"policy.name": "Apache Error Log  - Attempt to access an non-existent file", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Apache HTTP"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "or", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "message", "operator": "contain", "value": "File does not exist:"}, {"operand": "message", "operator": "contain", "value": "failed to open stream: No such file or directory"}, {"operand": "message", "operator": "contain", "value": "Failed opening"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685101447, "policy.state": "no", "_type": "1", "id": 10000000000086}, {"policy.name": "Apache Error Log - Attempt to access forbidden directory index", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Apache HTTP"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Directory index forbidden by rule"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685101575, "policy.state": "no", "_type": "1", "id": 10000000000087}, {"policy.name": "Apache Error Log - Apache segmentation fault", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Apache HTTP"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "MAJOR", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "exit signal Segmentation Fault"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685101101, "policy.state": "no", "_type": "1", "id": 10000000000088}, {"policy.name": "Linux Root Remote Login Failed", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "linux.login.audit.audit.status", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["linux.login.audit.remote.ip", "event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "linux.login.audit.user.name", "operator": "contain", "value": "root"}, {"operand": "message", "operator": "contain", "value": "failed"}, {"operand": "message", "operator": "contain", "value": "rhost"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685087691, "policy.state": "no", "_type": "1", "id": 10000000000089}, {"policy.name": "Linux Root Local Login Failed", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "linux.login.audit.audit.status", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "linux.login.audit.user.name", "operator": "contain", "value": "root"}, {"operand": "message", "operator": "contain", "value": "failed"}]}, {"filter": "exclude", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "rhost"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685087245, "policy.state": "no", "_type": "1", "id": 10000000000090}, {"policy.name": "Linux Root Remote Login Successful", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "linux.login.audit.audit.status", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["linux.login.audit.remote.ip"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "linux.login.audit.user.name", "operator": "contain", "value": "root"}, {"operand": "message", "operator": "contain", "value": "success"}, {"operand": "message", "operator": "contain", "value": "rhost"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685087810, "policy.state": "no", "_type": "1", "id": 10000000000091}, {"policy.name": "Linux Local Login Failed", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "linux.login.audit.audit.status", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "WARNING", "policy.result.by": ["event.source", "linux.login.audit.user.name"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "failed"}]}, {"filter": "exclude", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "rhost"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685080743, "policy.state": "no", "_type": "1", "id": 10000000000092}, {"policy.name": "Linux - Failed Authentication Attempts", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "group", "entities": [10000000000019], "data.point": "linux.login.audit.event.type", "aggregator": "count", "operator": ">=", "value": 5, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "linux.login.audit.audit.status", "operator": "=", "value": "failed"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1683805342, "policy.state": "no", "_type": "1", "id": 10000000000093}, {"policy.name": "Linux - Critical Syslog Events", "policy.type": "Log", "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "linux.syslog.event.type", "aggregator": "count", "operator": ">=", "value": 25, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 1, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1683806275, "policy.state": "no", "_type": "1", "id": 10000000000094, "policy.tags": ["<PERSON><PERSON><PERSON>"]}, {"policy.name": "UDP Flood Attack", "policy.type": "Flow", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entities": [], "data.point": "packets.per.sec", "aggregator": "avg", "operator": ">=", "value": 1000000, "trigger.mode": "individual", "evaluation.window": 10, "evaluation.window.unit": "minute", "evaluation.frequency": 1, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "protocol", "operator": "=", "value": "17 (UDP)"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1683890284, "policy.state": "no", "_type": "1", "id": 10000000000095}, {"policy.name": "Unusual High Traffic to ASN", "policy.type": "Flow", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entities": [], "data.point": "volume.bytes.per.sec", "aggregator": "avg", "operator": ">=", "value": 12500000, "trigger.mode": "individual", "evaluation.window": 10, "evaluation.window.unit": "minute", "evaluation.frequency": 1, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["destination.as"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1683886700, "policy.state": "no", "_type": "1", "id": 10000000000096}, {"policy.name": "High BPS - TCP", "policy.type": "Flow", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entities": [], "data.point": "volume.bytes.per.sec", "aggregator": "avg", "operator": ">=", "value": 250000000, "trigger.mode": "individual", "evaluation.window": 10, "evaluation.window.unit": "minute", "evaluation.frequency": 1, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["destination.ip"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "protocol", "operator": "=", "value": "6 (TCP)"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1683886393, "policy.state": "no", "_type": "1", "id": 10000000000097}, {"policy.name": "High BPS - UDP", "policy.type": "Flow", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entities": [], "data.point": "volume.bytes.per.sec", "aggregator": "avg", "operator": ">=", "value": 125000000, "trigger.mode": "individual", "evaluation.window": 10, "evaluation.window.unit": "minute", "evaluation.frequency": 1, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["destination.ip"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "protocol", "operator": "=", "value": "17 (UDP)"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1683886308, "policy.state": "no", "_type": "1", "id": 10000000000098}, {"policy.name": "High TCP RST", "policy.type": "Flow", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entities": [], "data.point": "packets.per.sec", "aggregator": "avg", "operator": ">=", "value": 100000, "trigger.mode": "individual", "evaluation.window": 10, "evaluation.window.unit": "minute", "evaluation.frequency": 1, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["source.ip"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "tcp.flags", "operator": "=", "value": "unknown"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1683886097, "policy.state": "no", "_type": "1", "id": 10000000000099}, {"policy.name": "DDoS - Amplification Reflection Attack", "policy.type": "Flow", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entities": [], "data.point": "volume.bytes.per.sec", "aggregator": "avg", "operator": ">=", "value": 1000000, "trigger.mode": "individual", "evaluation.window": 10, "evaluation.window.unit": "minute", "evaluation.frequency": 1, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["source.ip"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "protocol", "operator": "=", "value": "17 (UDP)"}, {"operand": "source.port", "operator": "=", "value": "0 (Unknown)"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1683890020, "policy.state": "no", "_type": "1", "id": 10000000000100}, {"policy.name": "Very Low or No Flow", "policy.type": "Flow", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entities": [], "data.point": "flows", "aggregator": "avg", "operator": "<=", "value": 5, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 1, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1683889709, "policy.state": "no", "_type": "1", "id": 10000000000101}, {"policy.name": "Unusual High Traffic from ASN", "policy.type": "Flow", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entities": [], "data.point": "volume.bytes.per.sec", "aggregator": "avg", "operator": ">=", "value": 12500000, "trigger.mode": "individual", "evaluation.window": 10, "evaluation.window.unit": "minute", "evaluation.frequency": 1, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["source.as"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1683887065, "policy.state": "no", "_type": "1", "id": 10000000000102}, {"policy.name": "High BPS - Source Interface", "policy.type": "Flow", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entities": [], "data.point": "volume.bytes.per.sec", "aggregator": "avg", "operator": ">=", "value": 250000000, "trigger.mode": "individual", "evaluation.window": 10, "evaluation.window.unit": "minute", "evaluation.frequency": 1, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["source.if.index"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1683883715, "policy.state": "no", "_type": "1", "id": 10000000000103}, {"policy.name": "ICMP Flood Attack", "policy.type": "Flow", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entities": [], "data.point": "packets.per.sec", "aggregator": "avg", "operator": ">=", "value": 100000, "trigger.mode": "individual", "evaluation.window": 10, "evaluation.window.unit": "minute", "evaluation.frequency": 1, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["destination.ip"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "protocol", "operator": "=", "value": "1 (ICMP)"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1683877516, "policy.state": "no", "_type": "1", "id": 10000000000104}, {"policy.name": "DNS Attack", "policy.type": "Flow", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entities": [], "data.point": "packets.per.sec", "aggregator": "avg", "operator": ">=", "value": 250000, "trigger.mode": "individual", "evaluation.window": 10, "evaluation.window.unit": "minute", "evaluation.frequency": 1, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "source.port", "operator": "=", "value": "53 (Unknown/DNS)"}, {"operand": "destination.port", "operator": "=", "value": "53 (Domain)"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1683885946, "policy.state": "no", "_type": "1", "id": 10000000000105}, {"policy.name": "UDP Badport Attack", "policy.type": "Flow", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entities": [], "data.point": "volume.bytes.per.sec", "aggregator": "avg", "operator": ">=", "value": 125000000, "trigger.mode": "individual", "evaluation.window": 10, "evaluation.window.unit": "minute", "evaluation.frequency": 1, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["source.ip"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "protocol", "operator": "=", "value": "17 (UDP)"}, {"operand": "source.port", "operator": "in", "value": ["7 (<PERSON>)", "37 (Time)", "137 (NetBIOS-NS)", "138 (NetBIOS-DGM)"]}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1683885621, "policy.state": "no", "_type": "1", "id": 10000000000106}, {"policy.name": "Very High Packets/s", "policy.type": "Flow", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entities": [], "data.point": "packets.per.sec", "aggregator": "avg", "operator": ">=", "value": 100000, "trigger.mode": "individual", "evaluation.window": 10, "evaluation.window.unit": "minute", "evaluation.frequency": 1, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["source.ip"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1683884289, "policy.state": "no", "_type": "1", "id": 10000000000107}, {"policy.name": "High BPS - Destination Interface", "policy.type": "Flow", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entities": [], "data.point": "volume.bytes.per.sec", "aggregator": "avg", "operator": ">=", "value": 250000000, "trigger.mode": "individual", "evaluation.window": 10, "evaluation.window.unit": "minute", "evaluation.frequency": 1, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["destination.if.index"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1683883832, "policy.state": "no", "_type": "1", "id": 10000000000108}, {"policy.name": "Malicious Activity - Black IP", "policy.type": "Flow", "policy.scheduled": "no", "policy.context": {"entities": [], "data.point": "flows", "aggregator": "sum", "operator": ">", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 30, "evaluation.frequency.unit": "second", "policy.severity": "CRITICAL", "policy.result.by": ["source.ip"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "source.threat", "operator": "=", "value": "Black IP"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1681187953, "policy.state": "yes", "_type": "1", "id": 10000000000109, "policy.tags": ["<PERSON><PERSON><PERSON>"]}, {"policy.name": "Linux Local Login Successful", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Linux"], "data.point": "linux.login.audit.audit.status", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["linux.login.audit.remote.ip", "linux.login.audit.user.name"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "linux.login.audit.audit.status", "operator": "=", "value": "success"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1684930521, "policy.state": "yes", "_type": "1", "id": 10000000000110}, {"policy.name": "TCP SYN Flood Attack", "policy.type": "Flow", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entities": [], "data.point": "packets.per.sec", "aggregator": "avg", "operator": ">=", "value": 100000, "trigger.mode": "individual", "evaluation.window": 10, "evaluation.window.unit": "minute", "evaluation.frequency": 1, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["source.ip"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "tcp.flags", "operator": "=", "value": "SYN"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1681302685, "policy.state": "yes", "_type": "1", "id": 10000000000111}], "version": "1.1"}]}