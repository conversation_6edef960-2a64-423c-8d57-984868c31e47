{"entity": "Credential Profile", "collection": "credential.profile", "version": "1.0", "author": "<PERSON>", "props": [{"name": "credential.profile.name", "title": "Profile Name", "type": "string", "rules": ["required", "unique"]}, {"name": "credential.profile.protocol", "title": "Protocol", "type": "string", "rules": ["required"], "values": ["Powershell", "SNMP V1/V2c", "SNMP V3", "SSH", "JDBC", "HTTP/HTTPS", "JMX", "JMS", "Cloud", "Telnet"]}], "entries": [{"type": "inline", "records": [{"credential.profile.name": "Default SNMP", "credential.profile.protocol": "SNMP V1/V2c", "credential.profile.context": {"snmp.community": "public", "snmp.version": "v2c"}, "id": 10000000000001}], "version": "1.0"}, {"type": "inline", "records": [{"credential.profile.name": "Basic Mail Configuration", "credential.profile.protocol": "Unknown", "credential.profile.context": {"username": "", "password": ""}, "id": 10000000000002}], "version": "1.1"}]}