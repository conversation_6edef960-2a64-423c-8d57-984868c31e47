{"entity": "Remote Event Processor", "collection": "remote.event.processor", "version": "1.0", "author": "<PERSON>", "props": [{"name": "remote.event.processor.name", "title": "Name", "type": "string", "rules": ["required", "unique"]}, {"name": "remote.event.processor.host", "title": "Host", "type": "string", "rules": ["required", "unique"]}, {"name": "remote.event.processor.ip", "title": "IP Address", "type": "string", "rules": ["required"]}, {"name": "remote.event.processor.uuid", "title": "UUID", "type": "string", "rules": ["required"]}, {"name": "remote.event.processor.type", "title": "Type", "type": "string", "rules": ["required"]}, {"name": "remote.event.processor.version", "title": "Version", "type": "string", "rules": ["required"]}]}