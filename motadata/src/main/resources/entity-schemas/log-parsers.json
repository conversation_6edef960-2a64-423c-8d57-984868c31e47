{"entity": "Log Parser", "collection": "log.parser", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "log.parser.name", "title": "Name", "type": "string", "rules": ["required", "unique"]}, {"name": "log.parser.type", "title": "Type", "type": "string", "rules": ["required"], "values": ["regex", "json", "delimiter", "custom"]}, {"name": "log.parser.event", "title": "Event", "type": "string", "rules": ["required"]}, {"name": "log.parser.condition", "title": "Condition", "type": "string", "rules": ["required"], "values": ["all", "any"]}, {"name": "log.parser.condition.keywords", "title": "Keywords", "type": "list"}, {"name": "log.parser.delimiter", "title": "Delimiter", "type": "string", "rules": ["required"], "prerequisites": [{"rule": "log.parser.type", "value": "delimiter"}]}, {"name": "log.parser.source.type", "title": "Source Type", "type": "string", "rules": ["required"]}, {"name": "log.parser.fields", "title": "Fields", "type": "list", "rules": ["required"]}, {"name": "log.parser.plugin", "title": "Plugin", "type": "numeric"}, {"name": "log.parser.source.vendor", "title": "<PERSON><PERSON><PERSON>", "type": "string"}], "entries": [{"type": "inline", "records": [{"log.parser.name": "Oracle Database", "log.parser.event": "Wed Feb 10 14:23:19 2021 +01:00 LENGTH : '329' ACTION :[102] 'INSERT INTO users (user_id, username, email)               VALUES (123, 'testuser', '<EMAIL>')' DATABASE USER:[4] 'jdoe' PRIVILEGE:[6] 'SYSDBA' CLIENT USER:[0] 'john' STATUS:[1] '0' DBID:[10] '2943533768' SESSIONID:[10] '3444967295' USERHOST:[3] 'PC1' CLIENT ADDRESS:[0] '(ADDRESS=(PROTOCOL=tcp)(HOST=127.0.0.1)(PORT=55962))' ACTION NUMBER:[1] '2'", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1669396254, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": 1612963399, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "329", "log.parser.field.name": "length", "log.parser.field.type": "none"}, {"log.parser.field.value": "INSERT INTO users (user_id, username, email)               VALUES (123, 'testuser', '<EMAIL>')", "log.parser.field.name": "action", "log.parser.field.type": "none"}, {"log.parser.field.value": "jdoe", "log.parser.field.name": "database.user", "log.parser.field.type": "none"}, {"log.parser.field.value": "SYSDBA", "log.parser.field.name": "privilege", "log.parser.field.type": "none"}, {"log.parser.field.value": "john", "log.parser.field.name": "client.user", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "status", "log.parser.field.type": "none"}, {"log.parser.field.value": "2943533768", "log.parser.field.name": "db.id", "log.parser.field.type": "none"}, {"log.parser.field.value": "3444967295", "log.parser.field.name": "session.id", "log.parser.field.type": "none"}, {"log.parser.field.value": "PC1", "log.parser.field.name": "user.host", "log.parser.field.type": "none"}, {"log.parser.field.value": "(ADDRESS=(PROTOCOL=tcp)(HOST=127.0.0.1)(PORT=55962))", "log.parser.field.name": "client.address", "log.parser.field.type": "none"}, {"log.parser.field.value": "INSERT", "log.parser.field.name": "command", "log.parser.field.type": "none"}], "log.parser.type": "custom", "log.parser.plugin": 1********00008, "log.parser.source.type": "Oracle Database", "plugin.id": 600001, "log.parser.entities": [], "id": 1********00001, "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "log.parser.source.vendor": "Oracle"}, {"log.parser.name": "MySQL Slow Query", "log.parser.event": "# User@Host: debian-sys-maint[debian-sys-maint] @ localhost []# Query_time: 0.002205  Lock_time: 0.000165 Rows_sent: 0  Rows_examined: 0 SET timestamp=1443102034;select count(*) into @discard from `information_schema`.`VIEWS`;", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1669396195, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "debian-sys-maint", "log.parser.field.name": "user.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "localhost", "log.parser.field.name": "host.name", "log.parser.field.type": "none"}, {"log.parser.field.value": 1443102034, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "0", "log.parser.field.name": "query.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "lock.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "rows.sent", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "rows.examined", "log.parser.field.type": "none"}, {"log.parser.field.value": "select count(*) into @discard from `information_schema`.`VIEWS`;", "log.parser.field.name": "query", "log.parser.field.type": "none"}], "log.parser.type": "custom", "log.parser.plugin": **************, "log.parser.source.type": "MySQL", "log.parser.upload": "no", "plugin.id": 600002, "log.parser.entities": [], "id": 1********00002, "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "log.parser.source.vendor": "MySQL"}, {"log.parser.name": "Microsoft Exchange", "log.parser.event": "2017-09-15T03:09:33.526Z,,Transport,,*,service started; #MaxConcurrentSubmissions=20; MaxConcurrentDeliveries=20; MaxSmtpOutConnections=Unlimited", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1669396153, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": 1505425173, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "Transport", "log.parser.field.name": "source", "log.parser.field.type": "none"}, {"log.parser.field.value": "*", "log.parser.field.name": "direction", "log.parser.field.type": "none"}, {"log.parser.field.value": "service started; #MaxConcurrentSubmissions=20; MaxConcurrentDeliveries=20; MaxSmtpOutConnections=Unlimited", "log.parser.field.name": "description", "log.parser.field.type": "none"}, {"log.parser.field.value": "transport.connectivity.log", "log.parser.field.name": "log.type", "log.parser.field.type": "none"}], "log.parser.type": "custom", "log.parser.plugin": 1********00005, "log.parser.source.type": "Other", "log.parser.upload": "no", "plugin.id": 600003, "log.parser.entities": [], "id": 1********00003, "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "log.parser.source.vendor": "Microsoft Exchange"}, {"log.parser.name": "Linux Syslog", "log.parser.event": "<38>Feb  3 11:52:49 mindarray-pc7 sshd[21992]: Accepted password for mindarray from ************* port 56141 ssh2", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1669395920, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "38", "log.parser.field.name": "priority", "log.parser.field.type": "none"}, {"log.parser.field.value": 949558969, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "mindarray-pc7", "log.parser.field.name": "logsource", "log.parser.field.type": "none"}, {"log.parser.field.value": "sshd", "log.parser.field.name": "program", "log.parser.field.type": "none"}, {"log.parser.field.value": "21992", "log.parser.field.name": "pid", "log.parser.field.type": "none"}, {"log.parser.field.value": "security/authorization messages", "log.parser.field.name": "facility", "log.parser.field.type": "none"}, {"log.parser.field.value": "Informational", "log.parser.field.name": "severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "mindarray", "log.parser.field.name": "user.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "*************", "log.parser.field.name": "remote.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "56141", "log.parser.field.name": "port", "log.parser.field.type": "none"}, {"log.parser.field.value": "Linux Login Audit", "log.parser.field.name": "event.category", "log.parser.field.type": "none"}, {"log.parser.field.value": "success", "log.parser.field.name": "audit.status", "log.parser.field.type": "none"}], "log.parser.type": "custom", "log.parser.plugin": 1********00004, "log.parser.source.type": "Linux", "log.parser.upload": "no", "plugin.id": 600004, "log.parser.entities": [], "id": 1********00004, "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "log.parser.source.vendor": "Linux"}, {"log.parser.name": "Fortinet", "log.parser.event": "<189>date=2016-09-19 time=18:06:43 devname=DTSPL-FW devid=FW60CA3913000813 logid=********13 type=traffic subtype=forward level=notice vd=root srcip=*********** srcport=28055 srcintf=\\\"internal6\\\" dstip=******* dstport=53 dstintf=\\\"internal5\\\" poluuid=9e973e02-6e65-51e6-2fde-e6238f5acd57 sessionid=1300543 proto=17 action=accept policyid=4 dstcountry=\\\"United States\\\" srccountry=\\\"Reserved\\\" trandisp=snat transip=************** transport=28055 service=\\\"DNS\\\" duration=90 sentbyte=67 rcvdbyte=83 sentpkt=1 rcvdpkt=1 appcat=\\\"unscanned\\\" devtype=\\\"Linux PC\\\" osname=\\\"Linux\\\" osversion=\\\"2.6, 3.3, 3.6, 3.8, 3.9\\\" mastersrcmac=00:0c:29:70:d5:63 srcmac=00:0c:29:70:d5:63", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1669395871, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "189", "log.parser.field.name": "priority", "log.parser.field.type": "none"}, {"log.parser.field.value": "local use 7", "log.parser.field.name": "facility", "log.parser.field.type": "none"}, {"log.parser.field.value": "Notice", "log.parser.field.name": "severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "traffic", "log.parser.field.name": "type", "log.parser.field.type": "none"}, {"log.parser.field.value": "notice", "log.parser.field.name": "log.level", "log.parser.field.type": "none"}, {"log.parser.field.value": "***********", "log.parser.field.name": "source.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "28055", "log.parser.field.name": "source.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "\\internal6\\", "log.parser.field.name": "source.interface", "log.parser.field.type": "none"}, {"log.parser.field.value": "*******", "log.parser.field.name": "destination.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "53", "log.parser.field.name": "destination.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "\\internal5\\", "log.parser.field.name": "destination.interface", "log.parser.field.type": "none"}, {"log.parser.field.value": "17", "log.parser.field.name": "protocol", "log.parser.field.type": "none"}, {"log.parser.field.value": "accept", "log.parser.field.name": "action", "log.parser.field.type": "none"}, {"log.parser.field.value": "\\United States\\", "log.parser.field.name": "destination.country", "log.parser.field.type": "none"}, {"log.parser.field.value": "\\Reserved\\", "log.parser.field.name": "source.country", "log.parser.field.type": "none"}, {"log.parser.field.value": "snat", "log.parser.field.name": "translation.type", "log.parser.field.type": "none"}, {"log.parser.field.value": "**************", "log.parser.field.name": "nat.source.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "28055", "log.parser.field.name": "nat.source.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "\\DNS\\", "log.parser.field.name": "service", "log.parser.field.type": "none"}, {"log.parser.field.value": "90", "log.parser.field.name": "duration", "log.parser.field.type": "none"}, {"log.parser.field.value": "67", "log.parser.field.name": "sent.bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": "83", "log.parser.field.name": "received.bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": "1", "log.parser.field.name": "sent.packets", "log.parser.field.type": "none"}, {"log.parser.field.value": "1", "log.parser.field.name": "received.packets", "log.parser.field.type": "none"}, {"log.parser.field.value": "\\unscanned\\", "log.parser.field.name": "application.category", "log.parser.field.type": "none"}, {"log.parser.field.value": "\\Linux\\", "log.parser.field.name": "os.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "\\2.6, 3.3, 3.6, 3.8, 3.9\\", "log.parser.field.name": "os.version", "log.parser.field.type": "none"}, {"log.parser.field.value": "00:0c:29:70:d5:63", "log.parser.field.name": "source.mac", "log.parser.field.type": "none"}, {"log.parser.field.value": "Traffic", "log.parser.field.name": "event.category", "log.parser.field.type": "none"}, {"log.parser.field.value": 150, "log.parser.field.name": "bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": 1474288603, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}], "log.parser.type": "custom", "log.parser.plugin": 1********00003, "log.parser.source.type": "Firewall", "log.parser.upload": "no", "plugin.id": 600005, "log.parser.entities": [], "id": 1********00005, "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "log.parser.source.vendor": "Fortinet"}, {"log.parser.name": "Cisco Switch", "log.parser.event": "<190>2001139: 30w3d: %SEC-6-IPACCESSLOGP: list 190 denied tcp **************(40714) -> 0.0.0.0(23), 1 packet", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1669395654, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "SEC", "log.parser.field.name": "facility.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "IPACCESSLOGP", "log.parser.field.name": "facility.description", "log.parser.field.type": "none"}, {"log.parser.field.value": "list 190 denied tcp **************(40714) -> 0.0.0.0(23), 1 packet", "log.parser.field.name": "log.message", "log.parser.field.type": "none"}, {"log.parser.field.value": "Informational", "log.parser.field.name": "severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "190", "log.parser.field.name": "list.number", "log.parser.field.type": "none"}, {"log.parser.field.value": "denied", "log.parser.field.name": "action", "log.parser.field.type": "none"}, {"log.parser.field.value": "tcp", "log.parser.field.name": "protocol", "log.parser.field.type": "none"}, {"log.parser.field.value": "**************", "log.parser.field.name": "source.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "40714", "log.parser.field.name": "source.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "0.0.0.0", "log.parser.field.name": "destination.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "23", "log.parser.field.name": "destination.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "1", "log.parser.field.name": "packets", "log.parser.field.type": "none"}, {"log.parser.field.value": "Cisco Device Packets Destined Audit", "log.parser.field.name": "event.category", "log.parser.field.type": "none"}, {"log.parser.field.value": 1669395654, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}], "log.parser.type": "custom", "log.parser.plugin": 1********00002, "log.parser.source.type": "Switch", "log.parser.upload": "no", "plugin.id": 600006, "log.parser.entities": [], "id": 1********00006, "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "log.parser.source.vendor": "Cisco Systems"}, {"log.parser.name": "Cisco ASA", "log.parser.event": "Apr 15 2017 00:21:14 ************ : %ASA-5-111010: User 'john', running 'CLI' from IP 0.0.0.0, executed 'dir disk0:/dap.xml'", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1669395599, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": 1492195874, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "************", "log.parser.field.name": "source.address", "log.parser.field.type": "none"}, {"log.parser.field.value": "ASA", "log.parser.field.name": "firewall.type", "log.parser.field.type": "none"}, {"log.parser.field.value": "111010", "log.parser.field.name": "asa.message.id", "log.parser.field.type": "none"}, {"log.parser.field.value": "Notice", "log.parser.field.name": "severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "Cisco Firewall Command Interface Audit", "log.parser.field.name": "event.category", "log.parser.field.type": "none"}], "log.parser.type": "custom", "log.parser.plugin": 1********00001, "log.parser.source.type": "Firewall", "log.parser.upload": "no", "plugin.id": 600007, "log.parser.entities": [], "id": **************, "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "log.parser.source.vendor": "Cisco Systems"}, {"log.parser.name": "Windows Event", "log.parser.event": "{\"event.timestamp\":*************,\"id\":4776,\"provider\":\"Microsoft-Windows-Security-Auditing\",\"level.description\":0,\"level\":\"Informational\",\"source\":\"Security\",\"template\":\"The computer attempted to validate the credentials for an account.\\r\\n\\r\\nAuthentication Package:\\t%1\\r\\nLogon Account:\\t%2\\r\\nSource Workstation:\\t%3\\r\\nError Code:\\t%4\",\"message\":\"The computer attempted to validate the credentials for an account.\\r\\n\\r\\nAuthentication Package:\\tMICROSOFT_AUTHENTICATION_PACKAGE_V1_0\\r\\nLogon Account:\\tAdministrator\\r\\nSource Workstation:\\t\\r\\nError Code:\\t0x0\",\"task\":\"Credential Validation\",\"audit\":\"Audit Success\"}", "log.parser.condition.keywords": ["source", "provider", "template"], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": "MICROSOFT_AUTHENTICATION_PACKAGE_V1_0", "log.parser.field.name": "authentication.package", "log.parser.field.type": "none"}, {"log.parser.field.value": "Administrator", "log.parser.field.name": "logon.account", "log.parser.field.type": "none"}, {"log.parser.field.value": "0x0", "log.parser.field.name": "error.code", "log.parser.field.type": "none"}, {"log.parser.field.value": "The computer attempted to validate the credentials for an account", "log.parser.field.name": "log.message", "log.parser.field.type": "none"}, {"log.parser.field.value": *************, "log.parser.field.name": "event.timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": 4776, "log.parser.field.name": "id", "log.parser.field.type": "none"}, {"log.parser.field.value": "Microsoft-Windows-Security-Auditing", "log.parser.field.name": "provider", "log.parser.field.type": "none"}, {"log.parser.field.value": 0, "log.parser.field.name": "level.description", "log.parser.field.type": "none"}, {"log.parser.field.value": "Informational", "log.parser.field.name": "level", "log.parser.field.type": "none"}, {"log.parser.field.value": "Security", "log.parser.field.name": "source", "log.parser.field.type": "none"}, {"log.parser.field.value": "Credential Validation", "log.parser.field.name": "task", "log.parser.field.type": "none"}, {"log.parser.field.value": "Audit Success", "log.parser.field.name": "audit", "log.parser.field.type": "none"}], "log.parser.type": "custom", "log.parser.plugin": 1********00014, "log.parser.source.type": "Windows", "id": 1********00008, "plugin.id": 600008, "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "log.parser.source.vendor": "Windows"}, {"log.parser.name": "PaloAlto", "log.parser.event": "Jun 15 14:44:07 PA-5050-Primary.KSEC.KOTAKGROUP.COM 1,2017/06/15 14:44:07,002201000654,TRAFFIC,end,1,2017/06/15 14:44:07,***********,***********,0.0.0.0,0.0.0.0,EMAIL ACCESS,,ksec\\\\ksecexchadmin,A-D_Replication_UDP,vsys1,BOF-102,WAN-010,ae1.524,ae1.502,Log-Fwd-Syslog-2,2017/06/15 14:44:07,33673446,1,51108,389,0,0,0x5e,udp,allow,377,148,229,2,2017/06/15 14:43:15,31,any,0,226075333,0x0,10.0.0.0-10.255.255.255,10.0.0.0-10.255.255.255,0,1,1,aged-out,0,0,0,0,,PA-5050-Primary,from-policy", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1669396304, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "2017/06/15 14:44:07", "log.parser.field.name": "receive.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "002201000654", "log.parser.field.name": "serial.number", "log.parser.field.type": "none"}, {"log.parser.field.value": "TRAFFIC", "log.parser.field.name": "type", "log.parser.field.type": "none"}, {"log.parser.field.value": "end", "log.parser.field.name": "content.type", "log.parser.field.type": "none"}, {"log.parser.field.value": 1497518047, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "***********", "log.parser.field.name": "source.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "***********", "log.parser.field.name": "destination.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "0.0.0.0", "log.parser.field.name": "nat.source.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "0.0.0.0", "log.parser.field.name": "nat.destination.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "EMAIL ACCESS", "log.parser.field.name": "rule.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "ksec\\\\ksecexchadmin", "log.parser.field.name": "destination.user", "log.parser.field.type": "none"}, {"log.parser.field.value": "A-D_Replication_UDP", "log.parser.field.name": "application", "log.parser.field.type": "none"}, {"log.parser.field.value": "vsys1", "log.parser.field.name": "virtual.system", "log.parser.field.type": "none"}, {"log.parser.field.value": "BOF-102", "log.parser.field.name": "source.zone", "log.parser.field.type": "none"}, {"log.parser.field.value": "WAN-010", "log.parser.field.name": "destination.zone", "log.parser.field.type": "none"}, {"log.parser.field.value": "ae1.524", "log.parser.field.name": "inbound.interface", "log.parser.field.type": "none"}, {"log.parser.field.value": "ae1.502", "log.parser.field.name": "outbound.interface", "log.parser.field.type": "none"}, {"log.parser.field.value": "Log-Fwd-Syslog-2", "log.parser.field.name": "log.action", "log.parser.field.type": "none"}, {"log.parser.field.value": "33673446", "log.parser.field.name": "session.id", "log.parser.field.type": "none"}, {"log.parser.field.value": "1", "log.parser.field.name": "repeat.count", "log.parser.field.type": "none"}, {"log.parser.field.value": "51108", "log.parser.field.name": "source.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "389", "log.parser.field.name": "destination.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "nat.source.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "nat.destination.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "0x5e", "log.parser.field.name": "flags", "log.parser.field.type": "none"}, {"log.parser.field.value": "udp", "log.parser.field.name": "protocol", "log.parser.field.type": "none"}, {"log.parser.field.value": "allow", "log.parser.field.name": "action", "log.parser.field.type": "none"}, {"log.parser.field.value": "377", "log.parser.field.name": "bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": "148", "log.parser.field.name": "sent.bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": "229", "log.parser.field.name": "received.bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": "2", "log.parser.field.name": "packets", "log.parser.field.type": "none"}, {"log.parser.field.value": "2017/06/15 14:43:15", "log.parser.field.name": "start.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "31", "log.parser.field.name": "elapsed.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "any", "log.parser.field.name": "category", "log.parser.field.type": "none"}, {"log.parser.field.value": "226075333", "log.parser.field.name": "sequence.number", "log.parser.field.type": "none"}, {"log.parser.field.value": "0x0", "log.parser.field.name": "action.flags", "log.parser.field.type": "none"}, {"log.parser.field.value": "10.0.0.0-10.255.255.255", "log.parser.field.name": "source.location", "log.parser.field.type": "none"}, {"log.parser.field.value": "10.0.0.0-10.255.255.255", "log.parser.field.name": "destination.location", "log.parser.field.type": "none"}, {"log.parser.field.value": "1", "log.parser.field.name": "packets.sent", "log.parser.field.type": "none"}, {"log.parser.field.value": "1", "log.parser.field.name": "packets.received", "log.parser.field.type": "none"}, {"log.parser.field.value": "aged-out", "log.parser.field.name": "session.end.reason", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "device.group.hierarchy.level.1", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "device.group.hierarchy.level.2", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "device.group.hierarchy.level.3", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "device.group.hierarchy.level.4", "log.parser.field.type": "none"}, {"log.parser.field.value": "PA-5050-Primary", "log.parser.field.name": "device.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "from-policy", "log.parser.field.name": "action.source", "log.parser.field.type": "none"}, {"log.parser.field.value": "TRAFFIC", "log.parser.field.name": "event.category", "log.parser.field.type": "none"}], "log.parser.type": "custom", "log.parser.plugin": 1********00009, "log.parser.source.type": "Firewall", "log.parser.upload": "no", "plugin.id": 600009, "log.parser.entities": [], "id": 1********00009, "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "log.parser.source.vendor": "Palo Alto Networks"}, {"log.parser.name": "Windows Update", "log.parser.event": "2022/10/19 18:56:04.1318907 6308  5100  ComApi          Serializing CUpdate 288966EC-8350-432A-87B0-15C6919BDFC4.1", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1669395362, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": 1666185964, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "6308", "log.parser.field.name": "process.id", "log.parser.field.type": "none"}, {"log.parser.field.value": "5100", "log.parser.field.name": "thread.id", "log.parser.field.type": "none"}, {"log.parser.field.value": "ComApi", "log.parser.field.name": "component.name", "log.parser.field.type": "none"}], "log.parser.type": "custom", "log.parser.plugin": 1********00015, "log.parser.source.type": "Windows", "log.parser.upload": "no", "plugin.id": 600010, "log.parser.entities": [], "id": 1********00010, "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "log.parser.source.vendor": "Windows"}, {"log.parser.name": "MySql", "log.parser.event": "131001 03:25:43 [Warning] Invalid (old?) table or database name '#sql-b60_11c'", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1669393832, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": 1380578143, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "Warning", "log.parser.field.name": "severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "Invalid (old?) table or database name '#sql-b60_11c", "log.parser.field.name": "log.message", "log.parser.field.type": "none"}], "log.parser.type": "custom", "log.parser.plugin": 1********00006, "log.parser.source.type": "MySQL", "log.parser.upload": "no", "plugin.id": 600012, "log.parser.entities": [], "id": 1********00011, "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "log.parser.source.vendor": "MySQL"}, {"log.parser.name": "<PERSON>ph<PERSON>", "log.parser.event": "<30>date=2017-03-10 time=14:21:32 timezone=\"IST\" device_name=\"CR100iNG\" device_id=C18615458265-W6SZ4F log_id=010302602002 log_type=\"Firewall\" log_component=\"Appliance Access\" log_subtype=\"Denied\" status=\"Deny\" priority=Information duration=0 fw_rule_id=0 user_name=\"<EMAIL>\" user_gp=\"Employee\" iap=1 ips_policy_id=0 appfilter_policy_id=1 application=\"\" application_risk=0 application_technology=\"\" application_category=\"\" in_interface=\"PortA.12\" out_interface=\"\" src_mac=08:11:96:d3:00:ec src_ip=************ src_country_code= dst_ip=************* dst_country_code= protocol=\"UDP\" src_port=137 dst_port=137 sent_pkts=0  recv_pkts=0 sent_bytes=0 recv_bytes=0 tran_src_ip= tran_src_port=0 tran_dst_ip= tran_dst_port=0 srczonetype=\"\" srczone=\"\" dstzonetype=\"\" dstzone=\"\" dir_disp=\"\" connid=\"\" vconnid=\"\"", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1669396156, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "30", "log.parser.field.name": "priority", "log.parser.field.type": "none"}, {"log.parser.field.value": "system daemons", "log.parser.field.name": "facility", "log.parser.field.type": "none"}, {"log.parser.field.value": "Informational", "log.parser.field.name": "severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "Firewall", "log.parser.field.name": "log.type", "log.parser.field.type": "none"}, {"log.parser.field.value": "<PERSON><PERSON>", "log.parser.field.name": "audit.status", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "duration", "log.parser.field.type": "none"}, {"log.parser.field.value": "<EMAIL>", "log.parser.field.name": "user.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "Employee", "log.parser.field.name": "user.group", "log.parser.field.type": "none"}, {"log.parser.field.value": "PortA.12", "log.parser.field.name": "in.interface", "log.parser.field.type": "none"}, {"log.parser.field.value": "08:11:96:d3:00:ec", "log.parser.field.name": "source.mac", "log.parser.field.type": "none"}, {"log.parser.field.value": "************", "log.parser.field.name": "source.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "*************", "log.parser.field.name": "destination.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "UDP", "log.parser.field.name": "protocol", "log.parser.field.type": "none"}, {"log.parser.field.value": "137", "log.parser.field.name": "source.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "137", "log.parser.field.name": "destination.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "sent.packets", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "received.packets", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "sent.bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "received.bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "translated.source.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "translated.destination.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "Firewall", "log.parser.field.name": "event.category", "log.parser.field.type": "none"}, {"log.parser.field.value": 0, "log.parser.field.name": "bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": 1489135892, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}], "log.parser.type": "custom", "log.parser.plugin": 1********00012, "log.parser.source.type": "Firewall", "log.parser.upload": "no", "plugin.id": 600013, "log.parser.entities": [], "id": 1********00012, "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "log.parser.source.vendor": "Cyberoam Technologies"}, {"log.parser.name": "Windows Firewall", "log.parser.event": "2018-10-16 08:20:36 ALLOW UDP 127.0.0.1 127.0.0.1 54348 53 0 - - - - - - - SEND", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1669395803, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "ALLOW", "log.parser.field.name": "action", "log.parser.field.type": "none"}, {"log.parser.field.value": "UDP", "log.parser.field.name": "protocol", "log.parser.field.type": "none"}, {"log.parser.field.value": "127.0.0.1", "log.parser.field.name": "src-ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "127.0.0.1", "log.parser.field.name": "dst-ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "54348", "log.parser.field.name": "src-port", "log.parser.field.type": "none"}, {"log.parser.field.value": "53", "log.parser.field.name": "dst-port", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "size", "log.parser.field.type": "none"}, {"log.parser.field.value": "SEND", "log.parser.field.name": "path", "log.parser.field.type": "none"}, {"log.parser.field.value": 1539658236, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}], "log.parser.type": "custom", "log.parser.plugin": 1********00013, "log.parser.source.type": "Windows", "log.parser.upload": "no", "plugin.id": 600014, "log.parser.entities": [], "id": 1********00013, "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "log.parser.source.vendor": "Windows"}, {"log.parser.name": "PostgreSQL", "log.parser.event": "2016-11-18 19:14:51 IST LOG: database system is ready to accept connections", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1669396355, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": 1479476691, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "LOG", "log.parser.field.name": "severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "database system is ready to accept connections", "log.parser.field.name": "log.message", "log.parser.field.type": "none"}, {"log.parser.field.value": "PostgreSQL Power Audit", "log.parser.field.name": "event.category", "log.parser.field.type": "none"}], "log.parser.type": "custom", "log.parser.plugin": 1********00010, "log.parser.source.type": "PostgreSQL", "log.parser.upload": "no", "plugin.id": 600015, "log.parser.entities": [], "id": 1********00014, "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "log.parser.source.vendor": "PostgreSQL"}, {"log.parser.name": "Apache Access", "log.parser.event": "127.0.0.1 - <PERSON> [10/Dec/2019:13:55:36 -0700] \"GET /server-status HTTP/1.1\" 200 2326 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36\"", "log.parser.log.positions": [{"start.position": 0, "end.position": 9}, {"start.position": 10, "end.position": 11}, {"start.position": 12, "end.position": 17}, {"start.position": 19, "end.position": 45}, {"start.position": 48, "end.position": 51}, {"start.position": 52, "end.position": 66}, {"start.position": 72, "end.position": 75}, {"start.position": 77, "end.position": 80}, {"start.position": 81, "end.position": 85}, {"start.position": 87, "end.position": 104}, {"start.position": 107, "end.position": 222}], "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": "127.0.0.1", "log.parser.field.name": "remote.ip", "log.parser.field.type": "ip"}, {"log.parser.field.value": "-", "log.parser.field.name": "remote.logical.user.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "<PERSON>", "log.parser.field.name": "remote.user.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "10/Dec/2019:13:55:36 -0700", "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "GET", "log.parser.field.name": "method", "log.parser.field.type": "none"}, {"log.parser.field.value": "/server-status", "log.parser.field.name": "request", "log.parser.field.type": "none"}, {"log.parser.field.value": "1.1", "log.parser.field.name": "http.version", "log.parser.field.type": "none"}, {"log.parser.field.value": "200", "log.parser.field.name": "status.code", "log.parser.field.type": "none"}, {"log.parser.field.value": "2326", "log.parser.field.name": "sent.bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": "http://localhost/", "log.parser.field.name": "referrer", "log.parser.field.type": "none"}, {"log.parser.field.value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36", "log.parser.field.name": "user.agent", "log.parser.field.type": "none"}], "id": 1********00015, "plugin.id": 600016, "log.parser.type": "regex", "log.parser.source.type": "Apache HTTP", "log.parser.date.time.formatter.type": "formatter", "log.parser.date.time.format": "dd/MMM/yyyy:HH:mm:ss Z", "regex": "^((?:\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})|(?:::1))\\s+(\\S+)\\s+(\\S+)\\s+\\[(\\d+\\/\\w+\\/\\d+:\\d+:\\d+:\\d+\\s+[-+]\\d+)]\\s+\\\"(?:(\\S+)\\s+(\\S+)\\s+HTTP\\/(?:(\\S+))?)?\\\"\\s+(\\d+)\\s+((?:\\d+)|-)(?:\\s+\\\"(.*?)\\\"\\s+\\\"(.*?)\\\")?", "log.parser.source.vendor": "Apache HTTP"}, {"log.parser.name": "Microsoft IIS W3C", "log.parser.event": "2020-12-18 06:55:49 ::1 GET /_test/WebForm1.aspx - 8123 MOTADATA0\\Administrator ::1 Mozilla/5.0+(Windows+NT+6.3;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/86.0.4240.198+Safari/537.36 http://localhost:8123/_test/ 200 0 0 2", "log.parser.log.positions": [], "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": "2020-12-18 06:55:49", "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "::1", "log.parser.field.name": "server.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "GET", "log.parser.field.name": "method", "log.parser.field.type": "none"}, {"log.parser.field.value": "/_test/WebForm1.aspx", "log.parser.field.name": "uri.stem", "log.parser.field.type": "none"}, {"log.parser.field.value": "**************", "log.parser.field.name": "uri.query", "log.parser.field.type": "ip"}, {"log.parser.field.value": "8123", "log.parser.field.name": "server.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "MOTADATA0\\Administrator", "log.parser.field.name": "user.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "::1", "log.parser.field.name": "client.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "Mozilla/5.0+(Windows+NT+6.3;+Win64;+x64)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/86.0.4240.198+Safari/537.36", "log.parser.field.name": "user.agent", "log.parser.field.type": "none"}, {"log.parser.field.value": "http://localhost:8123/_test/", "log.parser.field.name": "referer", "log.parser.field.type": "none"}, {"log.parser.field.value": "200", "log.parser.field.name": "status.code", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "sub.status.code", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "windows.status.code", "log.parser.field.type": "none"}, {"log.parser.field.value": "2", "log.parser.field.name": "time.taken", "log.parser.field.type": "none"}], "id": 1********00016, "plugin.id": 600017, "log.parser.type": "regex", "log.parser.source.type": "Microsoft IIS", "log.parser.entities": [], "log.parser.date.time.formatter.type": "formatter", "log.parser.date.time.format": "yyyy-MM-dd HH:mm:ss", "regex": "^(\\d+-\\d+-\\d+ \\d+:\\d+:\\d+)\\s+((\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})|::1)\\s+(\\S+)\\s+(\\S+)\\s+(\\S+)\\s+(\\d+)\\s+(\\S+)\\s+((\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})|::1)\\s+(\\S+)\\s+(\\S+)\\s+(\\d+)\\s+(\\d+)\\s+(\\d+)\\s+(\\d+)", "log.parser.source.vendor": "Microsoft IIS"}, {"log.parser.name": "Microsoft IIS NCSA", "log.parser.event": "************ - Microsoft\\fred [08/Apr/2001:17:39:04 -0800] \"GET /scripts/iisadmin/ism.dll?http/serv HTTP/1.0\" 200 3401", "log.parser.log.positions": [], "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": "**************", "log.parser.field.name": "client.ip", "log.parser.field.type": "ip"}, {"log.parser.field.value": "-", "log.parser.field.name": "user.identifier", "log.parser.field.type": "none"}, {"log.parser.field.value": "Microsoft\\fred", "log.parser.field.name": "user.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "08/Apr/2001:17:39:04 -0800", "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "GET", "log.parser.field.name": "method", "log.parser.field.type": "none"}, {"log.parser.field.value": "/scripts/iisadmin/ism.dll?http/serv", "log.parser.field.name": "request", "log.parser.field.type": "ip"}, {"log.parser.field.value": "1.0", "log.parser.field.name": "http.version", "log.parser.field.type": "none"}, {"log.parser.field.value": "200", "log.parser.field.name": "status.code", "log.parser.field.type": "none"}, {"log.parser.field.value": "3401", "log.parser.field.name": "bytes", "log.parser.field.type": "none"}], "id": 1********00017, "plugin.id": 600018, "log.parser.type": "regex", "log.parser.source.type": "Microsoft IIS", "log.parser.date.time.formatter.type": "formatter", "log.parser.date.time.format": "dd/MMM/yyyy:HH:mm:ss Z", "regex": "^((\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})|::1)\\s+(\\S+)\\s+(\\S+)\\s+\\[(\\d+\\/\\w+\\/\\d+:\\d+:\\d+:\\d+\\s+[-+]\\d+)\\]\\s+\\\"(\\S+)\\s+(\\S+)\\s+HTTP\\/(\\S+)\\\"\\s+(\\S+)\\s+(\\S+)", "log.parser.source.vendor": "Microsoft IIS"}, {"log.parser.name": "Microsoft IIS", "log.parser.event": "**************, anonymous, 03/20/01, 23:58:11, MSFTPSVC, SALES1, **************, 60, 275, 0, 0, 0, PASS, /Intro.htm, -,", "log.parser.log.positions": [], "log.parser.condition": "all", "log.parser.condition.keywords": [], "log.parser.fields": [{"log.parser.field.value": "**************", "log.parser.field.name": "client.ip", "log.parser.field.type": "ip"}, {"log.parser.field.value": "anonymous", "log.parser.field.name": "user.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "03/20/01, 23:58:11", "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "MSFTPSVC", "log.parser.field.name": "service.instance", "log.parser.field.type": "none"}, {"log.parser.field.value": "SALES1", "log.parser.field.name": "server.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "**************", "log.parser.field.name": "server.ip", "log.parser.field.type": "ip"}, {"log.parser.field.value": "60", "log.parser.field.name": "time.taken", "log.parser.field.type": "none"}, {"log.parser.field.value": "275", "log.parser.field.name": "client.sent.bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "server.sent.bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "service.status.code", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "windows.status.code", "log.parser.field.type": "none"}, {"log.parser.field.value": "PASS", "log.parser.field.name": "request.type", "log.parser.field.type": "none"}, {"log.parser.field.value": "/Intro.htm", "log.parser.field.name": "target.operation", "log.parser.field.type": "none"}, {"log.parser.field.value": "-", "log.parser.field.name": "parameters", "log.parser.field.type": "none"}], "id": 1********00018, "plugin.id": 600019, "log.parser.type": "regex", "log.parser.source.type": "Microsoft IIS", "log.parser.date.time.formatter.type": "formatter", "log.parser.date.time.format": "MM/dd/yy, HH:mm:ss", "regex": "^([0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}),\\s+([^,]+),\\s+([0-9]{2}\\/[0-9]{2}\\/[0-9]{2},\\s+[0-9]{1,2}:[0-9]{1,2}:[0-9]{1,2}),\\s+([^,]+),\\s+([^,]+),\\s+([0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}),\\s+([^,]+),\\s+([^,]+),\\s+([^,]+),\\s+([^,]+),\\s+([^,]+),\\s+([^,]+),\\s+([^,]+),\\s+([^,]+),", "log.parser.source.vendor": "Microsoft IIS"}, {"log.parser.name": "Apache Error Log", "log.parser.event": "[Fri Feb 01 22:03:08.318615 2019] [authz_core:debug] [pid 9:tid 140597881775872] mod_authz_core.c(820): [client **********:50752] AH01626: authorization result of Require all granted: granted", "log.parser.log.positions": [{"start.position": 1, "end.position": 32}, {"start.position": 46, "end.position": 51}, {"start.position": 112, "end.position": 122}, {"start.position": 130, "end.position": 191}], "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": "Fri Feb 01 22:03:08.318615 2019", "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "debug", "log.parser.field.name": "severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "**********", "log.parser.field.name": "client.ip", "log.parser.field.type": "ip"}, {"log.parser.field.value": "AH01626: authorization result of Require all granted: grante", "log.parser.field.name": "log.message", "log.parser.field.type": "none"}], "log.parser.type": "regex", "log.parser.source.type": "Apache HTTP", "id": **************, "plugin.id": 600020, "log.parser.date.time.formatter.type": "formatter", "log.parser.date.time.format": "EEE MMM dd HH:mm:ss.SSSSSS yyyy", "regex": "^\\[(\\w+ \\w+ \\d+ \\d+:\\d+:\\d+(?:.\\d+)?\\s+\\d+)\\](?:\\s+)?\\[(?:\\w+\\:)?(\\w+)\\]\\s+\\[(?:\\w+\\s+\\d+(?::)?)?(?:\\w+\\s+\\d+)?\\](?:\\s+\\S+:)?\\s+(?:\\[client\\s+([\\w+.]+):\\d+\\])?(.*)", "log.parser.source.vendor": "Apache HTTP"}, {"log.parser.name": "SonicWall", "log.parser.event": "id=firewall sn=0017C5E89AB4 time=\"2017-06-07 12:06:02 UTC\" fw=************** pri=6 c=1024 m=537 msg=\"Connection Closed\" app=49175 appName=\"General HTTP\" n=145105588 src=***************:50995:X0:acplmum-ho-l007.avendus.com dst=**************:80:X1:domain.not.configured srcMac=bc:16:f5:0d:99:d2 dstMac=00:26:88:85:ab:f4 proto=tcp/http sent=2341 rcvd=3703 spkt=9 rpkt=9 cdur=383", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1681367985, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "0017C5E89AB4", "log.parser.field.name": "sonicwall.traffic.serial.number", "log.parser.field.type": "none"}, {"log.parser.field.value": 1496837162, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "**************", "log.parser.field.name": "sonicwall.traffic.firewall.wan.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "6", "log.parser.field.name": "sonicwall.traffic.priority", "log.parser.field.type": "none"}, {"log.parser.field.value": "Traffic", "log.parser.field.name": "sonicwall.traffic.category", "log.parser.field.type": "none"}, {"log.parser.field.value": "Connection Closed", "log.parser.field.name": "sonicwall.traffic.message.info", "log.parser.field.type": "none"}, {"log.parser.field.value": "49175", "log.parser.field.name": "sonicwall.traffic.application.id", "log.parser.field.type": "none"}, {"log.parser.field.value": "General HTTP", "log.parser.field.name": "sonicwall.traffic.application.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "***************", "log.parser.field.name": "sonicwall.traffic.source.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "**************", "log.parser.field.name": "sonicwall.traffic.destination.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "bc:16:f5:0d:99:d2", "log.parser.field.name": "sonicwall.traffic.source.mac", "log.parser.field.type": "none"}, {"log.parser.field.value": "00:26:88:85:ab:f4", "log.parser.field.name": "sonicwall.traffic.destination.mac", "log.parser.field.type": "none"}, {"log.parser.field.value": "tcp/http", "log.parser.field.name": "sonicwall.traffic.protocol", "log.parser.field.type": "none"}, {"log.parser.field.value": "2341", "log.parser.field.name": "sonicwall.traffic.sent.bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": "3703", "log.parser.field.name": "sonicwall.traffic.received.bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": "9", "log.parser.field.name": "sonicwall.traffic.sent.packets", "log.parser.field.type": "none"}, {"log.parser.field.value": "9", "log.parser.field.name": "sonicwall.traffic.received.packets", "log.parser.field.type": "none"}, {"log.parser.field.value": "383", "log.parser.field.name": "sonicwall.traffic.connection.duration", "log.parser.field.type": "none"}, {"log.parser.field.value": "SonicWall Traffic", "log.parser.field.name": "event.category", "log.parser.field.type": "none"}, {"log.parser.field.value": 6044, "log.parser.field.name": "sonicwall.traffic.bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": 18, "log.parser.field.name": "sonicwall.traffic.packets", "log.parser.field.type": "none"}, {"log.parser.field.value": "kernel messages", "log.parser.field.name": "sonicwall.traffic.facility", "log.parser.field.type": "none"}, {"log.parser.field.value": "Informational", "log.parser.field.name": "sonicwall.traffic.severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "50995", "log.parser.field.name": "sonicwall.traffic.source.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "X0", "log.parser.field.name": "sonicwall.traffic.source.interface", "log.parser.field.type": "none"}, {"log.parser.field.value": "acplmum-ho-l007.avendus.com", "log.parser.field.name": "sonicwall.traffic.source.resolved.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "80", "log.parser.field.name": "sonicwall.traffic.destination.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "X1", "log.parser.field.name": "sonicwall.traffic.destination.interface", "log.parser.field.type": "none"}, {"log.parser.field.value": "domain.not.configured", "log.parser.field.name": "sonicwall.traffic.destination.resolved.name", "log.parser.field.type": "none"}], "log.parser.type": "custom", "log.parser.source.vendor": "SonicWall", "log.parser.plugin": 1********00011, "log.parser.source.type": "Firewall", "log.parser.upload": "no", "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "plugin.id": 600021, "log.parser.entities": [], "id": 1********00020}, {"log.parser.name": "Nginx Access", "log.parser.event": "127.0.0.1 - <PERSON> [10/Dec/2019:13:55:36 -0700] \"GET /server-status HTTP/1.1\" 200 2326 \"http://localhost/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36\"", "log.parser.log.positions": [{"start.position": 0, "end.position": 9}, {"start.position": 10, "end.position": 11}, {"start.position": 12, "end.position": 17}, {"start.position": 19, "end.position": 45}, {"start.position": 48, "end.position": 51}, {"start.position": 52, "end.position": 66}, {"start.position": 72, "end.position": 75}, {"start.position": 77, "end.position": 80}, {"start.position": 81, "end.position": 85}, {"start.position": 87, "end.position": 104}, {"start.position": 107, "end.position": 222}], "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": "127.0.0.1", "log.parser.field.name": "remote.ip", "log.parser.field.type": "ip"}, {"log.parser.field.value": "-", "log.parser.field.name": "remote.logical.user.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "<PERSON>", "log.parser.field.name": "remote.user.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "10/Dec/2019:13:55:36 -0700", "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "GET", "log.parser.field.name": "method", "log.parser.field.type": "none"}, {"log.parser.field.value": "/server-status", "log.parser.field.name": "request", "log.parser.field.type": "none"}, {"log.parser.field.value": "1.1", "log.parser.field.name": "http.version", "log.parser.field.type": "none"}, {"log.parser.field.value": "200", "log.parser.field.name": "status.code", "log.parser.field.type": "none"}, {"log.parser.field.value": "2326", "log.parser.field.name": "sent.bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": "http://localhost/", "log.parser.field.name": "referrer", "log.parser.field.type": "none"}, {"log.parser.field.value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36", "log.parser.field.name": "user.agent", "log.parser.field.type": "none"}], "id": 1********00021, "plugin.id": 600022, "log.parser.type": "regex", "log.parser.source.type": "<PERSON><PERSON><PERSON>", "log.parser.date.time.formatter.type": "formatter", "log.parser.date.time.format": "dd/MMM/yyyy:HH:mm:ss Z", "regex": "^((?:\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})|(?:::1))\\s+(\\S+)\\s+(\\S+)\\s+\\[(\\d+\\/\\w+\\/\\d+:\\d+:\\d+:\\d+\\s+[-+]\\d+)]\\s+\\\"(?:(\\S+)\\s+(\\S+)\\s+HTTP\\/(?:(\\S+))?)?\\\"\\s+(\\d+)\\s+((?:\\d+)|-)(?:\\s+\\\"(.*?)\\\"\\s+\\\"(.*?)\\\")?", "log.parser.source.vendor": "<PERSON><PERSON><PERSON>"}, {"log.parser.name": "<PERSON><PERSON><PERSON>", "log.parser.event": "[Fri Feb 01 22:03:08.318615 2019] [authz_core:debug] [pid 9:tid 140597881775872] mod_authz_core.c(820): [client **********:50752] AH01626: authorization result of Require all granted: granted", "log.parser.log.positions": [{"start.position": 1, "end.position": 32}, {"start.position": 46, "end.position": 51}, {"start.position": 112, "end.position": 122}, {"start.position": 130, "end.position": 191}], "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": "Fri Feb 01 22:03:08.318615 2019", "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "debug", "log.parser.field.name": "severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "**********", "log.parser.field.name": "client.ip", "log.parser.field.type": "ip"}, {"log.parser.field.value": "AH01626: authorization result of Require all granted: grante", "log.parser.field.name": "log.message", "log.parser.field.type": "none"}], "log.parser.type": "regex", "log.parser.source.type": "<PERSON><PERSON><PERSON>", "id": 1********00022, "plugin.id": 600023, "log.parser.date.time.formatter.type": "formatter", "log.parser.date.time.format": "EEE MMM dd HH:mm:ss.SSSSSS yyyy", "regex": "^\\[(\\w+ \\w+ \\d+ \\d+:\\d+:\\d+(?:.\\d+)?\\s+\\d+)\\](?:\\s+)?\\[(?:\\w+\\:)?(\\w+)\\]\\s+\\[(?:\\w+\\s+\\d+(?::)?)?(?:\\w+\\s+\\d+)?\\](?:\\s+\\S+:)?\\s+(?:\\[client\\s+([\\w+.]+):\\d+\\])?(.*)", "log.parser.source.vendor": "<PERSON><PERSON><PERSON>"}, {"log.parser.name": "Nutanix Access", "log.parser.event": "[2023-12-13T19:11:33.317Z] \"GET /stats HTTP/1.1\" 200 233 0 193192 4 - \"***********\" \"ELinks/0.12pre6 (textmode; Linux; -)\" \"-\" \"127.0.0.1:9462\" \"-\"", "log.parser.log.positions": [{"start.position": 1, "end.position": 25}, {"start.position": 28, "end.position": 31}, {"start.position": 32, "end.position": 38}, {"start.position": 44, "end.position": 47}, {"start.position": 49, "end.position": 52}, {"start.position": 53, "end.position": 56}, {"start.position": 85, "end.position": 121}, {"start.position": 128, "end.position": 142}], "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": "2023-12-13T19:11:33.317Z", "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "GET", "log.parser.field.name": "method", "log.parser.field.type": "none"}, {"log.parser.field.value": "/stats", "log.parser.field.name": "request", "log.parser.field.type": "none"}, {"log.parser.field.value": "1.1", "log.parser.field.name": "http.version", "log.parser.field.type": "none"}, {"log.parser.field.value": "200", "log.parser.field.name": "status.code", "log.parser.field.type": "none"}, {"log.parser.field.value": "233", "log.parser.field.name": "sent.bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": "ELinks/0.12pre6 (textmode; Linux; -)", "log.parser.field.name": "user.agent", "log.parser.field.type": "none"}, {"log.parser.field.value": "127.0.0.1:9462", "log.parser.field.name": "server.ip", "log.parser.field.type": "none"}], "log.parser.type": "regex", "log.parser.source.type": "Nutanix", "id": 1********00023, "plugin.id": 600024, "log.parser.date.time.formatter.type": "formatter", "log.parser.date.time.format": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", "regex": "^(?:[^\\[]*\\[){1}(\\d{4}\\-\\d{1,2}\\-\\d{1,2}T\\d{1,2}:\\d{1,2}:\\d{1,2}.\\d{3}Z)\\]\\ \\\"([^\\ ]*)\\ ([^\\ ]*)(?:[^\\/]*\\/){1}([^\\\"]*)\\\"\\ (\\d+)\\ (\\d+)(?:[^\\\"]*\\\"){3}([^\\\"]*)(?:[^\\\"]*\\\"){4}([^\\\"]*)", "log.parser.source.vendor": "Nutanix"}, {"log.parser.name": "Nutanix Hardware Change", "log.parser.event": "2023-08-25 20:49:17 *********** type:removed HardwareRAIDDisk | old_serial:22413D075B4B | old_firmware:D3DJ004 | old_manufacturer:MTFDDAV480TDS | old_ppn:MTFDDAV480TDS | location:1", "log.parser.log.positions": [{"start.position": 0, "end.position": 19}, {"start.position": 20, "end.position": 31}, {"start.position": 37, "end.position": 61}], "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": "2023-08-25 20:49:17", "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "***********", "log.parser.field.name": "hardware.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "removed HardwareRAIDDisk", "log.parser.field.name": "type", "log.parser.field.type": "none"}], "log.parser.type": "regex", "log.parser.source.type": "Nutanix", "id": 1********00024, "plugin.id": 600025, "log.parser.date.time.formatter.type": "formatter", "log.parser.date.time.format": "yyyy-MM-dd HH:mm:ss", "regex": "^(\\d{4}\\-\\d{1,2}\\-\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2})\\ ([^\\ ]*)(?:[^\\:]*\\:){1}(\\w+\\ \\w+)", "log.parser.source.vendor": "Nutanix"}, {"log.parser.name": "Nutanix", "log.parser.event": "2023-09-22 11:51:40 INFO nu_data_format_util.py:568 Finished transferring hw_raid component(s) data to proto", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1702548773, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": 1695363700, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "INFO", "log.parser.field.name": "nutanix.collector.severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "Nutanix Collector", "log.parser.field.name": "event.category", "log.parser.field.type": "none"}], "log.parser.type": "custom", "log.parser.plugin": 1********00016, "log.parser.source.type": "Nutanix", "log.parser.upload": "no", "plugin.id": 600026, "log.parser.entities": [], "id": 1********00025, "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "log.parser.source.vendor": "Nutanix"}, {"log.parser.name": "VMware ESXi", "log.parser.event": "<167>2024-06-19T09:15:00.200Z esxi32.motadata.local Rhttpproxy: verbose rhttpproxy[2100963] [Originator@6876 sub=Proxy Req 27771] Resolved endpoint : [N7Vmacore4Http16LocalServiceSpecE:0x000000f2f1f044d0] _serverNamespace = /sdk action = Redirect _port = 8307", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1721402426, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "167", "log.parser.field.name": "vmware.esxi.priority", "log.parser.field.type": "none"}, {"log.parser.field.value": 1718768700, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "esxi32.motadata.local", "log.parser.field.name": "vmware.esxi.hostname", "log.parser.field.type": "none"}, {"log.parser.field.value": "Rhttpproxy", "log.parser.field.name": "vmware.esxi.service", "log.parser.field.type": "none"}, {"log.parser.field.value": "local use 4", "log.parser.field.name": "vmware.esxi.facility", "log.parser.field.type": "none"}, {"log.parser.field.value": "Debug", "log.parser.field.name": "vmware.esxi.severity", "log.parser.field.type": "none"}], "log.parser.type": "custom", "log.parser.plugin": 1********00017, "id": 1********00026, "log.parser.source.type": "VMware ESXi", "log.parser.upload": "no", "log.parser.source.vendor": "VMware", "plugin.id": 600027, "log.parser.entities": [], "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds"}, {"log.parser.name": "VMware vCenter", "log.parser.event": "<134>1 2024-06-19T15:33:54.122455+05:30 localhost dnsmasq 2059 ID47 [exampleSDID@32473 iut=\"3\" eventSource=\"Application\" eventID=\"1011\"] Jun 19 15:33:54 dnsmasq[2059]: cached ************* is NXDOMAIN", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1721403561, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "134", "log.parser.field.name": "vmware.vcenter.priority", "log.parser.field.type": "none"}, {"log.parser.field.value": "1", "log.parser.field.name": "vmware.vcenter.version", "log.parser.field.type": "none"}, {"log.parser.field.value": 1718791434, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "localhost", "log.parser.field.name": "vmware.vcenter.hostname", "log.parser.field.type": "none"}, {"log.parser.field.value": "dnsmasq", "log.parser.field.name": "vmware.vcenter.application.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "2059", "log.parser.field.name": "vmware.vcenter.process.id", "log.parser.field.type": "none"}, {"log.parser.field.value": "ID47", "log.parser.field.name": "vmware.vcenter.message.id", "log.parser.field.type": "none"}, {"log.parser.field.value": "[exampleSDID@32473 iut=\"3\" eventSource=\"Application\" eventID=\"1011\"]", "log.parser.field.name": "vmware.vcenter.structured.data", "log.parser.field.type": "none"}, {"log.parser.field.value": "local use 0", "log.parser.field.name": "vmware.vcenter.facility", "log.parser.field.type": "none"}, {"log.parser.field.value": "Informational", "log.parser.field.name": "vmware.vcenter.severity", "log.parser.field.type": "none"}], "log.parser.type": "custom", "log.parser.source.vendor": "VMware", "log.parser.plugin": 1********00018, "id": 1********00027, "log.parser.source.type": "vCenter", "log.parser.upload": "no", "plugin.id": 600028, "log.parser.entities": [], "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds"}, {"log.parser.name": "Azure Log Parser", "log.parser.event": "{\"Caller\":\"87c1158d-f64f-4a06-80b2-7188a1cfbf02\",\"Category\":\"Administrative\",\"Claims\":\"{\\\"aio\\\":\\\"k2BgYFjUtcN/50YPfq7D2hPu1whaAgA=\\\",\\\"appid\\\":\\\"aaa130f3-5ed7-404b-87f5-90a5a42750ea\\\",\\\"appidacr\\\":\\\"1\\\",\\\"aud\\\":\\\"https://management.azure.com/\\\",\\\"exp\\\":\\\"**********\\\",\\\"http://schemas.microsoft.com/identity/claims/identityprovider\\\":\\\"https://sts.windows.net/5b4acec3-2592-4187-9489-98c654cc6c87/\\\",\\\"http://schemas.microsoft.com/identity/claims/objectidentifier\\\":\\\"87c1158d-f64f-4a06-80b2-7188a1cfbf02\\\",\\\"http://schemas.microsoft.com/identity/claims/tenantid\\\":\\\"5b4acec3-2592-4187-9489-98c654cc6c87\\\",\\\"http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier\\\":\\\"87c1158d-f64f-4a06-80b2-7188a1cfbf02\\\",\\\"iat\\\":\\\"**********\\\",\\\"idtyp\\\":\\\"app\\\",\\\"iss\\\":\\\"https://sts.windows.net/5b4acec3-2592-4187-9489-98c654cc6c87/\\\",\\\"nbf\\\":\\\"**********\\\",\\\"rh\\\":\\\"0.AUoAw85KW5Ilh0GUiZjGVMxsh0ZIf3kAutdPukPawfj2MBNKAAA.\\\",\\\"uti\\\":\\\"mk7JQsaIIEqlIZz6dpMZAA\\\",\\\"ver\\\":\\\"1.0\\\",\\\"xms_idrel\\\":\\\"7 8\\\",\\\"xms_tcdt\\\":\\\"**********\\\"}\",\"CorrelationId\":\"e19dad6e-e9a6-4028-b95a-02bbf959ebcd\",\"EventName\":\"Begin request\",\"EventTimestamp\":**********,\"Level\":\"Informational\",\"OperationID\":\"e19dad6e-e9a6-4028-b95a-02bbf959ebcd\",\"OperationName\":\"List Storage Account Keys\",\"Status\":\"Started\",\"SubStatus\":\"\",\"SubmissionTimestamp\":**********,\"SubscriptionID\":\"5807cfb0-41a6-4da6-b920-71d934d4a2af\",\"TenantID\":\"5b4acec3-2592-4187-9489-98c654cc6c87\"}", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": **********, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "87c1158d-f64f-4a06-80b2-7188a1cfbf02", "log.parser.field.name": "administrative.caller", "log.parser.field.type": "none"}, {"log.parser.field.value": "Administrative", "log.parser.field.name": "event.category", "log.parser.field.type": "none"}, {"log.parser.field.value": "{\"aio\":\"k2BgYFjUtcN/50YPfq7D2hPu1whaAgA=\",\"appid\":\"aaa130f3-5ed7-404b-87f5-90a5a42750ea\",\"appidacr\":\"1\",\"aud\":\"https://management.azure.com/\",\"exp\":\"**********\",\"http://schemas.microsoft.com/identity/claims/identityprovider\":\"https://sts.windows.net/5b4acec3-2592-4187-9489-98c654cc6c87/\",\"http://schemas.microsoft.com/identity/claims/objectidentifier\":\"87c1158d-f64f-4a06-80b2-7188a1cfbf02\",\"http://schemas.microsoft.com/identity/claims/tenantid\":\"5b4acec3-2592-4187-9489-98c654cc6c87\",\"http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier\":\"87c1158d-f64f-4a06-80b2-7188a1cfbf02\",\"iat\":\"**********\",\"idtyp\":\"app\",\"iss\":\"https://sts.windows.net/5b4acec3-2592-4187-9489-98c654cc6c87/\",\"nbf\":\"**********\",\"rh\":\"0.AUoAw85KW5Ilh0GUiZjGVMxsh0ZIf3kAutdPukPawfj2MBNKAAA.\",\"uti\":\"mk7JQsaIIEqlIZz6dpMZAA\",\"ver\":\"1.0\",\"xms_idrel\":\"7 8\",\"xms_tcdt\":\"**********\"}", "log.parser.field.name": "administrative.claims", "log.parser.field.type": "none"}, {"log.parser.field.value": "e19dad6e-e9a6-4028-b95a-02bbf959ebcd", "log.parser.field.name": "administrative.correlation.id", "log.parser.field.type": "none"}, {"log.parser.field.value": "Begin request", "log.parser.field.name": "administrative.event.name", "log.parser.field.type": "none"}, {"log.parser.field.value": **********, "log.parser.field.name": "event.timestamp", "log.parser.field.type": "none"}, {"log.parser.field.value": "Informational", "log.parser.field.name": "administrative.level", "log.parser.field.type": "none"}, {"log.parser.field.value": "e19dad6e-e9a6-4028-b95a-02bbf959ebcd", "log.parser.field.name": "administrative.operation.id", "log.parser.field.type": "none"}, {"log.parser.field.value": "List Storage Account Keys", "log.parser.field.name": "administrative.operation.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "Started", "log.parser.field.name": "administrative.status", "log.parser.field.type": "none"}, {"log.parser.field.value": **********, "log.parser.field.name": "administrative.submission.timestamp", "log.parser.field.type": "none"}, {"log.parser.field.value": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "log.parser.field.name": "administrative.subscription.id", "log.parser.field.type": "none"}, {"log.parser.field.value": "5b4acec3-2592-4187-9489-98c654cc6c87", "log.parser.field.name": "administrative.tenant.id", "log.parser.field.type": "none"}, {"log.parser.field.value": **********, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}], "log.parser.type": "custom", "log.parser.plugin": **************, "id": **************, "log.parser.source.type": "Azure Cloud", "log.parser.upload": "no", "plugin.id": 600029, "log.parser.entities": [], "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds"}, {"log.parser.name": "Windows Event Collector", "log.parser.event": "{\"audit\":\"Audit Success\",\"id\":\"4648\",\"level\":\"0\",\"level-description\":\"Information\",\"message\":\"A logon was attempted using explicit credentials.  Subject: Security ID:  S-1-5-20 Account Name:  SQLVM$ Account Domain:  W Logon ID:  0x3E4 Logon GUID:  {********-0000-0000-0000-********0000}  Account Whose Credentials Were Used: Account Name:  ADMIN Account Domain:  SQLVM Logon GUID:  {********-0000-0000-0000-********0000}  Target Server: Target Server Name: localhost Additional Information: localhost  Process Information: Process ID:  0x4cc4 Process Name:  C:\\\\Windows\\\\System32\\\\svchost.exe  Network Information: Network Address: - Port:   -  This event is generated when a process attempts to log on an account by explicitly specifying that account’s credentials.  This most commonly occurs in batch-type configurations such as scheduled tasks, or when using the RUNAS command.\",\"provider\":\"Microsoft-Windows-Security-Auditing\",\"source\":\"Security\",\"task\":\"Logon\",\"timestamp\":\"10/1/2024 7:14:52 PM\",\"timezone\":\"+05:30:00\"}", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": **********, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "Audit Success", "log.parser.field.name": "windows.event.collector.audit", "log.parser.field.type": "none"}, {"log.parser.field.value": "4648", "log.parser.field.name": "windows.event.collector.id", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "windows.event.collector.level", "log.parser.field.type": "none"}, {"log.parser.field.value": "Information", "log.parser.field.name": "windows.event.collector.level.description", "log.parser.field.type": "none"}, {"log.parser.field.value": "Microsoft-Windows-Security-Auditing", "log.parser.field.name": "windows.event.collector.provider", "log.parser.field.type": "none"}, {"log.parser.field.value": "Security", "log.parser.field.name": "windows.event.collector.source", "log.parser.field.type": "none"}, {"log.parser.field.value": "Logon", "log.parser.field.name": "windows.event.collector.task", "log.parser.field.type": "none"}, {"log.parser.field.value": **********, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "+05:30:00", "log.parser.field.name": "windows.event.collector.timezone", "log.parser.field.type": "none"}, {"log.parser.field.value": "success", "log.parser.field.name": "windows.event.collector.audit.status", "log.parser.field.type": "none"}], "log.parser.type": "custom", "log.parser.plugin": 1********00014, "id": 1********00029, "log.parser.source.type": "Windows", "log.parser.upload": "no", "plugin.id": 600030, "log.parser.entities": [], "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds"}, {"log.parser.name": "VMware vCenter Task", "log.parser.event": "{    \"complete.time\":\"2024-11-05 14:50:41.77 +0000 UTC\",    \"description\":\"Datacenter.powerOnVm\",    \"execution.time.ms\":4,    \"name\":\"PowerOnMultiVM_Task\",    \"start.time\":\"2024-11-05 14:50:41.765999 +0000 UTC\",    \"status\":\"success\",    \"timestamp\":\"2024-11-05 14:50:41.765999 +0000 UTC\",    \"user.name\":\"VSPHERE.LOCAL\\\\Administrator\",    \"vm.name\":\"Datacenter\" }", "id": 1********00030, "log.parser.condition.keywords": [], "log.parser.condition": "all", "plugin.id": 600031, "log.parser.fields": [{"log.parser.field.value": "2024-11-05 14:50:41.77 +0000 UTC", "log.parser.field.name": "complete.time", "log.parser.field.type": "Any"}, {"log.parser.field.value": "Datacenter.powerOnVm", "log.parser.field.name": "description", "log.parser.field.type": "Any"}, {"log.parser.field.value": "4", "log.parser.field.name": "execution.time.ms", "log.parser.field.type": "Any"}, {"log.parser.field.value": "PowerOnMultiVM_Task", "log.parser.field.name": "name", "log.parser.field.type": "Any"}, {"log.parser.field.value": "2024-11-05 14:50:41.765999 +0000 UTC", "log.parser.field.name": "start.time", "log.parser.field.type": "Any"}, {"log.parser.field.value": "success", "log.parser.field.name": "status", "log.parser.field.type": "Any"}, {"log.parser.field.value": "2024-11-05 14:50:41.765999 +0000 UTC", "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "VSPHERE.LOCAL\\Administrator", "log.parser.field.name": "user.name", "log.parser.field.type": "Any"}, {"log.parser.field.value": "Datacenter", "log.parser.field.name": "vm.name", "log.parser.field.type": "Any"}], "log.parser.log.positions": ["complete.time", "description", "execution.time.ms", "name", "start.time", "status", "timestamp", "user.name", "vm.name"], "log.parser.type": "json", "log.parser.source.vendor": "VMware", "log.parser.source.type": "vCenter", "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "log.parser.upload": "no"}, {"log.parser.name": "Cisco ACI Fault Log", "log.parser.event": "{\"ack\":\"no\",\"affected\":\"topology/pod-1/node-102/sys/phys-[eth1/3]\",\"cause\":\"threshold-crossed\",\"changeSet\":\"crcLast:4\",\"childAction\":\"\",\"code\":\"F381328\",\"created\":\"2025-01-20T11:12:31.796+00:00\",\"delegated\":\"no\",\"delegatedFrom\":\"\",\"descr\":\"TCA: CRC Align Errors current value(eqptIngrErrPkts5min:crcLast) value 4% raised above threshold 1%\",\"dn\":\"subj-[topology/pod-1/node-102/sys/phys-[eth1/3]]/fr-4295312079\",\"domain\":\"infra\",\"highestSeverity\":\"warning\",\"id\":\"4295312079\",\"ind\":\"modification\",\"lc\":\"raised\",\"modTs\":\"never\",\"occur\":\"5356\",\"origSeverity\":\"warning\",\"prevSeverity\":\"cleared\",\"rule\":\"tca-eqpt-ingr-err-pkts5min-crc-last\",\"severity\":\"warning\",\"status\":\"\",\"subject\":\"counter\",\"type\":\"operational\"}", "id": 1********00031, "log.parser.condition.keywords": [], "log.parser.condition": "all", "plugin.id": 600032, "log.parser.fields": [{"log.parser.field.value": 1737704365, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "topology/pod-1/node-102/sys/phys-[eth1/3]", "log.parser.field.name": "cisco.aci.fault.log.affected.object", "log.parser.field.type": "none"}, {"log.parser.field.value": "threshold-crossed", "log.parser.field.name": "cisco.aci.fault.log.cause", "log.parser.field.type": "none"}, {"log.parser.field.value": "F381328", "log.parser.field.name": "cisco.aci.fault.log.code", "log.parser.field.type": "none"}, {"log.parser.field.value": 1737371551, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "TCA: CRC Align Errors current value(eqptIngrErrPkts5min:crcLast) value 4% raised above threshold 1%", "log.parser.field.name": "cisco.aci.fault.log.description", "log.parser.field.type": "none"}, {"log.parser.field.value": "subj-[topology/pod-1/node-102/sys/phys-[eth1/3]]/fr-4295312079", "log.parser.field.name": "cisco.aci.fault.log.distinguished.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "infra", "log.parser.field.name": "cisco.aci.fault.log.domain", "log.parser.field.type": "none"}, {"log.parser.field.value": "modification", "log.parser.field.name": "cisco.aci.fault.log.indicator", "log.parser.field.type": "none"}, {"log.parser.field.value": "raised", "log.parser.field.name": "cisco.aci.fault.log.lifecycle", "log.parser.field.type": "none"}, {"log.parser.field.value": "tca-eqpt-ingr-err-pkts5min-crc-last", "log.parser.field.name": "cisco.aci.fault.log.rule", "log.parser.field.type": "none"}, {"log.parser.field.value": "warning", "log.parser.field.name": "cisco.aci.fault.log.severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "counter", "log.parser.field.name": "cisco.aci.fault.log.subject", "log.parser.field.type": "none"}, {"log.parser.field.value": "operational", "log.parser.field.name": "cisco.aci.fault.log.type", "log.parser.field.type": "none"}], "log.parser.type": "custom", "log.parser.plugin": 1********00020, "log.parser.source.type": "Cisco ACI", "log.parser.upload": "no", "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "log.parser.entities": []}], "version": "1.5"}, {"type": "inline", "records": [{"log.parser.name": "Cisco Router", "log.parser.event": "<190>2001139: 30w3d: %SEC-6-IPACCESSLOGP: list 190 denied tcp **************(40714) -> 0.0.0.0(23), 1 packet", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1669395654, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "SEC", "log.parser.field.name": "facility.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "IPACCESSLOGP", "log.parser.field.name": "facility.description", "log.parser.field.type": "none"}, {"log.parser.field.value": "list 190 denied tcp **************(40714) -> 0.0.0.0(23), 1 packet", "log.parser.field.name": "log.message", "log.parser.field.type": "none"}, {"log.parser.field.value": "Informational", "log.parser.field.name": "severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "190", "log.parser.field.name": "list.number", "log.parser.field.type": "none"}, {"log.parser.field.value": "denied", "log.parser.field.name": "action", "log.parser.field.type": "none"}, {"log.parser.field.value": "tcp", "log.parser.field.name": "protocol", "log.parser.field.type": "none"}, {"log.parser.field.value": "**************", "log.parser.field.name": "source.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "40714", "log.parser.field.name": "source.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "0.0.0.0", "log.parser.field.name": "destination.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "23", "log.parser.field.name": "destination.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "1", "log.parser.field.name": "packets", "log.parser.field.type": "none"}, {"log.parser.field.value": "Cisco Device Packets Destined Audit", "log.parser.field.name": "event.category", "log.parser.field.type": "none"}, {"log.parser.field.value": 1669395654, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}], "log.parser.type": "custom", "log.parser.plugin": 1********00021, "log.parser.source.type": "Router", "log.parser.upload": "no", "plugin.id": 600033, "log.parser.entities": [], "id": 1********00032, "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "log.parser.source.vendor": "Cisco Systems"}], "version": "1.6"}, {"type": "inline", "records": [{"log.parser.name": "VCenter Envoy Lo<PERSON>", "log.parser.event": "2025-04-15 12:34:56 INFO envoy[12345] [abc@123 sub=xyz] Connection established to upstream cluster", "log.parser.log.positions": [{"start.position": 11, "end.position": 19}, {"start.position": 20, "end.position": 24}, {"start.position": 25, "end.position": 30}, {"start.position": 31, "end.position": 36}, {"start.position": 38, "end.position": 55}, {"start.position": 56, "end.position": 98}], "log.parser.condition": "all", "log.parser.condition.keywords": [], "log.parser.fields": [{"log.parser.field.value": "12:34:56", "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "INFO", "log.parser.field.name": "severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "envoy", "log.parser.field.name": "type", "log.parser.field.type": "none"}, {"log.parser.field.value": "12345", "log.parser.field.name": "type.digit", "log.parser.field.type": "none"}, {"log.parser.field.value": "[abc@123 sub=xyz]", "log.parser.field.name": "originator", "log.parser.field.type": "none"}, {"log.parser.field.value": "Connection established to upstream cluster", "log.parser.field.name": "message.info", "log.parser.field.type": "none"}], "id": 1********00033, "plugin.id": 600134, "log.parser.type": "regex", "log.parser.source.type": "vCenter", "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "regex": "(\\S+)\\s+(\\S+)\\s+(envoy)\\[(\\d+)\\]\\s+(\\[\\w+@\\d+\\s+sub=\\w+\\])\\s+(.+)", "log.parser.source.vendor": ""}, {"log.parser.name": "VCenter Firewall Reload Log Audit", "log.parser.event": "2025-04-15T14:22:10Z [1234]INFO:system-log:Service started successfully", "log.parser.log.positions": [{"start.position": 0, "end.position": 20}, {"start.position": 22, "end.position": 26}, {"start.position": 27, "end.position": 31}, {"start.position": 32, "end.position": 42}, {"start.position": 43, "end.position": 71}], "log.parser.condition": "all", "log.parser.condition.keywords": [], "log.parser.fields": [{"log.parser.field.value": "2025-04-15T14:22:10Z", "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "1234", "log.parser.field.name": "execution", "log.parser.field.type": "none"}, {"log.parser.field.value": "INFO", "log.parser.field.name": "severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "system-log", "log.parser.field.name": "type", "log.parser.field.type": "none"}, {"log.parser.field.value": "Service started successfully", "log.parser.field.name": "message_info", "log.parser.field.type": "none"}], "id": 1********00034, "plugin.id": 600135, "log.parser.type": "regex", "log.parser.source.type": "vCenter", "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "regex": "(\\S+)\\s+\\[(\\d+)\\](\\S+):([a-z,-]*|[a-z,.]*):(.*)", "log.parser.source.vendor": ""}, {"log.parser.name": "VCenter Log Audit", "log.parser.event": "User alice@************ logged in via web interface from Chrome browser", "log.parser.log.positions": [{"start.position": 5, "end.position": 10}, {"start.position": 11, "end.position": 23}, {"start.position": 24, "end.position": 33}, {"start.position": 34, "end.position": 71}], "log.parser.condition": "all", "log.parser.condition.keywords": [], "log.parser.fields": [{"log.parser.field.value": "alice", "log.parser.field.name": "user.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "************", "log.parser.field.name": "source.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "logged in", "log.parser.field.name": "action", "log.parser.field.type": "none"}, {"log.parser.field.value": "via web interface from Chrome browser", "log.parser.field.name": "message.info", "log.parser.field.type": "none"}], "id": 1********00035, "plugin.id": 600136, "log.parser.type": "regex", "log.parser.source.type": "vCenter", "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "regex": "(?:User|user)\\s+(\\S+)@(\\S+)\\s+(logged in|logged out)\\s+(.*)", "log.parser.source.vendor": ""}, {"log.parser.name": "VCenter Log Collector Audit", "log.parser.event": "2025-04-14T09:32:17.245Z [INFO] [vrslcm-log-collector] [ThreadID:2874] [User:<EMAIL>] [SessionID:f3b68a1c-8a44-4e87-9d12-b7ac35d21ef0] VM 'vm-production-db01' task 'PowerOffVM_Task' initiated by '<EMAIL>' started at '2025-04-14T09:30:12.123Z' completed at '2025-04-14T09:32:15.456Z' with status 'success' - Task description: Power off virtual machine", "log.parser.log.positions": [{"start.position": 159, "end.position": 177}, {"start.position": 185, "end.position": 200}, {"start.position": 216, "end.position": 243}, {"start.position": 257, "end.position": 281}, {"start.position": 297, "end.position": 321}, {"start.position": 336, "end.position": 343}, {"start.position": 365, "end.position": 390}], "log.parser.condition": "all", "log.parser.condition.keywords": [], "log.parser.fields": [{"log.parser.field.value": "vm-production-db01", "log.parser.field.name": "vm.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "PowerOffVM_Task", "log.parser.field.name": "task.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "<EMAIL>", "log.parser.field.name": "user.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "2025-04-14T09:30:12.123Z", "log.parser.field.name": "start.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "2025-04-14T09:32:15.456Z", "log.parser.field.name": "complete.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "success", "log.parser.field.name": "status", "log.parser.field.type": "none"}, {"log.parser.field.value": "Power off virtual machine", "log.parser.field.name": "task.description", "log.parser.field.type": "none"}], "id": 1********00036, "plugin.id": 600137, "log.parser.type": "regex", "log.parser.source.type": "vCenter", "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "regex": "VM '([^']+)' task '([^']+)' initiated by '([^']+)' started at '([^']+)' completed at '([^']+)' with status '([^']+)' - Task description: (.+)$", "log.parser.source.vendor": ""}, {"log.parser.name": "VCenter Service Control Audit Log", "log.parser.event": "2025-04-15T10:35:42.123+0000 INFO service-control Service started successfully with parameters [mode:normal, timeout:30s]", "log.parser.log.positions": [{"start.position": 0, "end.position": 28}, {"start.position": 29, "end.position": 33}, {"start.position": 34, "end.position": 49}, {"start.position": 50, "end.position": 121}], "log.parser.condition": "all", "log.parser.condition.keywords": [], "log.parser.fields": [{"log.parser.field.value": "2025-04-15T10:35:42.123+0000", "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "INFO", "log.parser.field.name": "severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "service-control", "log.parser.field.name": "type", "log.parser.field.type": "none"}, {"log.parser.field.value": "Service started successfully with parameters [mode:normal, timeout:30s]", "log.parser.field.name": "message.info", "log.parser.field.type": "none"}], "id": 1********00037, "plugin.id": 600138, "log.parser.type": "regex", "log.parser.source.type": "vCenter", "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "regex": "(\\S+)\\s+(\\S+)\\s+(service-control)\\s+(.*)", "log.parser.source.vendor": ""}, {"log.parser.name": "VCenter Stat Monitor Audit Log", "log.parser.event": "2025-04-15T08:32:17.432+0000 INFO StatsMonitor[1234] [AppServer sub=HealthCheck] CPU utilization at 78%, within acceptable limits", "log.parser.log.positions": [{"start.position": 0, "end.position": 28}, {"start.position": 29, "end.position": 33}, {"start.position": 34, "end.position": 46}, {"start.position": 47, "end.position": 51}, {"start.position": 54, "end.position": 63}, {"start.position": 68, "end.position": 79}, {"start.position": 81, "end.position": 129}], "log.parser.condition": "all", "log.parser.condition.keywords": [], "log.parser.fields": [{"log.parser.field.value": "2025-04-15T08:32:17.432+0000", "log.parser.field.name": "timestamp", "log.parser.field.type": "none"}, {"log.parser.field.value": "INFO", "log.parser.field.name": "severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "StatsMonitor", "log.parser.field.name": "type", "log.parser.field.type": "none"}, {"log.parser.field.value": "1234", "log.parser.field.name": "type.digit", "log.parser.field.type": "none"}, {"log.parser.field.value": "AppServer", "log.parser.field.name": "originator", "log.parser.field.type": "none"}, {"log.parser.field.value": "HealthCheck", "log.parser.field.name": "subject", "log.parser.field.type": "none"}, {"log.parser.field.value": "CPU utilization at 78%, within acceptable limits", "log.parser.field.name": "message.info", "log.parser.field.type": "none"}], "id": 1********00038, "plugin.id": 600139, "log.parser.type": "regex", "log.parser.source.type": "vCenter", "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "regex": "(\\S+)\\s+(\\S+)\\s+(StatsMonitor)\\[(\\d+)\\]\\s+\\[(\\S+)\\s+sub=(\\S+)\\]\\s+(.*)", "log.parser.source.vendor": ""}, {"log.parser.name": "VCenter Trust Management Audit Log", "log.parser.event": "2025-04-15T14:23:45.782+0000 [tomcat-exec-237 INFO] Thread pool monitoring detected increased load: 75% utilization", "log.parser.log.positions": [{"start.position": 0, "end.position": 28}, {"start.position": 30, "end.position": 45}, {"start.position": 46, "end.position": 51}, {"start.position": 64, "end.position": 115}], "log.parser.condition": "all", "log.parser.condition.keywords": [], "log.parser.fields": [{"log.parser.field.value": "2025-04-15T14:23:45.782+0000", "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "tomcat-exec-237", "log.parser.field.name": "execution", "log.parser.field.type": "none"}, {"log.parser.field.value": "INFO]", "log.parser.field.name": "severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "monitoring detected increased load: 75% utilization", "log.parser.field.name": "message.info", "log.parser.field.type": "none"}], "id": 1********00039, "plugin.id": 600140, "log.parser.type": "regex", "log.parser.source.type": "vCenter", "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "regex": "(\\S+)\\s+\\[(tomcat-exec-\\d+)\\s+(\\S+)\\s+\\S+\\s+\\S+\\s+(.*)", "log.parser.source.vendor": ""}, {"log.parser.name": "WebLogic Server", "log.parser.event": "2025-4-15 09:32:45,328 | INFO | [AuthenticationService] | UserAuthenticator | User '<EMAIL>' successfully authenticated from IP ************", "log.parser.log.positions": [{"start.position": 0, "end.position": 18}, {"start.position": 25, "end.position": 29}, {"start.position": 33, "end.position": 54}, {"start.position": 58, "end.position": 75}, {"start.position": 77, "end.position": 150}], "log.parser.condition": "all", "log.parser.condition.keywords": [], "log.parser.fields": [{"log.parser.field.value": "2025-4-15 09:32:45", "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "INFO", "log.parser.field.name": "severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "AuthenticationService", "log.parser.field.name": "component.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "UserAuthenticator", "log.parser.field.name": "component.class", "log.parser.field.type": "none"}, {"log.parser.field.value": " User '<EMAIL>' successfully authenticated from IP ************", "log.parser.field.name": "component.detail", "log.parser.field.type": "none"}], "id": 1********00040, "plugin.id": 600141, "log.parser.type": "regex", "log.parser.source.type": "Oracle WebLogic", "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "regex": "^(\\d+-\\d+-\\d+ \\d+:\\d+:\\d+),\\d+\\s+\\|\\s+(\\S+)\\s+\\|\\s+\\[(\\S+)\\]\\s+\\|\\s+(\\S+)\\s+\\|(.*)", "log.parser.source.vendor": ""}, {"log.parser.name": "WebLogic SQL Service", "log.parser.event": "admin_user QUERY SQL_SERVICE 2025-04-15 14:32:07,543 [[ACTIVE] ExecuteThread: '3' for queue: 'weblogic.kernel.Default'] INFO", "log.parser.log.positions": [{"start.position": 0, "end.position": 10}, {"start.position": 17, "end.position": 28}, {"start.position": 29, "end.position": 48}, {"start.position": 55, "end.position": 61}, {"start.position": 120, "end.position": 124}], "log.parser.condition": "all", "log.parser.condition.keywords": [], "log.parser.fields": [{"log.parser.field.value": "admin_user", "log.parser.field.name": "user.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "SQL_SERVICE", "log.parser.field.name": "component.id", "log.parser.field.type": "none"}, {"log.parser.field.value": "2025-04-15 14:32:07", "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "ACTIVE", "log.parser.field.name": "status", "log.parser.field.type": "none"}, {"log.parser.field.value": "INFO", "log.parser.field.name": "severity", "log.parser.field.type": "none"}], "id": 1********00041, "plugin.id": 600142, "log.parser.type": "regex", "log.parser.source.type": "Oracle WebLogic", "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "regex": "(\\S+)\\s+\\S+\\s+(\\S+)\\s+(\\d+-\\d+-\\d+\\s\\d+:\\d+:\\d+),\\d+\\s+\\[\\[(\\w+).*\\s+(INFO|DEBUG|WARN|ERROR)", "log.parser.source.vendor": ""}, {"log.parser.name": "Windows DHCP Audit Server Log", "log.parser.event": "10,04/16/25,14:23:51,<PERSON><PERSON>,************1,WIN10-PC,0,0xA12F34,3600,Administrator,00E04C680123,***********/24", "log.parser.log.positions": [{"start.position": 0, "end.position": 2}, {"start.position": 3, "end.position": 11}, {"start.position": 12, "end.position": 20}, {"start.position": 21, "end.position": 27}, {"start.position": 28, "end.position": 41}, {"start.position": 42, "end.position": 50}, {"start.position": 51, "end.position": 52}, {"start.position": 53, "end.position": 61}, {"start.position": 62, "end.position": 66}, {"start.position": 67, "end.position": 80}, {"start.position": 81, "end.position": 93}, {"start.position": 94, "end.position": 108}], "log.parser.condition": "all", "log.parser.condition.keywords": [], "log.parser.fields": [{"log.parser.field.value": "10", "log.parser.field.name": "id", "log.parser.field.type": "none"}, {"log.parser.field.value": "04/16/25", "log.parser.field.name": "date", "log.parser.field.type": "none"}, {"log.parser.field.value": "14:23:51", "log.parser.field.name": "time", "log.parser.field.type": "none"}, {"log.parser.field.value": "Assign", "log.parser.field.name": "description", "log.parser.field.type": "none"}, {"log.parser.field.value": "************1", "log.parser.field.name": "remote.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "WIN10-PC", "log.parser.field.name": "host.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "0", "log.parser.field.name": "error.code", "log.parser.field.type": "none"}, {"log.parser.field.value": "0xA12F34", "log.parser.field.name": "duid.length", "log.parser.field.type": "none"}, {"log.parser.field.value": "3600", "log.parser.field.name": "duid.bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": "Administrator", "log.parser.field.name": "user.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "00E04C680123", "log.parser.field.name": "dhcid", "log.parser.field.type": "none"}, {"log.parser.field.value": "***********/24", "log.parser.field.name": "subnet.prefix", "log.parser.field.type": "none"}], "id": 1********00042, "plugin.id": 600143, "log.parser.type": "regex", "log.parser.source.type": "Windows DHCP", "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "regex": "^(\\d+),(\\d{2}\\/\\d{2}\\/\\d{2}),(\\d{2}:\\d{2}:\\d{2}),([^,]*),([^,]*),([^,]*),([^,]*),([^,]*),([^,]*),([^,]*),([^,]*),([^,]*)", "log.parser.source.vendor": ""}, {"log.parser.name": "Plexonics Switch", "log.parser.event": "2023-11-16T08:46:33+05:30 ************** syslog - ID608 [PL-ENVIRONMENT] FAN: Chassis fan 2 speed critical (RPM: 15200) at 42°C", "log.parser.log.positions": [{"start.position": 0, "end.position": 25}, {"start.position": 26, "end.position": 40}, {"start.position": 50, "end.position": 55}, {"start.position": 56, "end.position": 72}, {"start.position": 73, "end.position": 76}, {"start.position": 78, "end.position": 127}], "log.parser.condition": "all", "log.parser.condition.keywords": [], "log.parser.fields": [{"log.parser.field.value": "2023-11-16T08:46:33+05:30", "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "**************", "log.parser.field.name": "ip.address", "log.parser.field.type": "none"}, {"log.parser.field.value": "ID608", "log.parser.field.name": "log.id", "log.parser.field.type": "none"}, {"log.parser.field.value": "[PL-ENVIRONMENT]", "log.parser.field.name": "log.info.string", "log.parser.field.type": "none"}, {"log.parser.field.value": "FAN", "log.parser.field.name": "log.type", "log.parser.field.type": "none"}, {"log.parser.field.value": "Chassis fan 2 speed critical (RPM: 15200) at 42°C", "log.parser.field.name": "message.info", "log.parser.field.type": "none"}], "id": 1********00043, "plugin.id": 600144, "log.parser.type": "regex", "log.parser.source.type": "Switch", "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "regex": "(\\d+-\\d+-\\d+T\\d+:\\d+:\\d+\\+\\d+:\\d+)\\s+(\\S+)\\s+syslog\\s+-\\s+(ID\\d+)\\s+(\\[PL-\\S+)\\s+(\\S+):\\s+(.*)", "log.parser.source.vendor": ""}, {"log.parser.name": "JBOSS Wildfly", "log.parser.event": "ERROR [IDM-403] (Worker-5) Proc-42: Failed to process invoice due to missing data", "log.parser.log.positions": [{"start.position": 0, "end.position": 5}, {"start.position": 7, "end.position": 14}, {"start.position": 17, "end.position": 25}, {"start.position": 27, "end.position": 34}, {"start.position": 36, "end.position": 81}], "log.parser.condition": "all", "log.parser.condition.keywords": [], "log.parser.fields": [{"log.parser.field.value": "ERROR", "log.parser.field.name": "severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "IDM-403", "log.parser.field.name": "error.code", "log.parser.field.type": "none"}, {"log.parser.field.value": "Worker-5", "log.parser.field.name": "thread.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "Proc-42", "log.parser.field.name": "process.id", "log.parser.field.type": "none"}, {"log.parser.field.value": "Failed to process invoice due to missing data", "log.parser.field.name": "message.info", "log.parser.field.type": "none"}], "id": 1********00044, "plugin.id": 600145, "log.parser.type": "regex", "log.parser.source.type": "<PERSON><PERSON><PERSON>", "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "regex": "(FATAL|ERROR|SEVERE|WARN|WARNING|INFO|DEBUG|FINE|TRACE|FINEST|...) \\[(\\S+)\\] \\((.*)\\) (\\S+): (.*)", "log.parser.source.vendor": ""}, {"log.parser.name": "Load Balancer Traffic", "log.parser.event": "<134>12 Apr 2025 10:18:30 INFO ************* ******** 443 *********** 8084 sslsess999 Mozilla/5.0 302 /login HTTP/1.1", "log.parser.log.positions": [{"start.position": 5, "end.position": 25}, {"start.position": 26, "end.position": 30}, {"start.position": 31, "end.position": 44}, {"start.position": 45, "end.position": 53}, {"start.position": 54, "end.position": 57}, {"start.position": 58, "end.position": 69}, {"start.position": 70, "end.position": 74}, {"start.position": 75, "end.position": 85}, {"start.position": 86, "end.position": 97}, {"start.position": 98, "end.position": 101}, {"start.position": 102, "end.position": 108}, {"start.position": 109, "end.position": 117}], "log.parser.condition": "all", "log.parser.condition.keywords": [], "log.parser.fields": [{"log.parser.field.value": "12 Apr 2025 10:18:30", "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "INFO", "log.parser.field.name": "severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "*************", "log.parser.field.name": "client.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "********", "log.parser.field.name": "proxy.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "443", "log.parser.field.name": "virtual.service.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "***********", "log.parser.field.name": "real.service.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "8084", "log.parser.field.name": "real.service.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "sslsess999", "log.parser.field.name": "ssl.session.id", "log.parser.field.type": "none"}, {"log.parser.field.value": "Mozilla/5.0", "log.parser.field.name": "agent", "log.parser.field.type": "none"}, {"log.parser.field.value": "302", "log.parser.field.name": "response.code", "log.parser.field.type": "none"}, {"log.parser.field.value": "/login", "log.parser.field.name": "url", "log.parser.field.type": "none"}, {"log.parser.field.value": "HTTP/1.1", "log.parser.field.name": "version", "log.parser.field.type": "none"}], "id": 1********00045, "plugin.id": 600146, "log.parser.type": "regex", "log.parser.source.type": "<PERSON><PERSON>r", "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "regex": "(\\d+\\s+\\S+\\s+\\d+\\s+\\d+:\\d+:\\d+)\\s+(\\S+)\\s+(\\S+)\\s+(\\S+)\\s(\\S+)\\s+(\\S+)\\s+(\\S+)\\s+(\\S+)\\s+(.*)\\s+(\\d+)\\s+(\\S+)\\s+(\\S+)|(\\d+\\s+\\S+\\s+\\d+\\s+\\d+:\\d+:\\d+)\\s+(\\S+)\\s+(\\S+)\\s+(\\S+)\\s(\\S+)\\s+(\\S+)\\s+(\\S+)\\s+(\\S+)\\s+(.*)", "log.parser.source.vendor": ""}], "version": "1.8"}]}