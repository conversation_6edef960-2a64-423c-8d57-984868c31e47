{"entity": "Flow Sampling Rate", "collection": "flow.sampling.rate", "version": "1.0", "author": "Vvek", "props": [{"name": "interface", "title": "Interface Index", "type": "string", "rules": ["required", "unique"]}, {"name": "interface.name", "title": "Interface Name", "type": "string", "rules": ["required"]}, {"name": "interface.description", "title": "Interface Description", "type": "string", "rules": []}, {"name": "interface.alias", "title": "Interface <PERSON>", "type": "string", "rules": []}, {"name": "interface.speed.bytes.per.sec", "title": "Interface Speed", "type": "numeric", "rules": []}, {"name": "interface.sampling.rate", "title": "Sampling Rate", "type": "numeric", "rules": ["required"]}]}