{"entity": "Backup Profile", "collection": "backup.profile", "version": "1.0", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props": [{"name": "backup.profile.name", "title": "Profile Name", "type": "string", "rules": ["required", "unique"]}, {"name": "backup.profile.type", "title": "Backup Profile Type", "type": "string", "default": "System", "private": true}], "entries": [{"type": "inline", "records": [{"backup.profile.name": "Config DB Backup Profile", "backup.profile.type": "Config DB", "backup.storage.profile": 10000000000001, "id": 10000000000001}, {"backup.profile.name": "Report DB Backup Profile", "backup.profile.type": "Report DB", "backup.profile.context": {"datastore.types": ["metric", "log", "trap", "alert", "config.history", "system.event", "netroute"]}, "backup.storage.profile": 10000000000002, "id": 10000000000002}], "version": "1.0"}]}