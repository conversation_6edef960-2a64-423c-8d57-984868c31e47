{"entity": "<PERSON>g Pa<PERSON><PERSON>", "collection": "log.parser.plugin", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "log.parser.plugin.name", "title": "Name", "type": "string", "rules": ["required", "unique"]}, {"name": "log.parser.plugin.context", "title": "<PERSON><PERSON><PERSON> Plugin Context", "type": "map", "rules": ["required"]}], "entries": [{"type": "script", "directory": "plugins", "records": [{"id": 10000000000001, "log.parser.plugin.name": "CiscoASALogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "CiscoASALogParserPlugin.java"}}, {"id": 10000000000002, "log.parser.plugin.name": "CiscoSwitchLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "CiscoSwitchLogParserPlugin.java"}}, {"id": 10000000000003, "log.parser.plugin.name": "FortinetLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "FortinetLogParserPlugin.java"}}, {"id": 10000000000004, "log.parser.plugin.name": "LinuxLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "LinuxLogParserPlugin.java"}}, {"id": 10000000000005, "log.parser.plugin.name": "MicrosoftExchangeLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "MicrosoftExchangeLogParserPlugin.java"}}, {"id": 10000000000006, "log.parser.plugin.name": "MySQLLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "MySQLLogParserPlugin.java"}}, {"id": 10000000000007, "log.parser.plugin.name": "MySQLSlowQueryLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "MySQLSlowQueryLogParserPlugin.java"}}, {"id": 10000000000008, "log.parser.plugin.name": "OracleDatabaseLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "OracleDatabaseLogParserPlugin.java"}}, {"id": 10000000000009, "log.parser.plugin.name": "PaloAltoLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "PaloAltoLogParserPlugin.java"}}, {"id": 10000000000010, "log.parser.plugin.name": "PostgreSQLLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "PostgreSQLLogParserPlugin.java"}}, {"id": 10000000000011, "log.parser.plugin.name": "SonicWallLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "SonicWallLogParserPlugin.java"}}, {"id": 10000000000012, "log.parser.plugin.name": "SophosLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "SophosLogParserPlugin.java"}}, {"id": 10000000000013, "log.parser.plugin.name": "WindowsFirewallLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "WindowsFirewallLogParserPlugin.java"}}, {"id": 10000000000014, "log.parser.plugin.name": "WindowsLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "WindowsLogParserPlugin.java"}}, {"id": 10000000000015, "log.parser.plugin.name": "WindowsUpdateLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "WindowsUpdateLogParserPlugin.java"}}, {"id": 10000000000016, "log.parser.plugin.name": "NutanixLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "NutanixLogParserPlugin.java"}}, {"id": 10000000000017, "log.parser.plugin.name": "VMwareESXiLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "VMwareESXiLogParserPlugin.java"}}, {"id": 10000000000018, "log.parser.plugin.name": "VMwarevCenterLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "VMwarevCenterLogParserPlugin.java"}}, {"id": 10000000000019, "log.parser.plugin.name": "AzureLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "AzureLogParserPlugin.java"}}, {"id": 10000000000020, "log.parser.plugin.name": "CiscoACILogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "CiscoACILogParserPlugin.java"}}], "version": "1.4"}, {"type": "script", "directory": "plugins", "records": [{"id": 10000000000021, "log.parser.plugin.name": "CiscoRouterLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "CiscoRouterLogParserPlugin.java"}}], "version": "1.5"}]}