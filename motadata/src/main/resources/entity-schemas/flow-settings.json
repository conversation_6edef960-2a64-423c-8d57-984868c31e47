{"entity": "Flow Setting", "collection": "flow.setting", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "flow.settings.sflow.direction", "title": "sFlow v5 Direction", "type": "string", "rules": ["required"], "value": ["ingress", "egress"]}, {"name": "flow.settings.sflow.port", "title": "sFlow Port", "type": "numeric", "rules": ["required"]}, {"name": "flow.settings.netflow.port", "title": "NetFlow Port", "type": "numeric", "rules": ["required"]}, {"name": "flow.settings.aggregation.interval.minutes", "title": "Aggregation Time (min)", "type": "numeric", "rules": ["required"]}], "entries": [{"type": "inline", "records": [{"id": 10000000000001, "flow.settings.sflow.direction": "ingress", "flow.settings.sflow.port": 6343, "flow.settings.netflow.port": 2055, "flow.settings.bgp.sflow.port": 6344, "flow.settings.bgp.netflow.port": 2056, "flow.settings.aggregation.interval.minutes": 3, "flow.settings.bgp.flow.enabled": "no"}], "version": "1.3"}]}