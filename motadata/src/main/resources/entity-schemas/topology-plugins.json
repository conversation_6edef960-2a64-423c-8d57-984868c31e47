{"entity": "Topology Plugin", "collection": "topology.plugin", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "topology.plugin.name", "title": "Topology Name", "type": "string", "rules": ["required", "unique"]}, {"name": "topology.plugin.vendor", "title": "<PERSON><PERSON><PERSON>", "type": "string", "rules": ["required"]}, {"name": "topology.plugin.make.model", "title": "Make Model", "type": "string"}, {"name": "topology.plugin.entity.type", "title": "Entity Type", "type": "string", "values": ["Monitor", "Group"], "rules": ["required"]}, {"name": "topology.plugin.entities", "title": "Monitor(s)/Group(s)", "type": "list"}, {"name": "topology.plugin.type", "title": "Type", "type": "string", "rules": ["required"], "values": ["Custom", "SNMP"]}, {"name": "topology.plugin.variables", "title": "Variable(s)", "type": "map"}], "entries": [{"type": "script", "directory": "topology", "records": [{"topology.plugin.name": "SSH CDP Topology", "topology.plugin.vendor": "Generic", "topology.plugin.type": "Custom", "topology.plugin.entity.type": "Monitor", "topology.plugin.entities": [], "topology.plugin.protocol": "CDP", "topology.plugin.context": {"timeout": 60, "script": "cdp.py", "script.language": "python"}, "id": 10000000000001}, {"topology.plugin.name": "SSH Cisco ISIS Topology", "topology.plugin.vendor": "Cisco Systems", "topology.plugin.type": "Custom", "topology.plugin.entity.type": "Monitor", "topology.plugin.entities": [], "topology.plugin.protocol": "IS-IS", "topology.plugin.context": {"timeout": 60, "script": "cisco_isis.py", "script.language": "python"}, "id": 10000000000002}, {"topology.plugin.name": "SSH Juniper ISIS Topology", "topology.plugin.vendor": "Juniper Networks", "topology.plugin.type": "Custom", "topology.plugin.entity.type": "Monitor", "topology.plugin.entities": [], "topology.plugin.protocol": "IS-IS", "topology.plugin.context": {"timeout": 60, "script": "juniper_isis.py", "script.language": "python"}, "id": 10000000000003}, {"topology.plugin.name": "SSH Cisco BGP Topology", "topology.plugin.vendor": "Cisco Systems", "topology.plugin.type": "Custom", "topology.plugin.entity.type": "Monitor", "topology.plugin.entities": [], "topology.plugin.protocol": "BGP", "topology.plugin.context": {"timeout": 60, "script": "cisco_bgp.py", "script.language": "python"}, "id": 10000000000004}, {"topology.plugin.name": "SSH Juniper BGP Topology", "topology.plugin.vendor": "Juniper Networks", "topology.plugin.type": "Custom", "topology.plugin.entity.type": "Monitor", "topology.plugin.entities": [], "topology.plugin.protocol": "BGP", "topology.plugin.context": {"timeout": 60, "script": "juniper_bgp.py", "script.language": "python"}, "id": 10000000000005}, {"topology.plugin.name": "SSH LLDP Topology", "topology.plugin.vendor": "Generic", "topology.plugin.type": "Custom", "topology.plugin.entity.type": "Monitor", "topology.plugin.entities": [], "topology.plugin.protocol": "LLDP", "topology.plugin.context": {"timeout": 60, "script": "lldp.py", "script.language": "python"}, "id": 10000000000006}, {"topology.plugin.name": "SSH Cisco OSPF Topology", "topology.plugin.vendor": "Cisco Systems", "topology.plugin.type": "Custom", "topology.plugin.entity.type": "Monitor", "topology.plugin.entities": [], "topology.plugin.protocol": "OSPF", "topology.plugin.context": {"timeout": 60, "script": "cisco_ospf.py", "script.language": "python"}, "id": 10000000000007}, {"topology.plugin.name": "SSH Juniper OSPF Topology", "topology.plugin.vendor": "Juniper Networks", "topology.plugin.type": "Custom", "topology.plugin.entity.type": "Monitor", "topology.plugin.entities": [], "topology.plugin.protocol": "OSPF", "topology.plugin.context": {"timeout": 60, "script": "juniper_ospf.py", "script.language": "python"}, "id": 10000000000008}], "version": "1.0"}]}