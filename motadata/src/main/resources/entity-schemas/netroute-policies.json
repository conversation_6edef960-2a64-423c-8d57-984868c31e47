{"entity": "NetRoute Policy", "collection": "netroute.policy", "version": "1.0", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props": [{"name": "policy.name", "title": "Policy Name", "type": "string", "rules": ["required"]}, {"name": "policy.type", "title": "Policy Type", "type": "string", "rules": ["required"]}, {"name": "policy.tags", "title": "Policy Tag(s)", "type": "list"}, {"name": "policy.context", "title": "Policy Context", "type": "map", "rules": ["required"]}, {"name": "policy.title", "title": "Policy Title", "type": "string"}, {"name": "policy.message", "title": "Policy Message", "type": "string"}, {"name": "policy.actions", "title": "Policy Action(s)", "type": "map"}], "entries": [{"type": "inline", "records": [{"policy.type": "NetRoute", "policy.name": "Availability", "policy.context": {"entity.type": "NetRoute", "policy.evaluation.type": "Source-to-destination", "entities": [], "metric": "status", "data.type": ["string"]}, "policy.email.notification.recipients": [], "policy.renotification.timer.seconds": 0, "policy.message": "$$$counter$$$ has entered into $$$severity$$$ state with value $$$value$$$ on netroute $$$netroute.name$$$", "policy.title": "$$$severity$$$ - $$$netroute.name$$$", "policy.renotify": "no", "policy.actions": {}, "policy.creation.time": 1654001842, "policy.state": "yes", "_type": "1", "id": 10000000000001}], "version": "1.0"}]}