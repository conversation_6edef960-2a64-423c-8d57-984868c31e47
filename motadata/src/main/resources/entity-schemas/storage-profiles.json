{"entity": "Storage Profile", "collection": "storage_profile", "version": "1.0", "author": "", "props": [{"name": "storage.profile.name", "title": "Storage Profile Name", "type": "string", "rules": ["required", "unique"]}, {"name": "storage.profile.protocol", "title": "Protocol", "type": "string", "rules": ["required"], "values": ["TFTP", "FTP", "SCP/SFTP", "LOCAL"]}], "entries": [{"type": "inline", "records": [{"storage.profile.name": "Config DB Backup Storage Profile", "storage.profile.protocol": "LOCAL", "storage.profile.context": {}, "id": 10000000000001}, {"storage.profile.name": "Report DB Backup Storage Profile", "storage.profile.protocol": "LOCAL", "storage.profile.context": {}, "id": 10000000000002}], "version": "1.0"}]}