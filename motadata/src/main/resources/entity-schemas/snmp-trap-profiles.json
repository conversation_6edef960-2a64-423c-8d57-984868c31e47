{"entity": "SNMP Trap Profile", "collection": "snmp.trap.profile", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "snmp.trap.profile.name", "title": "Trap Name", "type": "string", "rules": ["required"]}, {"name": "snmp.trap.profile.oid", "title": "Trap OID", "type": "string", "rules": ["required", "unique"]}, {"name": "snmp.trap.profile.drop.status", "title": "Accept Trap", "type": "string", "rules": ["required"], "values": ["yes", "no"]}, {"name": "snmp.trap.profile.auto.clear.status", "title": "Auto Clear Trap", "type": "string", "rules": ["required"], "values": ["yes", "no"], "prerequisites": [{"rule": "snmp.trap.profile.drop.status", "value": "no"}]}, {"name": "snmp.trap.profile.auto.clear.oid", "title": "Auto Clear OID", "type": "string", "prerequisites": [{"rule": "snmp.trap.profile.auto.clear.status", "value": "yes"}]}, {"name": "snmp.trap.profile.auto.clear.timer", "title": "Auto Clear Timer", "type": "numeric", "values": [5, 10, 15, 30, 60], "prerequisites": [{"rule": "snmp.trap.profile.auto.clear.status", "value": "yes"}]}, {"name": "snmp.trap.profile.translator", "title": "Trap Translator", "type": "string"}], "entries": [{"type": "directory", "records": ["snmp-trap-profiles"], "version": "1.1"}]}