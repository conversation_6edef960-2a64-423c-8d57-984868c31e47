{"entity": "Data Retention Policy", "collection": "data.retention.policy", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "data.retention.policy.context", "title": "data.retention.policy", "type": "map", "rules": ["required"]}], "entries": [{"type": "inline", "records": [{"id": 10000000000001, "data.retention.policy.context": {"PERFORMANCE_METRIC": {"raw": 30, "aggregated": 180}, "LOG": {"raw": 7, "aggregated": 180}, "FLOW": {"raw": 2, "aggregated": 180}, "TRAP": {"raw": 7, "aggregated": 180}, "NOTIFICATION": {"raw": 7}, "AUDIT": {"raw": 30}, "METRIC_POLICY": {"raw": 90}, "CONFIG": {"version": 15}, "NETROUTE_METRIC": {"raw": 7}}}], "version": "1.1"}]}