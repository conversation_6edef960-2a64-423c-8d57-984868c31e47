{"entity": "Integration Profile", "collection": "integration.profile", "version": "1.0", "author": "<PERSON><PERSON><PERSON>", "props": [{"name": "integration.profile.name", "title": "Integration Profile Name", "type": "string", "rules": ["required", "unique"]}, {"name": "integration.profile.description", "title": "Integration Profile Description", "type": "string"}, {"name": "integration", "title": "Integration", "type": "numeric", "rules": ["required"]}, {"name": "integration.profile.context", "title": "Integration Profile Context", "type": "map", "rules": ["required"]}]}