{"172.16.8.128": {"result": {"4": {"last.query.time": "2022-06-08 12:19:54.867000"}, "mssql.query": [{"mssql.query.creation.time": "2022-06-08 12:20:19.713000", "mssql.query.last.execution.time": "2022-06-08 12:20:20.477000", "mssql.query.physical.reads": 0, "mssql.query.logical.reads": 8, "mssql.query.logical.writes": 0, "mssql.query.execution.count": 1, "mssql.query.cpu.time.sec": 282, "mssql.query.elapsed.time.sec": 287, "mssql.query.avg.elapsed.time.sec": 287, "mssql.query": "SELECT db.name DB<PERSON><PERSON>, tl.request_session_id,wt.blocking_session_id,tl.resource_type,h1.TEXT AS blocked_query,h2.TEXT AS blocking_query,session1.login_name as blocked_sql_user ,session1.nt_domain as blocked_domain,session1.nt_user_name as blocked_nt_user,session1.program_name,session1.host_name,session2.login_name as blocking_sql_user ,session2.nt_domain as blocking_domain,session2.nt_user_name as blocking_nt_user,tl.request_mode FROM sys.dm_tran_locks AS tl INNER JOIN sys.databases db ON db.database_id = tl.resource_database_id INNER JOIN sys.dm_os_waiting_tasks AS wt ON tl.lock_owner_address = wt.resource_address INNER JOIN sys.dm_exec_connections ec1 ON ec1.session_id = tl.request_session_id INNER JOIN sys.dm_exec_sessions session1 ON session1.session_id = tl.request_session_id INNER JOIN sys.dm_exec_connections ec2 ON ec2.session_id = wt.blocking_session_id INNER JOIN sys.dm_exec_sessions session2 ON session2.session_id = wt.blocking_session_id CROSS APPLY sys.dm_exec_sql_text(ec1.most_recent_sql_handle) AS h1 CROSS APPLY sys.dm_exec_sql_text(ec2.most_recent_sql_handle) AS h2"}, {"mssql.query.creation.time": "2022-06-08 12:19:54.867000", "mssql.query.last.execution.time": "2022-06-08 12:19:55.637000", "mssql.query.physical.reads": 0, "mssql.query.logical.reads": 204, "mssql.query.logical.writes": 0, "mssql.query.execution.count": 1, "mssql.query.cpu.time.sec": 11, "mssql.query.elapsed.time.sec": 11, "mssql.query.avg.elapsed.time.sec": 11, "mssql.query": "select sysd.name,sysddmig.*,sysddmid.statement as table_name,sysddmic.column_id,sysddmic.column_name,sysddmic.column_usage,sysddmigs.user_seeks,sysddmigs.user_scans,sysddmigs.avg_total_user_cost,sysddmigs.avg_user_impact from sys.dm_db_missing_index_details as sysddmid cross apply sys.dm_db_missing_index_columns (sysddmid.index_handle) sysddmic inner join sys.dm_db_missing_index_groups as sysddmig on sysddmig.index_handle = sysddmid.index_handle inner join sys.dm_db_missing_index_group_stats as sysddmigs on sysddmig.index_group_handle = sysddmigs.group_handle inner join sys.databases as sysd on sysd.database_id = sysddmid.database_id order by sysddmigs.avg_user_impact desc ,sysddmig.index_group_handle,sysddmig.index_handle,sysddmic.column_id"}], "correlation.metrics": ["mssql.query"]}, "errors": []}, "172.16.8.115": {"result": {"4": {"last.query.time": "2022-06-08 12:19:54.867000"}, "mssql.query": [{"mssql.query.creation.time": "2022-06-08 12:20:19.713000", "mssql.query.last.execution.time": "2022-06-08 12:20:20.477000", "mssql.query.physical.reads": 0, "mssql.query.logical.reads": 8, "mssql.query.logical.writes": 0, "mssql.query.execution.count": 1, "mssql.query.cpu.time.sec": 282, "mssql.query.elapsed.time.sec": 287, "mssql.query.avg.elapsed.time.sec": 287, "mssql.query": "SELECT db.name DB<PERSON><PERSON>, tl.request_session_id,wt.blocking_session_id,tl.resource_type,h1.TEXT AS blocked_query,h2.TEXT AS blocking_query,session1.login_name as blocked_sql_user ,session1.nt_domain as blocked_domain,session1.nt_user_name as blocked_nt_user,session1.program_name,session1.host_name,session2.login_name as blocking_sql_user ,session2.nt_domain as blocking_domain,session2.nt_user_name as blocking_nt_user,tl.request_mode FROM sys.dm_tran_locks AS tl INNER JOIN sys.databases db ON db.database_id = tl.resource_database_id INNER JOIN sys.dm_os_waiting_tasks AS wt ON tl.lock_owner_address = wt.resource_address INNER JOIN sys.dm_exec_connections ec1 ON ec1.session_id = tl.request_session_id INNER JOIN sys.dm_exec_sessions session1 ON session1.session_id = tl.request_session_id INNER JOIN sys.dm_exec_connections ec2 ON ec2.session_id = wt.blocking_session_id INNER JOIN sys.dm_exec_sessions session2 ON session2.session_id = wt.blocking_session_id CROSS APPLY sys.dm_exec_sql_text(ec1.most_recent_sql_handle) AS h1 CROSS APPLY sys.dm_exec_sql_text(ec2.most_recent_sql_handle) AS h2"}, {"mssql.query.creation.time": "2022-06-08 12:19:54.867000", "mssql.query.last.execution.time": "2022-06-08 12:19:55.637000", "mssql.query.physical.reads": 0, "mssql.query.logical.reads": 204, "mssql.query.logical.writes": 0, "mssql.query.execution.count": 1, "mssql.query.cpu.time.sec": 11, "mssql.query.elapsed.time.sec": 11, "mssql.query.avg.elapsed.time.sec": 11, "mssql.query": "select sysd.name,sysddmig.*,sysddmid.statement as table_name,sysddmic.column_id,sysddmic.column_name,sysddmic.column_usage,sysddmigs.user_seeks,sysddmigs.user_scans,sysddmigs.avg_total_user_cost,sysddmigs.avg_user_impact from sys.dm_db_missing_index_details as sysddmid cross apply sys.dm_db_missing_index_columns (sysddmid.index_handle) sysddmic inner join sys.dm_db_missing_index_groups as sysddmig on sysddmig.index_handle = sysddmid.index_handle inner join sys.dm_db_missing_index_group_stats as sysddmigs on sysddmig.index_group_handle = sysddmigs.group_handle inner join sys.databases as sysd on sysd.database_id = sysddmid.database_id order by sysddmigs.avg_user_impact desc ,sysddmig.index_group_handle,sysddmig.index_handle,sysddmic.column_id"}], "correlation.metrics": ["mssql.query"]}, "errors": []}, "fd00:1:1:1::132": {"result": {"4": {"last.query.time": "2022-06-08 12:19:54.867000"}, "mssql.query": [{"mssql.query.creation.time": "2022-06-08 12:20:19.713000", "mssql.query.last.execution.time": "2022-06-08 12:20:20.477000", "mssql.query.physical.reads": 0, "mssql.query.logical.reads": 8, "mssql.query.logical.writes": 0, "mssql.query.execution.count": 1, "mssql.query.cpu.time.sec": 282, "mssql.query.elapsed.time.sec": 287, "mssql.query.avg.elapsed.time.sec": 287, "mssql.query": "SELECT db.name DB<PERSON><PERSON>, tl.request_session_id,wt.blocking_session_id,tl.resource_type,h1.TEXT AS blocked_query,h2.TEXT AS blocking_query,session1.login_name as blocked_sql_user ,session1.nt_domain as blocked_domain,session1.nt_user_name as blocked_nt_user,session1.program_name,session1.host_name,session2.login_name as blocking_sql_user ,session2.nt_domain as blocking_domain,session2.nt_user_name as blocking_nt_user,tl.request_mode FROM sys.dm_tran_locks AS tl INNER JOIN sys.databases db ON db.database_id = tl.resource_database_id INNER JOIN sys.dm_os_waiting_tasks AS wt ON tl.lock_owner_address = wt.resource_address INNER JOIN sys.dm_exec_connections ec1 ON ec1.session_id = tl.request_session_id INNER JOIN sys.dm_exec_sessions session1 ON session1.session_id = tl.request_session_id INNER JOIN sys.dm_exec_connections ec2 ON ec2.session_id = wt.blocking_session_id INNER JOIN sys.dm_exec_sessions session2 ON session2.session_id = wt.blocking_session_id CROSS APPLY sys.dm_exec_sql_text(ec1.most_recent_sql_handle) AS h1 CROSS APPLY sys.dm_exec_sql_text(ec2.most_recent_sql_handle) AS h2"}, {"mssql.query.creation.time": "2022-06-08 12:19:54.867000", "mssql.query.last.execution.time": "2022-06-08 12:19:55.637000", "mssql.query.physical.reads": 0, "mssql.query.logical.reads": 204, "mssql.query.logical.writes": 0, "mssql.query.execution.count": 1, "mssql.query.cpu.time.sec": 11, "mssql.query.elapsed.time.sec": 11, "mssql.query.avg.elapsed.time.sec": 11, "mssql.query": "select sysd.name,sysddmig.*,sysddmid.statement as table_name,sysddmic.column_id,sysddmic.column_name,sysddmic.column_usage,sysddmigs.user_seeks,sysddmigs.user_scans,sysddmigs.avg_total_user_cost,sysddmigs.avg_user_impact from sys.dm_db_missing_index_details as sysddmid cross apply sys.dm_db_missing_index_columns (sysddmid.index_handle) sysddmic inner join sys.dm_db_missing_index_groups as sysddmig on sysddmig.index_handle = sysddmid.index_handle inner join sys.dm_db_missing_index_group_stats as sysddmigs on sysddmig.index_group_handle = sysddmigs.group_handle inner join sys.databases as sysd on sysd.database_id = sysddmid.database_id order by sysddmigs.avg_user_impact desc ,sysddmig.index_group_handle,sysddmig.index_handle,sysddmic.column_id"}], "correlation.metrics": ["mssql.query"]}, "errors": []}}