{"172.16.8.128": {"result": {"mssql.cpu.busy.seconds": 17, "mssql.io.busy.seconds": 14, "mssql.idle.seconds": 2033, "mssql.reads.rate": 0, "mssql.writes.rate": 0, "mssql.errors": 0, "mssql.connections": 44, "mssql.active.transactions": 0, "mssql.log.growths": 0, "mssql.lazy.writes.per.sec": 655, "mssql.free.list.stalls.per.sec": 7792, "mssql.errors.per.sec": 638386, "mssql.transactions.per.sec": 8643997, "mssql.buffer.cache.hit.ratio.percent": 100.0, "mssql.cache.object.counts": 0, "mssql.cache.used.objects": 0, "mssql.cache.pages": 213, "mssql.cache.hit.ratio.percent": 55.35, "mssql.deadlocks.per.sec": 0, "mssql.lock.waits.per.sec": 844, "mssql.lock.timeouts.per.sec": 5106, "mssql.average.wait.time.ms": 34156, "mssql.latch.waits.per.sec": 81741, "mssql.average.latch.wait.time.ms": 201013, "mssql.lock.requests.per.sec": 1118325642, "mssql.table.lock.escalations.per.sec": 0, "mssql.sql.recompilations.per.sec": 114, "mssql.page.splits.per.sec": 65725, "mssql.failed.auto.params.per.sec": 104436, "mssql.auto.param.attempts.per.sec": 114313, "mssql.worktable.creates.per.sec": 122542, "mssql.workfile.creates.per.sec": 402176, "mssql.sql.compilations.per.sec": 430074, "mssql.batch.requests.per.sec": 615099, "mssql.full.scans.per.sec": 1861259, "mssql.probe.scans.per.sec": 40805280, "mssql.range.scans.per.sec": 112099426, "mssql.blocked.processes": 0, "mssql.user.connections": 2, "mssql.logouts.per.sec": 60095, "mssql.logins.per.sec": 60097, "mssql.database.pages": 1448, "mssql.page.reads.per.sec": 70563, "mssql.page.writes.per.sec": 115444, "mssql.checkpoint.pages.per.sec": 127782, "mssql.page.life.expectancy": 523191, "mssql.page.lookups.per.sec": 345504109, "mssql.free.pages": 11143, "mssql.memory.pending.grants": 0, "mssql.memory.outstanding.grants": 1, "mssql.stolen.pages": 32425, "mssql.pages": 45016, "mssql.connection.memory.bytes": 499712, "mssql.lock.memory.bytes": 794624, "mssql.optimizer.memory.bytes": 1032192, "mssql.granted.workspace.memory.bytes": 1048576, "mssql.sql.cache.memory.bytes": 1654784, "mssql.target.server.memory.bytes": 368812032, "mssql.server.memory.provisioned.bytes": 368812032, "started.time.sec": 18123184, "started.time": "209 days, 18 hours, 13 minutes, 4 seconds", "mssql.version": "Microsoft SQL Server 2017 (RTM) - 14.0.1000.169 (X64) \n\tAug 22 2017 17:04:49 \n\tCopyright (C) 2017 Microsoft Corporation\n\tExpress Edition (64-bit) on Windows 10 Pro 10.0 <X64> (Build 18362: ) (Hypervisor)\n"}, "errors": []}, "fd00:1:1:1::132": {"result": {"mssql.cpu.busy.seconds": 17, "mssql.io.busy.seconds": 14, "mssql.idle.seconds": 2033, "mssql.reads.rate": 0, "mssql.writes.rate": 0, "mssql.errors": 0, "mssql.connections": 44, "mssql.active.transactions": 0, "mssql.log.growths": 0, "mssql.lazy.writes.per.sec": 655, "mssql.free.list.stalls.per.sec": 7792, "mssql.errors.per.sec": 638386, "mssql.transactions.per.sec": 8643997, "mssql.buffer.cache.hit.ratio.percent": 100.0, "mssql.cache.object.counts": 0, "mssql.cache.used.objects": 0, "mssql.cache.pages": 213, "mssql.cache.hit.ratio.percent": 55.35, "mssql.deadlocks.per.sec": 0, "mssql.lock.waits.per.sec": 844, "mssql.lock.timeouts.per.sec": 5106, "mssql.average.wait.time.ms": 34156, "mssql.latch.waits.per.sec": 81741, "mssql.average.latch.wait.time.ms": 201013, "mssql.lock.requests.per.sec": 1118325642, "mssql.table.lock.escalations.per.sec": 0, "mssql.sql.recompilations.per.sec": 114, "mssql.page.splits.per.sec": 65725, "mssql.failed.auto.params.per.sec": 104436, "mssql.auto.param.attempts.per.sec": 114313, "mssql.worktable.creates.per.sec": 122542, "mssql.workfile.creates.per.sec": 402176, "mssql.sql.compilations.per.sec": 430074, "mssql.batch.requests.per.sec": 615099, "mssql.full.scans.per.sec": 1861259, "mssql.probe.scans.per.sec": 40805280, "mssql.range.scans.per.sec": 112099426, "mssql.blocked.processes": 0, "mssql.user.connections": 2, "mssql.logouts.per.sec": 60095, "mssql.logins.per.sec": 60097, "mssql.database.pages": 1448, "mssql.page.reads.per.sec": 70563, "mssql.page.writes.per.sec": 115444, "mssql.checkpoint.pages.per.sec": 127782, "mssql.page.life.expectancy": 523191, "mssql.page.lookups.per.sec": 345504109, "mssql.free.pages": 11143, "mssql.memory.pending.grants": 0, "mssql.memory.outstanding.grants": 1, "mssql.stolen.pages": 32425, "mssql.pages": 45016, "mssql.connection.memory.bytes": 499712, "mssql.lock.memory.bytes": 794624, "mssql.optimizer.memory.bytes": 1032192, "mssql.granted.workspace.memory.bytes": 1048576, "mssql.sql.cache.memory.bytes": 1654784, "mssql.target.server.memory.bytes": 368812032, "mssql.server.memory.provisioned.bytes": 368812032, "started.time.sec": 18123184, "started.time": "209 days, 18 hours, 13 minutes, 4 seconds", "mssql.version": "Microsoft SQL Server 2017 (RTM) - 14.0.1000.169 (X64) \n\tAug 22 2017 17:04:49 \n\tCopyright (C) 2017 Microsoft Corporation\n\tExpress Edition (64-bit) on Windows 10 Pro 10.0 <X64> (Build 18362: ) (Hypervisor)\n"}, "errors": []}}