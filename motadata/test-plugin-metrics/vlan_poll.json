{"************": {"errors": [], "metric.timeout": 10, "object.ip": "************", "object.type": "Switch", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"vlan": [{"vlan": "20", "vlan.name": "1st_Flr", "vlan.port": "10136", "vlan.ports": 1, "vlan.status": "Active"}, {"vlan": "1", "vlan.name": "default", "vlan.port": "10101,10102", "vlan.ports": 2, "vlan.status": "Active"}, {"vlan": "30", "vlan.name": "2nd_Flr", "vlan.port": "10103,10112,10142,10107,10113,10115,10121,10127,10140,10105,10109,10110,10130,10125,10131,10132,10137,10106,10120,10124,10134,10133,10104,10108,10111,10116,10118,10123,10128,10114,10117,10119,10126,10139,10122,10129,10135,10141", "vlan.ports": 38, "vlan.status": "Active"}]}, "snmp.community": "public", "snmp.version": "v2c"}, "fd00:1:1:1::47": {"errors": [], "plugin.id": 222, "metric.timeout": 10, "object.ip": "fd00:1:1:1::47", "object.type": "Switch", "object.vendor": "Hewlett Packard Enterprise", "port": 161, "rediscover.job": "Network Metric", "result": {"vlan": [{"vlan": "1", "vlan.name": "default", "vlan.port": "56,57,58,23,24,25,55,59,60,26,61,54", "vlan.ports": 12, "vlan.status": "Active"}, {"vlan": "20", "vlan.name": "1st_floor", "vlan.port": "11,16,5,6,12,14,17,20,1,8,18,7,10,19,21,9,22,13,15", "vlan.ports": 19, "vlan.status": "Active"}, {"vlan": "300", "vlan.name": "wi-fi", "vlan.port": "4,2,3", "vlan.ports": 3, "vlan.status": "Active"}]}, "snmp.community": "public", "snmp.version": "v2c"}, "***********": {"errors": [], "metric.timeout": 10, "object.ip": "***********", "object.type": "Switch", "object.vendor": "D-Link", "port": 161, "rediscover.job": "Network Metric", "result": {"vlan": [{"vlan": "40", "vlan.name": "3rd_Flr", "vlan.port": "9,14,19,28,32,38,6,8,17,34,42,3,4,21,23,36,43,45,10,12,13,25,35,46,2,15,18,26,30,44,11,22,31,33,41,1,5,7,20,24,27,40,16,29,37,47", "vlan.ports": 46, "vlan.status": "Active"}, {"vlan": "1", "vlan.name": "default", "vlan.port": "49,48,39,52,50,51", "vlan.ports": 6, "vlan.status": "Active"}]}, "snmp.community": "public", "snmp.version": "v2c"}, "**********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844603127", "credential.profile.protocol": "SNMP V3", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587783], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844603129", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:27.148 pm 10/06/2022", "discovery.target": "**********", "discovery.target.name": "**********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624260, "event.timestamp": 1654845945, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587916, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800587783, "metric.credential.profile.protocol": "SNMP V3", "metric.discovery.method": "REMOTE", "metric.name": "VLAN", "metric.object": 58829800587784, "metric.plugin": "vlan", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:55:40.986 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000004, 10000000000013], "object.host": "cisco_core.motadata.local", "object.id": 9, "object.ip": "**********", "object.make.model": "Cisco Catalyst 93xx Switch Stack", "object.name": "cisco_core.motadata.local", "object.snmp.device.catalog": 58829800549968, "object.state": "ENABLE", "object.system.oid": ".*******.*******.2494", "object.target": "**********", "object.type": "Switch", "object.vendor": "Cisco Systems", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 177, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"vlan": [{"vlan": "1", "vlan.name": "default", "vlan.port": "32,44,33,34,35,45,46,47", "vlan.ports": 8, "vlan.status": "Active"}, {"vlan": "400", "vlan.name": "QA_8.0", "vlan.port": "21", "vlan.ports": 1, "vlan.status": "Active"}, {"vlan": "500", "vlan.name": "QA_Testing", "vlan.port": "30,17,18", "vlan.ports": 3, "vlan.status": "Active"}, {"vlan": "30", "vlan.name": "3rd_Flr", "vlan.port": "9", "vlan.ports": 1, "vlan.status": "Active"}, {"vlan": "40", "vlan.name": "VLAN0040", "vlan.port": "10", "vlan.ports": 1, "vlan.status": "Active"}, {"vlan": "300", "vlan.name": "Wi-Fi", "vlan.port": "12,11", "vlan.ports": 2, "vlan.status": "Active"}, {"vlan": "20", "vlan.name": "1st_Flr", "vlan.port": "14,36,8,27,19,22", "vlan.ports": 6, "vlan.status": "Active"}, {"vlan": "200", "vlan.name": "cctv", "vlan.port": "16,28", "vlan.ports": 2, "vlan.status": "Active"}]}, "snmp.security.level": "No Authentication No Privacy", "snmp.security.user.name": "trapuser", "snmp.version": "v3", "status": "succeed", "timeout": 60}, "*************": {"_type": "1", "credential.profile.name": "SNMP-Test-1654846121810", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800588090], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654846121812", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:58:45.715 pm 10/06/2022", "discovery.target": "*************", "discovery.target.name": "*************", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624607, "event.timestamp": 1654846135, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800588150, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800588090, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "VLAN", "metric.object": 58829800588091, "metric.plugin": "vlan", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:58:47.740 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000048, 10000000000013], "object.host": "hp4202vl-48g-MIMIC8806", "object.id": 13, "object.ip": "*************", "object.make.model": "ProCurve Switch 4202vl-48G", "object.name": "hp4202vl-48g-MIMIC8806", "object.snmp.device.catalog": 58829800527850, "object.state": "ENABLE", "object.system.oid": ".*******.********.*********", "object.target": "*************", "object.type": "Switch", "object.vendor": "Hewlett Packard Enterprise", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 177, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"vlan": [{"vlan": "1", "vlan.name": "DEFAULT_VLAN", "vlan.port": "43,48,40,38,44,18,25,41,20,35,36,39,24,49,23,22,37,47,21,46,17,19,42,45,26", "vlan.ports": 25, "vlan.status": "Active"}, {"vlan": "25", "vlan.name": "authVlan", "vlan.port": "3,6,15,9,2,11,32,13,16,7,10,12,34,1,4,5,27,29,33,8,30,31,14,28", "vlan.ports": 24, "vlan.status": "Active"}]}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "*************": {"_type": "1", "credential.profile.name": "SNMP-Test-1654846130604", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800588151], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654846130605", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:58:54.513 pm 10/06/2022", "discovery.target": "*************", "discovery.target.name": "*************", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624645, "event.timestamp": 1654846145, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800588210, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800588151, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "VLAN", "metric.object": 58829800588152, "metric.plugin": "vlan", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:58:56.534 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000048, 10000000000013], "object.host": "hp4202vl-48g-MIMIC9136", "object.id": 14, "object.ip": "*************", "object.make.model": "ProCurve Switch 4202vl-48G", "object.name": "hp4202vl-48g-MIMIC9136", "object.snmp.device.catalog": 58829800527850, "object.state": "ENABLE", "object.system.oid": ".*******.********.*********", "object.target": "*************", "object.type": "Switch", "object.vendor": "Hewlett Packard Enterprise", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 177, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"vlan": [{"vlan": "1", "vlan.name": "DEFAULT_VLAN", "vlan.port": "40,37,39,43,47,48,21,24,36,23,35,38,42,44,46,45,19,20,26,17,18,22,25,41,49", "vlan.ports": 25, "vlan.status": "Active"}, {"vlan": "25", "vlan.name": "authVlan", "vlan.port": "30,13,33,10,12,16,29,6,9,28,32,8,1,2,7,11,31,3,4,34,5,14,15,27", "vlan.ports": 24, "vlan.status": "Active"}]}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "*************": {"_type": "1", "credential.profile.name": "SNMP-Test-1654846139370", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800588211], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654846139371", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:59:03.840 pm 10/06/2022", "discovery.target": "*************", "discovery.target.name": "*************", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624704, "event.timestamp": 1654846155, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800588297, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800588211, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "VLAN", "metric.object": 58829800588212, "metric.plugin": "vlan", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:59:05.868 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000048, 10000000000013], "object.host": "J4850A-MIMIC9087", "object.id": 15, "object.ip": "*************", "object.make.model": "HP ProCurve Switch 5304XL", "object.name": "J4850A-MIMIC9087", "object.snmp.device.catalog": 58829800561806, "object.state": "ENABLE", "object.system.oid": ".*******.********.*********", "object.target": "*************", "object.type": "Switch", "object.vendor": "Hewlett Packard Enterprise", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 177, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"vlan": [{"vlan": "123", "vlan.name": "MAXVLAN123", "vlan.port": "7,4,6", "vlan.ports": 3, "vlan.status": "Active"}, {"vlan": "1", "vlan.name": "lacpTrkVlan", "vlan.port": "1,2", "vlan.ports": 2, "vlan.status": "Active"}, {"vlan": "25", "vlan.name": "authVlan", "vlan.port": "3,5", "vlan.ports": 2, "vlan.status": "Active"}]}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}}