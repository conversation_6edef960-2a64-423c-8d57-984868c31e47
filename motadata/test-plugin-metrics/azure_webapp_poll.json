{"127.0.0.1": {"19": {"metadata.fields": {"name": "testmotadataweb", "type": "appsrvc"}}, "_type": "1", "cloud.client.id": "aaa130f3-5ed7-404b-87f5-90a5a42750ea", "cloud.secret.key": "**********************************", "cloud.tenant.id": "5b4acec3-2592-4187-9489-98c654cc6c87", "cloud.type": "Azure Cloud", "credential.profile.name": "Azure-Cloud-Test1655203790900", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 24, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "Azure-Cloud-Test-1655203790903", "discovery.object.type": "Azure Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:20:25.670 pm 14/06/2022", "discovery.total.objects": 24, "errors": [], "event.id": 176993700500085, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Azure WebApp", "metric.object": ***************, "metric.plugin": "azurewebapp", "metric.polling.min.time": 300, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Azure WebApp", "object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:35.698 pm 14/06/2022", "object.custom.fields": {"***************": "appsrvc", "***************": "testmotadataweb"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 45, "object.name": "testmotadataweb(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.state": "ENABLE", "object.target": "testmotadataweb(motadata-freetier)", "object.type": "Azure WebApp", "object.vendor": "Azure Cloud", "plugin.engine": "go", "plugin.id": 19, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"19": {"metadata.fields": {"name": "testmotadataweb", "type": "appsrvc"}}, "azure.location": "Central US", "azure.name": "testmotadataweb", "azure.provisioning.state": "Running", "azure.webapp": "testmotadataweb", "azure.webapp.HTTP101.requests": 0, "azure.webapp.HTTP2xx.requests": 0, "azure.webapp.HTTP3xx.requests": 0, "azure.webapp.HTTP401.requests": 0, "azure.webapp.HTTP403.requests": 0, "azure.webapp.HTTP404.requests": 1, "azure.webapp.HTTP406.requests": 0, "azure.webapp.HTTP4xx.requests": 1, "azure.webapp.HTTP5xx.requests": 0, "azure.webapp.app.domains": 0, "azure.webapp.avg.memory.bytes": 34968576, "azure.webapp.avg.response.time": 0.03, "azure.webapp.config.workers": 1, "azure.webapp.configured.target.workers": 0, "azure.webapp.connections": 0, "azure.webapp.cpu.time.seconds": 45.5, "azure.webapp.current.assemblies": 0, "azure.webapp.default.host": "testmotadataweb.azurewebsites.net", "azure.webapp.disk.queue.length": 0, "azure.webapp.gen0.garbage.collections": 0, "azure.webapp.gen1.garbage.collections": 0, "azure.webapp.gen2.garbage.collections": 0, "azure.webapp.handles": 0, "azure.webapp.http.queue.length": 0, "azure.webapp.io.other.bytes.per.sec": 0, "azure.webapp.io.other.ops.per.sec": 0, "azure.webapp.io.read.bytes.per.sec": 0, "azure.webapp.io.read.ops.per.sec": 0, "azure.webapp.io.write.bytes.per.sec": 0, "azure.webapp.io.write.ops.per.sec": 0, "azure.webapp.memory.bytes": 34968576, "azure.webapp.memory.percentage": 56, "azure.webapp.private.memory.bytes": 0, "azure.webapp.queued.requests": 0, "azure.webapp.received.bytes.rate": 0, "azure.webapp.requests.rate": 0, "azure.webapp.resource.group": "motadata-freetier", "azure.webapp.sent.bytes.rate": 0, "azure.webapp.sites": 1, "azure.webapp.threads.count": 0, "azure.webapp.unloaded.app.domains": 0, "status": "Up"}, "status": "succeed", "timeout": 60}}