{"************": {"result": {"mssql.process": [{"mssql.process": 52, "mssql.process.kernel.id": 2164, "mssql.process.status": "suspended", "mssql.process.hostname": "SQLNODE1", "mssql.process.name": "Microsoft® Windows® Operating System", "mssql.process.command": "EXECUTE", "mssql.process.io.ops.rate": 112, "mssql.process.memory.bytes": 32768, "mssql.process.host": 3300, "mssql.process.wait.time.ms": 631}], "correlation.metrics": ["mssql.process"], "mssql.suspended.processes": 1}, "errors": []}, "************": {"result": {}, "errors": [{"error": "('08S01', '[08S01] [Microsoft][ODBC Driver 17 for SQL Server]TCP Provider: Error code 0x2746 (10054) (SQLDriverConnect)')\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/pluginengine/clients/databaseclient.py at line 535\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/pluginengine/clients/databaseclient.py at line 90\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/pluginengine/plugins/metric/mssqlprocess/plugin.py at line 43\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/tests/unit/plugins/metric/test_mssqlprocessplugin.py at line 57\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/python.py at line 183\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/python.py at line 1641\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 162\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 255\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 311\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 255\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 215\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 126\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 109\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 348\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 323\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 269\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 316\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/config/__init__.py at line 163\n\tat /home/<USER>/pycharm/helpers/pycharm/_jb_pytest_runner.py at line 45\n", "message": "Failed to establish DB connection on ************:1435", "error.code": "MD047"}]}}