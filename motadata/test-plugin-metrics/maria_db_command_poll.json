{"************": {"result": {}, "errors": [{"error": "1129: Host '************' is blocked because of many connection errors; unblock with 'mysqladmin flush-hosts'\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/pluginengine/clients/databaseclient.py at line 231\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/pluginengine/clients/databaseclient.py at line 90\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/pluginengine/plugins/metric/mariadbcommand/plugin.py at line 41\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/tests/unit/plugins/metric/test_mariadbcommandplugin.py at line 39\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/python.py at line 183\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/python.py at line 1641\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 162\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 255\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 311\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 255\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 215\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 126\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 109\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 348\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 323\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 269\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 316\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/config/__init__.py at line 163\n\tat /home/<USER>/pycharm/helpers/pycharm/_jb_pytest_runner.py at line 45\n", "message": "Failed to establish DB connection on ************:3306", "error.code": "MD047"}]}, "172.16.8.165": {"result": {"mariadb.admin.commands": 982904.0, "mariadb.analyze.commands": 0.0, "mariadb.change.database.commands": 0.0, "mariadb.change.master.commands": 0.0, "mariadb.check.commands": 0.0, "mariadb.create.database.commands": 0.0, "mariadb.drop.database.commands": 0.0, "mariadb.flush.commands.rate": 0.0, "mariadb.grant.commands": 0.0, "mariadb.kill.commands": 0.0, "mariadb.optimize.commands": 0.0, "mariadb.repair.commands": 0.0, "mariadb.reset.commands": 0.0, "mariadb.revoke.commands": 0.0, "mariadb.alter.table.commands": 0.0, "mariadb.create.function.commands": 0.0, "mariadb.create.index.commands": 0.0, "mariadb.create.table.commands": 0.0, "mariadb.drop.function.commands": 0.0, "mariadb.drop.index.commands": 0.0, "mariadb.drop.table.commands": 0.0, "mariadb.rename.table.commands": 0.0, "mariadb.handler.close.commands": 0.0, "mariadb.handler.open.commands": 0.0, "mariadb.handler.read.commands": 0.0, "mariadb.set.option.commands": 982904.0, "mariadb.insert.select.commands": 0.0, "mariadb.load.commands": 0.0, "mariadb.purge.commands": 0.0, "mariadb.replace.commands": 0.0, "mariadb.replace.select.commands": 0.0, "mariadb.truncate.commands": 0.0, "mariadb.show.binary.log.commands": 0.0, "mariadb.show.binary.log.event.commands": 0.0, "mariadb.show.database.commands": 0.0, "mariadb.show.field.commands": 0.0, "mariadb.show.grant.commands": 0.0, "mariadb.show.key.commands": 0.0, "mariadb.show.engine.log.commands": 0.0, "mariadb.show.master.status.commands": 0.0, "mariadb.show.open.table.commands": 0.0, "mariadb.show.processlist.commands": 120909.0, "mariadb.show.slave.host.commands": 0.0, "mariadb.show.slave.status.commands": 0.0, "mariadb.show.table.commands": 0.0, "mariadb.show.variable.commands": 324560.0, "mariadb.show.status.commands": 325428.0, "mariadb.begin.transaction.commands.rate": 0.0, "mariadb.commit.transaction.commands.rate": 0.0, "mariadb.lock.table.commands": 0.0, "mariadb.rollback.transaction.commands.rate": 0.0, "mariadb.unlock.table.commands": 0.0}, "errors": []}}