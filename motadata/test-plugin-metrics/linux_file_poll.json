{"***********": {"errors": [], "object.ip": "***********", "objects": [{"object.name": "/home/<USER>/mind8/linux space/linux space"}], "password": "Mind@123", "port": 22, "result": {"system.file": [{"status": "Up", "system.file": "/home/<USER>/mind8/linux space/linux space", "system.file.creation.time": "2020-12-09 15:23:29", "system.file.last.modified.time": "2020-12-09 15:23:29", "system.file.mode.group": "read write", "system.file.mode.others": "read", "system.file.mode.owner": "read write", "system.file.modified.duration.minutes": 787380, "system.file.owner": "motadata", "system.file.size.bytes": 0}]}, "timeout": 60, "username": "motadata"}, "************": {"errors": [], "object.ip": "************", "objects": [{"object.name": "/motadata/motadata/nfacctd.conf"}], "password": "motadata", "port": 22, "result": {"system.file": [{"status": "Up", "system.file": "/motadata/motadata/nfacctd.conf", "system.file.creation.time": "2020-05-26 09:24:17", "system.file.last.modified.time": "2020-07-31 11:20:56", "system.file.mode.group": "read", "system.file.mode.others": "read", "system.file.mode.owner": "read write", "system.file.modified.duration.minutes": 1080385, "system.file.owner": "motadata", "system.file.size.bytes": 532}]}, "timeout": 60, "username": "motadata"}, "************": {"errors": [], "object.ip": "************", "objects": [{"object.name": "/motadata/motadata/nfacctd.conf"}], "password": "motadata", "port": 22, "result": {"system.file": [{"status": "Up", "system.file": "/motadata/motadata/nfacctd.conf", "system.file.creation.time": "2020-05-26 09:24:17", "system.file.last.modified.time": "2020-07-31 11:20:56", "system.file.mode.group": "read", "system.file.mode.others": "read", "system.file.mode.owner": "read write", "system.file.modified.duration.minutes": 1080385, "system.file.owner": "motadata", "system.file.size.bytes": 532}]}, "timeout": 60, "username": "motadata"}, "***********": {"errors": [], "object.ip": "***********", "objects": [{"object.name": "/motadata/motadata/nfacctd.conf"}], "password": "motadata", "port": 22, "result": {"system.file": [{"status": "Down", "system.file": "/motadata/motadata/nfacctd.conf"}]}, "timeout": 60, "username": "motadata"}, "************": {"errors": [], "object.ip": "************", "objects": [{"object.name": "/motadata/motadata/nfacctd.conf"}], "password": "motadata", "port": 22, "result": {"system.file": [{"status": "Down", "system.file": "/motadata/motadata/nfacctd.conf"}]}, "timeout": 60, "username": "motadata"}, "************": {"errors": [], "object.ip": "************", "objects": [{"object.name": "/motadata/motadata/nfacctd.conf"}], "password": "motadata", "port": 22, "result": {"system.file": [{"status": "Down", "system.file": "/motadata/motadata/nfacctd.conf"}]}, "timeout": 60, "username": "motadata"}}