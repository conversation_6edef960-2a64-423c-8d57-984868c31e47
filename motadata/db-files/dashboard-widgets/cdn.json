[{"_type": "0", "id": 10000000001683, "visualization.name": "Request Size", "visualization.description": "Request Size Azure CDN", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cdn.response.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.cdn.response.bytes", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Request Size", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"lable": "", "value": "azure.cdn.response.bytes.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001684, "visualization.name": "Endpoints", "visualization.description": "Endpoints Azure CDN", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cdn.endpoints", "aggregator": "last", "entity.type": "Group", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.cdn.endpoints", "icon": {"name": "end-point", "placement": "prefix"}}, "header": {"title": "Endpoints", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Active", "value": "azure.cdn.endpoints.last"}, {"label": "Deactive", "value": "azure.cdn.endpoints.last"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001685, "visualization.name": "Origin Latency", "visualization.description": "Origin Latency Azure CDN", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cdn.latency.ms", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.cdn.latency.ms", "icon": {"name": "tacho-meter", "placement": "prefix"}}, "header": {"title": "Origin Latency", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.cdn.latency.ms.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001686, "visualization.name": "Request Count", "visualization.description": "Request Count Azure CDN", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cdn.requests", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.cdn.requests", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Request Count", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.cdn.requests.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001687, "visualization.name": "Total Latency", "visualization.description": "Total Latency Azure CDN", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cdn.latency.ms", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.cdn.latency.ms", "icon": {"name": "backup", "placement": "prefix"}}, "header": {"title": "Total Latency", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.cdn.latency.ms.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001688, "visualization.name": "Response Size", "visualization.description": "Response Size Azure CDN", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cdn.response.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.cdn.response.bytes", "icon": {"name": "response-time", "placement": "prefix"}}, "header": {"title": "Response Size", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.cdn.response.bytes.avg"}]}}}, "container.type": "dashboard"}, {"id": 10000000001689, "visualization.name": "TOP CDN with Deactivated Endpoints", "visualization.description": "TOP CDN with Deactivated Endpoints Azure CDN", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": ""}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001690, "visualization.name": "TOP CDN with Requests", "visualization.description": "TOP CDN with Requests Azure CDN", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cdn.requests", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": "azure.cdn.requests.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001691, "visualization.name": "TOP CDN By Latency", "visualization.description": "TOP CDN By Latency Azure CDN", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cdn.latency.ms", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001692, "visualization.name": "TOP CDN By Hit Ratio", "visualization.description": "TOP CDN By Hit Ratio Azure CDN", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cdn.byte.hit.ratio.percent", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}]