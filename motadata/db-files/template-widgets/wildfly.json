[{"id": 10000000000899, "_type": "0", "visualization.name": "Sessions", "visualization.description": "Wildfly Sessions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "wildfly.active.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.created.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.rejected.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "wildfly.expired.sessions"}, {"type": "metric", "data.point": "wildfly.active.sessions"}, {"type": "metric", "data.point": "wildfly.created.sessions"}, {"type": "metric", "data.point": "wildfly.rejected.sessions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "wildfly.active.sessions", "icon": {"name": "sessions", "placement": "prefix"}}, "header": {"title": "Sessions", "style": {"font.size": "medium"}, "data.points": [{"label": "Rejected Sessions", "value": "wildfly.rejected.sessions.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Active", "value": "wildfly.active.sessions.last"}, {"label": "Created", "value": "wildfly.created.sessions.last"}]}}}}, {"id": 10000000000900, "_type": "0", "visualization.name": "Heap Memory", "visualization.description": "Wildfly Heap Memory", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "wildfly.heap.memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.heap.memory.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.memory.used.percent"}, {"type": "metric", "data.point": "system.memory.installed.bytes"}, {"type": "metric", "data.point": "system.memory.used.bytes"}, {"type": "metric", "data.point": "wildfly.heap.memory.used.bytes"}, {"type": "metric", "data.point": "wildfly.heap.memory.used.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "wildfly.heap.memory.used.percent", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Heap Memory", "style": {"font.size": "medium"}, "data.points": [{"label": "Heap Memory Used", "value": "wildfly.heap.memory.used.bytes.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "wildfly.heap.memory.used.percent.last", "type": "gauge"}]}}}}, {"id": 10000000000901, "_type": "0", "visualization.name": "Classes", "visualization.description": "Wildfly Classes", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "wildfly.loaded.classes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.unloaded.classes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "wildfly.loaded.classes"}, {"type": "metric", "data.point": "wildfly.unloaded.classes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "wildfly.loaded.classes", "icon": {"name": "queue", "placement": "prefix"}}, "header": {"title": "Classes", "style": {"font.size": "medium"}}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Loaded", "value": "wildfly.loaded.classes.last"}, {"label": "Unloaded", "value": "wildfly.unloaded.classes.last"}]}}}}, {"id": 10000000000902, "_type": "0", "visualization.name": "Garbage Collector", "visualization.description": "Wildfly Garbage Collector", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "wildfly.collections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.collections.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.compilation.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "wildfly.collections"}, {"type": "metric", "data.point": "wildfly.collections.time.ms"}, {"type": "metric", "data.point": "wildfly.compilation.time.ms"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "wildfly.collections", "icon": {"name": "delete", "placement": "prefix"}}, "header": {"title": "Garbage Collector", "style": {"font.size": "medium"}, "data.points": [{"label": "Collections", "value": "wildfly.collections.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Collections", "value": "wildfly.collections.time.ms.last"}, {"label": "Compilation", "value": "wildfly.compilation.time.ms.last"}]}}}}, {"id": 10000000000903, "_type": "0", "visualization.name": "Transactions", "visualization.description": "Wildfly Transactions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "wildfly.transactions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.running.transactions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.committed.transactions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "wildfly.transactions"}, {"type": "metric", "data.point": "wildfly.running.transactions"}, {"type": "metric", "data.point": "wildfly.committed.transactions"}, {"type": "metric", "data.point": "wildfly.application.rolledback.transactions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "wildfly.running.transactions", "icon": {"name": "transactions", "placement": "prefix"}}, "header": {"title": "Transactions", "style": {"font.size": "medium"}, "data.points": [{"label": "Transactions", "value": "wildfly.transactions.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Running", "value": "wildfly.running.transactions.last"}, {"label": "Committed", "value": "wildfly.committed.transactions.last"}]}}}}, {"id": 10000000000904, "_type": "0", "visualization.name": "Traffic", "visualization.description": "Wildfly Traffic", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "wildfly.sent.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.received.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "wildfly.sent.bytes.per.sec"}, {"type": "metric", "data.point": "wildfly.received.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "wildfly.sent.bytes.per.sec", "icon": {"name": "repeat", "placement": "prefix"}}, "header": {"title": "Traffic", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Send", "value": "wildfly.sent.bytes.per.sec.last"}, {"label": "Received", "value": "wildfly.received.bytes.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000905, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000906, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"id": 10000000000907, "_type": "0", "visualization.name": "Sessions Details", "visualization.description": "Wildfly Sessions Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "wildfly.active.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.rejected.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.created.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.expired.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000908, "_type": "0", "visualization.name": "Transactions Details", "visualization.description": "Wildfly Transactions Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "wildfly.transactions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.running.transactions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.committed.transactions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.rolledback.transactions", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000909, "_type": "0", "visualization.name": "Request Rate", "visualization.description": "Wildfly Request Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "wildfly.requests.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000910, "_type": "0", "visualization.name": "Application Traffic", "visualization.description": "Wildfly Application Traffic", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "wildfly.sent.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.received.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000911, "_type": "0", "visualization.name": "Heap Memory Utilization", "visualization.description": "Wildfly Heap Memory Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "wildfly.heap.memory.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000912, "_type": "0", "visualization.name": "Request Latency", "visualization.description": "Wildfly Request Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "wildfly.request.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000913, "_type": "0", "visualization.name": "Thread Pool Utilization", "visualization.description": "Wildfly Thread Pool Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "wildfly.overall.jdbc.pool.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000914, "_type": "0", "visualization.name": "Errors Count", "visualization.description": "Wildfly Errors Count", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "wildfly.errors", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000915, "_type": "0", "visualization.name": "Errors Count (per sec)", "visualization.description": "Wildfly Errors Count", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "wildfly.requests.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000916, "_type": "0", "visualization.name": "Loaded and Unloaded Classes", "visualization.description": "Wildfly Loaded and Unloaded Classes", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "wildfly.loaded.classes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.unloaded.classes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000917, "_type": "0", "visualization.name": "JDBC Pool Details", "visualization.description": "Wildfly JDBC Pool Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "wildfly.jdbc.pool~available.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.jdbc.pool~created.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.jdbc.pool~destroyed.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.jdbc.pool~connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.jdbc.pool~used.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.jdbc.pool~used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "wildfly.jdbc.pool", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "wildfly.jdbc.pool~available.connections.last", "title": "Available", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "wildfly.jdbc.pool~created.connections.last", "title": "Created", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "wildfly.jdbc.pool~destroyed.connections.last", "title": "Destroyed", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "wildfly.jdbc.pool~connections.last", "title": "Connections", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "wildfly.jdbc.pool~used.connections.last", "title": "Used", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "wildfly.jdbc.pool~used.percent.last", "title": "Used Percent", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}]}}}, {"id": 10000000000918, "_type": "0", "visualization.name": "Thread Pool Details", "visualization.description": "Wildfly Thread Pool Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "wildfly.thread.pool~threads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.thread.pool~max.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.thread.pool~busy.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.thread.pool~idle.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "wildfly.thread.pool~used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "wildfly.thread.pool", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "wildfly.thread.pool~threads.last", "title": "Threads", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "wildfly.thread.pool~max.threads.last", "title": "max.threads", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "wildfly.thread.pool~busy.threads.last", "title": "busy.threads", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "wildfly.thread.pool~idle.threads.last", "title": "idle.threads", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "wildfly.thread.pool~used.percent.last", "title": "used.percent", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}]}}}]