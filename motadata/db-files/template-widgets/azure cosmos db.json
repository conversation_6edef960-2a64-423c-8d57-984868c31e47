[{"_type": "0", "id": 10000000000237, "visualization.name": "Available Storage", "visualization.description": "CosmosDB Available Storage", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.available.storage.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.cosmos.db.available.storage.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.cosmos.db.available.storage.bytes", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Available Storage", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.cosmos.db.available.storage.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000238, "visualization.name": "Request", "visualization.description": "CosmosDB Request", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.cosmos.db.requests"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.cosmos.db.requests", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Requests", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.cosmos.db.requests.last"}]}}}}, {"_type": "0", "id": 10000000000239, "visualization.name": "Document Count", "visualization.description": "CosmosDB Document Count", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.documents", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.cosmos.db.documents"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.cosmos.db.documents", "icon": {"name": "document", "placement": "prefix"}}, "header": {"title": "Document Count", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.cosmos.db.documents.last"}]}}}}, {"_type": "0", "id": 10000000000240, "visualization.name": "Availability", "visualization.description": "CosmosDB Availability", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.service.availability.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.cosmos.db.service.availability.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.cosmos.db.service.availability.percent", "icon": {"name": "availability", "placement": "prefix"}}, "header": {"title": "Availability", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.cosmos.db.service.availability.percent.last"}]}}}}, {"_type": "0", "id": 10000000000241, "visualization.name": "Data Usage", "visualization.description": "CosmosDB Data Usage", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.data.usage.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.cosmos.db.data.usage.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.cosmos.db.data.usage.bytes", "icon": {"name": "pie-chart", "placement": "prefix"}}, "header": {"title": "Data Usage", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.cosmos.db.data.usage.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000242, "visualization.name": "Index Usage", "visualization.description": "CosmosDB Index Usage", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.index.usage.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.cosmos.db.index.usage.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.cosmos.db.index.usage.bytes", "icon": {"name": "index", "placement": "prefix"}}, "header": {"title": "Index Usage", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.cosmos.db.index.usage.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000243, "visualization.name": "Request", "visualization.description": "CosmosDB Request", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000244, "visualization.name": "Total Requests", "visualization.description": "CosmosDB Total Requests", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.request.units", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000245, "visualization.name": "Provisioned Throughput", "visualization.description": "CosmosDB Provisioned Throughput", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.provisioned.throughput", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000246, "visualization.name": "Data and Index Storage Consumption", "visualization.description": "CosmosDB Data and Index Storage Consumption", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.data.usage.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.cosmos.db.index.usage.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000247, "visualization.name": "Service Availability", "visualization.description": "CosmosDB Service Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.service.availability.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000248, "visualization.name": "MongoDB Requests and Charges", "visualization.description": "CosmosDB MongoDB Requests and Charges", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.mongo.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.cosmos.db.mongo.request.charge", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]