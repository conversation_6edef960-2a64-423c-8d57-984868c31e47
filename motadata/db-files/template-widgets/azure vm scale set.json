[{"_type": "0", "id": 10000000000291, "visualization.name": "CPU", "visualization.description": "Scaleset CPU", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.vmscaleset.cpu.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.vmscaleset.cpu.percent", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.vmscaleset.cpu.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000000292, "visualization.name": "Disk Throughput", "visualization.description": "Scaleset Disk Throughput", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.disk.read.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vmscaleset.disk.write.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.vmscaleset.disk.read.bytes.per.sec"}, {"type": "metric", "data.point": "azure.vmscaleset.disk.write.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.vmscaleset.disk.write.bytes.per.sec", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Disk Throughput", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "azure.vmscaleset.disk.read.bytes.per.sec.last"}, {"label": "Write", "value": "azure.vmscaleset.disk.write.bytes.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000293, "visualization.name": "Disk IOPS", "visualization.description": "Scaleset Disk IOPS", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.disk.read.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vmscaleset.disk.write.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.vmscaleset.disk.read.ops.per.sec"}, {"type": "metric", "data.point": "azure.vmscaleset.disk.write.ops.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.vmscaleset.disk.write.ops.per.sec", "icon": {"name": "rtt", "placement": "prefix"}}, "header": {"title": "Disk IOPS", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "azure.vmscaleset.disk.read.ops.per.sec.last"}, {"label": "Write", "value": "azure.vmscaleset.disk.write.ops.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000294, "visualization.name": "Network", "visualization.description": "Scaleset Network", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.network.in.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vmscaleset.network.out.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.vmscaleset.network.in.bytes"}, {"type": "metric", "data.point": "azure.vmscaleset.network.out.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.vmscaleset.network.in.bytes", "icon": {"name": "network", "placement": "prefix"}}, "header": {"title": "Network", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "In Bytes", "value": "azure.vmscaleset.network.in.bytes.last"}, {"label": "Out Bytes", "value": "azure.vmscaleset.network.out.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000295, "visualization.name": "Flows", "visualization.description": "Scaleset Flows", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.inbound.flows", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vmscaleset.outbound.flows", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.vmscaleset.inbound.flows"}, {"type": "metric", "data.point": "azure.vmscaleset.outbound.flows"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.vmscaleset.inbound.flows", "icon": {"name": "sitemap", "placement": "prefix"}}, "header": {"title": "Flows", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Inbound", "value": "azure.vmscaleset.inbound.flows.last"}, {"label": "Outbound", "value": "azure.vmscaleset.outbound.flows.last"}]}}}}, {"_type": "0", "id": 10000000000296, "visualization.name": "CPU Credits", "visualization.description": "Scaleset CPU Credits", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.cpu.consumed.credits", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vmscaleset.cpu.remaining.credits", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.vmscaleset.cpu.consumed.credits"}, {"type": "metric", "data.point": "azure.vmscaleset.cpu.remaining.credits"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.vmscaleset.cpu.consumed.credits", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU Credits", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Consumed", "value": "azure.vmscaleset.cpu.consumed.credits.last"}, {"label": "Remaining", "value": "azure.vmscaleset.cpu.remaining.credits.last"}]}}}}, {"_type": "0", "id": 10000000000297, "visualization.name": "CPU Utilization", "visualization.description": "Scaleset CPU Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000298, "visualization.name": "Consumed Credits", "visualization.description": "Scaleset Consumed Credits", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.cpu.consumed.credits", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vmscaleset.cpu.remaining.credits", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000299, "visualization.name": "Network Traffic", "visualization.description": "Scaleset Network Traffic", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.network.in.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vmscaleset.network.out.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000300, "visualization.name": "Maximum Inbound Flow", "visualization.description": "Scaleset Maximum Inbound Flow", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.maximum.inbound.flows.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000301, "visualization.name": "Maximum Outbound Flow", "visualization.description": "Scaleset Maximum Outbound Flow", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.maximum.outbound.flows.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000302, "visualization.name": "Disk Operations", "visualization.description": "Scaleset Disk Operations", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.disk.read.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vmscaleset.disk.write.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000303, "visualization.name": "Disk Queue Length", "visualization.description": "Scaleset Disk Queue Length", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.disk.queue.length", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000304, "visualization.name": "Disk Throughput Chart", "visualization.description": "Scaleset Disk Throughput Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.disk.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vmscaleset.disk.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]