[{"id": 10000000000366, "_type": "0", "visualization.name": "Frontend Sessions", "visualization.description": "HA Proxy Frontend Sessions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.frontend~received.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~sent.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~session.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ha.proxy.frontend~received.bytes.per.sec"}, {"type": "metric", "data.point": "ha.proxy.frontend~sent.bytes.per.sec"}, {"type": "metric", "data.point": "ha.proxy.frontend~session.used.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "sessions", "placement": "prefix"}}, "header": {"title": "Frontend Sessions", "style": {"font.size": "medium"}, "data.points": [{"label": "Used", "value": "ha.proxy.frontend~session.used.percent.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Received", "value": "ha.proxy.frontend~received.bytes.per.sec.last"}, {"label": "<PERSON><PERSON>", "value": "ha.proxy.frontend~sent.bytes.per.sec.last"}]}}}}, {"id": 10000000000367, "_type": "0", "visualization.name": "Frontend Errors", "visualization.description": "HA Proxy Frontend Errors", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.frontend~http.server.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~http.client.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~request.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ha.proxy.frontend~http.server.errors"}, {"type": "metric", "data.point": "ha.proxy.frontend~http.client.errors"}, {"type": "metric", "data.point": "ha.proxy.frontend~request.errors"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "errors", "placement": "prefix"}, "color.data.point": "ha.proxy.frontend~request.errors"}, "header": {"title": "Frontend Errors", "style": {"font.size": "medium"}, "data.points": [{"label": "Request Error", "value": "ha.proxy.frontend~request.errors.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Server", "value": "ha.proxy.frontend~http.server.errors.last"}, {"label": "Client", "value": "ha.proxy.frontend~http.client.errors.last"}]}}}}, {"id": 10000000000368, "_type": "0", "visualization.name": "Backend Requests", "visualization.description": "HA Proxy Backend Requests", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.backend~response.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~denied.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~denied.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ha.proxy.backend~response.time.ms"}, {"type": "metric", "data.point": "ha.proxy.backend~denied.requests"}, {"type": "metric", "data.point": "ha.proxy.backend~denied.requests"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Backend Requests", "style": {"font.size": "medium"}, "data.points": [{"label": "Average Response Time ", "value": "ha.proxy.backend~response.time.ms.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Requests", "value": "ha.proxy.backend~denied.requests.last"}, {"label": "Denied", "value": "ha.proxy.backend~denied.requests.last"}]}}}}, {"id": 10000000000369, "_type": "0", "visualization.name": "Backend Connections", "visualization.description": "HA Proxy Backend Connections", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.backend~retried.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~redispatch.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ha.proxy.backend~retried.connections"}, {"type": "metric", "data.point": "ha.proxy.backend~redispatch.requests"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Backend Connections", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Retried", "value": "ha.proxy.backend~retried.connections.last"}, {"label": "Redispatched", "value": "ha.proxy.backend~redispatch.requests.last"}]}}}}, {"id": 10000000000370, "_type": "0", "visualization.name": "Pipes", "visualization.description": "HA Proxy Pipes", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.used.pipes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.free.pipes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ha.proxy.used.pipes"}, {"type": "metric", "data.point": "ha.proxy.free.pipes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "ha.proxy.used.pipes", "icon": {"name": "transactions", "placement": "prefix"}}, "header": {"title": "Pipes", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Used", "value": "ha.proxy.used.pipes.last"}, {"label": "Free", "value": "ha.proxy.free.pipes.last"}]}}}}, {"id": 10000000000371, "_type": "0", "visualization.name": "<PERSON><PERSON> Size", "visualization.description": "HA Proxy Queue Size", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.queue.size", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ha.proxy.queue.size"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "ha.proxy.queue.size", "icon": {"name": "queue", "placement": "prefix"}}, "header": {"title": "<PERSON><PERSON> Size", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ha.proxy.queue.size.last"}]}}}}, {"_type": "0", "id": 10000000000372, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000373, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"id": 10000000000374, "_type": "0", "visualization.name": "Frontend Session", "visualization.description": "HA Proxy Frontend Session", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.frontend~received.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~session.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~sent.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000375, "_type": "0", "visualization.name": "Connections", "visualization.description": "HA Proxy Connections", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.connections.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000376, "_type": "0", "visualization.name": "Sessions", "visualization.description": "HA Proxy Sessions", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.sessions.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000377, "_type": "0", "visualization.name": "<PERSON><PERSON> Size", "visualization.description": "HA Proxy Queue Size", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.queue.size", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000378, "_type": "0", "visualization.name": "Frontend Errors", "visualization.description": "HA Proxy Frontend Errors", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.frontend~http.client.errors", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~http.server.errors", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~request.errors", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000379, "_type": "0", "visualization.name": "Backend Requests", "visualization.description": "HA Proxy Backend Requests", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.backend~response.time.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~request.errors", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~denied.responses", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000380, "_type": "0", "visualization.name": "Backend Connections", "visualization.description": "HA Proxy Backend Connections", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.backend~retried.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~redispatch.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000381, "_type": "0", "visualization.name": "Frontend Details", "visualization.description": "HA Proxy Frontend Detail", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.frontend~server.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~pid", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~session.connections.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~session.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~queue.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~sent.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~received.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend~server.name.last", "title": "Server Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"icon": {"placement": "prefix", "conditions": [{"icon": "times-circle", "operator": "=", "value": "DOWN"}, {"icon": "check-circle", "operator": "=", "value": "UP"}]}}}, {"name": "ha.proxy.frontend~pid.last", "title": "PID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend~session.connections.per.sec.last", "title": "Session Connections", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend~session.used.percent.last", "title": "Sessions", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "gauge"}}}, {"name": "ha.proxy.frontend~queue.connections.last", "title": "Queue Connections", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend~sent.bytes.per.sec.last", "title": "Sent Bytes Rate", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend~received.bytes.per.sec.last", "title": "Received Bytes Rate", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000382, "_type": "0", "visualization.name": "Frontend Response Time Details", "visualization.description": "HA Proxy Frontend Response Time Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.frontend~response.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~queue.response.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend~response.time.ms.last", "title": "Response Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend~queue.response.time.ms.last", "title": "Queue Response Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000383, "_type": "0", "visualization.name": "Frontend HTTP Errors", "visualization.description": "HA Proxy Frontend HTTP Errors", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.frontend~http.client.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~http.server.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend~http.client.errors.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend~http.server.errors.last", "title": "Server Error", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000384, "_type": "0", "visualization.name": "Frontend Retries And Redispatches", "visualization.description": "HA Proxy Frontend Retries And Redispatches", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.frontend~redispatch.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~retried.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend~redispatch.requests.last", "title": "Redispatches Requests", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend~retried.connections.last", "title": "Retries Connections", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000385, "_type": "0", "visualization.name": "Frontend Denials and Errors", "visualization.description": "HA Proxy Frontend Denials and Errors", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.frontend~denied.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~denied.responses", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~request.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~response.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.frontend~connection.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend~denied.requests.last", "title": "Denied Requests", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend~denied.responses.last", "title": "Denied Responses", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend~request.errors.last", "title": "Request Errors", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend~response.errors.last", "title": "Response Errors", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.frontend~connection.errors.last", "title": "Connection Errors", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000386, "_type": "0", "visualization.name": "Backend Details", "visualization.description": "HA Proxy Backend Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.backend~server.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~pid", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~session.connections.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~session.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~queue.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~sent.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~received.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.backend", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.backend~server.name.last", "title": "Server Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.backend~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status"}, {"name": "ha.proxy.backend~pid", "title": "PID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.backend~session.connections.per.sec.last", "title": "Session Connections", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.backend~session.used.percent.last", "title": "Sessions", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "gauge"}}}, {"name": "ha.proxy.backend~queue.connections.last", "title": "Queue Connections", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.backend~sent.bytes.last", "title": "Sent Bytes Rate", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.backend~received.bytes.last", "title": "Received Bytes Rate", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000387, "_type": "0", "visualization.name": "Backend Response Time Details", "visualization.description": "HA Proxy Backend Response Time Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.backend~response.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~queue.response.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.backend", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.backend~response.time.ms.last", "title": "Response Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.backend~queue.response.time.ms.last", "title": "Queue Response Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000388, "_type": "0", "visualization.name": "Backend HTTP Errors", "visualization.description": "HA Proxy Backend HTTP Errors", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.backend~http.client.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~http.server.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.backend", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.backend~http.client.errors.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.backend~http.server.errors.last", "title": "Server Errors", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000389, "_type": "0", "visualization.name": "Backend Retries and Redispatches", "visualization.description": "HA Proxy Backend Retries and Redispatches", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.backend~redispatch.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~retried.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.backend", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.backend~redispatch.requests.last", "title": "Redispatches Requests", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ha.proxy.backend~retried.connections.last", "title": "Retries Connections", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000390, "_type": "0", "visualization.name": "Backend Denials and Errors", "visualization.description": "HA Proxy Backend Denials and Errors", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ha.proxy.backend~denied.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~denied.responses", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~request.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~response.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ha.proxy.backend~connection.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ha.proxy.backend", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "ha.proxy.backend~denied.requests.last", "title": "Denied Requests", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "ha.proxy.backend~denied.responses.last", "title": "Denied Responses", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "ha.proxy.backend~request.errors.last", "title": "Request Errors", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "ha.proxy.backend~response.errors.last", "title": "Response Errors", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "ha.proxy.backend~connection.errors.last", "title": "Connection Errors", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}]}}}]