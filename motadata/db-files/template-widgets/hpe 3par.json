[{"_type": "0", "id": 10000000002886, "visualization.name": "Local Capacity", "visualization.description": "Cluster Storage capacity", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.system.storage.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.system.storage.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.system.storage.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hpe.3par.system.storage.used.percent"}, {"type": "metric", "data.point": "hpe.3par.system.storage.capacity.bytes"}, {"type": "metric", "data.point": "hpe.3par.system.storage.free.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Capacity", "style": {"font.size": "medium"}, "data.points": [{"label": "Total", "value": "hpe.3par.system.storage.capacity.bytes.last"}, {"label": "Free", "value": "hpe.3par.system.storage.free.bytes.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "hpe.3par.system.storage.used.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "disk"}, "color.data.point": "hpe.3par.system.storage.used.percent"}}}}, {"_type": "0", "id": 10000000002887, "visualization.name": "Host", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.hosts", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hpe.3par.hosts"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "host"}, "font.size": "medium", "color.data.point": "hpe.3par.hosts"}, "header": {"title": "Host", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": " ", "value": "hpe.3par.hosts.last"}]}}}}, {"_type": "0", "id": 10000000002888, "visualization.name": "CPG", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.cpgs", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hpe.3par.cpgs"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "cpg"}, "font.size": "medium", "color.data.point": "hpe.3par.cpgs"}, "header": {"title": "CPG", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": " ", "value": "hpe.3par.cpgs.last"}]}}}}, {"_type": "0", "id": 10000000002889, "visualization.name": "Disk", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.disks", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hpe.3par.disks"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "disk"}, "font.size": "medium", "color.data.point": "hpe.3par.disks"}, "header": {"title": "Disk", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": " ", "value": "hpe.3par.disks.last"}]}}}}, {"_type": "0", "id": 10000000002890, "visualization.name": "Volume", "visualization.description": "Cluster Storage Volume", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.volumes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.virtual.volume.sets", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hpe.3par.volumes"}, {"type": "metric", "data.point": "hpe.3par.virtual.volume.sets"}]}], "visualization.properties": {"gauge": {"header": {"title": "Volume", "style": {"font.size": "medium"}, "data.points": [{"label": "Virtual", "value": "hpe.3par.virtual.volume.sets.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "hpe.3par.volumes.last"}]}, "style": {"icon": {"name": "volume"}, "font.size": "medium", "color.data.point": "hpe.3par.volumes"}}}}, {"_type": "0", "id": 10000000002891, "visualization.name": "LUN", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.luns", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hpe.3par.luns"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "luns"}, "font.size": "medium", "color.data.point": "hpe.3par.luns"}, "header": {"title": "LUN", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": " ", "value": "hpe.3par.luns.last"}]}}}}, {"_type": "0", "id": 10000000002892, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000002893, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000002894, "visualization.name": "Capacity Utilization", "visualization.description": "HPE Capacity Utilization", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.system.storage.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.system.storage.free.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.system.storage.capacity.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002895, "visualization.name": "Top 10 Node By CPU Interrupts Per Second", "visualization.description": "HPE Node", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.node~cpu.interrupts.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.node"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002896, "visualization.name": "Top 10 Node By CPU Context Switches Per Second", "visualization.description": "HPE Node", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.node~cpu.context.switches.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.node"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002897, "visualization.name": "Top 10 Node By CPU User (%)", "visualization.description": "HPE Node CPU Utilization", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.node~cpu.user.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.node"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002898, "visualization.name": "Top 10 Node By CPU System (%)", "visualization.description": "HPE Node CPU Utilization", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.node~cpu.system.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.node"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003900, "visualization.name": "Top 10 Node By CPU Idle (%)", "visualization.description": "HPE Node CPU Utilization", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.node~cpu.idle.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.node"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003902, "visualization.name": "Host Details", "visualization.category": "Grid", "visualization.type": "Host Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.host~id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.host~location", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.host~os", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.host~model", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.host~fc.paths", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.host~iscsi.paths", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Host Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 2}, {"name": "hpe.3par.host", "title": "Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.3par.host~id.last", "title": "ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.3par.host~location.last", "title": "Location", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"]}}, {"name": "hpe.3par.host~os.last", "title": "OS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.3par.host~model.last", "title": "Model", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.3par.host~fc.paths.last", "title": "Associated FC Paths", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}, "position": 7, "type": "view_more_drawer"}, {"name": "hpe.3par.host~iscsi.paths.last", "title": "Associated ISCSI Paths", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}, "position": 8, "type": "view_more_drawer"}]}}}, {"_type": "0", "id": 10000000003904, "visualization.name": "Host Set Details", "visualization.category": "Grid", "visualization.type": "Host Set Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.host.set~id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.host.set~uuid", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.host.set~members", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Host Set Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.3par.host.set", "title": "Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.3par.host.set~id.last", "title": "ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.3par.host.set~uuid.last", "title": "UUID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.3par.host.set~members.last", "title": "Members", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "view_more_drawer"}]}}}, {"_type": "0", "id": 10000000003905, "visualization.name": "Virtual Volume Set Details", "visualization.category": "Grid", "visualization.type": "Virtual Volume Set Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.virtual.volume.set~id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.virtual.volume.set~uuid", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.virtual.volume.set~members", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.virtual.volume.set~vvol.storage.container.enabled", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.virtual.volume.set~qos.enabled", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Host Set Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.3par.virtual.volume.set", "title": "Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.3par.virtual.volume.set~id.last", "title": "ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.3par.virtual.volume.set~uuid.last", "title": "UUID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.3par.virtual.volume.set~members.last", "title": "Members", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "view_more_drawer"}, {"name": "hpe.3par.virtual.volume.set~vvol.storage.container.enabled.last", "title": "Container Enabled", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.3par.virtual.volume.set~qos.enabled.last", "title": "QoS Enabled", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}]}}}, {"_type": "0", "id": 10000000003901, "visualization.name": "Node Details", "visualization.category": "Grid", "visualization.type": "Node Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.node~cpu.user.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node~cpu.system.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node~cpu.idle.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node~cpu.interrupts.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node~cpu.context.switches.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node~hit.io.read.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node~hit.io.write.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node~miss.io.read.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node~miss.io.write.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node~access.io.read.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node~access.io.write.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node~hit.io.read.ops.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node~hit.io.write.ops.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node~io.access.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node~lock.bulk.io.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Host Set Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.3par.node", "title": "Node ID", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.3par.node~cpu.user.percent.last", "title": "CPU User (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.3par.node~cpu.system.percent.last", "title": "CPU System (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.3par.node~cpu.idle.percent.last", "title": "CPU Idle (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "hpe.3par.node~cpu.interrupts.per.sec.last", "title": "CPU Interrupts Per Second", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.3par.node~cpu.context.switches.per.sec.last", "title": "CPU Context Switched Per Second", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.3par.node~hit.io.read.ops.per.sec.last", "title": "Hit Read IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "hpe.3par.node~hit.io.write.ops.per.sec.last", "title": "Hit Write IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "hpe.3par.node~miss.io.read.ops.per.sec.last", "title": "Miss Read IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "hpe.3par.node~miss.io.write.ops.per.sec.last", "title": "Miss Write IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "hpe.3par.node~access.io.read.ops.per.sec.last", "title": "Access Read IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11}, {"name": "hpe.3par.node~access.io.write.ops.per.sec.last", "title": "Access Write IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12}, {"name": "hpe.3par.node~hit.io.read.ops.percent.last", "title": "Hit Read IO (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 13}, {"name": "hpe.3par.node~hit.io.write.ops.percent.last", "title": "Hit Write IO (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 14}, {"name": "hpe.3par.node~io.access.per.sec.last", "title": "Total Access IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 15}, {"name": "hpe.3par.node~lock.bulk.io.per.sec.last", "title": "Lock Bulk IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 16}]}}}, {"_type": "0", "id": 10000000003903, "visualization.name": "Node Page Statistics", "visualization.category": "Grid", "visualization.type": "Node Page Statistics", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.node.page~free.pages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node.page~clean.pages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node.page~write.once.pages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node.page~multiple.write.pages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node.page~scheduled.write.pages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.node.page~deferred.cow.pending.pages", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "<PERSON>de Page <PERSON>", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.3par.node.page", "title": "Node ID", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.3par.node.page~free.pages.last", "title": "Free Pages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.3par.node.page~clean.pages.last", "title": "Clean Pages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "hpe.3par.node.page~write.once.pages.last", "title": "Once Write Pages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.3par.node.page~multiple.write.pages.last", "title": "Multiple Write Pages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.3par.node.page~scheduled.write.pages.last", "title": "Scheduled Write Pages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "hpe.3par.node.page~deferred.cow.pending.pages.last", "title": "Deferred COW Pending Pages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}]}}}, {"_type": "0", "id": 10000000003909, "visualization.name": "CPG Details", "visualization.category": "Grid", "visualization.type": "CPG Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.cpg~id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.cpg~uuid", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.cpg~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.cpg~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.cpg~free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.cpg~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.cpg~shared.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.cpg~private.base.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.cpg~private.snapshot.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.cpg~failed.persistent.virtual.volumes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.cpg~persistent.virtual.volumes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.cpg~degraded.virtual.volumes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "CPG Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.3par.cpg", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.3par.cpg~id.last", "title": "ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.3par.cpg~uuid.last", "title": "UUID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.3par.cpg~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "hpe.3par.cpg~capacity.bytes.last", "title": "Total Space", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.3par.cpg~free.bytes.last", "title": "Free Space", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.3par.cpg~used.bytes.last", "title": "Used Space", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "hpe.3par.cpg~shared.bytes.last", "title": "Shared Space", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "hpe.3par.cpg~private.base.bytes.last", "title": "Private Base", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "hpe.3par.cpg~private.snapshot.bytes.last", "title": "Private Snapshot", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "hpe.3par.cpg~failed.persistent.virtual.volumes.last", "title": "Failed Persistent Virtual Volumes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11}, {"name": "hpe.3par.cpg~persistent.virtual.volumes.last", "title": "Total Persistent Virtual Volumes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12}, {"name": "hpe.3par.cpg~degraded.virtual.volumes.last", "title": "Total Degraded Virtual Volumes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 13}]}}}, {"_type": "0", "id": 10000000003923, "visualization.name": "Volume Details", "visualization.category": "Grid", "visualization.type": "Volume Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.volume~id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.volume~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.volume~deduplication.state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.volume~copy.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.volume~provision.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.volume~creation.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.volume~wwn", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.volume~size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.volume~reserved.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.volume~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.volume~cpgs", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Volume Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.3par.volume", "title": "Volume Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.3par.volume~id.last", "title": "Volume ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.3par.volume~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.3par.volume~deduplication.state.last", "title": "Deduplication State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "hpe.3par.volume~copy.type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.3par.volume~provision.type.last", "title": "Provision Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.3par.volume~creation.time.last", "title": "Creation Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "hpe.3par.volume~wwn.last", "title": "WWN", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "hpe.3par.volume~size.bytes.last", "title": "Total Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "hpe.3par.volume~reserved.bytes.last", "title": "Reserved Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "hpe.3par.volume~used.bytes.last", "title": "Used Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11}, {"name": "hpe.3par.volume~cpgs.last", "title": "Associated CPG", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12, "type": "view_more_drawer"}]}}}, {"_type": "0", "id": 10000000003919, "visualization.name": "LUN Details", "visualization.category": "Grid", "visualization.type": "LUN Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.lun~io.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.lun~io.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.lun~io.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.lun~io.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.lun~queue.length", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.lun~busy.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "LUN Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.3par.lun", "title": "LUN ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.3par.lun~io.ops.per.sec.last", "title": "Total IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.3par.lun~io.bytes.per.sec.last", "title": "Total bps", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.3par.lun~io.bytes.last", "title": "Total IO Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "hpe.3par.lun~io.latency.ms.last", "title": "Service Time ms", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.3par.lun~queue.length.last", "title": "Queue Length", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.3par.lun~busy.percent.last", "title": "Busy (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}]}}}, {"_type": "0", "id": 10000000003927, "visualization.name": "Disk Details", "visualization.category": "Grid", "visualization.type": "Disk Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.disk~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.disk~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.disk~model", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.disk~serial.number", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.disk~manufacturer", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.disk~media.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.disk~protocol", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.disk~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.disk~free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.disk~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Disk Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.3par.disk", "title": "Disk", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.3par.disk~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.3par.disk~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.3par.disk~model.last", "title": "Model", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "hpe.3par.disk~serial.number.last", "title": "Serial Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.3par.disk~manufacturer.last", "title": "Manufacture", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.3par.disk~media.type.last", "title": "Media Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "hpe.3par.disk~protocol.last", "title": "Protocol", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "hpe.3par.disk~capacity.bytes.last", "title": "Total Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "hpe.3par.disk~free.bytes.last", "title": "Free Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "hpe.3par.disk~used.bytes.last", "title": "Used Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11}]}}}, {"_type": "0", "id": 10000000003928, "visualization.name": "Port Details", "visualization.category": "Grid", "visualization.type": "Port Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.port~mode", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.port~link.state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.port~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.port~wwn", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.port~node.wwn", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.port~label", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.port~devices", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.port~configurable.bitrate", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.port~max.bitrate", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.port~speed", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Port Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.3par.port", "title": "Port", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.3par.port~mode.last", "title": "Mode", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.3par.port~link.state.last", "title": "Link State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.3par.port~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "hpe.3par.port~wwn.last", "title": "WWN", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.3par.port~node.wwn.last", "title": "Node WWN", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.3par.port~label.last", "title": "Label", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "hpe.3par.port~devices.last", "title": "Devices Count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "type": "view_more_drawer"}, {"name": "hpe.3par.port~configurable.bitrate.last", "title": "Configurable Rate", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "hpe.3par.port~max.bitrate.last", "title": "Max Rate", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "hpe.3par.port~speed.last", "title": "Speed", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11}]}}}, {"_type": "0", "id": 10000000003929, "visualization.name": "FC Switch Details", "visualization.category": "Grid", "visualization.type": "FC Switch Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.port.fcswitch~logical.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.port.fcswitch~ports", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.port.fcswitch~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.3par.port.fcswitch~vendor", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "FC Switch Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.3par.port.fcswitch", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.3par.port.fcswitch~logical.name.last", "title": "Logical Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.3par.port.fcswitch~ports.last", "title": "Ports", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.3par.port.fcswitch~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "hpe.3par.port.fcswitch~vendor.last", "title": "<PERSON><PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}]}}, "visualization.empty.view": "no"}, {"_type": "0", "id": 10000000003906, "visualization.name": "Top 10 CPG by IO size", "visualization.description": "HPE CPG IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.cpg~io.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.cpg"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003907, "visualization.name": "Top 10 CPG by IOPS", "visualization.description": "HPE CPG IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.cpg~io.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.cpg"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003908, "visualization.name": "Top 10 CPG by Queue Length", "visualization.description": "HPE CPG", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.cpg~queue.length", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.cpg"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003910, "visualization.name": "Top 10 LUN by Read Size", "visualization.description": "HPE LUN IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.lun~io.read.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.lun"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003911, "visualization.name": "Top 10 LUN by Read IOPS", "visualization.description": "HPE LUN IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.lun~io.read.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.lun"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003912, "visualization.name": "Top 10 LUN by Read IO BPS", "visualization.description": "HPE LUN IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.lun~io.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.lun"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003913, "visualization.name": "Top 10 LUN by Write IO Size", "visualization.description": "HPE LUN IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.lun~io.write.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.lun"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003914, "visualization.name": "Top 10 LUN by Write IOPS", "visualization.description": "HPE LUN IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.lun~io.write.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.lun"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003915, "visualization.name": "Top 10 LUN by Write IO BPS", "visualization.description": "HPE LUN IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.lun~io.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.lun"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003916, "visualization.name": "Top 10 LUN by Total IO Size", "visualization.description": "HPE LUN IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.lun~io.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.lun"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003917, "visualization.name": "Top 10 LUN by IOPS", "visualization.description": "HPE LUN IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.lun~io.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.lun"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003918, "visualization.name": "Top 10 LUN by IO BPS", "visualization.description": "HPE LUN IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.lun~io.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.lun"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003924, "visualization.name": "Top 10 Disk by IO Size", "visualization.description": "HPE Disk IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.disk~io.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.disk"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003925, "visualization.name": "Top 10 Disk by IOPS", "visualization.description": "HPE Disk IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.disk~io.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.disk"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003926, "visualization.name": "Top 10 Disk by <PERSON>ue Length", "visualization.description": "HPE Disk IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.disk~queue.length", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.disk"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003920, "visualization.name": "Top 10 Volume by Read IOPS", "visualization.description": "HPE Volume IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.volume~io.read.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.volume"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003921, "visualization.name": "Top 10 Volume by Write IOPS", "visualization.description": "HPE Volume IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.volume~io.write.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.volume"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003922, "visualization.name": "Top 10 Volume by Total IOPS", "visualization.description": "HPE Volume IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.3par.volume~io.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.3par.volume"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}]