[{"_type": "0", "id": 10000000000787, "visualization.name": "Virtual Machine", "visualization.description": "Virtual Machine HyperV", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.virtual.machines", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.running.virtual.machines", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hyperv.virtual.machines"}, {"type": "metric", "data.point": "hyperv.running.virtual.machines"}]}], "visualization.properties": {"gauge": {"header": {"title": "Virtual Machines", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "VM", "value": "hyperv.virtual.machines.last"}, {"label": "Running VM", "value": "hyperv.running.virtual.machines.last"}]}, "style": {"icon": {"name": "vm"}, "color.data.point": "hyperv.running.virtual.machines"}}}}, {"_type": "0", "id": 10000000000788, "visualization.name": "CPU Utilization", "visualization.description": "CPU Utilization HyperV", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.cpu.idle.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hyperv.cpu.idle.percent"}, {"type": "metric", "data.point": "hyperv.cpu.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU", "data.points": [{"label": "CPU Idle", "value": "hyperv.cpu.idle.percent.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "hyperv.cpu.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "cpu"}, "color.data.point": "hyperv.cpu.percent"}}}}, {"_type": "0", "id": 10000000000789, "visualization.name": "Memory Utilization", "visualization.description": "Memory Utilization HyperV", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.memory.allocated.available.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.memory.committed.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.memory.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hyperv.memory.allocated.available.bytes"}, {"type": "metric", "data.point": "hyperv.memory.committed.bytes"}, {"type": "metric", "data.point": "hyperv.memory.free.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory", "data.points": [{"label": "Committed", "value": "hyperv.memory.committed.bytes.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Available", "value": "hyperv.memory.allocated.available.bytes.last"}, {"label": "Free", "value": "hyperv.memory.free.bytes.last"}]}, "style": {"icon": {"name": "memory"}, "color.data.point": "hyperv.memory.free.bytes"}}}}, {"_type": "0", "id": 10000000000790, "visualization.name": "Disk Utilization", "visualization.description": "Disk Utilization HyperV", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.disk.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.disk.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.disk.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hyperv.disk.capacity.bytes"}, {"type": "metric", "data.point": "hyperv.disk.free.bytes"}, {"type": "metric", "data.point": "hyperv.disk.used.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Disk", "data.points": [{"label": "Capacity", "value": "hyperv.disk.capacity.bytes.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Used", "value": "hyperv.disk.used.bytes.last"}, {"label": "Free", "value": "hyperv.disk.free.bytes.last"}]}, "style": {"icon": {"name": "disk"}, "color.data.point": "hyperv.disk.used.bytes"}}}}, {"_type": "0", "id": 10000000000791, "visualization.name": "Disk IOPS", "visualization.description": "Disk IOPS HyperV", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.disk.io.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.disk.io.read.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.disk.io.write.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hyperv.disk.io.ops.per.sec"}, {"type": "metric", "data.point": "hyperv.disk.io.read.ops.per.sec"}, {"type": "metric", "data.point": "hyperv.disk.io.write.ops.per.sec"}]}], "visualization.properties": {"gauge": {"header": {"title": "Disk IOPS", "data.points": [{"label": "Disk IO", "value": "hyperv.disk.io.ops.per.sec.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "hyperv.disk.io.read.ops.per.sec.last"}, {"label": "Write", "value": "hyperv.disk.io.write.ops.per.sec.last"}]}, "style": {"icon": {"name": "rtt"}, "color.data.point": "hyperv.disk.io.ops.per.sec"}}}}, {"_type": "0", "id": 10000000000792, "visualization.name": "Network Traffic HyperV", "visualization.description": "Network Traffic HyperV", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.network.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.network.in.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.network.out.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hyperv.network.bytes.per.sec"}, {"type": "metric", "data.point": "hyperv.network.in.bytes.per.sec"}, {"type": "metric", "data.point": "hyperv.network.out.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"header": {"title": "Network", "data.points": [{"label": "Network Traffic", "value": "hyperv.network.bytes.per.sec.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "In", "value": "hyperv.network.in.bytes.per.sec.last"}, {"label": "Out", "value": "hyperv.network.out.bytes.per.sec.last"}]}, "style": {"icon": {"name": "network"}, "color.data.point": "hyperv.network.bytes.per.sec"}}}}, {"_type": "0", "id": 10000000000793, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000794, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000795, "visualization.name": "Disk Throughput", "visualization.description": "Disk Throughput HyperV", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.disk.io.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.disk.io.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.disk.io.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000796, "visualization.name": "CPU Utilization", "visualization.description": "CPU Utilization (TimeSeries) HyperV", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.cpu.idle.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000797, "visualization.name": "Memory Pages", "visualization.description": "Memory Pages (TimeSeries) HyperV", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.paged.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.non.paged.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000798, "visualization.name": "Disk Utilization", "visualization.description": "Disk Utilization (TimeSeries) HyperV", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.disk.free.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.disk.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000799, "visualization.name": "Processor Details", "visualization.description": "Processor Details HyperV", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.logical.processors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.physical.processors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.virtual.processors", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"layout": "column", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "title": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.logical.processors.last", "title": "Logical Processor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.physical.processors.last", "title": "Physical Processor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.virtual.processors.last", "title": "Virtual Processor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000800, "visualization.name": "Page Allocation", "visualization.description": "Page Allocation (TimeSeries) HyperV", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.remote.physical.pages", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.physical.allocated.pages", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000801, "visualization.name": "Disk IOPS", "visualization.description": "Disk IOPS (TimeSeries) HyperV", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.disk.io.write.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.disk.io.read.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000802, "visualization.name": "Interrupts", "visualization.description": "Interrupts (TimeSeries) HyperV", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.interrupts.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000803, "visualization.name": "Average Pressure", "visualization.description": "Average Pressure (TimeSeries) HyperV", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.average.pressure", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000804, "visualization.name": "Disk Queue Length", "visualization.description": "Disk Queue Length (TimeSeries) HyperV", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.disk.io.queue.length", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000805, "visualization.name": "Context Switches", "visualization.description": "Context Switches (TimeSeries) HyperV", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.context.switches.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000806, "visualization.name": "Space Modification", "visualization.description": "Space Modification (TimeSeries) HyperV", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.gpa.space.modifications", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000807, "visualization.name": "Network Traffic", "visualization.granularity": "10 m", "visualization.description": "Network Traffic (TimeSeries) HyperV", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.network.in.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.network.out.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.network.output.queue.length", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 10, "unit": "m"}}]