[{"_type": "0", "id": 10000000001083, "visualization.name": "CPU (%)", "visualization.description": "CPU Utilization HP-UX", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.cpu.user.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.cpu.idle.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.cpu.percent"}, {"type": "metric", "data.point": "system.cpu.idle.percent"}, {"type": "metric", "data.point": "system.cpu.interrupt.percent"}, {"type": "metric", "data.point": "system.cpu.system.percent"}, {"type": "metric", "data.point": "system.cpu.user.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU", "style": {"font.size": "medium"}, "data.points": [{"label": "User", "value": "system.cpu.user.percent.last"}, {"label": "Idle", "value": "system.cpu.idle.percent.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "system.cpu.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "cpu"}, "color.data.point": "system.cpu.percent"}}}}, {"_type": "0", "id": 10000000001084, "visualization.name": "Memory (%)", "visualization.description": "Memory Utilization HP-UX", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.memory.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.memory.installed.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.memory.free.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.memory.used.percent"}, {"type": "metric", "data.point": "system.memory.free.percent"}, {"type": "metric", "data.point": "system.memory.committed.bytes"}, {"type": "metric", "data.point": "system.memory.free.bytes"}, {"type": "metric", "data.point": "system.cache.memory.bytes"}, {"type": "metric", "data.point": "system.memory.installed.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory", "style": {"font.size": "medium"}, "data.points": [{"label": "Installed", "value": "system.memory.installed.bytes.last"}, {"label": "Free", "value": "system.memory.free.percent.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "system.memory.used.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "memory"}, "color.data.point": "system.memory.used.percent"}}}}, {"_type": "0", "id": 10000000001085, "visualization.name": "Disk (%) ", "visualization.description": "Disk Utilization HP-UX", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.disk.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.disk.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.disk.free.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.disk.used.percent"}, {"type": "metric", "data.point": "system.disk.free.percent"}, {"type": "metric", "data.point": "system.disk.free.bytes"}, {"type": "metric", "data.point": "system.disk.capacity.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Disk", "style": {"font.size": "medium"}, "data.points": [{"label": "Capacity", "value": "system.disk.capacity.bytes.last"}, {"label": "Free", "value": "system.disk.free.percent.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "system.disk.used.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "disk"}, "color.data.point": "system.disk.used.percent"}}}}, {"_type": "0", "id": 10000000001086, "visualization.name": "Processes", "visualization.description": "Processes HP-UX", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.running.processes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.running.processes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Processes", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "system.running.processes.last"}]}, "style": {"icon": {"name": "processes"}, "color.data.point": "system.running.processes"}}}}, {"_type": "0", "id": 10000000001087, "visualization.name": "Interrupts", "visualization.description": "Interrupts HP-UX", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.interrupts.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.interrupts.per.sec"}]}], "visualization.properties": {"gauge": {"header": {"title": "Interrupts", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "system.interrupts.per.sec.last"}]}, "style": {"icon": {"name": "traffic"}, "color.data.point": "system.interrupts.per.sec"}}}}, {"_type": "0", "id": 10000000001088, "visualization.name": "Response Time", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ping.lost.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ping.latency.ms"}, {"type": "metric", "data.point": "ping.max.latency.ms"}, {"type": "metric", "data.point": "ping.min.latency.ms"}, {"type": "metric", "data.point": "ping.lost.packets"}]}], "visualization.properties": {"gauge": {"header": {"title": "Response Time", "style": {"font.size": "medium"}, "data.points": [{"label": "Error Packets", "value": "ping.lost.packets.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ping.latency.ms.last"}]}, "style": {"icon": {"name": "traffic"}, "color.data.point": "ping.latency.ms"}}}}, {"_type": "0", "id": 10000000001089, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000001090, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000001091, "visualization.name": "Disk Volume Details", "visualization.description": "Disk Volume Details HP-UX", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.disk.volume~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.disk.volume~free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.disk.volume~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.disk.volume~mount.path", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"view": "Storage Details", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "system.disk.volume", "alias": "system.disk.volume", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "system.disk.volume~capacity.bytes.last", "alias": "system.disk.volume~capacity.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "system.disk.volume~free.bytes.last", "alias": "system.disk.volume~free.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "system.disk.volume~used.bytes.last", "alias": "system.disk.volume~used.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000001092, "visualization.name": "CPU Details", "visualization.description": "System CPU Details HP-UX", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.cpu.idle.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.cpu.user.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.cpu.kernel.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.cpu.io.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001093, "visualization.name": "Memory Details", "visualization.description": "System Memory Details HP-UX", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.memory.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001094, "visualization.name": "Disk IO", "visualization.description": "Disk IO Details HP-UX", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.disk.io.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001095, "visualization.name": "Processor Queue Length", "visualization.description": "Processor Queue Length HP-UX", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.processor.queue.length", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001096, "visualization.name": "Context Switches", "visualization.description": "Context Switches HP-UX", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.context.switches.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001097, "visualization.name": "Blocked Processes", "visualization.description": "Blocked Processes HP-UX", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.blocked.processes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001098, "visualization.name": "CPU Core Details", "visualization.description": "CPU Core Details HP-UX", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.cpu.core~idle.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.cpu.core~percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.cpu.core~user.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.cpu.core~io.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "system.cpu.core~percent.last", "title": "Core(%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "process", "style": {}}, {"name": "system.cpu.core~idle.percent.last", "title": "Idle(%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "gauge"}}}, {"name": "system.cpu.core~user.percent.last", "title": "User(%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "alignment": "center", "style": {}}, {"name": "system.cpu.core~io.percent.last", "title": "IO(%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000001099, "visualization.name": "Process Details", "visualization.description": "Process Details HP-UX", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~command", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "system.process", "title": "Process", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "process", "style": {}}, {"name": "system.process~cpu.percent.last", "title": "CPU(%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "gauge"}}}, {"name": "system.process~memory.used.bytes.last", "title": "Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "alignment": "center", "style": {}}, {"name": "system.process~command.last", "title": "Command", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "alignment": "center", "style": {}}]}}, "visualization.empty.view": "no"}]