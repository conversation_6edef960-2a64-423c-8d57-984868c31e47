[{"_type": "0", "id": 10000000002919, "visualization.name": "Nutanix Host VMs", "visualization.description": "Nutanix Host VMS", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vms", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "nutanix.vms"}]}], "visualization.properties": {"gauge": {"header": {"title": "VMs", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "nutanix.vms.last"}]}, "style": {"icon": {"name": "vm"}, "color.data.point": "nutanix.vms.last"}}}}, {"_type": "0", "id": 10000000002920, "visualization.name": "Nutanix Host CPU", "visualization.description": "Nutanix Host CPU", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.cpu.sockets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.cpu.cores", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.cpu.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "nutanix.cpu.sockets"}, {"type": "metric", "data.point": "nutanix.cpu.cores"}, {"type": "metric", "data.point": "nutanix.cpu.threads"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU", "style": {"font.size": "medium"}, "data.points": [{"label": "Socket", "value": "nutanix.cpu.sockets.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Cores", "value": "nutanix.cpu.cores.last"}, {"label": "Threads", "value": "nutanix.cpu.threads.last"}]}}}}, {"_type": "0", "id": 10000000002921, "visualization.name": "Nutanix Host Memory Capacity", "visualization.description": "Nutanix Host VMS", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.memory.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "nutanix.memory.capacity.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory Capacity", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "nutanix.memory.capacity.bytes.last"}]}, "style": {"icon": {"name": "memory"}, "color.data.point": "nutanix.vms.last"}}}}, {"_type": "0", "id": 10000000002922, "visualization.name": "Nutanix Host Storage", "visualization.description": "Nutanix Host Storage", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.storage.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.storage.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.storage.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "nutanix.storage.capacity.bytes"}, {"type": "metric", "data.point": "nutanix.storage.used.bytes"}, {"type": "metric", "data.point": "nutanix.storage.free.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Storage", "style": {"font.size": "medium"}, "data.points": [{"label": "Total", "value": "nutanix.storage.capacity.bytes.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Used", "value": "nutanix.storage.used.bytes.last"}, {"label": "Free", "value": "nutanix.storage.free.bytes.last"}]}}}}, {"_type": "0", "id": 10000000002923, "visualization.name": "Nutanix Host CPU Usage", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "nutanix.cpu.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU Usage", "style": {"font.size": "medium", "icon": "cpu"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "nutanix.cpu.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "cpu"}}}}}, {"_type": "0", "id": 10000000002924, "visualization.name": "Nutanix Host Memory Usage", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.memory.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "nutanix.memory.used.percent"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "memory"}, "color.data.point": "nutanix.memory.used.percent"}, "header": {"title": "Memory", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "nutanix.memory.used.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000002925, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000002926, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000002927, "visualization.name": "Nutanix CPU vs Memory Utilization", "visualization.description": "Nutanix Host CPU vs Memory Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.memory.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002928, "visualization.name": "Nutanix Average IO Latency", "visualization.description": "Nutanix Host Average IO Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.hypervisor.io.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002929, "visualization.name": "Nutanix IO Bandwidth", "visualization.description": "Nutanix Host IO Bandwidth", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.io.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.io.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002930, "visualization.name": "Nutanix Storage IOPS", "visualization.description": "Nutanix Host Storage IOPS", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.io.read.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.io.write.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002931, "visualization.name": "Nutanix Controller IOPS", "visualization.description": "Nutanix Host Controller IOPS", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.controller.io.write.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.controller.io.read.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002932, "visualization.name": "Nutanix Controller IO Latency", "visualization.description": "Nutanix Host Controller IO Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.io.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002933, "visualization.name": "Nutanix Controller IO Bandwidth", "visualization.description": "Nutanix Host Controller IO Bandwidth", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.controller.io.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.controller.io.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002934, "visualization.name": "Nutanix VM Summary", "visualization.description": "Nutanix Host VM Summary", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~host.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~ip", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~description", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~guest.os", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~cpus", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~memory.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~disk.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~network.adapters", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~power.state", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "nutanix.vm", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.vm~host.last", "title": "Host Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.vm~ip.last", "title": "IP Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.vm~description.last", "title": "Description", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.vm~guest.os.last", "title": "Guest OS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.vm~cpus.last", "title": "VCPUs", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.vm~memory.capacity.bytes.last", "title": "Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.vm~disk.capacity.bytes.last", "title": "Disk", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.vm~network.adapters.last", "title": "Network Adapters", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.vm~power.state.last", "title": "Power State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "style": {}}]}}}, {"_type": "0", "id": 10000000002935, "visualization.name": "Nutanix Top VM by CPU Usage", "visualization.description": "Nutanix Top VM by CPU Usage", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~ip", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~cpu.percent", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.vm~host.last", "title": "Host Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "vm", "style": {}}, {"name": "nutanix.vm~ip.last", "title": "IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.vm~cpu.percent.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.vm~cpu.percent.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "nutanix.vm~cpu.percent.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000002936, "visualization.name": "Nutanix Top VM by Memory Usage", "visualization.description": "Nutanix Top VM by Memory Usage", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~ip", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~memory.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~memory.used.percent", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.vm~host.last", "title": "Host Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "vm", "style": {}}, {"name": "nutanix.vm~ip.last", "title": "IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.vm~memory.used.percent.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.vm~memory.used.percent.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "nutanix.vm~memory.used.percent.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000002937, "visualization.name": "Nutanix Top VM by IO Latency", "visualization.description": "Nutanix Top VM by IO Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~io.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~io.latency.ms", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~ip", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.vm~host", "title": "Host Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "vm", "style": {}}, {"name": "nutanix.vm~ip.last", "title": "IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.vm~io.latency.ms.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.vm~io.latency.ms.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "nutanix.vm~io.latency.ms.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000002938, "visualization.name": "Nutanix Disk Summary", "visualization.description": "Nutanix Disk Summary", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.disk~tier.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.disk~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.disk~online.state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.disk~model", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.disk~storage.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.disk~storage.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.disk~storage.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.disk~mount.path", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.disk~boot.status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "nutanix.disk", "title": "Disk", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.disk~tier.name.last", "title": "Tier Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.disk~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"icon": {"placement": "prefix", "conditions": [{"value": "Active", "operator": "=", "icon": "check-circle"}, {"value": "Inactive", "operator": "=", "icon": "times-circle"}]}, "classes": ["font-bold"]}}, {"name": "nutanix.disk~online.state.last", "title": "Is Online", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.disk~model.last", "title": "Module Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.disk~storage.capacity.bytes.last", "title": "Capacity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.disk~storage.free.bytes.last", "title": "Free", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.disk~storage.used.bytes.last", "title": "Used", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.disk~mount.path.last", "title": "Mount Path", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.disk~boot.status.last", "title": "Boot Disk", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes"}]}}}, {"_type": "0", "id": 10000000002939, "visualization.name": "Nutanix Top Disk by IO Latency", "visualization.description": "Nutanix Top Disk by IO Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.disk~io.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.disk~io.latency.ms", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.disk", "title": "Disk Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "vm", "style": {}}, {"name": "nutanix.disk~io.latency.ms.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.disk~io.latency.ms.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "nutanix.disk~io.latency.ms.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000002940, "visualization.name": "Nutanix Top Disk by IO Bandwidth", "visualization.description": "Nutanix Top Disk by IO Bandwidth", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.disk~io.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.disk~io.bytes.per.sec", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.disk", "title": "Disk Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "vm", "style": {}}, {"name": "nutanix.disk~io.bytes.per.sec.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.disk~io.bytes.per.sec.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "nutanix.disk~io.bytes.per.sec.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000002941, "visualization.name": "Nutanix Top Disk by IOPS", "visualization.description": "Nutanix Top Disk by IOPS", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.disk~io.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.disk~io.ops.per.sec", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.disk", "title": "Disk Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "vm", "style": {}}, {"name": "nutanix.disk~io.ops.per.sec.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "nutanix.disk~io.ops.per.sec.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "nutanix.disk~io.ops.per.sec.avg"}}, "sparkline": "yes"}}]