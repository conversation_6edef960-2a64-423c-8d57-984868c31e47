[{"_type": "0", "id": 10000000000882, "visualization.name": "VM CPU Utilization", "visualization.description": "CPU Utilization Hyper VM", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hyperv.vm~cpu.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "hyperv.vm~cpu.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "cpu"}, "color.data.point": "hyperv.vm~cpu.percent"}}}}, {"_type": "0", "id": 10000000000883, "visualization.name": "VM Memory Usages", "visualization.description": "Memory Utilization Hyper VM", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~memory.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hyperv.vm~memory.free.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Free", "value": "hyperv.vm~memory.free.bytes.last"}]}, "style": {"icon": {"name": "memory"}, "color.data.point": "hyperv.vm~memory.free.bytes"}}}}, {"_type": "0", "id": 10000000000884, "visualization.name": "VM Disk IOPS", "visualization.description": "Disk IOPS HyperV VM", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~disk.io.read.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~disk.io.write.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~disk.io.queue.size", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hyperv.vm~disk.io.read.ops.per.sec"}, {"type": "metric", "data.point": "hyperv.vm~disk.io.write.ops.per.sec"}, {"type": "metric", "data.point": "hyperv.vm~disk.io.queue.size"}]}], "visualization.properties": {"gauge": {"header": {"title": "Disk IO", "style": {"font.size": "medium"}, "data.points": [{"label": "<PERSON><PERSON> Size", "value": "hyperv.vm~disk.io.queue.size.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "hyperv.vm~disk.io.read.ops.per.sec.last"}, {"label": "Write", "value": "hyperv.vm~disk.io.write.ops.per.sec.last"}]}, "style": {"icon": {"name": "rtt"}, "color.data.point": "hyperv.vm~disk.io.queue.size"}}}}, {"_type": "0", "id": 10000000000885, "visualization.name": "VM Disk IO Throughput", "visualization.description": "Disk IO Throughput HyperV VM", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~disk.io.read.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~disk.io.write.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~disk.io.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hyperv.vm~disk.io.read.bytes.per.sec"}, {"type": "metric", "data.point": "hyperv.vm~disk.io.write.bytes.per.sec"}, {"type": "metric", "data.point": "hyperv.vm~disk.io.errors"}]}], "visualization.properties": {"gauge": {"header": {"title": "Disk Throughput", "style": {"font.size": "medium"}, "data.points": [{"label": "Disk IO Errors", "value": "hyperv.vm~disk.io.errors.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "hyperv.vm~disk.io.read.bytes.per.sec.last"}, {"label": "Write", "value": "hyperv.vm~disk.io.write.bytes.per.sec.last"}]}, "style": {"icon": {"name": "disk"}, "color.data.point": "hyperv.vm~disk.io.read.bytes.per.sec"}}}}, {"_type": "0", "id": 10000000000886, "visualization.name": "Network Traffic", "visualization.description": "Network Traffic HyperV VM", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~network.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~network.packets.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hyperv.vm~network.bytes.per.sec"}, {"type": "metric", "data.point": "hyperv.vm~network.packets.per.sec"}]}], "visualization.properties": {"gauge": {"header": {"title": "Network", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Packets", "value": "hyperv.vm~network.packets.per.sec.last"}, {"label": "Volume", "value": "hyperv.vm~network.bytes.per.sec.last"}]}, "style": {"icon": {"name": "topology"}, "color.data.point": "hyperv.vm~network.bytes.per.sec"}}}}, {"_type": "0", "id": 10000000000887, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "hyperv.vm~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "hyperv.vm~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "hyperv.vm~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "hyperv.vm~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "hyperv.vm~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "hyperv.vm~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "hyperv.vm~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "hyperv.vm~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "hyperv.vm~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "hyperv.vm~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "hyperv.vm~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "hyperv.vm~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000888, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "hyperv.vm~uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000889, "visualization.name": "VM Memory Pressure", "visualization.description": "Memory Pressure Hyper VM", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~average.pressure.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~current.pressure.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~minimum.pressure.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hyperv.vm~average.pressure.percent"}, {"type": "metric", "data.point": "hyperv.vm~current.pressure.percent"}, {"type": "metric", "data.point": "hyperv.vm~minimum.pressure.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory Pressure", "style": {"font.size": "medium"}, "data.points": [{"label": "Minimum", "value": "hyperv.vm~minimum.pressure.percent.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Current", "value": "hyperv.vm~current.pressure.percent.last"}, {"label": "Average", "value": "hyperv.vm~average.pressure.percent.last"}]}, "style": {"icon": {"name": "memory"}, "color.data.point": "hyperv.vm~current.pressure.percent"}}}}, {"_type": "0", "id": 10000000000890, "visualization.name": "VM Memory Utilization", "visualization.description": "VM Memory Utilization Hyper VM", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~memory.free.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": [13364271503388]}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000891, "visualization.name": "VM Guest CPU Utilization", "visualization.description": "VM Guest CPU Utilization Hyper VM", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~guest.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": [13364271503388]}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000892, "visualization.name": "VM CPU Utilization", "visualization.description": "VM CPU Utilization Hyper VM", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": [13364271503388]}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000893, "visualization.name": "Memory Pages", "visualization.description": "VM Memory Pages Hyper VM", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~physical.allocated.pages", "aggregator": "avg", "entity.type": "monitor", "entities": [13364271501961]}, {"data.point": "hyperv.vm~deposited.pages", "aggregator": "avg", "entity.type": "monitor", "entities": [13364271501961]}, {"data.point": "hyperv.vm~remote.physical.pages", "aggregator": "avg", "entity.type": "monitor", "entities": [13364271501961]}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000894, "visualization.name": "Address Space", "visualization.description": "VM Address Space Hyper VM", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~address.spaces", "aggregator": "avg", "entity.type": "monitor", "entities": [13364271501961]}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000895, "visualization.name": "Disk I/O Throughput", "visualization.description": "VM Disk I/O Throughput Hyper VM", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~disk.io.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~disk.io.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.vm~disk.io.errors", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000896, "visualization.name": "Network Traffic", "visualization.description": "VM Network Traffic Hyper VM", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~network.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": [13364271503388]}, {"data.point": "hyperv.vm~network.packets.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": [13364271503388]}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000897, "visualization.name": "Disk I/O", "visualization.description": "VM Disk I/O Utilization Hyper VM", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~disk.io.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": [13364271503388]}, {"data.point": "hyperv.vm~disk.io.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": [13364271503388]}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000898, "visualization.name": "GPA Pages", "visualization.description": "VM GPA Pages Hyper VM", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.vm~gpa.pages", "aggregator": "avg", "entity.type": "monitor", "entities": [13364271503090]}, {"data.point": "hyperv.vm~gpa.space.modifications", "aggregator": "avg", "entity.type": "monitor", "entities": [13364271503090]}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]