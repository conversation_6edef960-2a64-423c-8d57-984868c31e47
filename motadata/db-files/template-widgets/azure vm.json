[{"_type": "0", "id": 10000000000320, "visualization.name": "CPU", "visualization.description": "VM CPU", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vm.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.vm.cpu.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.vm.cpu.percent", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.vm.cpu.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000000321, "visualization.name": "Memory Pages", "visualization.description": "Azure Memory Pages", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vm.memory.page.reads.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vm.memory.page.writes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.vm.memory.page.reads.per.sec"}, {"type": "metric", "data.point": "azure.vm.memory.page.writes.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.vm.memory.page.reads.per.sec", "icon": {"name": "stopwatch", "placement": "prefix"}}, "header": {"title": "Memory Pages", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "azure.vm.memory.page.reads.per.sec.last"}, {"label": "Write", "value": "azure.vm.memory.page.writes.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000322, "visualization.name": "Disk IOPS", "visualization.description": "VM Disk IOPS", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vm.disk.io.ops.reads.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vm.disk.io.ops.writes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000323, "visualization.name": "Disk IO", "visualization.description": "VM Disk IO", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vm.disk.io.write.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vm.disk.io.read.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.vm.disk.io.write.bytes"}, {"type": "metric", "data.point": "azure.vm.disk.io.read.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.vm.disk.io.read.bytes", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Disk IO", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "azure.vm.disk.io.read.bytes.last"}, {"label": "Write", "value": "azure.vm.disk.io.write.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000324, "visualization.name": "Disk Throughput", "visualization.description": "VM Disk Throughput", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vm.disk.io.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vm.disk.io.read.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vm.disk.io.write.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.vm.disk.io.bytes.per.sec"}, {"type": "metric", "data.point": "azure.vm.disk.io.read.bytes.per.sec"}, {"type": "metric", "data.point": "azure.vm.disk.io.write.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.vm.disk.io.bytes.per.sec", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Disk Throughput", "style": {"font.size": "medium"}, "data.points": [{"label": "disk.io.bytes.per.sec", "value": "azure.vm.disk.io.bytes.per.sec.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "azure.vm.disk.io.read.bytes.per.sec.last"}, {"label": "Write", "value": "azure.vm.disk.io.write.bytes.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000325, "visualization.name": "Memory", "visualization.description": "VM Memory", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vm.memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vm.memory.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.vm.memory.used.bytes"}, {"type": "metric", "data.point": "azure.vm.memory.free.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.vm.memory.used.bytes", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Memory", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Used", "value": "azure.vm.memory.used.bytes.last"}, {"label": "Free", "value": "azure.vm.memory.free.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000326, "visualization.name": "Network", "visualization.description": "VM Network", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vm.network.traffic.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vm.network.in.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vm.network.out.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.vm.network.traffic.bytes"}, {"type": "metric", "data.point": "azure.vm.network.in.bytes"}, {"type": "metric", "data.point": "azure.vm.network.out.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.vm.network.traffic.bytes", "icon": {"name": "network", "placement": "prefix"}}, "header": {"title": "Network", "style": {"font.size": "medium"}, "data.points": [{"label": "traffic bytes per sec", "value": "azure.vm.network.traffic.bytes.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "In", "value": "azure.vm.network.in.bytes.last"}, {"label": "Out", "value": "azure.vm.network.out.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000327, "visualization.name": "CPU Utilization", "visualization.description": "VM CPU Utilization Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vm.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000328, "visualization.name": "Network Sent/Received Bytes", "visualization.description": "VM Network Sent/Received Bytes", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vm.network.sent.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vm.network.received.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000329, "visualization.name": "Network Sent/Received Packets", "visualization.description": "VM Network Sent/Received Packets", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vm.network.packets", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vm.network.received.packets", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vm.network.sent.packets", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000330, "visualization.name": "Network Traffic", "visualization.description": "VM Network Traffic", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vm.network.in.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vm.network.out.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000331, "visualization.name": "VM Memory Details", "visualization.description": "VM Memory Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vm.memory.free.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vm.memory.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000332, "visualization.name": "Swap Memory Details", "visualization.description": "VM Swap Memory Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vm.swap.memory.free.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vm.swap.memory.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000333, "visualization.name": "Pages Details", "visualization.description": "VM Pages Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vm.memory.pages.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vm.memory.page.faults.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vm.memory.page.reads.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vm.memory.page.writes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000334, "visualization.name": "Disk IO Read/Write Bytes", "visualization.description": "VM Disk IO Read/Write Bytes", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vm.disk.io.read.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.vm.disk.io.write.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]