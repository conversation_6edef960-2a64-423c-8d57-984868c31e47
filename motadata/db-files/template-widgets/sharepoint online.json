[{"_type": "0", "id": 10000000000616, "visualization.name": "Sites", "visualization.description": "Sharepoint Sites", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sharepoint.online.sites", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sharepoint.online.active.sites", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sharepoint.online.sites"}, {"type": "metric", "data.point": "sharepoint.online.active.sites"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "sharepoint.online.sites", "icon": {"name": "globe", "placement": "prefix"}}, "header": {"title": "Sharepoint Sites", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Active", "value": "sharepoint.online.active.sites.last"}, {"label": "Total", "value": "sharepoint.online.sites.last"}]}}}}, {"_type": "0", "id": 10000000000617, "visualization.name": "Users", "visualization.description": "Sharepoint Users", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sharepoint.online.active.users", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sharepoint.online.inactive.users", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sharepoint.online.active.users"}, {"type": "metric", "data.point": "sharepoint.online.inactive.users"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "sharepoint.online.active.users", "icon": {"name": "users", "placement": "prefix"}}, "header": {"title": "Sharepoint Users", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Active", "value": "sharepoint.online.active.users.last"}, {"label": "Inactive", "value": "sharepoint.online.inactive.users.last"}]}}}}, {"_type": "0", "id": 10000000000618, "visualization.name": "Sharepoint Files", "visualization.description": "Sharepoint Files", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sharepoint.online.active.files", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sharepoint.online.files", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sharepoint.online.active.files"}, {"type": "metric", "data.point": "sharepoint.online.files"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "sharepoint.online.active.files", "icon": {"name": "files", "placement": "prefix"}}, "header": {"title": "Files", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Active", "value": "sharepoint.online.active.files.last"}, {"label": "Total", "value": "sharepoint.online.files.last"}]}}}}, {"_type": "0", "id": 10000000000619, "visualization.name": "Files Shared", "visualization.description": "Sharepoint Files Shared", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sharepoint.online.shared.internal.files", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sharepoint.online.shared.external.files", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sharepoint.online.shared.internal.files"}, {"type": "metric", "data.point": "sharepoint.online.shared.external.files"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "sharepoint.online.shared.external.files", "icon": {"name": "files-shared", "placement": "prefix"}}, "header": {"title": "Files Shared", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Internal", "value": "sharepoint.online.shared.internal.files.last"}, {"label": "External", "value": "sharepoint.online.shared.external.files.last"}]}}}}, {"_type": "0", "id": 10000000000620, "visualization.name": "Total Storage Used", "visualization.description": "Sharepoint Total Storage Used", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sharepoint.online.storage.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sharepoint.online.storage.used.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "sharepoint.online.storage.used.bytes", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Total Storage Used", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "sharepoint.online.storage.used.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000621, "visualization.name": "Response Time", "visualization.description": "Sharepoint Response Time", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cloud.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cloud.latency.ms"}]}], "visualization.properties": {"gauge": {"header": {"title": "Response Time", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cloud.latency.ms.last"}]}, "style": {"icon": {"name": "response-time"}, "color.data.point": "cloud.latency.ms"}}}}, {"_type": "0", "id": 10000000000622, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000623, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000624, "visualization.name": "Response Time", "visualization.description": "Sharepoint Response Time", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cloud.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000625, "visualization.name": "Top 5 Sharepoint Sites By Storage Usage", "visualization.description": "Sharepoint Top 5 Sharepoint Sites By Storage Usage", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sharepoint.online.site~storage.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": "sharepoint.online.site~storage.used.bytes.avg"}}}}, {"_type": "0", "id": 10000000000626, "visualization.name": "Top 5 Sites By Active Sites", "visualization.description": "Sharepoint Top 5 Sites By Active Sites", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sharepoint.online.file.activity~events", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": "sharepoint.online.file.activity~events.avg"}}}}, {"_type": "0", "id": 10000000000628, "visualization.name": "Sharepoint Online Sites Details", "visualization.description": "Sharepoint Online Sites Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sharepoint.online.site~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sharepoint.online.site~files", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sharepoint.online.site~active.files", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sharepoint.online.site~storage.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sharepoint.online.site~storage.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sharepoint.online.site~last.modified", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sharepoint.online.site", "title": "Link", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sharepoint.online.site~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status"}, {"name": "Files", "title": "Files", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "formula": {"operation": "combine", "columns": ["sharepoint.online.site~files.last", "sharepoint.online.site~active.files.last"]}, "style": {"classes": ["font-bold"], "icon": {"name": "copy", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "sharepoint.online.site~files.last", "title": "Total", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"]}}, {"name": "sharepoint.online.site~active.files.last", "title": "Active", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"]}}, {"name": "Bytes", "title": "Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "formula": {"operation": "combine", "columns": ["sharepoint.online.site~storage.capacity.bytes.last", "sharepoint.online.site~storage.used.bytes.last"]}, "style": {"classes": ["font-bold"], "icon": {"name": "sd-card", "placement": "prefix", "classes": ["text-secondary-yellow"]}}}, {"name": "sharepoint.online.site~storage.capacity.bytes.last", "title": "Capacity", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"]}}, {"name": "sharepoint.online.site~storage.used.bytes.last", "title": "Used", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"]}}, {"name": "sharepoint.online.site~last.modified.last", "title": "Last Modified", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "calendar-alt", "placement": "prefix", "classes": ["text-secondary-orange"]}}}]}}}]