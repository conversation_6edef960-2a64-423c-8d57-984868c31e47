[{"_type": "0", "id": 10000000001910, "visualization.name": "Data Volume", " visualization.description": "Data Volume Sap MAXDB", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.maxdb.data.volume~used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sap.maxdb.data.volume~used.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "sap.maxdb.data.volume~used.percent", "icon": {"name": "database", "placement": "prefix"}}, "header": {"title": "Data Volume", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "sap.maxdb.data.volume~used.percent.last"}]}}}}, {"_type": "0", "id": 10000000001911, "visualization.name": "Log Volume", " visualization.description": "Log Volume Sap MAXDB", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.maxdb.log.volume~used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sap.maxdb.log.volume~used.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "sap.maxdb.log.volume~used.percent", "icon": {"name": "log", "placement": "prefix"}}, "header": {"title": "Log Volume", "style": {"font.size": "medium", "icon": "traffic"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "sap.maxdb.log.volume~used.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000001912, "visualization.name": "Lock Sessions", "visualization.description": "Lock Sessions SAP MAXDB", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.maxdb.blocked.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sap.maxdb.blocked.sessions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "sap.maxdb.blocked.sessions", "icon": {"name": "locks", "placement": "prefix"}}, "header": {"title": "Lock Sessions", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "sap.maxdb.blocked.sessions.last"}]}}}}, {"_type": "0", "id": 10000000001913, "visualization.name": "<PERSON>", "visualization.description": "Data Cache SAP MaxDB", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.maxdb.oms.data.cache.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.maxdb.sql.data.cache.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sap.maxdb.oms.data.cache.bytes"}, {"type": "metric", "data.point": "sap.maxdb.sql.data.cache.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "sap.maxdb.sql.data.cache.bytes", "icon": {"name": "database", "placement": "prefix"}}, "header": {"title": "<PERSON>", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "OMS", "value": "sap.maxdb.oms.data.cache.bytes.last"}, {"label": "SQL", "value": "sap.maxdb.sql.data.cache.bytes.last"}]}}}}, {"_type": "0", "id": 10000000001914, "visualization.name": "Catalog Cache", "visualization.description": "Catalog Cache SAP MaxDB ", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.maxdb.catalog.cache.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sap.maxdb.catalog.cache.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "sap.maxdb.catalog.cache.bytes", "icon": {"name": "files", "placement": "prefix"}}, "header": {"title": "Catalog Cache", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "sap.maxdb.catalog.cache.bytes.last"}]}}}}, {"_type": "0", "id": 10000000001915, "visualization.name": "OMS Heap Used", "visualization.description": "OMS Heap Used SAP MaxDB", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.maxdb.oms.heap~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sap.maxdb.oms.heap~used.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "sap.maxdb.oms.heap~used.bytes", "icon": {"name": "backup", "placement": "prefix"}}, "header": {"title": "OMS Heap Used", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "sap.maxdb.oms.heap~used.bytes.last"}]}}}}, {"_type": "0", "id": 10000000001916, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000001917, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"id": 10000000001918, "_type": "0", "visualization.name": "Session", "visualization.description": "Session SAP MaxDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.maxdb.lock.session", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001919, "_type": "0", "visualization.name": "Locks", "visualization.description": "Locks SAP MaxDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.maxdb.maximum.locks", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "sap.maxdb.used.locks", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001920, "_type": "0", "visualization.name": "Data", "visualization.description": "Data SAP MaxDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.maxdb.total.data.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "sap.maxdb.data.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001921, "visualization.name": "Top 5 Hit Rates", "visualization.description": "Top 5 Hit Rates SAP MaxDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "HorizontalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["sap.maxdb.cache"], "data.points": [{"data.point": "sap.maxdb.cache~hit.ratio.percent", "aggregator": "avg"}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "sap.maxdb.cache~hit.ratio.percent.avg"}}}, "visualization.result.by": ["sap.maxdb.cache"]}, {"id": 10000000001922, "visualization.name": "Top 5 Miss Rates", "visualization.description": "Top 5 Miss Rates SAP MaxDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "HorizontalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["sap.maxdb.cache"], "data.points": [{"data.point": "sap.maxdb.cache~hit.ratio.percent", "aggregator": "avg"}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "sap.maxdb.cache~hit.ratio.percent.avg"}}}, "visualization.result.by": ["sap.maxdb.cache"]}, {"_type": "0", "id": 10000000001923, "visualization.name": "Disk Volume Details", "visualization.description": "Disk Volume Details SAP MaxDB", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.maxdb.data.volume~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"view": "Storage Details", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.maxdb.data.volume", "alias": "system.disk.volume", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.maxdb.data.volume~used.bytes.last", "alias": "system.disk.volume.used.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000001924, "_type": "0", "visualization.name": "<PERSON><PERSON>", "visualization.description": "IO Buffer SAP MaxDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.maxdb.io.buffer.cache.bytes", "aggregator": "avg"}, {"data.point": "sap.maxdb.io.buffer.data.cache.bytes", "aggregator": "avg"}, {"data.point": "sap.maxdb.io.buffer.free.bytes", "aggregator": "avg"}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]